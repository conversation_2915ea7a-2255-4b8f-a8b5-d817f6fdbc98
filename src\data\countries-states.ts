// 国家和省/州数据
export interface Country {
  code: string;
  name: string;
  states?: State[];
}

export interface State {
  code: string;
  name: string;
}

export const countriesWithStates: Country[] = [
  {
    code: 'US',
    name: 'United States',
    states: [
      { code: 'AL', name: 'Alabama' },
      { code: 'AK', name: 'Alaska' },
      { code: 'AZ', name: 'Arizona' },
      { code: 'AR', name: 'Arkansas' },
      { code: 'CA', name: 'California' },
      { code: 'CO', name: 'Colorado' },
      { code: 'CT', name: 'Connecticut' },
      { code: 'DE', name: 'Delaware' },
      { code: 'FL', name: 'Florida' },
      { code: 'GA', name: 'Georgia' },
      { code: 'HI', name: 'Hawaii' },
      { code: 'ID', name: 'Idaho' },
      { code: 'IL', name: 'Illinois' },
      { code: 'IN', name: 'Indiana' },
      { code: 'IA', name: 'Iowa' },
      { code: 'K<PERSON>', name: 'Kansas' },
      { code: 'K<PERSON>', name: 'Kentucky' },
      { code: 'LA', name: 'Louisiana' },
      { code: 'ME', name: 'Maine' },
      { code: 'MD', name: 'Maryland' },
      { code: 'MA', name: 'Massachusetts' },
      { code: 'MI', name: 'Michigan' },
      { code: 'MN', name: 'Minnesota' },
      { code: 'MS', name: 'Mississippi' },
      { code: 'MO', name: 'Missouri' },
      { code: 'MT', name: 'Montana' },
      { code: 'NE', name: 'Nebraska' },
      { code: 'NV', name: 'Nevada' },
      { code: 'NH', name: 'New Hampshire' },
      { code: 'NJ', name: 'New Jersey' },
      { code: 'NM', name: 'New Mexico' },
      { code: 'NY', name: 'New York' },
      { code: 'NC', name: 'North Carolina' },
      { code: 'ND', name: 'North Dakota' },
      { code: 'OH', name: 'Ohio' },
      { code: 'OK', name: 'Oklahoma' },
      { code: 'OR', name: 'Oregon' },
      { code: 'PA', name: 'Pennsylvania' },
      { code: 'RI', name: 'Rhode Island' },
      { code: 'SC', name: 'South Carolina' },
      { code: 'SD', name: 'South Dakota' },
      { code: 'TN', name: 'Tennessee' },
      { code: 'TX', name: 'Texas' },
      { code: 'UT', name: 'Utah' },
      { code: 'VT', name: 'Vermont' },
      { code: 'VA', name: 'Virginia' },
      { code: 'WA', name: 'Washington' },
      { code: 'WV', name: 'West Virginia' },
      { code: 'WI', name: 'Wisconsin' },
      { code: 'WY', name: 'Wyoming' },
      { code: 'DC', name: 'District of Columbia' }
    ]
  },
  {
    code: 'CA',
    name: 'Canada',
    states: [
      { code: 'AB', name: 'Alberta' },
      { code: 'BC', name: 'British Columbia' },
      { code: 'MB', name: 'Manitoba' },
      { code: 'NB', name: 'New Brunswick' },
      { code: 'NL', name: 'Newfoundland and Labrador' },
      { code: 'NS', name: 'Nova Scotia' },
      { code: 'ON', name: 'Ontario' },
      { code: 'PE', name: 'Prince Edward Island' },
      { code: 'QC', name: 'Quebec' },
      { code: 'SK', name: 'Saskatchewan' },
      { code: 'NT', name: 'Northwest Territories' },
      { code: 'NU', name: 'Nunavut' },
      { code: 'YT', name: 'Yukon' }
    ]
  },
  {
    code: 'AU',
    name: 'Australia',
    states: [
      { code: 'NSW', name: 'New South Wales' },
      { code: 'QLD', name: 'Queensland' },
      { code: 'SA', name: 'South Australia' },
      { code: 'TAS', name: 'Tasmania' },
      { code: 'VIC', name: 'Victoria' },
      { code: 'WA', name: 'Western Australia' },
      { code: 'ACT', name: 'Australian Capital Territory' },
      { code: 'NT', name: 'Northern Territory' }
    ]
  },
  {
    code: 'CN',
    name: 'China',
    states: [
      { code: 'BJ', name: 'Beijing' },
      { code: 'TJ', name: 'Tianjin' },
      { code: 'HE', name: 'Hebei' },
      { code: 'SX', name: 'Shanxi' },
      { code: 'NM', name: 'Inner Mongolia' },
      { code: 'LN', name: 'Liaoning' },
      { code: 'JL', name: 'Jilin' },
      { code: 'HL', name: 'Heilongjiang' },
      { code: 'SH', name: 'Shanghai' },
      { code: 'JS', name: 'Jiangsu' },
      { code: 'ZJ', name: 'Zhejiang' },
      { code: 'AH', name: 'Anhui' },
      { code: 'FJ', name: 'Fujian' },
      { code: 'JX', name: 'Jiangxi' },
      { code: 'SD', name: 'Shandong' },
      { code: 'HA', name: 'Henan' },
      { code: 'HB', name: 'Hubei' },
      { code: 'HN', name: 'Hunan' },
      { code: 'GD', name: 'Guangdong' },
      { code: 'GX', name: 'Guangxi' },
      { code: 'HI', name: 'Hainan' },
      { code: 'CQ', name: 'Chongqing' },
      { code: 'SC', name: 'Sichuan' },
      { code: 'GZ', name: 'Guizhou' },
      { code: 'YN', name: 'Yunnan' },
      { code: 'XZ', name: 'Tibet' },
      { code: 'SN', name: 'Shaanxi' },
      { code: 'GS', name: 'Gansu' },
      { code: 'QH', name: 'Qinghai' },
      { code: 'NX', name: 'Ningxia' },
      { code: 'XJ', name: 'Xinjiang' },
      { code: 'HK', name: 'Hong Kong' },
      { code: 'MO', name: 'Macau' }
    ]
  },
  {
    code: 'DE',
    name: 'Germany',
    states: [
      { code: 'BW', name: 'Baden-Württemberg' },
      { code: 'BY', name: 'Bavaria' },
      { code: 'BE', name: 'Berlin' },
      { code: 'BB', name: 'Brandenburg' },
      { code: 'HB', name: 'Bremen' },
      { code: 'HH', name: 'Hamburg' },
      { code: 'HE', name: 'Hesse' },
      { code: 'MV', name: 'Mecklenburg-Vorpommern' },
      { code: 'NI', name: 'Lower Saxony' },
      { code: 'NW', name: 'North Rhine-Westphalia' },
      { code: 'RP', name: 'Rhineland-Palatinate' },
      { code: 'SL', name: 'Saarland' },
      { code: 'SN', name: 'Saxony' },
      { code: 'ST', name: 'Saxony-Anhalt' },
      { code: 'SH', name: 'Schleswig-Holstein' },
      { code: 'TH', name: 'Thuringia' }
    ]
  },
  {
    code: 'IN',
    name: 'India',
    states: [
      { code: 'AP', name: 'Andhra Pradesh' },
      { code: 'AR', name: 'Arunachal Pradesh' },
      { code: 'AS', name: 'Assam' },
      { code: 'BR', name: 'Bihar' },
      { code: 'CT', name: 'Chhattisgarh' },
      { code: 'GA', name: 'Goa' },
      { code: 'GJ', name: 'Gujarat' },
      { code: 'HR', name: 'Haryana' },
      { code: 'HP', name: 'Himachal Pradesh' },
      { code: 'JH', name: 'Jharkhand' },
      { code: 'KA', name: 'Karnataka' },
      { code: 'KL', name: 'Kerala' },
      { code: 'MP', name: 'Madhya Pradesh' },
      { code: 'MH', name: 'Maharashtra' },
      { code: 'MN', name: 'Manipur' },
      { code: 'ML', name: 'Meghalaya' },
      { code: 'MZ', name: 'Mizoram' },
      { code: 'NL', name: 'Nagaland' },
      { code: 'OR', name: 'Odisha' },
      { code: 'PB', name: 'Punjab' },
      { code: 'RJ', name: 'Rajasthan' },
      { code: 'SK', name: 'Sikkim' },
      { code: 'TN', name: 'Tamil Nadu' },
      { code: 'TG', name: 'Telangana' },
      { code: 'TR', name: 'Tripura' },
      { code: 'UP', name: 'Uttar Pradesh' },
      { code: 'UT', name: 'Uttarakhand' },
      { code: 'WB', name: 'West Bengal' },
      { code: 'DL', name: 'Delhi' }
    ]
  },
  {
    code: 'BR',
    name: 'Brazil',
    states: [
      { code: 'AC', name: 'Acre' },
      { code: 'AL', name: 'Alagoas' },
      { code: 'AP', name: 'Amapá' },
      { code: 'AM', name: 'Amazonas' },
      { code: 'BA', name: 'Bahia' },
      { code: 'CE', name: 'Ceará' },
      { code: 'DF', name: 'Distrito Federal' },
      { code: 'ES', name: 'Espírito Santo' },
      { code: 'GO', name: 'Goiás' },
      { code: 'MA', name: 'Maranhão' },
      { code: 'MT', name: 'Mato Grosso' },
      { code: 'MS', name: 'Mato Grosso do Sul' },
      { code: 'MG', name: 'Minas Gerais' },
      { code: 'PA', name: 'Pará' },
      { code: 'PB', name: 'Paraíba' },
      { code: 'PR', name: 'Paraná' },
      { code: 'PE', name: 'Pernambuco' },
      { code: 'PI', name: 'Piauí' },
      { code: 'RJ', name: 'Rio de Janeiro' },
      { code: 'RN', name: 'Rio Grande do Norte' },
      { code: 'RS', name: 'Rio Grande do Sul' },
      { code: 'RO', name: 'Rondônia' },
      { code: 'RR', name: 'Roraima' },
      { code: 'SC', name: 'Santa Catarina' },
      { code: 'SP', name: 'São Paulo' },
      { code: 'SE', name: 'Sergipe' },
      { code: 'TO', name: 'Tocantins' }
    ]
  },
  {
    code: 'MX',
    name: 'Mexico',
    states: [
      { code: 'AGU', name: 'Aguascalientes' },
      { code: 'BCN', name: 'Baja California' },
      { code: 'BCS', name: 'Baja California Sur' },
      { code: 'CAM', name: 'Campeche' },
      { code: 'CHP', name: 'Chiapas' },
      { code: 'CHH', name: 'Chihuahua' },
      { code: 'COA', name: 'Coahuila' },
      { code: 'COL', name: 'Colima' },
      { code: 'DUR', name: 'Durango' },
      { code: 'GUA', name: 'Guanajuato' },
      { code: 'GRO', name: 'Guerrero' },
      { code: 'HID', name: 'Hidalgo' },
      { code: 'JAL', name: 'Jalisco' },
      { code: 'MEX', name: 'Mexico' },
      { code: 'MIC', name: 'Michoacán' },
      { code: 'MOR', name: 'Morelos' },
      { code: 'NAY', name: 'Nayarit' },
      { code: 'NLE', name: 'Nuevo León' },
      { code: 'OAX', name: 'Oaxaca' },
      { code: 'PUE', name: 'Puebla' },
      { code: 'QUE', name: 'Querétaro' },
      { code: 'ROO', name: 'Quintana Roo' },
      { code: 'SLP', name: 'San Luis Potosí' },
      { code: 'SIN', name: 'Sinaloa' },
      { code: 'SON', name: 'Sonora' },
      { code: 'TAB', name: 'Tabasco' },
      { code: 'TAM', name: 'Tamaulipas' },
      { code: 'TLA', name: 'Tlaxcala' },
      { code: 'VER', name: 'Veracruz' },
      { code: 'YUC', name: 'Yucatán' },
      { code: 'ZAC', name: 'Zacatecas' },
      { code: 'CMX', name: 'Ciudad de México' }
    ]
  },
  {
    code: 'RU',
    name: 'Russia',
    states: [
      { code: 'AD', name: 'Adygea' },
      { code: 'AL', name: 'Altai' },
      { code: 'ALT', name: 'Altai Krai' },
      { code: 'AMU', name: 'Amur' },
      { code: 'ARK', name: 'Arkhangelsk' },
      { code: 'AST', name: 'Astrakhan' },
      { code: 'BA', name: 'Bashkortostan' },
      { code: 'BEL', name: 'Belgorod' },
      { code: 'BRY', name: 'Bryansk' },
      { code: 'BU', name: 'Buryatia' },
      { code: 'CE', name: 'Chechnya' },
      { code: 'CHE', name: 'Chelyabinsk' },
      { code: 'CHU', name: 'Chukotka' },
      { code: 'CU', name: 'Chuvashia' },
      { code: 'DA', name: 'Dagestan' },
      { code: 'IN', name: 'Ingushetia' },
      { code: 'IRK', name: 'Irkutsk' },
      { code: 'IVA', name: 'Ivanovo' },
      { code: 'KAM', name: 'Kamchatka' },
      { code: 'KC', name: 'Karachay-Cherkessia' },
      { code: 'KR', name: 'Karelia' },
      { code: 'KEM', name: 'Kemerovo' },
      { code: 'KHA', name: 'Khabarovsk' },
      { code: 'KK', name: 'Khakassia' },
      { code: 'KHM', name: 'Khanty-Mansi' },
      { code: 'KIR', name: 'Kirov' },
      { code: 'KO', name: 'Komi' },
      { code: 'KOS', name: 'Kostroma' },
      { code: 'KDA', name: 'Krasnodar' },
      { code: 'KYA', name: 'Krasnoyarsk' },
      { code: 'KGD', name: 'Kaliningrad' },
      { code: 'KLU', name: 'Kaluga' },
      { code: 'KUR', name: 'Kurgan' },
      { code: 'KRS', name: 'Kursk' },
      { code: 'LEN', name: 'Leningrad' },
      { code: 'LIP', name: 'Lipetsk' },
      { code: 'MAG', name: 'Magadan' },
      { code: 'ME', name: 'Mari El' },
      { code: 'MO', name: 'Mordovia' },
      { code: 'MOW', name: 'Moscow' },
      { code: 'MOS', name: 'Moscow Oblast' },
      { code: 'MUR', name: 'Murmansk' },
      { code: 'NEN', name: 'Nenets' },
      { code: 'NIZ', name: 'Nizhny Novgorod' },
      { code: 'NGR', name: 'Novgorod' },
      { code: 'NVS', name: 'Novosibirsk' },
      { code: 'OMS', name: 'Omsk' },
      { code: 'ORE', name: 'Orenburg' },
      { code: 'ORL', name: 'Oryol' },
      { code: 'PNZ', name: 'Penza' },
      { code: 'PER', name: 'Perm' },
      { code: 'PRI', name: 'Primorsky' },
      { code: 'PSK', name: 'Pskov' },
      { code: 'ROS', name: 'Rostov' },
      { code: 'RYA', name: 'Ryazan' },
      { code: 'SA', name: 'Sakha' },
      { code: 'SAK', name: 'Sakhalin' },
      { code: 'SAM', name: 'Samara' },
      { code: 'SAR', name: 'Saratov' },
      { code: 'SE', name: 'North Ossetia' },
      { code: 'SMO', name: 'Smolensk' },
      { code: 'SPE', name: 'Saint Petersburg' },
      { code: 'STA', name: 'Stavropol' },
      { code: 'SVE', name: 'Sverdlovsk' },
      { code: 'TAM', name: 'Tambov' },
      { code: 'TA', name: 'Tatarstan' },
      { code: 'TOM', name: 'Tomsk' },
      { code: 'TUL', name: 'Tula' },
      { code: 'TVE', name: 'Tver' },
      { code: 'TY', name: 'Tuva' },
      { code: 'TYU', name: 'Tyumen' },
      { code: 'UD', name: 'Udmurtia' },
      { code: 'ULY', name: 'Ulyanovsk' },
      { code: 'VLA', name: 'Vladimir' },
      { code: 'VGG', name: 'Volgograd' },
      { code: 'VLG', name: 'Vologda' },
      { code: 'VOR', name: 'Voronezh' },
      { code: 'YAN', name: 'Yamalo-Nenets' },
      { code: 'YAR', name: 'Yaroslavl' },
      { code: 'YEV', name: 'Jewish Autonomous Oblast' },
      { code: 'ZAB', name: 'Zabaykalsky' }
    ]
  }
];

// 其他国家（没有预定义省/州的国家）
export const otherCountries: Country[] = [
  { code: 'GB', name: 'United Kingdom' },
  { code: 'FR', name: 'France' },
  { code: 'IT', name: 'Italy' },
  { code: 'ES', name: 'Spain' },
  { code: 'JP', name: 'Japan' },
  { code: 'KR', name: 'South Korea' },
  { code: 'SG', name: 'Singapore' },
  { code: 'HK', name: 'Hong Kong' },
  { code: 'TW', name: 'Taiwan' },
  { code: 'MX', name: 'Mexico' },
  { code: 'NL', name: 'Netherlands' },
  { code: 'BE', name: 'Belgium' },
  { code: 'CH', name: 'Switzerland' },
  { code: 'AT', name: 'Austria' },
  { code: 'SE', name: 'Sweden' },
  { code: 'NO', name: 'Norway' },
  { code: 'DK', name: 'Denmark' },
  { code: 'FI', name: 'Finland' },
  { code: 'PL', name: 'Poland' },
  { code: 'CZ', name: 'Czech Republic' },
  { code: 'HU', name: 'Hungary' },
  { code: 'RO', name: 'Romania' },
  { code: 'BG', name: 'Bulgaria' },
  { code: 'HR', name: 'Croatia' },
  { code: 'SI', name: 'Slovenia' },
  { code: 'SK', name: 'Slovakia' },
  { code: 'LT', name: 'Lithuania' },
  { code: 'LV', name: 'Latvia' },
  { code: 'EE', name: 'Estonia' },
  { code: 'IE', name: 'Ireland' },
  { code: 'PT', name: 'Portugal' },
  { code: 'GR', name: 'Greece' },
  { code: 'CY', name: 'Cyprus' },
  { code: 'MT', name: 'Malta' },
  { code: 'LU', name: 'Luxembourg' },
  { code: 'IS', name: 'Iceland' },
  { code: 'NZ', name: 'New Zealand' },
  { code: 'TH', name: 'Thailand' },
  { code: 'VN', name: 'Vietnam' },
  { code: 'MY', name: 'Malaysia' },
  { code: 'ID', name: 'Indonesia' },
  { code: 'PH', name: 'Philippines' },
  { code: 'AE', name: 'United Arab Emirates' },
  { code: 'SA', name: 'Saudi Arabia' },
  { code: 'IL', name: 'Israel' },
  { code: 'TR', name: 'Turkey' },
  { code: 'RU', name: 'Russia' },
  { code: 'ZA', name: 'South Africa' }
];

// 合并所有国家
export const allCountries: Country[] = [...countriesWithStates, ...otherCountries];

// 根据国家代码获取省/州列表
export const getStatesByCountry = (countryCode: string): State[] => {
  const country = countriesWithStates.find(c => c.code === countryCode);
  return country?.states || [];
};

// 检查国家是否有预定义的省/州
export const hasStates = (countryCode: string): boolean => {
  return countriesWithStates.some(c => c.code === countryCode);
};
