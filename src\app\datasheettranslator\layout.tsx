import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Datasheet Translator - PDF Translate Tool | China Electron',
  description: 'Professional PDF translator for electronic component datasheets. Translate technical documents with our advanced PDF translate tool. Free online PDF translator for electronic components.',
  keywords: 'datasheet translator, PDF translate, PDF translator, technical document translator, electronic component datasheet, PDF translation tool',
  alternates: {
    canonical: 'https://www.chinaelectron.com/datasheettranslator',
  },
  openGraph: {
    title: 'Datasheet Translator - PDF Translate Tool | China Electron',
    description: 'Professional PDF translator for electronic component datasheets. Translate technical documents with our advanced PDF translate tool.',
    url: 'https://www.chinaelectron.com/datasheettranslator',
    type: 'website',
  },
}

export default function DatasheetTranslatorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
