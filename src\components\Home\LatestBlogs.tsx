import Image from 'next/image'
import Link from 'next/link'

export default function LatestBlogs() {
  const blogs = [
    {
      id: '1',
      title: 'The Future of AI in Electronic Component Selection',
      image: 'https://dummyimage.com/800x400/0057b8/ffffff.png&text=AI+Components',
      date: '2024-03-20',
      category: 'AI Technology',
      excerpt: 'Discover how artificial intelligence is revolutionizing the electronic component selection process...'
    },
    {
      id: '2',
      title: 'Supply Chain Resilience in Electronics',
      image: 'https://dummyimage.com/800x400/0057b8/ffffff.png&text=Supply+Chain',
      date: '2024-03-18',
      category: 'Industry Insights',
      excerpt: 'Learn strategies for building a resilient electronic components supply chain...'
    },
    {
      id: '3',
      title: 'PCB Design Best Practices 2024',
      image: 'https://dummyimage.com/800x400/0057b8/ffffff.png&text=PCB+Design',
      date: '2024-03-15',
      category: 'Design Tips',
      excerpt: 'Updated guidelines for modern PCB design, including AI-assisted routing...'
    }
  ]

  return (
    <section className="py-16">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-3xl font-bold">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
              Latest from Blog
            </span>
          </h2>
          <Link href="#" className="text-blue-600 hover:text-blue-800">
            View All Posts →
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {blogs.map((blog) => (
            <Link
              key={blog.id}
              href="#"
              className="group"
            >
              <div className="relative h-48 mb-4 rounded-xl overflow-hidden">
                <Image
                  src={blog.image}
                  alt={blog.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                <div className="absolute bottom-4 left-4 text-white">
                  <span className="text-sm bg-blue-600 px-2 py-1 rounded">
                    {blog.category}
                  </span>
                </div>
              </div>
              <h3 className="font-semibold text-xl mb-2 group-hover:text-blue-600">
                {blog.title}
              </h3>
              <p className="text-gray-600 text-sm mb-2">{blog.excerpt}</p>
              <time className="text-sm text-gray-500">{blog.date}</time>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
} 