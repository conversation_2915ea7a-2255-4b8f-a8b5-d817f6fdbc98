// Manufacturer interface definition
export interface Brand {
  id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  website: string;
  logo_url: string;
  domestic?: string;
}

// 分页接口定义
export interface PaginatedResponse<T> {
  results: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Get all manufacturers with pagination
export async function fetchAllBrands(page: number = 1, limit: number = 20): Promise<{
  brands: Brand[], 
  total: number,
  currentPage: number,
  totalPages: number,
  error: string | null
}> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/brands?page=${page}&limit=${limit}`, {
      next: { revalidate: 86400 } // 24小时缓存
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }
    
    const data = await response.json() as PaginatedResponse<Brand>;
    
    /* console.log('API Response:', { 
      resultsLength: data.results?.length, 
      total: data.total, 
      page: data.page, 
      limit: data.limit,
      totalPages: data.totalPages || Math.ceil(data.total / data.limit)
    }); */
    
    // 计算总页数，如果API没有返回totalPages
    const calculatedTotalPages = Math.ceil(data.total / data.limit);
    
    return { 
      brands: data.results || [], 
      total: data.total || 0,
      currentPage: data.page || 1,
      totalPages: data.totalPages || calculatedTotalPages,
      error: null 
    };
  } catch (err) {
    console.error('Failed to fetch manufacturer list:', err);
    return { 
      brands: [], 
      total: 0,
      currentPage: page,
      totalPages: 0,
      error: err instanceof Error ? err.message : 'Unknown error' 
    };
  }
}

// Get Chinese manufacturers with pagination
export async function fetchChineseBrands(page: number = 1, limit: number = 20): Promise<{
  brands: Brand[], 
  total: number,
  currentPage: number,
  totalPages: number,
  error: string | null
}> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/brands?domestic=true&page=${page}&limit=${limit}`, {
      next: { revalidate: 86400 } // 24小时缓存
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }
    
    const data = await response.json() as PaginatedResponse<Brand>;
    
    // 计算总页数，如果API没有返回totalPages
    const calculatedTotalPages = Math.ceil(data.total / data.limit);
    
    return { 
      brands: data.results || [], 
      total: data.total || 0,
      currentPage: data.page || 1,
      totalPages: data.totalPages || calculatedTotalPages,
      error: null 
    };
  } catch (err) {
    console.error('Failed to fetch Chinese manufacturer list:', err);
    return { 
      brands: [], 
      total: 0,
      currentPage: page,
      totalPages: 0,
      error: err instanceof Error ? err.message : 'Unknown error' 
    };
  }
}

// Get international manufacturers with pagination
export async function fetchInternationalBrands(page: number = 1, limit: number = 20): Promise<{
  brands: Brand[], 
  total: number,
  currentPage: number,
  totalPages: number,
  error: string | null
}> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/brands?domestic=false&page=${page}&limit=${limit}`, {
      next: { revalidate: 86400 } // 24小时缓存
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }
    
    const data = await response.json() as PaginatedResponse<Brand>;
    
    // 计算总页数，如果API没有返回totalPages
    const calculatedTotalPages = Math.ceil(data.total / data.limit);
    
    return { 
      brands: data.results || [], 
      total: data.total || 0,
      currentPage: data.page || 1,
      totalPages: data.totalPages || calculatedTotalPages,
      error: null 
    };
  } catch (err) {
    console.error('Failed to fetch international manufacturer list:', err);
    return { 
      brands: [], 
      total: 0,
      currentPage: page,
      totalPages: 0,
      error: err instanceof Error ? err.message : 'Unknown error' 
    };
  }
} 