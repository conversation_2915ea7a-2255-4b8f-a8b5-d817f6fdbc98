我将分析您的项目中访问产品详情页面的所有路径和相关组件。

Search files...

Search files...

Search files...

Search files...

Search files...

Search files...
基于搜索结果，我将列出所有访问产品详情页面的路径及相关组件：

## 产品详情页面

1. **路径**: `/product/[id]`
2. **页面文件**: `src/app/product/[id]/page.tsx`
3. **数据获取API**: 
   - `https://webapi.962692556.workers.dev/products/${encodeURIComponent(productId)}`
   - `https://webapi.962692556.workers.dev/brand-distributors?brand_id=${encodeURIComponent(brandId)}`

## 跳转到产品详情页的来源页面和组件

### 1. 首页组件

#### 1.1 清仓/特价产品区域
- **路径**: `/` (首页)
- **组件**: 首页内的清仓产品区域
- **跳转代码**: 
```tsx
<Link 
  key={item.product_id}
  href={`/product/${item.product_id}`}
  className="bg-white/95 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all flex flex-col items-center justify-center h-16 overflow-hidden hover:scale-105 transform duration-200"
>



```
- **数据来源API** https://get-hot.962692556.workers.dev/latest


#### 1.2 每日热门组件 (DailyHot)
- **文件**: `src/components/Home/DailyHot.tsx`
- **跳转代码**:
```tsx
<Link
  href={`/product/${item.product_id}`}
  key={`${item.product_id}-${index}`}
  className="block py-2 px-3 rounded-lg hover:bg-white/80 transition-all duration-200 group"
>
```
- **数据来源**: 服务端提供的 `initialItems` 或客户端从API获取

- **数据来源API**https://get-hot.962692556.workers.dev/latest

#### 1.3 推荐产品组件 (RecommendedProducts)
- **文件**: `src/components/Home/RecommendedProducts.tsx`
- **注意**: 当前显示的是静态数据，不含产品链接，但应该会被修改为动态数据并添加链接
- **数据来源API** https://get-recommend.962692556.workers.dev/latest-recommend

### 2. 品牌页面

#### 2.1 品牌详情页
- **路径**: `/manufacturers/[id]`
- **文件**: `src/app/manufacturers/[id]/page.tsx`
- **跳转组件**: ProductList组件
- **跳转代码**: 
```tsx
<Link
  key={product.product_id}
  href={`/product/${product.product_id}`}
  className="bg-white border border-gray-100 rounded-lg p-4 hover:shadow-md hover:border-[#DE2910]/20 transition-all"
>
```
- **数据来源API**: `https://webapi.962692556.workers.dev/products?brand=${brandId}`

### 3. 搜索结果页面

#### 3.1 搜索结果卡片
- **路径**: `/search`
- **文件**: `src/components/Search/SearchResultCard.tsx`
- **跳转代码**: 
```tsx
<Link href={`/product/${result.id}`} className="hover:text-[#DE2910]">
  {result.metadata.name}
</Link>
```
- **数据来源API**: `https://webapi.962692556.workers.dev/products?${queryParams.toString()}`

### 4. 每日热门产品页面

- **路径**: `/dailyhot`
- **文件**: `src/app/dailyhot/page.tsx`
- **数据来源API**: 
  - `https://get-hot.962692556.workers.dev/latest?type=hot`
  - `https://webapi.962692556.workers.dev/products/models/check`

### 5. 产品详情页中的相关链接

#### 5.1 组件概览中的制造商链接
- **文件**: `src/components/ComponentDetail/ComponentOverview.tsx`
- **跳转代码**:
```tsx
<Link 
  href={`/manufacturers/${(component.metadata as any)?.brand?.id || ''}`}
  className="hover:text-[#DE2910] transition-colors"
>
  {manufacturer}
</Link>
```

## 产品详情页客户端组件

产品详情页使用 `ProductClientWrapper` 组件包装客户端交互逻辑:
- **文件**: `src/components/ClientComponents/ProductClientWrapper.tsx`
- **子组件**:
  - `ComponentOverview`: 产品概览
  - `Specifications`: 产品规格
  - `Distributors`: 分销商信息
  - `AssistInquiry`: 询价辅助

## 数据流转

1. 服务端获取产品详情数据
2. 数据转换为前端组件需要的格式 (通过 `convertToComponentFormat` 函数)
3. 传递给 `ProductClientWrapper` 组件进行客户端渲染

## 主要数据源API

1. 产品详情API: `https://webapi.962692556.workers.dev/products/${productId}`
2. 品牌分销商API: `https://webapi.962692556.workers.dev/brand-distributors?brand_id=${brandId}`
3. 按品牌获取产品API: `https://webapi.962692556.workers.dev/products?brand=${brandId}`
4. 搜索产品API: `https://webapi.962692556.workers.dev/products?${queryParams}`
5. 热门产品API: `https://get-hot.962692556.workers.dev/latest?type=hot`
6. 产品批量检查API: `https://webapi.962692556.workers.dev/products/models/check`

所有这些路径和组件都使用相同的模式 `/product/${productId}` 访问产品详情页，您需要确保在调整数据接口时保持这种URL结构的一致性，或者更新所有引用。
