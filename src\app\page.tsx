/**
 * Homepage with Server-Side Rendering (SSR)
 * 
 * This page implements Server-Side Rendering to improve SEO performance and initial load time.
 * Key implementation points:
 * 1. Data fetching occurs on the server using async functions
 * 2. The React component is declared as async to allow server-side data fetching
 * 3. Initial data is passed to client components as props
 * 4. All data fetching uses 'cache: no-store' to ensure fresh data
 * 5. Next.js revalidation is set to 3600 seconds (1 hour) for data refresh
 */
import RecommendedSearches from '@/components/Home/RecommendedSearches'
import SideAds from '@/components/Home/SideAds'
import AdCarousel from '@/components/Home/AdCarousel'
import DailyHot from '@/components/Home/DailyHot'
import PreferredManufacture from '@/components/Home/PreferredManufacture'
import TrustLogos from '@/components/Home/TrustLogos'
import categoryMapping from '@/data/category_mapping.json'
import Image from 'next/image'
import Link from 'next/link'
import EmailSubscriptionForm from '@/components/Home/EmailSubscriptionForm'
import ClientTestimonials from '@/components/Home/ClientTestimonials'
import { buildProductUrl } from '@/utils/productUrl'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import type { Metadata } from 'next'

// 将标题转换为URL友好的格式
function slugifyTitle(title: string): string {
  // 将所有符号和空格替换为连字符，并将连续的连字符替换为单个连字符
  return title
    .replace(/[^\w\s]/g, '-') // 将所有非字母数字字符替换为连字符
    .replace(/\s+/g, '-')     // 将空格替换为连字符
    .replace(/-+/g, '-')      // 将连续的连字符替换为单个连字符
    .replace(/^-|-$/g, '')    // 移除开头和结尾的连字符
    .toLowerCase();           // 转为小写
}

// 生成首页的元数据，包含canonical URL
export const generateMetadata = (): Metadata => {
  return {
    title: "China Electron - Global B2B Sourcing for Electronic Component Raw Materials",
    description: "China Electron: Your B2B gateway for sourcing electronic component raw materials. Connecting global purchasers, suppliers, traders, and manufacturers of IC chips, components, and accessories.",
    keywords: "China Electron, electronic raw materials, B2B sourcing, component marketplace, IC chips, global suppliers, electronic components, raw material procurement",
    alternates: {
      canonical: getCanonicalUrl(),
    },
  };
};

// 示例数据
const categoryItems = Object.entries(categoryMapping).map(([key, value]) => ({
  id: key,
  name: value.name,
  image: `https://dummyimage.com/200x200/0057b8/ffffff.png&text=${encodeURIComponent(value.name)}`,
  description: 'items' in value 
    ? `${value.items?.length || 0} products`
    : `${Object.keys(value.sub_categories || {}).length} subcategories`,
  count: 'items' in value
    ? value.items?.length || 0
    : Object.keys(value.sub_categories || {}).reduce((acc, subKey) => 
        acc + ((value.sub_categories as any)[subKey].items?.length || 0), 0)
}));



// Active Distributors 示例数据 - 5个活跃分销商
const activeDistributors = [
  {
    id: 'active1',
    name_en: 'Huaqiang Electronics',
    logo: 'https://dummyimage.com/200x200/e91e63/ffffff.png&text=HQE',
    description: 'Electronic Components Distributor with extensive inventory and global network'
  },
  {
    id: 'active2',
    name_en: 'Baolian Tech',
    logo: 'https://dummyimage.com/200x200/673ab7/ffffff.png&text=BLT',
    description: 'Specialized in industrial and automotive components supply chain'
  },
  {
    id: 'active3',
    name_en: 'Jingyuan Semiconductor',
    logo: 'https://dummyimage.com/200x200/2196f3/ffffff.png&text=JYS',
    description: 'Premier semiconductor components supplier for Chinese market'
  },
  {
    id: 'active4',
    name_en: 'Tianji Components',
    logo: 'https://dummyimage.com/200x200/009688/ffffff.png&text=TJC',
    description: 'One-stop shop for passive components and connectors'
  },
  {
    id: 'active5',
    name_en: 'Fada Electronics',
    logo: 'https://dummyimage.com/200x200/ff5722/ffffff.png&text=FDE',
    description: 'Specialized in memory and storage components distribution'
  }
];



// 示例的最新报价数据 - 用于在API无法获取数据时作为备用
const sampleQuotationData = [
  {
    id: 1,
    product_id: "67890",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese", // 修改为字符串类型
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 2,
    product_id: "67891",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 3,
    product_id: "67892",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 4,
    product_id: "67893",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 5,
    product_id: "67894",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 6,
    product_id: "67895",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 7,
    product_id: "67896",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 8,
    product_id: "67897",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  },
  {
    id: 9,
    product_id: "67898",
    model: "ATMEGA644PA-AU",
    brand_id: "123",
    brand_name_cn: "ATMEL",
    brand_name_en: "ATMEL",
    brand_name_ru: "ATMEL",
    domestic: "chinese",
    distributor_id: "12345",
    name_cn: "深圳力兴科技有限公司",
    name_en: "Shenzhen Lxw Technology Co., Ltd",
    name_ru: "Шэньчжэнь Лxw Технолоджи",
    logo: "https://example.com/logo.png",
    address_cn: "中国深圳市",
    address_en: "Shenzhen, China",
    address_ru: "Шэньчжэнь, Китай",
    create_at: "2025-05-05T12:34:56.789Z"
  }
];







// 广告数据类型定义
interface AdItem {
  id: string;
  title: string;
  image_url: string;
  link_url: string;
  alt_text: string;
  start_date: string;
  end_date: string;
  status: string;
  priority: number;
  page_location?: string;
}

interface AdsData {
  key: string;
  value: {
    homepage_ads: {
      side_ads: AdItem[];
      bottom_ads: AdItem[];
    }
  }
}

// 推荐搜索接口类型定义
interface RecommendedSearches {
  date: string;
  keywords: string[];
}

// Type definitions
interface HotItem {
  product_id: string
  model: string
  brand_en: string
  brand_cn: string
  domestic: string
  type?: string
  full?: {
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_en: string
      name_cn: string
      name_ru: string
      logo_url?: string
    }
    prices?: {
      quantity: number
      price: number
    }[]
    stock?: number
  }
}

interface AdItem {
  id: string
  title: string
  image_url: string
  link_url: string
  alt_text: string
  start_date: string
  end_date: string
  status: string
  priority: number
  page_location?: string
}

interface AdsData {
  key: string
  value: {
    homepage_ads: {
      side_ads: AdItem[]
      bottom_ads: AdItem[]
    }
  }
}

interface RecommendedSearchData {
  date: string
  keywords: string[]
}

interface Brand {
  brand_id: string
  brand_cn: string
  brand_en: string
  brand_ru: string
  domestic: string
  product_count: number
  distributor_count: number
}

interface BrandsResponse {
  success: boolean
  date: string
  summary: {
    total_brands: number
    total_products: number
    domestic_stats: {
      domestic: {
        count: number
        products: number
      }
      imported: {
        count: number
        products: number
      }
      unknown: {
        count: number
        products: number
      }
    }
  }
  brands: Brand[]
}

// 定义清仓品接口类型
interface LiquidationItem {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_en: string
  brand_cn: string
  domestic: string
  type: string
  full?: {
    url?: string // 添加url字段到full对象
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_en: string
      name_cn: string
      name_ru: string
      logo_url?: string
    }
    prices?: {
      quantity: number
      price: number
    }[]
    stock?: number
  }
}

// 定义分类数据接口
interface CategoryItem {
  code: string;
  parent_code: string | null;
  level: number;
  name_cn: string;
  name_en: string;
  name_ru: string;
  created_at: string;
  children?: Record<string, CategoryItem> | CategoryItem[];
}

interface CategoryResponse {
  success: boolean;
  latest_date: string;
  total_categories: number;
  category_tree: Record<string, CategoryItem>;
}

// 新增 QuotationItem 接口
interface QuotationItem {
  id: number;
  product_id: string;
  model: string;
  brand_id: string;
  brand_name_cn: string;
  brand_name_en: string;
  brand_name_ru: string;
  domestic: string; // 修改为字符串类型
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  logo: string;
  address_cn: string;
  address_en: string;
  address_ru: string;
  create_at: string;
  full?: {
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_en: string
      name_cn: string
      name_ru: string
      logo_url?: string
    }
    prices?: {
      quantity: number
      price: number
    }[]
    stock?: number
  }
}

interface QuotationResponse {
  error: boolean;
  message: string;
  latest_date?: string;
  data: QuotationItem[];
}

// 定义报价数据项的接口
interface QuotationItemWithDistributor extends QuotationItem {
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  logo: string;
  address_cn: string;
  address_en: string;
  address_ru: string;
  create_at: string;
}

// 定义活跃分销商接口
interface ActiveDistributor {
  id: string;
  name_en: string;
  logo: string;
  description: string;
  count?: number;
  latest_time?: number;
}

// 定义专题集合的类型
interface TopicCollection {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle: string;
  description: string;
  cover_image: string;
  tags: string;
  created_at: string;
  updated_at: string;
  author_id: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface InsightsResponse {
  data: TopicCollection[];
  pagination: PaginationData;
}

// 定义博客文章的类型
interface BlogArticle {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle?: string;
  description: string;
  cover_image: string;
  content_markdown: string;
  tags: string;
  category: string;
  location?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
}

interface BlogsResponse {
  data: BlogArticle[];
  pagination: PaginationData;
}

/**
 * 获取最新的专题数据
 * 返回最多4个最新的专题
 */
async function getLatestInsights(): Promise<TopicCollection[]> {
  try {
    // 由于当前API不可用，直接返回空数组
    // 后续API可用时可以取消下面的注释
    
    const response = await fetch('https://blog-manage.chinaelectron.com/api/insights?limit=4', {
      next: { revalidate: 86400 } // 缓存24小时
    });

    if (!response.ok) {
      console.error(`Failed to fetch spotlights: ${response.status} ${response.statusText}`);
      return [];
    }

    const data: InsightsResponse = await response.json();
    
    if (data && Array.isArray(data.data)) {
      return data.data.slice(0, 4); // 确保只取前4个
    }
    
    // 可选：如果需要，可以提供一些模拟数据
    /*
    return [
      {
        id: 1,
        type: 1,
        status: 1,
        title: "Sample Spotlight 1",
        subtitle: "Sample subtitle",
        description: "This is a sample spotlight description",
        cover_image: "https://dummyimage.com/800x600/0057b8/ffffff.png&text=Spotlight+1",
        tags: "sample,spotlight",
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        author_id: "1"
      },
      // 其他模拟数据...
    ];
    */
    
    // 暂时返回空数组
    // console.log('Spotlight功能暂时禁用');
    return [];
  } catch (error) {
    console.error('Error fetching insights:', error);
    return [];
  }
}

/**
 * 获取最新的博客文章数据
 * 返回最多12个最新的文章
 */
async function getLatestInsightArticles(): Promise<BlogArticle[]> {
  try {
    // 由于当前API可能不可用，直接返回空数组
    // 后续API可用时可以取消下面的注释
    
    const response = await fetch('https://blog-manage.chinaelectron.com/api/blogs?limit=12', {
      next: { revalidate: 86400 } // 缓存24小时
    });

    if (!response.ok) {
      console.error(`Failed to fetch blog articles: ${response.status} ${response.statusText}`);
      return [];
    }

    const data: BlogsResponse = await response.json();
    
    if (data && Array.isArray(data.data)) {
      return data.data.slice(0, 12); // 确保只取前12个
    }
    
    // 暂时返回空数组
    // console.log('Blog文章功能暂时禁用');
    return [];
  } catch (error) {
    console.error('Error fetching blog articles:', error);
    return [];
  }
}

/**
 * Server-side function to fetch hot items data
 * This function runs on the server during SSR and provides initial data to the client
 * Returns an array of hot items or an empty array if fetching fails
 */
async function getHotItems() {
  try {
    // Prepare request headers
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'User-Agent': 'ChinaElectron-Server/1.0',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://chinaelectron.com'
    }

    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch('https://get-hot.chinaelectron.com/latest?type=hot', {
      headers,
      signal: controller.signal,
      next: { revalidate: 3600 } // 缓存1小时，减少缓存时间
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`Failed to fetch hot items: ${response.status} ${response.statusText}`)
      return []
    }

    const data = await response.json()

    if (data && data.success && Array.isArray(data.data)) {
      console.log(`Successfully fetched ${data.data.length} hot items from server`)
      // 确保将full字段传递给组件
      return data.data as HotItem[]
    }

    console.warn('Invalid hot items data format received')
    return []
  } catch (error: any) {
    console.error('Error fetching hot items on server:', error.message || error)
    // 返回一些示例数据作为后备，避免完全空白
    return []
  }
}

/**
 * Server-side function to fetch ads data
 * This function runs on the server during SSR and provides initial ads data to the client
 * Returns ads data object
 */
async function getAdsData() {
  try {
    // 暂时注释掉API调用，直接返回默认数据
    /* 
    const response = await fetch('https://ads-manage.chinaelectron.com/api/ads/chinaelectron', {
      next: { revalidate: 86400 } // 缓存24小时
    })

    if (!response.ok) {
      console.error(`Failed to fetch ads data: ${response.status} ${response.statusText}`)
      return { 
        key: 'chinaelectron',
        value: {
          homepage_ads: {
            side_ads: [],
            bottom_ads: []
          }
        }
      } as AdsData
    }

    return await response.json() as AdsData
    */
    
    // 直接返回默认数据结构
    return { 
      key: 'chinaelectron',
      value: {
        homepage_ads: {
          side_ads: [],
          bottom_ads: []
        }
      }
    } as AdsData
  } catch (error) {
    // 不再记录错误，直接返回默认数据
    return { 
      key: 'chinaelectron',
      value: {
        homepage_ads: {
          side_ads: [],
          bottom_ads: []
        }
      }
    } as AdsData
  }
}

/**
 * Server-side function to fetch brands data
 * This function runs on the server during SSR and provides initial brands data to the client
 * Returns an array of brands sorted by distributor_count or an empty array if fetching fails
 */
async function getBrandsData() {
  try {
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    }

    const response = await fetch('https://get-hot.chinaelectron.com/brands', {
      headers,
      next: { revalidate: 86400 } // 缓存24小时
    })

    if (!response.ok) {
      console.error(`Failed to fetch brands data: ${response.status} ${response.statusText}`)
      return []
    }

    const data = await response.json() as BrandsResponse
    
    if (data && data.success && Array.isArray(data.brands)) {
      // Sort by distributor_count and filter to only include brands with at least 10 distributors and domestic is "chinese"
      const filteredBrands = [...data.brands]
        .filter(brand => brand.distributor_count >= 10 && brand.domestic === "chinese")
        .sort((a, b) => b.distributor_count - a.distributor_count);
      
      // Take top 10 brands
      const topBrands = filteredBrands.slice(0, 10);
      
      // 不再通过服务器获取logo，直接使用原始数据
      return topBrands;
    }
    
    return []
  } catch (error) {
    console.error('Error fetching brands data:', error)
    return []
  }
}

/**
 * Server-side function to fetch recommended searches
 * This function runs on the server during SSR and provides initial search keywords to the client
 * Returns an array of keywords or an empty array if fetching fails
 */
async function getRecommendedSearches() {
  try {
    const response = await fetch('https://get-recommend.chinaelectron.com/latest-recommend', {
      next: { revalidate: 86400 } // 缓存24小时
    })

    if (!response.ok) {
      console.error(`Failed to fetch recommended searches: ${response.status} ${response.statusText}`)
      return []
    }

    const data = await response.json() as RecommendedSearchData
    return data.keywords || []
  } catch (error) {
    console.error('Error fetching recommended searches:', error)
    return []
  }
}

/**
 * Server-side function to fetch liquidation items data
 * Gets products of type=flashsale and domestic=chinese
 * Returns an array of liquidation items or an empty array if fetching fails
 */
async function getLiquidationItems() {
  try {
    // Prepare request headers
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'User-Agent': 'ChinaElectron-Server/1.0',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://chinaelectron.com'
    }

    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch('https://get-hot.chinaelectron.com/latest?type=flashsale', {
      headers,
      signal: controller.signal,
      next: { revalidate: 3600 } // 缓存1小时，减少缓存时间
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`Failed to fetch liquidation items: ${response.status} ${response.statusText}`)
      return []
    }

    const data = await response.json()

    if (data && data.success && Array.isArray(data.data)) {
      // Filter only domestic: "chinese" items
      const filteredItems = data.data.filter((item: LiquidationItem) => item.domestic === "chinese") as LiquidationItem[]
      console.log(`Successfully fetched ${filteredItems.length} liquidation items from server (filtered from ${data.data.length} total)`)
      return filteredItems
    }

    console.warn('Invalid liquidation items data format received')
    return []
  } catch (error: any) {
    console.error('Error fetching liquidation items on server:', error.message || error)
    return []
  }
}

/**
 * Server-side function to fetch popular categories
 * This function runs on the server during SSR
 * Returns categories data or empty object if fetching fails
 */
async function getPopularCategories(): Promise<CategoryResponse | { success: boolean, category_tree?: Record<string, CategoryItem> }> {
  try {
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    }

    const response = await fetch('https://popular-categories.chinaelectron.com/api/latest-categories', {
      headers,
      next: { revalidate: 86400 } // 缓存24小时
    })

    if (!response.ok) {
      console.error(`Failed to fetch popular categories: ${response.status} ${response.statusText}`)
      return { success: false }
    }

    return await response.json() as CategoryResponse
  } catch (error) {
    console.error('Error fetching popular categories:', error)
    return { success: false }
  }
}

/**
 * Server-side function to fetch latest quotation data
 * This function runs on the server during SSR and provides initial data to the client
 * Returns an array of quotation items or sample data if fetching fails
 * 只返回domestic="chinese"的数据
 */
async function getLatestQuotations() {
  try {
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    }

    const response = await fetch('https://newly-quota.chinaelectron.com/api/latest', {
      headers,
      next: { revalidate: 86400 } // 缓存24小时
    })

    if (!response.ok) {
      console.error(`Failed to fetch latest quotations: ${response.status} ${response.statusText}`)
      return sampleQuotationData
    }

    const data = await response.json() as QuotationResponse
    
    if (!data.error && Array.isArray(data.data) && data.data.length > 0) {
      // 过滤出domestic="chinese"的数据
      const chineseQuotations = data.data.filter(item => item.domestic === "chinese")
      
      // 如果没有任何国产数据，返回示例数据
      if (chineseQuotations.length === 0) {
        // console.log('No Chinese domestic quotations found, using sample data')
        return sampleQuotationData
      }
      
      return chineseQuotations
    }
    
    // 如果API返回空数据，则使用示例数据
    return sampleQuotationData
  } catch (error) {
    console.error('Error fetching latest quotations:', error)
    return sampleQuotationData
  }
}

/**
 * Server-side function to fetch active distributors
 * This function runs on the server during SSR and provides initial data to the client
 * Returns an array of active distributors sorted by occurrence count
 */
async function getActiveDistributors(): Promise<ActiveDistributor[]> {
  try {
    // 获取最新的报价数据
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    }

    const response = await fetch('https://newly-quota.chinaelectron.com/api/latest', {
      headers,
      next: { revalidate: 86400 } // 缓存24小时
    })

    if (!response.ok) {
      console.error(`Failed to fetch latest quotations: ${response.status} ${response.statusText}`)
      return activeDistributors as ActiveDistributor[] // 返回示例数据
    }

    const data = await response.json()
    
    if (!data.error && Array.isArray(data.data) && data.data.length > 0) {
      // 计算每个分销商的出现次数，并记录最新的创建时间
      const distributorCounts = new Map<string, number>()
      const distributorLatestTime = new Map<string, number>()
      const distributorDetails = new Map<string, ActiveDistributor>()
      
      data.data.forEach((item: QuotationItemWithDistributor) => {
        const distributorId = item.distributor_id
        
        // 增加计数
        distributorCounts.set(distributorId, (distributorCounts.get(distributorId) || 0) + 1)
        
        // 更新最新时间（如果当前项的时间更新）
        const currentTime = new Date(item.create_at).getTime()
        const storedTime = distributorLatestTime.get(distributorId) || 0
        if (currentTime > storedTime) {
          distributorLatestTime.set(distributorId, currentTime)
          // 存储分销商详情
          distributorDetails.set(distributorId, {
            id: distributorId,
            name_en: item.name_en,
            logo: item.logo || 'default.jpg',
            description: '' // 移除描述信息
          })
        }
      })
      
      // 更新计数和最新时间
      for (const [id, distributor] of distributorDetails.entries()) {
        distributor.count = distributorCounts.get(id) || 0
        distributor.latest_time = distributorLatestTime.get(id) || 0
      }
      
      // 转换为数组并排序：首先按出现次数降序，相同次数则按创建时间降序
      let activeDistributorsList = Array.from(distributorDetails.values())
        .sort((a, b) => {
          // 首先比较出现次数
          const countA = a.count || 0
          const countB = b.count || 0
          if (countB !== countA) {
            return countB - countA
          }
          // 如果出现次数相同，则比较最新时间
          const timeA = a.latest_time || 0
          const timeB = b.latest_time || 0
          return timeB - timeA
        })
      
      // 返回所有分销商（不再限制为前5个）
      return activeDistributorsList
    }
    
    return activeDistributors as ActiveDistributor[] // 返回示例数据
  } catch (error) {
    console.error('Error fetching active distributors:', error)
    return activeDistributors as ActiveDistributor[] // 返回示例数据
  }
}

/**
 * 获取广告位配置信息
 * 返回广告位配置数据或空对象
 */
async function getAdsConfig() {
  try {
    const response = await fetch('https://ads-manage.chinaelectron.com/api/config/chinaelectron', {
      next: { revalidate: 86400 } // 缓存24小时
    });

    if (!response.ok) {
      console.error(`Failed to fetch ads config: ${response.status} ${response.statusText}`);
      return {};
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching ads config:', error);
    return {};
  }
}

/**
 * 获取详细广告数据
 * 返回首页的广告数据
 */
async function getDetailedAdsData() {
  try {
    const response = await fetch('https://ads-manage.chinaelectron.com/api/ads?page=home&platform=chinaelectron', {
      next: { revalidate: 86400 } // 缓存24小时
    });

    if (!response.ok) {
      console.error(`Failed to fetch detailed ads data: ${response.status} ${response.statusText}`);
      return { data: [] };
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching detailed ads data:', error);
    return { data: [] };
  }
}

// 广告项结构
interface DetailedAdItem {
  id: number;
  platform: string;
  page: string;
  page_location: string;
  tags: string;
  status: string;
  priority: number;
  img: string;
  img_alt: string;
  title: string;
  subtitle: string;
  description: string;
  target_url: string;
  effective_time: string;
  create_at: string;
}

// 将API广告数据转换为组件所需的格式
function convertToAdItems(ads: DetailedAdItem[]): AdItem[] {
  // console.log('原始广告数据:', JSON.stringify(ads));
  const converted = ads.map(ad => ({
    id: ad.id.toString(),
    title: ad.title,
    image_url: ad.img,
    link_url: ad.target_url || '#',
    alt_text: ad.img_alt || ad.title,
    start_date: ad.effective_time,
    end_date: '',
    status: ad.status,
    priority: ad.priority,
    page_location: ad.page_location
  }));
  // console.log('转换后的广告数据:', JSON.stringify(converted));
  return converted;
}

// 按照广告位过滤广告数据
function filterAdsByLocation(ads: DetailedAdItem[], locations: string[]): DetailedAdItem[] {
  return ads.filter(ad => 
    locations.includes(ad.page_location) && 
    ad.status === 'Enabled'
  ).sort((a, b) => b.priority - a.priority);
}

/**
 * Main Homepage Component (Server Component)
 * This is an async React Server Component that fetches data during server rendering
 * The fetched data is passed to client components for hydration
 */
export default async function Home() {
  // Server-side data fetching
  const hotItems = await getHotItems()
  const recommendedSearches = await getRecommendedSearches()
  const liquidationItems = await getLiquidationItems()
  const categoriesData = await getPopularCategories()

  // 获取广告位配置和详细广告数据
  const detailedAdsData = await getDetailedAdsData()
  
  // 注意: Spotlight和Blog文章功能暂时被禁用，相关API请求已被注释掉
  // 当API服务恢复后可以重新启用这些功能
  const latestInsights = await getLatestInsights();
  const latestArticles = await getLatestInsightArticles();
  
  // 将文章按3个一组分组，用于3列显示
  const articleGroups = [];
  for (let i = 0; i < Math.min(latestArticles.length, 9); i += 3) {
    articleGroups.push(latestArticles.slice(i, i + 3));
  }
  
  // 处理详细广告数据
  const allDetailedAds = detailedAdsData.data || [];
  
  // 过滤各个广告位的广告数据
  const leftSideAds = filterAdsByLocation(allDetailedAds, ['home_left_1', 'home_left_2']);
  const slideAds = filterAdsByLocation(allDetailedAds, ['home_slide']);

  // 转换为组件需要的格式
  const sideAdsItems = convertToAdItems(leftSideAds);
  const slideAdsItems = convertToAdItems(slideAds);

  // 如果API返回数据为空，则使用默认数据
  const sideAds = sideAdsItems.length > 0 ? sideAdsItems : [];
  const bottomAds: AdItem[] = [];
  const carouselAds = slideAdsItems.length > 0 ? slideAdsItems : bottomAds.slice(0, 7);

  // 广告位位置配置
  const sideAdLocations = ['home_left_1', 'home_left_2'];
  const slideAdLocation = 'home_slide';
  
  // Process categories data (get first 6 categories)
  const popularCategories: CategoryItem[] = categoriesData?.success && categoriesData.category_tree
    ? Object.values(categoriesData.category_tree).slice(0, 6)
    : []

  /**
   * 格式化日期，使用固定格式避免水合错误
   */
  function formatDate(dateString: string): string {
    if (!dateString) return '';
    
    try {
      const date = new Date(dateString);
      // 使用 ISO 格式的日期表示，保证服务端和客户端渲染一致
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    } catch (error) {
      return '';
    }
  }

  return (
    <div className="min-h-screen relative bg-white">

      {/* 主要内容 */}
      <div className="relative">
        {/* 推荐搜索区域 - 现代化样式 */}
        <RecommendedSearches
          keywords={recommendedSearches}
          isLoading={false}
        />

        {/* 广告与每日热门区域 - 现代化设计 */}
        <section className="py-8 relative">

          <div className="mx-auto px-14 relative">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-5">
              {/* 左侧：垂直广告 - 使用3列 */}
              <div className="lg:col-span-3">
                <SideAds ads={sideAds} adLocations={sideAdLocations} />
              </div>

              {/* 中间：轮播广告 - 使用7列 */}
              <div className="lg:col-span-7">
                <AdCarousel ads={carouselAds} adLocation={slideAdLocation} />
              </div>

              {/* 右侧：每日热门 - 使用2列 */}
              <div className="lg:col-span-2">
                <DailyHot initialItems={hotItems} />
              </div>
            </div>
          </div>
        </section>

        {/* Trust Logos 信任标识区域 */}
        <TrustLogos />

        {/* 横幅广告区域 */}
        {/* <section className="py-8">
          <div className="mx-auto px-14 relative">
            {wideBannerAdsItems.length > 0 ? (
              <Link 
                href={wideBannerAdsItems[0].link_url} 
                className="block w-full h-24 md:h-32 overflow-hidden rounded-lg relative" 
                data-location="home_width_1"
              >
                <Image
                  src={wideBannerAdsItems[0].image_url}
                  alt={wideBannerAdsItems[0].alt_text || wideBannerAdsItems[0].title}
                  fill
                  className="object-cover"
                />
              </Link>
            ) : (
              <Link href="#" className="block w-full h-24 md:h-32 overflow-hidden rounded-lg relative" data-location="home_width_1">
                <div className="absolute inset-0 bg-gradient-to-r from-[#0051a3] to-[#0062cc] flex items-center justify-center">
                  <div className="text-white text-xl md:text-2xl font-bold">Banner Advertisement Space</div>
                </div>
              </Link>
            )}
          </div>
        </section> */}

        {/* Liquidation Parts 清仓产品区域 */}
        <section className="py-12">
          <div className="mx-auto px-14 relative">
            <div className="relative bg-gradient-to-br from-[#3b82f6] via-[#2563eb] to-[#1d4ed8] rounded-lg p-8 overflow-hidden">
              {/* 动态光效背景 */}
              <div className="absolute inset-0">
                {/* 光效条纹 */}
                <div className="absolute top-0 left-1/4 w-1/2 h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transform -skew-x-12"></div>
                
                {/* 装饰性圆点 */}
                <div className="absolute top-0 left-0 w-full h-full" style={{ 
                  backgroundImage: `radial-gradient(circle at center, rgba(255,255,255,0.15) 2px, transparent 2px)`,
                  backgroundSize: '24px 24px'
                }}></div>
                
                {/* 装饰性圆形 */}
                <div className="absolute -top-20 -left-20 w-80 h-80 bg-white/10 rounded-full blur-2xl"></div>
                <div className="absolute -bottom-20 -right-20 w-80 h-80 bg-white/10 rounded-full blur-2xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
              </div>
              
              <div className="text-center mb-8 relative z-10">
                <h2 className="text-3xl font-bold mb-2 text-white">Liquidation Parts Now Available</h2>
              </div>
              
              {/* 清仓品网格 */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-4 relative z-10">
                {liquidationItems.slice(0, 18).map((item) => (
                  <Link
                    key={item.product_id}
                    href={item.url || item.full?.url || buildProductUrl(item)}
                    className="bg-white/95 backdrop-blur-sm rounded-lg shadow-lg hover:shadow-xl transition-all flex flex-col items-center justify-center h-16 overflow-hidden hover:scale-105 transform duration-200"
                  >
                    <div className="w-full h-full flex flex-col items-center justify-center px-3">
                      <span className="font-mono text-sm md:text-base font-medium text-gray-900 line-clamp-1 text-center">
                        {item.model}
                      </span>
                      <span className="text-sm text-gray-500 mt-0.5 line-clamp-1 text-center">
                        {item.full?.brand?.name_en || item.brand_en || item.brand_cn}
                      </span>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Popular Categories Section */}
        <section className="py-12 bg-gray-50">
          <div className="mx-auto px-14 relative">
            <div className="mb-8 flex justify-between items-center">
              <h2 className="text-3xl font-bold">Popular Categories</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6">
              {popularCategories.map((category: CategoryItem) => (
                <div key={category.code} className="bg-white rounded-md shadow-sm overflow-hidden hover:shadow-md transition-all">
                  {/* Category Header - 使用蓝色背景 */}
                  <div className="bg-red-500 text-white py-4 px-4 font-semibold text-center text-base">
                    {category.name_en}
                  </div>
                  
                  {/* Category Items - 增加高度和字号 */}
                  <div className="p-5">
                    <ul className="space-y-3">
                      {Object.values(category.children || {}).slice(0, 1).map((subCategory: any) => {
                        if (Array.isArray(subCategory.children)) {
                          return subCategory.children.slice(0, 6).map((item: any) => (
                            <li key={item.code} className="text-base">
                              <Link 
                                href={`/search?category=${item.code}`}
                                className="text-gray-700 hover:text-red-500 hover:underline truncate block py-1.5"
                              >
                                {item.name_en}
                              </Link>
                            </li>
                          ));
                        } else {
                          return Object.values(subCategory.children || {}).slice(0, 6).map((item: any) => (
                            <li key={item.code} className="text-base">
                              <Link 
                                href={`/search?category=${item.code}`}
                                className="text-gray-700 hover:text-blue-500 hover:underline truncate block py-1.5"
                              >
                                {item.name_en}
                              </Link>
                            </li>
                          ));
                        }
                      })}
                    </ul>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Client Testimonials Section */}
        <ClientTestimonials />

        {/* Newly Spotlight Section */}

        <section className="py-12">
          <div className="mx-auto px-14 relative">
            <div className="mb-8">
              <h2 className="text-3xl font-bold">Newest Insights</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {latestInsights.length > 0 ? (
                latestInsights.map((insight) => (
                  <Link 
                    key={insight.id}
                    href={`/insights/${encodeURIComponent(slugifyTitle(insight.title))}-${insight.id}`}
                    className="aspect-[16/9] rounded-md overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900 relative"
                  >
                    {insight.cover_image && (
                      <Image 
                        src={insight.cover_image}
                        alt={insight.title}
                        fill
                        className="object-cover opacity-40"
                      />
                    )}
                    <div className="absolute inset-0 p-6 flex flex-col justify-end">
                      <h3 className="text-white text-lg font-semibold">{insight.title}</h3>
                      <p className="text-gray-300 text-sm mt-2 line-clamp-2">{insight.description}</p>
                    </div>
                  </Link>
                ))
              ) : (
                // 如果没有数据，显示占位符
                Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="aspect-[16/9] rounded-md overflow-hidden bg-gradient-to-br from-gray-800 to-gray-900 relative">
                    <div className="absolute inset-0 p-6 flex flex-col justify-end">
                      <div className="h-6 w-32 bg-gray-700 rounded animate-pulse"></div>
                      <div className="h-4 w-full bg-gray-700 rounded mt-2 animate-pulse"></div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </section>


        {/* China Electric Spotlights Section */}
        
        <section className="py-12 bg-gray-50">
          <div className="mx-auto px-14 relative">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold mb-4">China Electric Spotlights</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {articleGroups.length > 0 ? (
                articleGroups.map((group, groupIndex) => (
                  <div key={groupIndex} className="bg-white rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow">
                    <div className="aspect-[16/7] relative">
                      {group[0]?.cover_image ? (
                        <Image 
                          src={group[0].cover_image}
                          alt={group[0].title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="absolute inset-0 bg-gray-200">
                          <div className="w-full h-full flex items-center justify-center">
                            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <div className="flex justify-between items-start gap-4 mb-4">
                        <Link href={`/spotlight/${encodeURIComponent(slugifyTitle(group[0]?.title))}-${group[0]?.id}`} className="text-lg font-semibold line-clamp-2 flex-1 hover:text-blue-600 transition-colors">
                          {group[0]?.title}
                        </Link>
                        <div className="text-sm text-gray-400 whitespace-nowrap">
                          {group[0]?.created_at ? formatDate(group[0].created_at) : ''}
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        {group.slice(1).map((article) => (
                          <div key={article.id} className="flex justify-between items-center gap-4">
                            <Link href={`/spotlight/${encodeURIComponent(slugifyTitle(article.title))}-${article.id}`} className="text-gray-600 hover:text-blue-600 transition-colors line-clamp-1 flex-1">
                              {article.title}
                            </Link>
                            <div className="text-sm text-gray-400 whitespace-nowrap">
                              {formatDate(article.created_at)}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                // 如果没有数据，显示占位符
                Array.from({ length: 3 }).map((_, index) => (
                  <div key={index} className="bg-white rounded-lg overflow-hidden shadow-sm">
                    <div className="aspect-[16/7] bg-gray-200"></div>
                    <div className="p-6">
                      <div className="mb-4">
                        <div className="h-5 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded w-1/4 mt-2 animate-pulse"></div>
                      </div>
                      <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded w-full animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </section>
        

        {/* Email Subscription Section */}
        <section className="py-16 relative overflow-hidden">
          {/* 浅蓝和白色块堆叠渐变背景 */}
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-white"></div>
            <div className="absolute top-0 left-0 w-full h-full">
              <div className="absolute -top-10 -left-10 w-40 h-40 bg-blue-100 rounded-full opacity-50 transform -rotate-12"></div>
              <div className="absolute top-20 right-20 w-64 h-64 bg-blue-50 rounded-full opacity-70"></div>
              <div className="absolute bottom-10 left-1/4 w-56 h-56 bg-blue-100 rounded-full opacity-40"></div>
              <div className="absolute -bottom-20 right-10 w-72 h-72 bg-blue-50 rounded-full opacity-60 transform rotate-12"></div>
            </div>
            <div className="absolute inset-0 bg-white opacity-30"></div>
          </div>
          
          {/* 内容 */}
          <div className="mx-auto px-14 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-6">Stay Updated with China Electric</h2>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                Subscribe to our newsletter and receive the latest industry news, product updates, and exclusive offers.
              </p>
              
              {/* 使用客户端组件替换表单 */}
              <EmailSubscriptionForm />
              
              <p className="text-sm text-gray-500 mt-4">
                We respect your privacy. Unsubscribe at any time.
              </p>
            </div>
          </div>
        </section>

      </div>
    </div>
  )
}


