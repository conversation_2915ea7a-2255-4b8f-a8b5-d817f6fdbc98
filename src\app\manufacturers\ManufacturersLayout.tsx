import Link from 'next/link';
import { Suspense } from 'react';
import { Brand } from './fetchBrands';

// Manufacturer filter tabs component
function BrandFilterTabs({ currentFilter }: { currentFilter: string }) {
  // 移除时间戳，改为静态链接
  const filters = [
    { id: 'all', label: 'All Manufacturers', href: '/manufacturers' },
    { id: 'chinese', label: 'Chinese Manufacturers', href: '/manufacturers/chinese' },
    { id: 'international', label: 'International Manufacturers', href: '/manufacturers/international' }
  ];

  return (
    <div className="flex mb-8">
      <div className="inline-flex bg-white rounded-lg shadow-sm p-1">
        {filters.map(filter => (
          <Link
            key={filter.id}
            href={filter.href}
            prefetch={false} // Disable prefetching
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors
              ${currentFilter === filter.id
                ? 'bg-[#DE2910] text-white'
                : 'text-gray-600 hover:bg-gray-100'
              }`}
          >
            {filter.label}
          </Link>
        ))}
      </div>
    </div>
  );
}

// 分页组件
function Pagination({ 
  currentPage, 
  totalPages, 
  filterType,
  total
}: { 
  currentPage: number, 
  totalPages: number, 
  filterType: string,
  total: number
}) {
  // 确保总页数至少为1页，并计算实际的总页数
  const calculatedTotalPages = Math.max(1, Math.ceil(total / 20));
  const displayTotalPages = Math.max(totalPages || 0, calculatedTotalPages);
  
  /* console.log('Pagination component:', { 
    currentPage, 
    totalPages, 
    calculatedTotalPages, 
    displayTotalPages,
    total,
    shouldShow: displayTotalPages > 1
  }); */
  
  // 只有当总页数大于1时才显示分页控件
  if (displayTotalPages <= 1) {
    return null;
  }
  
  // 确定基础URL
  let baseUrl = '/manufacturers';
  if (filterType === 'chinese') {
    baseUrl = '/manufacturers/chinese';
  } else if (filterType === 'international') {
    baseUrl = '/manufacturers/international';
  }

  // 确定要显示的页码范围（最多显示5个页码）
  let startPage = Math.max(1, currentPage - 2);
  let endPage = Math.min(displayTotalPages, startPage + 4);
  
  // 调整开始页，确保我们始终尝试显示5个页码
  if (endPage - startPage < 4) {
    startPage = Math.max(1, endPage - 4);
  }
  
  const pages = Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);

  return (
    <div className="flex flex-col items-center my-8">
      {/* 显示页码信息 */}
      <div className="mb-4 text-gray-600">
        Page {currentPage} of {displayTotalPages}
      </div>
      
      <div className="inline-flex shadow-sm rounded-md border border-gray-300 overflow-hidden bg-gray-50 p-1">
        {/* 前一页按钮 */}
        {currentPage > 1 && (
          <Link
            href={`${baseUrl}?page=${currentPage - 1}`}
            prefetch={false}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-l-md hover:bg-gray-100"
          >
            Previous
          </Link>
        )}
        
        {/* 页码 */}
        {pages.map(page => (
          <Link
            key={page}
            href={`${baseUrl}?page=${page}`}
            prefetch={false}
            className={`inline-flex items-center px-4 py-2 text-sm font-medium ${
              currentPage === page 
                ? 'bg-[#DE2910] text-white border border-[#DE2910] hover:bg-[#DE2910]/90' 
                : 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-100'
            }`}
          >
            {page}
          </Link>
        ))}
        
        {/* 下一页按钮 */}
        {currentPage < displayTotalPages && (
          <Link
            href={`${baseUrl}?page=${currentPage + 1}`}
            prefetch={false}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-r-md hover:bg-gray-100"
          >
            Next
          </Link>
        )}
      </div>
    </div>
  );
}

// Manufacturer list component
function BrandList({ brands }: { brands: Brand[] }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {brands.map(brand => {
        const brandName = brand.name_en || brand.name_cn || brand.name_ru || 'Unknown';
        const isDefaultLogo = brand.logo_url === 'https://brandimg.chinaelectron.com/no_brand_logo.png' || !brand.logo_url;
        
        return (
          <Link
            key={brand.id}
            href={`/manufacturers/${brand.id}`}
            prefetch={false} // 禁用预加载
            className="group bg-white rounded-xl p-4 border border-gray-100
                    hover:border-[#DE2910]/20 hover:shadow-md
                    transition-all duration-300"
          >
            {/* Logo */}
            <div className="h-16 mb-3 relative">
              {!isDefaultLogo ? (
                <img
                  src={brand.logo_url}
                  alt={brandName}
                  className="w-full h-full object-contain"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-200 rounded">
                  <span className="text-gray-600 font-medium text-sm">{brandName}</span>
                </div>
              )}
            </div>
            
            {/* Content */}
            <h3 className="font-medium text-gray-900 text-center
                        group-hover:text-[#DE2910] transition-colors">
              {brandName}
            </h3>
          </Link>
        );
      })}
      
      {/* Empty state */}
      {brands.length === 0 && (
        <div className="col-span-full text-center py-12">
          <p className="text-gray-500">No manufacturer data found</p>
        </div>
      )}
    </div>
  );
}

// Page layout component
export default function ManufacturersLayout({ 
  brands, 
  filterType,
  total,
  currentPage,
  totalPages 
}: { 
  brands: Brand[], 
  filterType: string,
  total?: number,
  currentPage?: number,
  totalPages?: number
}) {
  // Generate title based on filter type
  let filterTitle = "All Manufacturers";
  if (filterType === "chinese") {
    filterTitle = "Chinese Manufacturers";
  } else if (filterType === "international") {
    filterTitle = "International Manufacturers";
  }

  // 设置默认值
  const displayTotal = total || brands.length;
  const displayCurrentPage = currentPage || 1;
  const displayTotalPages = totalPages || 1;

  // console.log('ManufacturersLayout rendering:', { displayTotal, displayCurrentPage, displayTotalPages });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Header Section */}
      <div className="relative bg-gradient-to-r from-[#ef233c] to-[#dc2626] text-white py-12 mb-8 overflow-hidden">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0"
               style={{
                 backgroundImage: `linear-gradient(45deg, white 1px, transparent 1px),
                                  linear-gradient(-45deg, white 1px, transparent 1px)`,
                 backgroundSize: '30px 30px',
                 animation: 'float 10s ease-in-out infinite'
               }}
          />
        </div>

        {/* Floating factory elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/6 w-2 h-2 bg-white/30 rounded-sm animate-bounce" style={{animationDelay: '0s', animationDuration: '3s'}}></div>
          <div className="absolute top-1/3 right-1/5 w-1.5 h-1.5 bg-white/25 rounded-sm animate-bounce" style={{animationDelay: '1s', animationDuration: '4s'}}></div>
          <div className="absolute bottom-1/3 left-1/4 w-2.5 h-2.5 bg-white/20 rounded-sm animate-bounce" style={{animationDelay: '2s', animationDuration: '5s'}}></div>
          <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-white/35 rounded-sm animate-bounce" style={{animationDelay: '0.5s', animationDuration: '3.5s'}}></div>
        </div>

        <div className="relative max-w mx-auto px-4 sm:px-6 lg:px-14">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-4">
                <div className="bg-white/20 rounded-full p-3 mr-4 animate-pulse hover:animate-spin transition-all duration-300">
                  <svg className="w-8 h-8 text-white transform hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div className="animate-fadeIn">
                  <h1 className="text-4xl md:text-5xl font-bold mb-2 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    Manufacturer Directory
                  </h1>
                  <p className="text-xl text-white/90 animate-slideInUp">
                    Connect with trusted electronic component manufacturers
                  </p>
                </div>
              </div>
            </div>
            <div className="hidden md:block animate-slideInRight">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div className="text-3xl font-bold animate-pulse">{displayTotal}</div>
                <div className="text-sm text-white/80">Manufacturers</div>
                <div className="mt-2 text-xs text-white/70">{filterTitle}</div>
                <div className="mt-2 w-full bg-white/20 rounded-full h-1">
                  <div className="bg-white h-1 rounded-full animate-pulse" style={{width: '92%'}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="relative">
        <div className="max-w mx-auto px-14 pb-6">
          {/* Manufacturer filter tabs */}
          <div className="mb-8">
            <BrandFilterTabs currentFilter={filterType} />
          </div>
          
          {/* Manufacturer list with suspense for streaming */}
          <Suspense fallback={
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#DE2910]"></div>
            </div>
          }>
            <BrandList brands={brands} />
          </Suspense>
          
          {/* 分页控件 */}
          <Pagination 
            currentPage={displayCurrentPage}
            totalPages={displayTotalPages}
            filterType={filterType}
            total={displayTotal}
          />
        </div>
      </div>
    </div>
  );
} 