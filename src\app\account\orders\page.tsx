'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { getUserOrders } from '@/services/api'
import {
  ShoppingBag,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Truck,
  Calendar,
  DollarSign,
  Package,
  AlertCircle
} from 'lucide-react'

// 订单状态显示配置
const getOrderStatusDisplay = (status: string) => {
  const statusMap = {
    'pending': {
      label: 'Pending Payment',
      color: 'text-amber-700 bg-amber-50 border-amber-200',
      icon: Clock
    },
    'paid': {
      label: 'Paid',
      color: 'text-blue-700 bg-blue-50 border-blue-200',
      icon: CheckCircle
    },
    'processing': {
      label: 'Processing',
      color: 'text-blue-700 bg-blue-50 border-blue-200',
      icon: Clock
    },
    'shipped': {
      label: 'Shipped',
      color: 'text-green-700 bg-green-50 border-green-200',
      icon: Truck
    },
    'delivered': {
      label: 'Delivered',
      color: 'text-green-700 bg-green-50 border-green-200',
      icon: CheckCircle
    },
    'cancelled': {
      label: 'Cancelled',
      color: 'text-red-700 bg-red-50 border-red-200',
      icon: XCircle
    }
  }

  return statusMap[status as keyof typeof statusMap] || {
    label: status,
    color: 'text-gray-700 bg-gray-50 border-gray-200',
    icon: AlertCircle
  }
}

interface Order {
  order_id: string
  order_date: string
  order_total: number
  order_status: string
  payment_method?: string
  item_count?: number
  created_at?: string
  updated_at?: string
}

export default function OrdersPage() {
  const { isAuthenticated, userId, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (authLoading) return // 等待认证状态加载完成

    if (!isAuthenticated || !userId) {
      router.push('/login')
      return
    }
    loadOrders()
  }, [isAuthenticated, userId, authLoading, router])

  const loadOrders = async () => {
    if (!isAuthenticated || !userId) return

    try {
      setLoading(true)
      setError(null)

      const ordersData = await getUserOrders(userId)
      setOrders(ordersData)
    } catch (err) {
      console.error('Failed to load orders:', err)
      setError('Failed to load orders. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#DE2910] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !userId) {
    return null
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#DE2910] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your orders...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="text-center py-12">
          <XCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Orders</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={loadOrders}
            className="bg-gradient-to-r from-[#DE2910] to-[#FF6B45] text-white px-6 py-3 rounded-xl font-medium hover:from-[#FF3B20] hover:to-[#FF8055] hover:scale-105 transition-all duration-300 shadow-lg"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <ShoppingBag className="w-6 h-6 text-[#DE2910]" />
          <div>
            <h2 className="text-xl font-bold text-gray-900">Order History</h2>
            <p className="text-sm text-gray-600">Track and manage your orders</p>
          </div>
        </div>
        <div className="text-sm text-gray-500">
          {orders.length} {orders.length === 1 ? 'order' : 'orders'}
        </div>
      </div>

      <div className="bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
        {orders.length === 0 ? (
          /* 空状态 */
          <div className="p-12 text-center">
            <ShoppingBag className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Yet</h3>
            <p className="text-gray-500 mb-6">You haven't placed any orders yet. Start shopping to see your orders here.</p>
            <Link
              href="/search"
              className="inline-flex items-center bg-gradient-to-r from-[#DE2910] to-[#FF6B45] text-white px-6 py-3 rounded-xl font-medium hover:from-[#FF3B20] hover:to-[#FF8055] hover:scale-105 transition-all duration-300 shadow-lg"
            >
              Start Shopping
            </Link>
          </div>
        ) : (
          /* 订单列表 */
          <div className="p-6">
            <div className="space-y-4">
              {orders.map((order) => {
                const statusDisplay = getOrderStatusDisplay(order.order_status)
                const StatusIcon = statusDisplay.icon

                return (
                  <div key={order.order_id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 hover:border-[#DE2910]/20">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                      {/* Left Section */}
                      <div className="flex-1 space-y-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] rounded-lg flex items-center justify-center">
                              <ShoppingBag className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h3 className="font-semibold text-gray-900 text-lg">Order #{order.order_id}</h3>
                              <p className="text-sm text-gray-500">Placed on {formatDate(order.order_date)}</p>
                            </div>
                          </div>
                          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusDisplay.color}`}>
                            <StatusIcon className="w-4 h-4 mr-1" />
                            {statusDisplay.label}
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="flex items-center space-x-2">
                            <DollarSign className="w-4 h-4 text-gray-400" />
                            <div>
                              <p className="text-xs text-gray-500">Total Amount</p>
                              <p className="text-sm font-medium text-gray-900">${order.order_total.toFixed(2)}</p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Package className="w-4 h-4 text-gray-400" />
                            <div>
                              <p className="text-xs text-gray-500">Items</p>
                              <p className="text-sm font-medium text-gray-900">
                                {order.item_count || 0} item{(order.item_count || 0) !== 1 ? 's' : ''}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Calendar className="w-4 h-4 text-gray-400" />
                            <div>
                              <p className="text-xs text-gray-500">Payment Method</p>
                              <p className="text-sm font-medium text-gray-900">
                                {order.payment_method || 'Not specified'}
                              </p>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-end pt-2 border-t border-gray-100">
                          <Link
                            href={`/orders/${order.order_id}`}
                            className="inline-flex items-center space-x-1 text-[#DE2910] hover:text-[#FF6B45] font-medium text-sm transition-colors"
                          >
                            <Eye className="w-4 h-4" />
                            <span>View Details</span>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
