# 数据库设计文档

## 数据库概览

### 基础信息
- **数据库类型**: Cloudflare D1 (基于SQLite)
- **数据库名称**: `little-field-db`
- **表总数**: 38张表
- **字符编码**: UTF-8
- **时区**: UTC

### 设计原则
1. **数据一致性**: 使用外键约束保证数据完整性
2. **性能优化**: 关键字段建立索引
3. **扩展性**: 支持多语言和国际化
4. **安全性**: 敏感数据加密存储
5. **可维护性**: 清晰的命名规范和文档

## 表结构分类

### 1. 产品相关表 (4张)

#### 1.1 products (主产品表)
**用途**: 存储核心产品信息
```sql
CREATE TABLE products (
    product_id TEXT PRIMARY KEY,           -- 产品ID，格式：CMA100101416
    model TEXT NOT NULL,                   -- 产品型号
    brand_id TEXT NOT NULL,                -- 品牌ID (外键)
    price_key TEXT,                        -- 价格表关联键
    stock_key TEXT,                        -- 库存表关联键
    datasheet_key TEXT,                    -- 数据手册键
    image_keys TEXT,                       -- 图片键列表
    parameters JSON,                       -- 产品参数(JSON格式)
    description TEXT,                      -- 产品描述
    updated_at TIMESTAMP,                  -- 更新时间
    FOREIGN KEY (brand_id) REFERENCES brands(id)
);
```

**索引设计**:
- 主键索引: `product_id`
- 外键索引: `brand_id`
- 搜索索引: `model`

**数据示例**:
```json
{
  "product_id": "CMA100101416",
  "model": "LM358",
  "brand_id": "TI",
  "parameters": {
    "en": [
      {"param_name": "Supply Voltage", "param_value": "3V to 32V"},
      {"param_name": "Package", "param_value": "DIP-8, SOIC-8"}
    ],
    "cn": [
      {"param_name": "供电电压", "param_value": "3V至32V"},
      {"param_name": "封装", "param_value": "DIP-8, SOIC-8"}
    ]
  }
}
```

#### 1.2 distributor_products (分销商产品表)
**用途**: 存储分销商的产品信息
```sql
CREATE TABLE distributor_products (
    product_id TEXT,                       -- 产品ID
    distributor_id TEXT,                   -- 分销商ID
    model TEXT NOT NULL,                   -- 产品型号
    brand_id TEXT NOT NULL,                -- 品牌ID
    price_key TEXT,                        -- 价格键
    stock_key TEXT,                        -- 库存键
    datasheet_url TEXT,                    -- 数据手册URL
    image_list TEXT,                       -- 图片列表
    parameters_key TEXT,                   -- 参数键
    description TEXT,                      -- 产品描述
    updated_at TEXT,                       -- 更新时间
    rohs INTEGER,                          -- RoHS认证状态
    PRIMARY KEY (product_id, distributor_id)
);
```

**索引设计**:
- 复合主键: `(product_id, distributor_id)`
- 分销商索引: `distributor_id`

#### 1.3 discount_products (折扣产品表)
**用途**: 存储促销和折扣产品信息
```sql
CREATE TABLE discount_products (
    product_id TEXT PRIMARY KEY,           -- 产品ID
    model TEXT,                            -- 产品型号
    brand_id TEXT,                         -- 品牌ID
    original_price REAL,                   -- 原价
    discount_price REAL,                   -- 折扣价
    discount_rate REAL,                    -- 折扣率
    start_date DATE,                       -- 开始日期
    end_date DATE,                         -- 结束日期
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 1.4 products202503 (产品历史表)
**用途**: 存储产品历史版本数据
```sql
CREATE TABLE products202503 (
    product_id TEXT PRIMARY KEY,           -- 产品ID
    model TEXT,                            -- 产品型号
    brand_id TEXT,                         -- 品牌ID
    price_key TEXT,                        -- 价格键
    stock_key TEXT,                        -- 库存键
    datasheet_url TEXT,                    -- 数据表URL
    image_list TEXT,                       -- 图片列表
    parameters_key TEXT,                   -- 参数键
    description TEXT,                      -- 产品描述
    updated_at TEXT,                       -- 更新时间
    rohs INTEGER,                          -- RoHS认证
    summarizer TEXT                        -- 摘要
);
```

### 2. 品牌相关表 (2张)

#### 2.1 brands (品牌表)
**用途**: 存储品牌制造商信息
```sql
CREATE TABLE brands (
    id TEXT PRIMARY KEY,                   -- 品牌ID
    name_cn TEXT,                          -- 中文名称
    name_en TEXT,                          -- 英文名称
    name_ru TEXT,                          -- 俄文名称
    introduction_cn TEXT,                  -- 中文介绍
    introduction_en TEXT,                  -- 英文介绍
    introduction_ru TEXT,                  -- 俄文介绍
    website TEXT,                          -- 官方网站
    logo_url TEXT,                         -- Logo URL
    domestic TEXT,                         -- 是否国产品牌
    area TEXT                              -- 所属地区
);
```

**多语言支持**:
- 支持中文、英文、俄文三种语言
- 品牌介绍支持多语言版本

#### 2.2 brand_distributor_mapping (品牌分销商映射表)
**用途**: 建立品牌与分销商的关系
```sql
CREATE TABLE brand_distributor_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 映射ID
    brand_id TEXT NOT NULL,                -- 品牌ID
    distributor_id TEXT NOT NULL,          -- 分销商ID
    relationship_type TEXT,                -- 关系类型
    authorization_level TEXT,              -- 授权级别
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(brand_id, distributor_id)
);
```

### 3. 分销商相关表 (2张)

#### 3.1 distributors (分销商表)
**用途**: 存储分销商基本信息
```sql
CREATE TABLE distributors (
    id TEXT PRIMARY KEY,                   -- 分销商ID
    name_cn TEXT,                          -- 中文名称
    name_en TEXT,                          -- 英文名称
    name_ru TEXT,                          -- 俄文名称
    website TEXT,                          -- 官方网站
    logo_url TEXT,                         -- Logo URL
    contact_email TEXT,                    -- 联系邮箱
    contact_phone TEXT,                    -- 联系电话
    address TEXT,                          -- 地址
    country TEXT,                          -- 国家
    rating REAL,                           -- 评级
    status TEXT,                           -- 状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2 distributors-portal (分销商门户表)
**用途**: 分销商门户网站信息
```sql
CREATE TABLE "distributors-portal" (
    id TEXT PRIMARY KEY,                   -- 门户ID
    distributor_id TEXT,                   -- 分销商ID
    portal_url TEXT,                       -- 门户URL
    api_endpoint TEXT,                     -- API端点
    access_token TEXT,                     -- 访问令牌
    sync_status TEXT,                      -- 同步状态
    last_sync_time TIMESTAMP,              -- 最后同步时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 价格和库存表 (3张)

#### 4.1 price (价格表)
**用途**: 存储产品阶梯价格信息
```sql
CREATE TABLE price (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 价格ID
    code TEXT,                             -- 产品代码(关联product_id)
    quantity INTEGER,                      -- 数量阶梯
    price REAL,                            -- 单价
    created_at TEXT,                       -- 创建时间
    UNIQUE(code, quantity)                 -- 唯一约束
);
```

**价格策略**:
- 支持阶梯定价
- 数量越大，单价越低
- 实时价格更新

#### 4.2 stocks (库存表)
**用途**: 存储产品库存信息
```sql
CREATE TABLE stocks (
    code TEXT PRIMARY KEY,                 -- 产品代码(关联product_id)
    stocks INTEGER,                        -- 库存数量
    created_at TEXT                        -- 创建时间
);
```

#### 4.3 parameters (参数表)
**用途**: 存储产品技术参数
```sql
CREATE TABLE parameters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 参数ID
    code TEXT,                             -- 产品代码
    languages TEXT,                        -- 语言标识
    param_name TEXT,                       -- 参数名称
    param_value TEXT,                      -- 参数值
    created_at TEXT,                       -- 创建时间
    UNIQUE(code, languages, param_name)    -- 唯一约束
);
```

### 5. 分类相关表 (4张)

#### 5.1 categories (分类表)
**用途**: 存储产品分类层次结构
```sql
CREATE TABLE categories (
    code TEXT PRIMARY KEY,                 -- 分类代码
    parent_code TEXT,                      -- 父分类代码
    level INTEGER,                         -- 分类层级
    name_cn TEXT,                          -- 中文名称
    name_en TEXT,                          -- 英文名称
    name_ru TEXT,                          -- 俄文名称
    created_at TEXT                        -- 创建时间
);
```

**层次结构**:
- 支持多级分类
- 树形结构设计
- 多语言分类名称

#### 5.2 product_category_map (产品分类映射表)
**用途**: 建立产品与分类的关系
```sql
CREATE TABLE product_category_map (
    product_code TEXT PRIMARY KEY,         -- 产品代码
    category_code TEXT,                    -- 分类代码
    created_at TEXT                        -- 创建时间
);
```

#### 5.3 popular-categories (热门分类表)
**用途**: 存储热门产品分类
```sql
CREATE TABLE "popular-categories" (
    code TEXT PRIMARY KEY,                 -- 分类代码
    parent_code TEXT,                      -- 父分类代码
    level INTEGER,                         -- 分类层级
    name_cn TEXT,                          -- 中文名称
    name_en TEXT,                          -- 英文名称
    name_ru TEXT,                          -- 俄文名称
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 5.4 category_distributor_mapping (分类分销商映射表)
**用途**: 分类与分销商的关系映射
```sql
CREATE TABLE category_distributor_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 映射ID
    category_id TEXT NOT NULL,             -- 分类ID
    distributor_id TEXT NOT NULL,          -- 分销商ID
    accountfor REAL,                       -- 占比
    UNIQUE(category_id, distributor_id)    -- 唯一约束
);
```

### 6. 用户、购物车和订单表 (11张)

#### 6.1 users (用户表)
**用途**: 存储用户基本信息
```sql
CREATE TABLE users (
    user_id TEXT PRIMARY KEY,              -- 用户ID
    email TEXT NOT NULL UNIQUE,            -- 邮箱(唯一)
    password_hash TEXT NOT NULL,           -- 密码哈希
    role TEXT NOT NULL,                    -- 用户角色
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**索引设计**:
- 主键索引: `user_id`
- 唯一索引: `email`
- 普通索引: `email` (查询优化)

#### 6.2 user_addresses (用户地址表)
**用途**: 存储用户地址信息
```sql
CREATE TABLE user_addresses (
    address_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,                 -- 用户ID (外键)
    address_type TEXT NOT NULL,            -- 地址类型(shipping/billing)
    first_name TEXT,                       -- 名
    last_name TEXT,                        -- 姓
    company TEXT,                          -- 公司
    address_line1 TEXT NOT NULL,           -- 地址行1
    address_line2 TEXT,                    -- 地址行2
    city TEXT NOT NULL,                    -- 城市
    state_province TEXT,                   -- 州/省
    postal_code TEXT,                      -- 邮政编码
    country TEXT NOT NULL,                 -- 国家
    phone TEXT,                            -- 电话
    is_default BOOLEAN DEFAULT FALSE,      -- 是否默认地址
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 6.3 contact_information (联系信息表)
**用途**: 存储用户联系信息
```sql
CREATE TABLE contact_information (
    contact_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,                 -- 用户ID (外键)
    username TEXT,                         -- 用户名
    email TEXT,                            -- 邮箱
    is_default BOOLEAN DEFAULT FALSE,      -- 是否默认联系方式
    first_name TEXT,                       -- 名
    last_name TEXT,                        -- 姓
    phone TEXT,                            -- 电话
    job_role TEXT,                         -- 职位
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 6.4 compliance_statement (合规声明表)
**用途**: 存储用户合规声明信息
```sql
CREATE TABLE compliance_statement (
    statement_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,                 -- 用户ID (外键)
    resell_components BOOLEAN,             -- 是否转售组件
    ultimate_consignee TEXT,               -- 最终收货人
    country TEXT,                          -- 国家
    application_type TEXT,                 -- 应用类型
    compliance_confirmed BOOLEAN DEFAULT FALSE, -- 合规确认
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 6.5 cart (购物车表)
**用途**: 存储用户购物车信息
```sql
CREATE TABLE cart (
    cart_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,                 -- 用户ID (外键)
    product_id TEXT NOT NULL,              -- 产品ID
    model_number TEXT,                     -- 型号
    brand TEXT,                            -- 品牌
    description TEXT,                      -- 描述
    unit_price DECIMAL(10,2),              -- 单价
    stock_quantity INTEGER,                -- 库存数量
    cart_quantity INTEGER NOT NULL,        -- 购物车数量
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 6.6 orders (订单表)
**用途**: 存储订单主信息
```sql
CREATE TABLE orders (
    order_id TEXT PRIMARY KEY,             -- 订单ID
    user_id TEXT NOT NULL,                 -- 用户ID (外键)
    status TEXT NOT NULL,                  -- 订单状态
    total_amount DECIMAL(10,2) NOT NULL,   -- 总金额
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

**索引设计**:
- 主键索引: `order_id`
- 外键索引: `user_id`

#### 6.7 billing_info (账单信息表)
**用途**: 存储订单账单信息
```sql
CREATE TABLE billing_info (
    billing_id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,                -- 订单ID (外键)
    user_id TEXT NOT NULL,                 -- 用户ID
    first_name TEXT,                       -- 名
    last_name TEXT,                        -- 姓
    company TEXT,                          -- 公司
    address_line1 TEXT,                    -- 地址行1
    address_line2 TEXT,                    -- 地址行2
    city TEXT,                             -- 城市
    state_province TEXT,                   -- 州/省
    postal_code TEXT,                      -- 邮政编码
    country TEXT,                          -- 国家
    phone TEXT,                            -- 电话
    email TEXT,                            -- 邮箱
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);
```

#### 6.8 shipping_info (配送信息表)
**用途**: 存储订单配送信息
```sql
CREATE TABLE shipping_info (
    shipping_id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,                -- 订单ID (外键)
    user_id TEXT NOT NULL,                 -- 用户ID
    first_name TEXT,                       -- 名
    last_name TEXT,                        -- 姓
    company TEXT,                          -- 公司
    address_line1 TEXT,                    -- 地址行1
    address_line2 TEXT,                    -- 地址行2
    city TEXT,                             -- 城市
    state_province TEXT,                   -- 州/省
    postal_code TEXT,                      -- 邮政编码
    country TEXT,                          -- 国家
    phone TEXT,                            -- 电话
    shipping_method TEXT,                  -- 配送方式
    tracking_number TEXT,                  -- 跟踪号
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);
```

#### 6.9 order_items (订单明细表)
**用途**: 存储订单商品明细
```sql
CREATE TABLE order_items (
    item_id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT,                         -- 订单ID (外键)
    user_id TEXT NOT NULL,                 -- 用户ID
    ce_id TEXT,                            -- CE产品ID
    mfr_number TEXT,                       -- 制造商编号
    manufacturer TEXT,                     -- 制造商
    description TEXT,                      -- 产品描述
    quantity INTEGER,                      -- 数量
    unit_price REAL,                       -- 单价
    ext_price REAL,                        -- 扩展价格
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);
```

#### 6.10 order_fees (订单费用表)
**用途**: 存储订单费用明细
```sql
CREATE TABLE order_fees (
    fee_id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT,                         -- 订单ID (外键)
    user_id TEXT NOT NULL,                 -- 用户ID
    merchandise_total REAL,                -- 商品总额
    freight REAL,                          -- 运费
    handling_fee REAL,                     -- 手续费
    total REAL,                            -- 总计
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);
```

### 7. 询价和报价表 (3张)

#### 7.1 requests (询价请求表)
**用途**: 存储客户询价请求
```sql
CREATE TABLE requests (
    id TEXT PRIMARY KEY,                   -- 请求ID
    bom_id TEXT,                           -- BOM ID
    user_id TEXT,                          -- 用户ID
    type TEXT,                             -- 请求类型
    status TEXT,                           -- 状态
    contact_name TEXT,                     -- 联系人姓名
    business_email TEXT,                   -- 商务邮箱
    company_name TEXT,                     -- 公司名称
    country TEXT,                          -- 国家
    quantity INTEGER,                      -- 数量
    model TEXT,                            -- 型号
    brand_id TEXT,                         -- 品牌ID
    brand_name TEXT,                       -- 品牌名称
    target_price REAL,                     -- 目标价格
    delivery_time TEXT,                    -- 交货时间
    date_code TEXT,                        -- 日期代码
    distribution_id TEXT,                  -- 分销商ID
    distribution_name TEXT,                -- 分销商名称
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 7.2 quote (报价表)
**用途**: 存储供应商报价信息
```sql
CREATE TABLE quote (
    id TEXT PRIMARY KEY,                   -- 报价ID
    request_id TEXT,                       -- 询价请求ID (外键)
    bom_id TEXT,                           -- BOM ID
    user_id TEXT,                          -- 用户ID
    type TEXT,                             -- 报价类型
    status TEXT,                           -- 状态
    model TEXT,                            -- 型号
    prices TEXT,                           -- 价格信息(JSON)
    shipping TEXT,                         -- 运费信息
    quantity INTEGER,                      -- 数量
    brand_id TEXT,                         -- 品牌ID
    brand_name TEXT,                       -- 品牌名称
    target_price REAL,                     -- 目标价格
    delivery_time TEXT,                    -- 交货时间
    date_code TEXT,                        -- 日期代码
    distribution_id TEXT,                  -- 分销商ID
    distribution_name TEXT,                -- 分销商名称
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 7.3 newly_quota (最新报价表)
**用途**: 存储最新报价数据
```sql
CREATE TABLE newly_quota (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 报价ID
    model TEXT,                            -- 型号
    brand TEXT,                            -- 品牌
    price REAL,                            -- 价格
    quantity INTEGER,                      -- 数量
    supplier TEXT,                         -- 供应商
    domestic TEXT,                         -- 国内/国外
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 8. 内容管理表 (5张)

#### 8.1 blog (博客表)
**用途**: 存储博客文章内容
```sql
CREATE TABLE blog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 文章ID
    type INTEGER DEFAULT 0,                -- 文章类型
    status INTEGER DEFAULT 0,              -- 发布状态
    title TEXT NOT NULL,                   -- 标题
    subtitle TEXT,                         -- 副标题
    description TEXT,                      -- 描述
    cover_image TEXT,                      -- 封面图片
    content_markdown TEXT,                 -- Markdown内容
    tags TEXT,                             -- 标签
    category TEXT,                         -- 分类
    location TEXT,                         -- 位置
    view_count INTEGER,                    -- 浏览次数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    author_id TEXT NOT NULL,               -- 作者ID (外键)
    FOREIGN KEY (author_id) REFERENCES author(author_id)
);
```

#### 8.2 insights (专题表)
**用途**: 存储技术专题信息
```sql
CREATE TABLE insights (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 专题ID
    type INTEGER DEFAULT 0,                -- 专题类型
    status INTEGER DEFAULT 0,              -- 发布状态
    title TEXT NOT NULL,                   -- 标题
    subtitle TEXT,                         -- 副标题
    description TEXT,                      -- 描述
    cover_image TEXT,                      -- 封面图片
    tags TEXT,                             -- 标签
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    author_id TEXT NOT NULL,               -- 作者ID (外键)
    FOREIGN KEY (author_id) REFERENCES author(author_id)
);
```

#### 8.3 author (作者表)
**用途**: 存储作者信息
```sql
CREATE TABLE author (
    author_id TEXT PRIMARY KEY,            -- 作者ID
    name TEXT NOT NULL,                    -- 姓名
    avatar TEXT,                           -- 头像URL
    description TEXT,                      -- 描述
    bio TEXT                               -- 个人简介
);
```

#### 8.4 insights_blog_map (专题博客映射表)
**用途**: 建立专题与博客文章的关系
```sql
CREATE TABLE insights_blog_map (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 映射ID
    insights_id INTEGER,                   -- 专题ID (外键)
    blog_id INTEGER,                       -- 博客ID (外键)
    FOREIGN KEY (insights_id) REFERENCES insights(id),
    FOREIGN KEY (blog_id) REFERENCES blog(id)
);
```

#### 8.5 ads (广告表)
**用途**: 存储广告信息
```sql
CREATE TABLE ads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 广告ID
    platform TEXT,                        -- 平台
    page TEXT,                             -- 页面
    page_location TEXT,                    -- 页面位置
    tags TEXT,                             -- 标签
    status TEXT,                           -- 状态
    priority INTEGER,                      -- 优先级
    img TEXT,                              -- 图片URL
    img_alt TEXT,                          -- 图片描述
    title TEXT,                            -- 标题
    subtitle TEXT,                         -- 副标题
    description TEXT,                      -- 描述
    target_url TEXT,                       -- 目标URL
    effective_time TEXT,                   -- 有效时间
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 9. 系统管理表 (4张)

#### 9.1 recommend (推荐表)
**用途**: 存储推荐搜索关键词
```sql
CREATE TABLE recommend (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 推荐ID
    keyword TEXT NOT NULL,                 -- 关键词
    date DATE NOT NULL,                    -- 日期
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 9.2 subscribe (订阅表)
**用途**: 存储邮件订阅信息
```sql
CREATE TABLE subscribe (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 订阅ID
    email TEXT,                            -- 邮箱
    create_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 9.3 tdkh (TDK表)
**用途**: 存储页面SEO信息
```sql
CREATE TABLE tdkh (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- TDK ID
    page_url TEXT,                         -- 页面URL
    title TEXT,                            -- 页面标题
    description TEXT,                      -- 页面描述
    keywords TEXT,                         -- 关键词
    h1 TEXT,                               -- H1标签
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 9.4 product_index (产品索引表)
**用途**: 存储产品索引信息
```sql
CREATE TABLE product_index (
    id INTEGER PRIMARY KEY AUTOINCREMENT,  -- 索引ID
    first_text TEXT,                       -- 首字母
    product_id TEXT,                       -- 产品ID
    model TEXT,                            -- 型号
    url TEXT                               -- 产品URL
);
```

## 数据关系设计

### 主要外键关系

#### 产品相关关系
```
products.brand_id → brands.id
products.product_id → price.code
products.product_id → stocks.code
products.product_id → parameters.code
product_category_map.product_code → products.product_id
product_category_map.category_code → categories.code
```

#### 用户订单关系
```
orders.user_id → users.user_id
billing_info.order_id → orders.order_id
shipping_info.order_id → orders.order_id
order_items.order_id → orders.order_id
order_fees.order_id → orders.order_id
cart.user_id → users.user_id
user_addresses.user_id → users.user_id
```

#### 询价报价关系
```
quote.request_id → requests.id
requests.user_id → users.user_id
quote.user_id → users.user_id
```

#### 内容管理关系
```
blog.author_id → author.author_id
insights.author_id → author.author_id
insights_blog_map.insights_id → insights.id
insights_blog_map.blog_id → blog.id
```

### 索引策略

#### 主键索引
- 所有表的主键自动创建唯一索引
- 复合主键支持多字段索引

#### 外键索引
```sql
-- 用户相关索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_id ON orders(user_id);

-- 产品相关索引
CREATE INDEX idx_products_brand_id ON products(brand_id);
CREATE INDEX idx_products_model ON products(model);
CREATE INDEX idx_price_code ON price(code);
CREATE INDEX idx_stocks_code ON stocks(code);

-- 分销商相关索引
CREATE INDEX idx_distributor_products_distributor_id ON distributor_products(distributor_id);
```

#### 搜索优化索引
```sql
-- 全文搜索索引
CREATE INDEX idx_products_search ON products(model, description);
CREATE INDEX idx_blog_search ON blog(title, tags, category);

-- 时间范围索引
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_requests_create_at ON requests(create_at);
```

## 数据完整性约束

### 外键约束
- 启用外键约束检查
- 级联删除策略
- 引用完整性保证

### 唯一性约束
```sql
-- 用户邮箱唯一
ALTER TABLE users ADD CONSTRAINT uk_users_email UNIQUE (email);

-- 产品价格阶梯唯一
ALTER TABLE price ADD CONSTRAINT uk_price_code_quantity UNIQUE (code, quantity);

-- 参数多语言唯一
ALTER TABLE parameters ADD CONSTRAINT uk_parameters_code_lang_name
UNIQUE (code, languages, param_name);
```

### 检查约束
```sql
-- 价格必须为正数
ALTER TABLE price ADD CONSTRAINT ck_price_positive CHECK (price > 0);

-- 库存不能为负数
ALTER TABLE stocks ADD CONSTRAINT ck_stocks_non_negative CHECK (stocks >= 0);

-- 用户角色限制
ALTER TABLE users ADD CONSTRAINT ck_users_role
CHECK (role IN ('admin', 'user', 'supplier', 'distributor'));
```

## 数据流设计

### 产品数据流
```
1. 产品录入 → products表
2. 价格更新 → price表
3. 库存同步 → stocks表
4. 参数维护 → parameters表
5. 分类映射 → product_category_map表
```

### 订单数据流
```
1. 用户下单 → orders表
2. 订单明细 → order_items表
3. 费用计算 → order_fees表
4. 账单信息 → billing_info表
5. 配送信息 → shipping_info表
```

### 询价数据流
```
1. 客户询价 → requests表
2. 供应商报价 → quote表
3. 价格比较 → 业务逻辑处理
4. 订单转换 → orders表
```

## 性能优化策略

### 查询优化
1. **索引优化**: 为常用查询字段建立索引
2. **分页查询**: 使用LIMIT和OFFSET进行分页
3. **联表优化**: 减少不必要的JOIN操作
4. **缓存策略**: 热点数据缓存

### 存储优化
1. **数据类型**: 选择合适的数据类型
2. **字段长度**: 合理设置字段长度
3. **JSON存储**: 复杂数据使用JSON格式
4. **归档策略**: 历史数据定期归档

### 维护策略
1. **定期分析**: 定期分析表统计信息
2. **索引重建**: 定期重建索引
3. **数据清理**: 清理无效和过期数据
4. **备份恢复**: 定期备份重要数据
