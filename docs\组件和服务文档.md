# 组件和服务文档

## 文档概览

本文档详细记录了ChinaElectron平台的所有React组件、服务类、工具函数、上下文和类型定义。项目采用TypeScript开发，遵循现代React开发最佳实践。

## 目录结构

```
src/
├── components/          # React组件
│   ├── ComponentDetail/ # 产品详情组件
│   ├── Home/           # 首页组件
│   ├── Search/         # 搜索相关组件
│   ├── common/         # 通用组件
│   └── security/       # 安全相关组件
├── contexts/           # React Context
├── services/           # 服务类和API调用
├── utils/              # 工具函数
├── types/              # TypeScript类型定义
└── lib/                # 库和配置
```

## 1. React组件

### 1.1 产品详情组件 (ComponentDetail)

#### ComponentOverview (产品概览)
**文件**: `src/components/ComponentDetail/ComponentOverview.tsx`

**Props接口**:
```typescript
interface ComponentOverviewProps {
  component: ComponentDetail;
  parsedData: FullData;
}
```

**功能特性**:
- 产品基本信息展示
- 产品图片轮播
- 制造商信息链接
- 产品描述渲染

**使用示例**:
```tsx
<ComponentOverview 
  component={componentData.component} 
  parsedData={componentData.parsedData} 
/>
```

#### Specifications (规格参数)
**文件**: `src/components/ComponentDetail/Specifications.tsx`

**Props接口**:
```typescript
interface SpecificationsProps {
  component: ComponentDetail;
  parsedData: FullData;
  summarizerContent?: ReactNode;
}

interface ParameterItem {
  param_name: string;
  param_value: string;
}
```

**功能特性**:
- 多语言参数展示
- 参数搜索和筛选
- 参数复制功能
- 响应式表格布局

**数据处理逻辑**:
```typescript
const parameters: ParameterItem[] = 
  (parsedData as any)?.parameters?.english || 
  parsedData?.parameter?.languages?.english?.parameters?.main_parameters?.map?.(
    (key: string, value: string) => ({ param_name: key, param_value: value })
  ) || [];
```

#### PriceAndStock (价格库存)
**文件**: `src/components/ComponentDetail/PriceAndStock.tsx`

**功能特性**:
- 阶梯价格展示
- 实时库存信息
- 数量选择器
- 询价按钮集成

#### Distributors (分销商信息)
**文件**: `src/components/ComponentDetail/Distributors.tsx`

**功能特性**:
- 授权分销商列表
- 分销商评级展示
- 联系方式显示
- 分销商页面跳转

#### DocumentsAndFiles (文档文件)
**文件**: `src/components/ComponentDetail/DocumentsAndFiles.tsx`

**功能特性**:
- 数据手册下载
- CAD模型下载
- PDF在线预览
- 文件类型图标

#### AIAssistant (AI助手)
**文件**: `src/components/ComponentDetail/AIAssistant.tsx`

**功能特性**:
- 产品问答功能
- 应用建议生成
- 替代方案推荐
- 智能对话界面

#### AssistInquiry (询价辅助)
**文件**: `src/components/ComponentDetail/AssistInquiry.tsx`

**功能特性**:
- 快速询价表单
- 数量和规格选择
- 联系信息填写
- 询价请求提交

### 1.2 首页组件 (Home)

#### RecommendedSearches (推荐搜索)
**文件**: `src/components/Home/RecommendedSearches.tsx`

**Props接口**:
```typescript
interface RecommendedSearchesProps {
  keywords: string[];
  isLoading: boolean;
}
```

**功能特性**:
- 热门关键词展示
- 搜索跳转功能
- 加载状态处理
- 响应式布局

#### AdCarousel (广告轮播)
**文件**: `src/components/Home/AdCarousel.tsx`

**功能特性**:
- 自动轮播功能
- 手动切换控制
- 广告点击跟踪
- 移动端适配

#### SideAds (侧边广告)
**文件**: `src/components/Home/SideAds.tsx`

**功能特性**:
- 垂直广告展示
- 多广告位支持
- 点击统计
- 响应式隐藏

#### DailyHot (每日热门)
**文件**: `src/components/Home/DailyHot.tsx`

**Props接口**:
```typescript
interface DailyHotProps {
  initialItems: HotItem[];
}
```

**功能特性**:
- 热门产品列表
- 实时数据更新
- 产品快速跳转
- 趋势指示器

#### CategoryShowcase (分类展示)
**文件**: `src/components/Home/CategoryShowcase.tsx`

**功能特性**:
- 产品分类网格
- 分类图标展示
- 分类页面跳转
- 多语言支持

#### PreferredManufacture (优选制造商)
**文件**: `src/components/Home/PreferredManufacture.tsx`

**功能特性**:
- 品牌Logo展示
- 品牌信息卡片
- 品牌页面跳转
- 轮播展示

#### PopularParts (热门产品)
**文件**: `src/components/Home/PopularParts.tsx`

**功能特性**:
- 产品卡片展示
- 价格信息显示
- 库存状态指示
- 快速询价功能

#### LatestBlogs (最新博客)
**文件**: `src/components/Home/LatestBlogs.tsx`

**功能特性**:
- 博客文章列表
- 文章摘要展示
- 作者信息显示
- 博客页面跳转

#### Newsletter (邮件订阅)
**文件**: `src/components/Home/Newsletter.tsx`

**功能特性**:
- 邮箱订阅表单
- 表单验证
- 订阅状态反馈
- 隐私政策链接

#### QuickNav (快速导航)
**文件**: `src/components/Home/QuickNav.tsx`

**功能特性**:
- 快速链接网格
- 图标导航
- 功能分类
- 移动端优化

### 1.3 搜索组件 (Search)

#### SearchClient (搜索客户端)
**文件**: `src/components/Search/SearchClient.tsx`

**功能特性**:
- 搜索状态管理
- 搜索历史记录
- 搜索建议
- 实时搜索

#### ProductList (产品列表)
**文件**: `src/components/Search/ProductList.tsx`

**功能特性**:
- 产品卡片布局
- 列表/网格切换
- 产品信息展示
- 快速操作按钮

#### ProductTable (产品表格)
**文件**: `src/components/Search/ProductTable.tsx`

**功能特性**:
- 表格形式展示
- 列排序功能
- 批量选择
- 导出功能

#### SearchFilters (搜索筛选器)
**文件**: `src/components/Search/SearchFilters.tsx`

**功能特性**:
- 多维度筛选
- 筛选条件保存
- 筛选重置
- 筛选结果统计

#### SearchPagination (搜索分页)
**文件**: `src/components/Search/SearchPagination.tsx`

**功能特性**:
- 分页导航
- 页面大小选择
- 跳转到指定页
- 总数统计

#### SmartIdentification (智能识别)
**文件**: `src/components/Search/SmartIdentification.tsx`

**功能特性**:
- AI产品识别
- 模糊搜索优化
- 识别结果展示
- 识别历史记录

#### AIAnalysis (AI分析)
**文件**: `src/components/Search/AIAnalysis.tsx`

**功能特性**:
- 搜索结果分析
- 市场趋势分析
- 替代产品推荐
- 技术建议

### 1.4 通用组件 (common)

#### LoadingSpinner (加载动画)
**文件**: `src/components/common/LoadingSpinner.tsx`

**Props接口**:
```typescript
interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}
```

**功能特性**:
- 多种尺寸支持
- 自定义颜色
- CSS动画效果
- 无障碍支持

### 1.5 安全组件 (security)

#### Turnstile (人机验证)
**文件**: `src/components/security/Turnstile.tsx`

**Props接口**:
```typescript
interface TurnstileProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  theme?: 'light' | 'dark' | 'auto';
  size?: 'normal' | 'compact';
}
```

**功能特性**:
- Cloudflare Turnstile集成
- 主题切换支持
- 错误处理
- 回调函数支持

### 1.6 客户端包装组件 (ClientComponents)

#### ProductClientWrapper (产品客户端包装器)
**文件**: `src/components/ClientComponents/ProductClientWrapper.tsx`

**Props接口**:
```typescript
interface ProductClientWrapperProps {
  componentData: {
    component: ComponentDetail;
    parsedData: FullData;
  };
  summarizerComponent?: ReactNode;
}
```

**功能特性**:
- 客户端状态管理
- 认证状态检查
- 组件懒加载
- 错误边界处理

## 2. React Context

### 2.1 AuthContext (认证上下文)
**文件**: `src/contexts/AuthContext.tsx`

**Context接口**:
```typescript
interface AuthContextType {
  isAuthenticated: boolean;
  token: string | null;
  userId: string | null;
  login: (username: string, password: string, turnstileToken?: string | null) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
}
```

**功能特性**:
- 用户认证状态管理
- JWT Token管理
- 登录/登出功能
- 本地存储同步
- 路由保护

**使用示例**:
```tsx
const { isAuthenticated, login, logout } = useAuth();
```

**核心方法**:

#### login方法
```typescript
const login = async (username: string, password: string, turnstileToken?: string | null) => {
  // Turnstile验证
  // API请求认证
  // Token存储
  // 状态更新
  // 路由跳转
}
```

#### logout方法
```typescript
const logout = async () => {
  // 清除本地存储
  // 重置状态
  // 路由跳转
}
```

## 3. 服务类和API调用

### 3.1 API服务 (api.ts)
**文件**: `src/services/api.ts`

#### 接口定义
```typescript
// RFQ请求接口
export interface RFQRequest {
  id: string;
  user_id: string;
  type: string;
  status: string;
  contact_name: string;
  business_email: string;
  company_name: string;
  country: string;
  quantity: number;
  model: string;
  brand_name: string;
  target_price?: number;
  delivery_time?: string;
  date_code?: string;
  distribution_name?: string;
  create_at: string;
}

// 博客文章接口
export interface BlogArticle {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle?: string;
  description: string;
  cover_image: string;
  content_markdown: string;
  tags: string;
  category: string;
  location?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
}
```

#### 核心API函数

##### fetchRFQRequests (获取询价请求)
```typescript
export const fetchRFQRequests = async (userId: string | null): Promise<RFQRequest[]> => {
  // 参数验证
  // Token获取
  // API请求
  // 错误处理
  // 数据返回
}
```

##### fetchBlogArticles (获取博客文章)
```typescript
export const fetchBlogArticles = async (
  page: number = 1, 
  limit: number = 10, 
  category?: string, 
  tag?: string, 
  author_id?: string
): Promise<BlogResponse> => {
  // URL构建
  // 请求发送
  // 响应处理
  // 数据格式化
}
```

### 3.2 Turnstile服务
**文件**: `src/services/turnstileService.ts`

**功能特性**:
- Turnstile Token验证
- 服务端验证集成
- 错误处理
- 重试机制

## 4. 工具函数

### 4.1 产品URL工具 (productUrl.ts)
**文件**: `src/utils/productUrl.ts`

#### 接口定义
```typescript
interface BaseProductData {
  product_id: string;
  model: string;
  brand_name_en?: string;
  brand_en?: string;
  brand_cn?: string;
  brand?: {
    name_en?: string;
    name_cn?: string;
    name_ru?: string;
  };
  category_path?: string[];
  category_names?: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
}
```

#### 核心函数

##### buildProductUrl (构建产品URL)
```typescript
export function buildProductUrl(item: BaseProductData): string {
  // 分类名称提取
  // 品牌名称格式化
  // 特殊字符处理
  // URL构建
  // 格式: /product/{一级分类}-{二级分类}-{三级分类}-{品牌名称}-{model}-{产品id}
}
```

**URL格式化规则**:
- 空格替换为下划线
- 特殊字符替换为横杠
- 统一转换为小写
- 多语言支持

### 4.2 规范化URL工具 (canonicalUrl.ts)
**文件**: `src/utils/canonicalUrl.ts`

```typescript
export const getCanonicalUrl = (path: string = ''): string => {
  const baseUrl = 'https://www.chinaelectron.com';
  // 路径规范化
  // URL拼接
  // 返回完整URL
}
```

**功能特性**:
- SEO友好URL生成
- 路径规范化
- 基础域名配置
- 相对路径处理

## 5. TypeScript类型定义

### 5.1 组件类型 (component.ts)
**文件**: `src/types/component.ts`

#### 核心接口

##### FullData (完整数据接口)
```typescript
export interface FullData {
  name: string;
  desc: string;
  model: string;
  location: string;
  manufacture: string;
  parameter: {
    languages: {
      english: {
        name: string;
        desc: string;
        parameters: {
          main_parameters: Record<string, any>;
          secondary_parameters: Record<string, any>;
          common_parameters: Record<string, any>;
        };
        features: string[];
        applications: string[];
      };
      chinese: any;
      russian: any;
    }
  };
  img: string[];
  datasheet: string[];
  cad: string[];
  update_time: string;
  category_path?: string[];
  category_id?: string;
  category_names?: Record<string, {
    name_cn: string;
    name_en: string;
    name_ru: string;
  }>;
}
```

##### ComponentDetail (组件详情接口)
```typescript
export interface ComponentDetail {
  id: string;
  metadata: {
    cad: string;
    datasheet: string;
    desc: string;
    full_data: string | FullData;
    id: string;
    img: string;
    manufacture: string;
    model: string;
    name: string;
    category_path?: string[];
    category_names?: Record<string, {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }>;
  };
  parsedFullData?: FullData;
  score?: number;
  values?: any[];
}
```

##### APIResponse (API响应接口)
```typescript
export interface APIResponse {
  success: boolean;
  query_id: string;
  matches: ComponentDetail[];
}
```

## 6. 配置和库

### 6.1 应用配置 (config.ts)
**文件**: `src/lib/config.ts`

```typescript
export const config = {
  // Cloudflare Turnstile 配置
  turnstile: {
    siteKey: process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || 'YOUR_SITE_KEY',
    secretKey: process.env.TURNSTILE_SECRET_KEY || 'YOUR_SECRET_KEY',
  },
  // API 配置
  api: {
    authBaseUrl: 'https://auth.chinaelectron.com',
    verifyBaseUrl: 'https://verify.chinaelectron.com',
  }
}
```

**配置项说明**:
- **Turnstile配置**: 人机验证服务配置
- **API配置**: 各种API服务的基础URL
- **环境变量**: 支持开发和生产环境配置

## 组件使用最佳实践

### 1. 组件导入规范
```typescript
// 优先使用命名导入
import { ComponentOverview } from '@/components/ComponentDetail/ComponentOverview';

// 默认导入用于主要组件
import LoadingSpinner from '@/components/common/LoadingSpinner';
```

### 2. Props类型定义
```typescript
// 明确定义Props接口
interface MyComponentProps {
  title: string;
  optional?: boolean;
  children?: ReactNode;
}

// 使用泛型增强类型安全
interface GenericProps<T> {
  data: T;
  onSelect: (item: T) => void;
}
```

### 3. 状态管理
```typescript
// 使用TypeScript增强useState
const [data, setData] = useState<ProductData | null>(null);
const [loading, setLoading] = useState<boolean>(false);
const [error, setError] = useState<string>('');
```

### 4. 错误处理
```typescript
// 统一错误处理模式
try {
  const result = await apiCall();
  setData(result);
} catch (error) {
  console.error('API Error:', error);
  setError(error instanceof Error ? error.message : 'Unknown error');
} finally {
  setLoading(false);
}
```

### 5. 性能优化
```typescript
// 使用React.memo优化渲染
const OptimizedComponent = React.memo(({ data }: Props) => {
  return <div>{data.title}</div>;
});

// 使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// 使用useCallback缓存函数
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```
