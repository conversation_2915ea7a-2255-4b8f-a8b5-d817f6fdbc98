import { NextRequest, NextResponse } from 'next/server';

interface BreadcrumbItem {
  label: string;
  path: string;
}

// 生成基本的面包屑作为后备方案
function generateFallbackBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const paths = pathname.split('/').filter(path => path);
  const items: BreadcrumbItem[] = [{ label: 'Home', path: '/' }];
  let currentPath = '';

  paths.forEach(path => {
    currentPath += `/${path}`;
    const label = path.charAt(0).toUpperCase() + path.slice(1);
    items.push({ label, path: currentPath });
  });

  return items;
}

// 调用 Cloudflare Workers API 获取面包屑（带重试机制）
async function fetchBreadcrumbsFromWorker(fullUrl: string, maxRetries: number = 3): Promise<BreadcrumbItem[]> {
  const workerUrl = `https://webapi.chinaelectron.com/breadcrumbs?path=${encodeURIComponent(fullUrl)}`;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 只在开发环境或第一次尝试时记录日志
      if (process.env.NODE_ENV === 'development' || attempt === 1) {
        console.log(`Fetching breadcrumbs from worker (attempt ${attempt}/${maxRetries}):`, workerUrl);
      }

      const response = await fetch(workerUrl, {
        // 添加缓存策略
        next: { revalidate: 3600 }, // 1小时缓存
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'ChinaElectron-NextJS/1.0'
        },
        // 添加超时设置
        signal: AbortSignal.timeout(10000) // 10秒超时
      });

      if (!response.ok) {
        console.error(`Worker API failed with status: ${response.status} (attempt ${attempt}/${maxRetries})`);

        // 如果是最后一次尝试，返回空数组
        if (attempt === maxRetries) {
          return [];
        }

        // 等待一段时间后重试（递增延迟）
        await new Promise(resolve => setTimeout(resolve, attempt * 1000));
        continue;
      }

      const data = await response.json();

      // 验证返回的数据格式
      if (data && Array.isArray(data.breadcrumbs)) {
        if (process.env.NODE_ENV === 'development' || attempt > 1) {
          console.log(`Successfully fetched breadcrumbs on attempt ${attempt}`);
        }
        return data.breadcrumbs;
      }

      console.error(`Invalid breadcrumb data format from worker (attempt ${attempt}/${maxRetries}):`, data);

      // 如果是最后一次尝试，返回空数组
      if (attempt === maxRetries) {
        return [];
      }

      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, attempt * 1000));

    } catch (error) {
      console.error(`Error fetching breadcrumbs from worker (attempt ${attempt}/${maxRetries}):`, error);

      // 如果是最后一次尝试，返回空数组
      if (attempt === maxRetries) {
        return [];
      }

      // 等待一段时间后重试（递增延迟）
      await new Promise(resolve => setTimeout(resolve, attempt * 1000));
    }
  }

  // 理论上不会到达这里，但为了类型安全
  return [];
}

// API 路由处理函数
export async function GET(req: NextRequest) {
  // 获取查询参数
  const url = new URL(req.url);
  const pathname = url.searchParams.get('path') || '/';

  try {
    // 构建完整的URL（Workers API需要完整URL）
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.chinaelectron.com';
    const fullUrl = `${baseUrl}${pathname}`;

    // 调用 Cloudflare Workers API 获取面包屑
    const breadcrumbs = await fetchBreadcrumbsFromWorker(fullUrl);

    // 如果 Workers API 返回了有效数据，直接使用
    if (breadcrumbs && breadcrumbs.length > 0) {
      return NextResponse.json({
        breadcrumbs,
        path: pathname
      });
    }

    // 如果 Workers API 失败，使用后备方案
    console.warn('Using fallback breadcrumbs for path:', pathname);
    const fallbackBreadcrumbs = generateFallbackBreadcrumbs(pathname);

    return NextResponse.json({
      breadcrumbs: fallbackBreadcrumbs,
      path: pathname
    });
  } catch (error) {
    console.error('Error generating breadcrumbs:', error);

    // 错误情况下也使用后备方案
    const fallbackBreadcrumbs = generateFallbackBreadcrumbs(pathname);

    return NextResponse.json({
      breadcrumbs: fallbackBreadcrumbs,
      path: pathname
    });
  }
}