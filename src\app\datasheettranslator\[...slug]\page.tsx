import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import ServerBreadcrumbWrapper from '@/components/Breadcrumb/ServerBreadcrumbWrapper'
import DatasheetTranslatorClient from './DatasheetTranslatorClient'

// 获取产品详情的函数
async function getProductDetail(productId: string) {
  try {
    const response = await fetch(
      `https://api.chinaelectron.com/api/products/${encodeURIComponent(productId)}`,
      {
        headers: {
          'Accept': 'application/json',
        },
        next: { revalidate: 3600 }
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch product: ${response.status}`);
    }

    const data = await response.json();
    return data.product || null;
  } catch (err) {
    console.error('Error fetching product:', err);
    return null;
  }
}

// 从slug中提取产品信息的函数
function extractInfoFromSlug(slug: string[]): {
  productId: string | null;
  category: string;
  brand: string;
  model: string;
  fullFileName: string;
} {
  if (!slug || slug.length === 0) {
    return {
      productId: null,
      category: '',
      brand: '',
      model: '',
      fullFileName: ''
    };
  }

  // 将所有slug段连接成完整的文件名
  const fullFileName = slug.join('/');

  // 从文件名中提取产品ID (CMA开头的部分)
  const productIdMatch = fullFileName.match(/(CMA\d+)/);
  const productId = productIdMatch ? productIdMatch[1] : null;

  // 解析文件名格式: electronic_components_sensors_special_Image-Sensor_onsemi-AR0220AT4B00XUEA2-DPBR_CMA101796142.pdf
  // 格式: {category1}_{category2}_{category3}_{subcategory}_{brand}-{model}_{productId}.pdf

  let category = '';
  let brand = '';
  let model = '';

  try {
    // 移除.pdf扩展名
    const nameWithoutExt = fullFileName.replace(/\.pdf$/, '');

    // 按下划线分割
    const parts = nameWithoutExt.split('_');

    if (parts.length >= 3) {
      // 倒数第二个部分包含品牌和型号
      const brandModelPart = parts[parts.length - 2];

      // 前面的部分是品类信息
      const categoryParts = parts.slice(0, -2);

      // 构建品类信息
      category = categoryParts.join(' ').replace(/_/g, ' ');

      // 解析品牌和型号 - 格式: onsemi-AR0220AT4B00XUEA2-DPBR
      if (brandModelPart && brandModelPart.includes('-')) {
        const firstDashIndex = brandModelPart.indexOf('-');
        brand = brandModelPart.substring(0, firstDashIndex);
        model = brandModelPart.substring(firstDashIndex + 1);
      }
    }
  } catch (error) {
    console.error('Error parsing filename:', error);
  }

  return {
    productId,
    category: category || 'Electronic Components',
    brand: brand || 'Unknown Brand',
    model: model || (productId || 'Unknown Model'),
    fullFileName
  };
}

// 元数据生成函数
export async function generateMetadata(props: { params: Promise<{ slug: string[] }> }): Promise<Metadata> {
  const params = await props.params;
  const slug = params.slug;

  const { productId, category, brand, model, fullFileName } = extractInfoFromSlug(slug);

  if (!productId) {
    return {
      title: 'Datasheet Translator - PDF Translate Tool | China Electron',
      description: 'Free PDF translator for electronic component datasheets. Translate technical documents with our advanced PDF translate tool.',
    };
  }

  // 尝试获取产品详情以获取更准确的信息
  const productDetail = await getProductDetail(productId);

  // 使用API数据或从URL解析的数据
  const finalBrand = productDetail?.brand?.name_en || brand;
  const finalModel = productDetail?.model || model;
  const finalCategory = category;

  // 构建canonical URL - 确保包含.pdf扩展名
  const canonicalUrl = `https://www.chinaelectron.com/datasheettranslator/${fullFileName}`;

  return {
    title: `${finalModel} Datasheet PDF Translator | China Electron`,
    description: `Free PDF translator for ${finalModel} ${finalCategory} datasheet by ${finalBrand}.`,
    keywords: `${finalModel} datasheet translator, PDF translate, PDF translator, ${finalBrand}, ${finalCategory}, datasheet translation, technical document translator`,
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: `${finalModel} Datasheet PDF Translator | ${finalBrand}`,
      description: `Free PDF translator for ${finalModel} ${finalCategory} datasheet by ${finalBrand}. Translate technical documents with our advanced PDF translate tool.`,
      url: canonicalUrl,
      type: 'website',
    },
  };
}

// 主页面组件
export default async function DatasheetTranslatorSlugPage(props: { params: Promise<{ slug: string[] }> }) {
  const params = await props.params;
  const slug = params.slug;

  const { productId } = extractInfoFromSlug(slug);

  if (!productId) {
    notFound();
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      <Suspense fallback={
        <div className="h-screen flex flex-col bg-gray-50">
          <div className="flex-shrink-0 bg-white border-b border-gray-200">
            <ServerBreadcrumbWrapper />
          </div>
          <div className="flex-1 flex justify-center items-center">
            <LoadingSpinner />
          </div>
        </div>
      }>
        <DatasheetTranslatorClient slug={slug} />
      </Suspense>
    </div>
  )
}
