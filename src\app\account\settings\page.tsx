'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import {
  Settings,
  Lock,
  Eye,
  EyeOff,
  Shield,
  CheckCircle,
  AlertCircle,
  Key
} from 'lucide-react'

interface PasswordForm {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

function SettingsPage() {
  const { token } = useAuth()
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState({ type: '', content: '' })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  const [passwordForm, setPasswordForm] = useState<PasswordForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setMessage({ type: 'error', content: 'New password and confirmation do not match' })
      return
    }
    
    if (passwordForm.newPassword.length < 8) {
      setMessage({ type: 'error', content: 'Password must be at least 8 characters long' })
      return
    }
    
    setLoading(true)
    try {
      // TODO: 实现密码更改API
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))
      setMessage({ type: 'success', content: 'Password updated successfully' })
      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' })
    } catch (error) {
      setMessage({ type: 'error', content: 'Password update failed, please try again' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="w-full">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Settings className="w-6 h-6 text-[#DE2910]" />
          <div>
            <h2 className="text-xl font-bold text-gray-900">Account Settings</h2>
            <p className="text-sm text-gray-600">Manage your account security settings</p>
          </div>
        </div>
      </div>

      {/* Message Alert */}
      {message.content && (
        <div className={`p-4 rounded-xl mb-6 flex items-center space-x-2 ${
          message.type === 'success'
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-red-50 text-red-700 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="w-5 h-5 text-green-500" />
          ) : (
            <AlertCircle className="w-5 h-5 text-red-500" />
          )}
          <span>{message.content}</span>
        </div>
      )}

      <div className="bg-white/50 backdrop-blur-sm rounded-xl border border-white/20 p-6">
        {/* Password Change */}
        <div className="bg-white rounded-xl border border-gray-200">
          <div className="px-6 py-5 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] rounded-lg flex items-center justify-center">
                <Lock className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Change Password</h3>
                <p className="text-sm text-gray-500">Update your password to keep your account secure</p>
              </div>
            </div>
          </div>
          <form onSubmit={handlePasswordSubmit} className="p-6 space-y-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">Current Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Key className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type={showPasswords.current ? 'text' : 'password'}
                    value={passwordForm.currentPassword}
                    onChange={e => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                    className="w-full px-3 py-3 pl-10 pr-10 rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm focus:border-[#DE2910] focus:ring-4 focus:ring-[#DE2910]/20 transition-all duration-300 ease-out placeholder-gray-400 text-sm"
                    placeholder="Enter your current password"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPasswords({...showPasswords, current: !showPasswords.current})}
                  >
                    {showPasswords.current ? (
                      <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">New Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type={showPasswords.new ? 'text' : 'password'}
                    value={passwordForm.newPassword}
                    onChange={e => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                    className="w-full px-3 py-3 pl-10 pr-10 rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm focus:border-[#DE2910] focus:ring-4 focus:ring-[#DE2910]/20 transition-all duration-300 ease-out placeholder-gray-400 text-sm"
                    placeholder="Enter your new password"
                    minLength={8}
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPasswords({...showPasswords, new: !showPasswords.new})}
                  >
                    {showPasswords.new ? (
                      <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
                <p className="mt-2 text-xs text-gray-500">Password must be at least 8 characters long</p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">Confirm New Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-4 w-4 text-gray-400" />
                  </div>
                  <input
                    type={showPasswords.confirm ? 'text' : 'password'}
                    value={passwordForm.confirmPassword}
                    onChange={e => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                    className="w-full px-3 py-3 pl-10 pr-10 rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm focus:border-[#DE2910] focus:ring-4 focus:ring-[#DE2910]/20 transition-all duration-300 ease-out placeholder-gray-400 text-sm"
                    placeholder="Confirm your new password"
                    required
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPasswords({...showPasswords, confirm: !showPasswords.confirm})}
                  >
                    {showPasswords.confirm ? (
                      <EyeOff className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="flex justify-end pt-4 border-t border-gray-100">
              <button
                type="submit"
                disabled={loading}
                className="bg-gradient-to-r from-[#DE2910] to-[#FF6B45] text-white font-semibold py-3 px-6 rounded-xl hover:from-[#FF3B20] hover:to-[#FF8055] hover:scale-105 hover:-translate-y-0.5 transition-all duration-300 ease-out shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center space-x-2"
              >
                <span>{loading ? 'Updating...' : 'Update Password'}</span>
                {!loading && <Key className="w-4 h-4" />}
              </button>
            </div>
          </form>
        </div>

        {/* Password Security Tips */}
        <div className="bg-blue-50 rounded-xl p-6 mt-6 border border-blue-200">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <Shield className="w-4 h-4 text-blue-600" />
            </div>
            <h3 className="text-lg font-medium text-blue-800">Password Security Tips</h3>
          </div>
          <ul className="text-sm text-blue-700 space-y-2">
            <li className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span>Use a combination of letters, numbers, and symbols</span>
            </li>
            <li className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span>Avoid using personal information like birthdays or names</span>
            </li>
            <li className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span>Don't reuse passwords from other websites</span>
            </li>
            <li className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span>Consider using a password manager for secure storage</span>
            </li>
            <li className="flex items-start space-x-2">
              <CheckCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
              <span>Change your passwords regularly for better security</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage 