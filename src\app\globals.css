@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  /* 隐藏滚动条但保留滚动功能 */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* 适用于Firefox */
  .no-scrollbar {
    scrollbar-width: none;
  }
  
  /* 适用于IE和Edge */
  .no-scrollbar {
    -ms-overflow-style: none;
  }
}

:root {
  /* 主色调 - 现代化配色 */
  --primary-red: #DE2910;
  --primary-gold: #FFCD00;
  --primary-blue: #1e40af;
  --primary-dark: #1f2937;

  /* 浅色背景 - 更现代的渐变背景 */
  --bg-light: #FFFFFF;
  --bg-lighter: #F8FAFC;
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --bg-section: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);

  /* 现代化渐变色 */
  --gradient-primary: linear-gradient(135deg, var(--primary-red) 0%, #FF6B45 100%);
  --gradient-gold: linear-gradient(135deg, var(--primary-gold) 0%, #FF9B3D 100%);
  --gradient-blue: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-dark: linear-gradient(135deg, #374151 0%, #1f2937 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  /* 卡片和边框 - 更现代的设计 */
  --bg-card: rgba(255, 255, 255, 0.95);
  --bg-card-hover: rgba(255, 255, 255, 1);
  --border-light: rgba(0, 0, 0, 0.06);
  --border-hover: rgba(222, 41, 16, 0.15);
  --border-focus: rgba(59, 130, 246, 0.3);

  /* 现代化阴影系统 */
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-card-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-button: 0 4px 14px 0 rgba(222, 41, 16, 0.25);
  --shadow-button-hover: 0 6px 20px 0 rgba(222, 41, 16, 0.35);
  --shadow-section: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  
  /* 文字颜色 */
  --text-primary: #1F2937;
  --text-secondary: #4B5563;
  --text-light: #6B7280;
  
  /* 主色调 - 工业蓝 */
  --primary-50: #e6f7ff;
  --primary-100: #bae7ff;
  --primary-500: #0051a3;  /* DigiKey蓝 */
  --primary-600: #003d7a;
  --primary-700: #002952;
  
  /* 辅助色 */
  --accent-500: #00a870;  /* 工业绿 */
  --accent-600: #008656;
  
  /* 警示色 */
  --warning-500: #ed1c24;  /* 警告红 */
  --warning-600: #c41017;
  
  /* 中性色 */
  --neutral-50: #f5f7fa;
  --neutral-100: #ebeef5;
  --neutral-200: #e4e7ed;
  --neutral-300: #dcdfe6;
  --neutral-700: #606266;
  --neutral-800: #303133;
  --neutral-900: #1a1a1a;

  /* 渐变 */
  --gradient-red: linear-gradient(135deg, var(--primary-500), #ff4d4f);
  --gradient-tech: linear-gradient(135deg, var(--primary-500) 0%, var(--accent-500) 100%);
  --gradient-gold: linear-gradient(135deg, #ffd700 0%, var(--highlight-500) 100%);
  
  /* 阴影 */
  --shadow-soft: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-strong: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-glow: 0 0 15px rgba(0, 87, 184, 0.3);

  /* 中国红主题色 */
  --china-red: #DE2910;  /* 中国红 */
  --china-gold: #FFCD00; /* 金色 */
  
  /* 调整渐变色 */
  --gradient-button: linear-gradient(135deg, #FF4D8C 0%, #FF1A6C 100%);
  
  /* 调整边框和阴影 */
  --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);  /* 更强的阴影 */
  
  /* 新增过渡色 */
  --hover-card: rgba(255, 255, 255, 0.05);  /* 悬停状态的卡片背景 */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--text-primary);
  background: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  min-height: 100vh;
  line-height: 1.6;
}

@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 添加动画类 */
.animate-fadeIn {
  animation: fadeIn 1s ease-out;
}

.animate-slideInUp {
  animation: slideInUp 1s ease-out 0.3s both;
}

.animate-slideInRight {
  animation: slideInRight 1s ease-out 0.5s both;
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
  100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
}

.animate-fadeIn {
  animation: fadeIn 0.8s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 添加新的工具类 */
.tech-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.7);
  transition: all 0.3s ease;
}

.tech-card:hover {
  border-color: var(--primary-500);
  transform: translateY(-2px);
}

.gradient-text {
  background: var(--gradient-text);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* 添加新的渐变和阴影样式 */
.gradient-border {
  background: linear-gradient(var(--neutral-50), var(--neutral-50)) padding-box,
              linear-gradient(to right, var(--primary-500), var(--accent-500)) border-box;
  border: 2px solid transparent;
}

.hover-lift {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.1);
}

/* 添加新的渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%,
    rgba(147, 51, 234, 0.1) 100%
  );
}

/* 添加新的卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px -15px rgba(0, 0, 0, 0.1);
}

/* 添加玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 更新卡片样式 */
.card-glass {
  background: var(--bg-card);
  border: 1px solid var(--border-light);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card-glass:hover {
  background: #FFFFFF;
  border-color: var(--border-hover);
  box-shadow: var(--shadow-card);
  transform: translateY(-2px);
}

/* 添加按钮样式 */
.btn-gradient {
  background: var(--gradient-primary);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
  box-shadow: var(--shadow-button);
}

/* 现代化组件样式 */
.component-card {
  @apply bg-white/95 backdrop-blur-sm rounded-lg border border-gray-100/50
         hover:border-[#DE2910]/20 hover:shadow-xl hover:shadow-gray-200/50
         hover:bg-white hover:-translate-y-1
         transition-all duration-500 ease-out;
  box-shadow: var(--shadow-card);
}

.component-card:hover {
  box-shadow: var(--shadow-card-hover);
}

.section-title {
  @apply text-gray-900 font-bold text-2xl md:text-3xl;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  @apply text-gray-600 text-lg;
}

/* 现代化按钮样式 */
.btn-primary {
  @apply bg-gradient-to-r from-[#DE2910] to-[#FF6B45]
         text-white font-semibold px-6 py-3 rounded-xl
         hover:from-[#FF3B20] hover:to-[#FF8055]
         hover:scale-105 hover:-translate-y-0.5
         transition-all duration-300 ease-out
         shadow-lg hover:shadow-xl;
  box-shadow: var(--shadow-button);
}

.btn-primary:hover {
  box-shadow: var(--shadow-button-hover);
}

.btn-secondary {
  @apply bg-white/90 backdrop-blur-sm text-gray-700 font-medium px-6 py-3 rounded-xl
         border border-gray-200/50
         hover:border-[#DE2910]/30 hover:bg-white
         hover:text-[#DE2910] hover:scale-105 hover:-translate-y-0.5
         transition-all duration-300 ease-out;
  box-shadow: var(--shadow-card);
}

.btn-secondary:hover {
  box-shadow: var(--shadow-card-hover);
}

/* 添加闪光动画 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

/* 添加脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 现代化动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 现代化卡片样式 */
.modern-card {
  @apply bg-white/95 backdrop-blur-sm rounded-lg border border-gray-100/50
         hover:shadow-xl hover:shadow-gray-200/50 hover:-translate-y-2
         transition-all duration-500 ease-out;
  box-shadow: var(--shadow-card);
}

.modern-card:hover {
  box-shadow: var(--shadow-card-hover);
}

/* 带padding的现代化卡片 */
.modern-card-padded {
  @apply modern-card p-6;
}

/* 现代化渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-primary {
  background: linear-gradient(135deg, #DE2910 0%, #FF6B45 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 现代化分割线 */
.modern-divider {
  @apply relative;
}

.modern-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(0,0,0,0.1) 50%, transparent 100%);
}

/* 现代化页面布局样式 */
.modern-section {
  @apply py-16 relative;
}

/* 现代化容器 */
.modern-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative;
}

/* 现代化网格布局 */
.modern-grid {
  @apply grid gap-8 lg:gap-12;
}

/* 现代化标题样式 */
.modern-title {
  @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-6;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.modern-subtitle {
  @apply text-lg md:text-xl text-gray-600 mb-8 max-w-3xl;
}

/* 现代化按钮变体 */
.btn-modern {
  @apply px-8 py-4 rounded-2xl font-semibold text-lg
         transition-all duration-300 ease-out
         hover:scale-105 hover:-translate-y-1
         focus:outline-none focus:ring-4 focus:ring-opacity-50;
}

.btn-modern-primary {
  @apply btn-modern bg-gradient-to-r from-blue-600 to-indigo-600
         text-white shadow-lg hover:shadow-xl
         hover:from-blue-700 hover:to-indigo-700
         focus:ring-blue-500;
}

.btn-modern-secondary {
  @apply btn-modern bg-white/90 backdrop-blur-sm text-gray-700
         border-2 border-gray-200 shadow-md hover:shadow-lg
         hover:bg-white hover:border-blue-300 hover:text-blue-600
         focus:ring-gray-300;
}

/* 现代化输入框样式 */
.modern-input {
  @apply w-full px-6 py-4 rounded-2xl border-2 border-gray-200
         bg-white/90 backdrop-blur-sm
         focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20
         transition-all duration-300 ease-out
         placeholder-gray-400;
}

/* 现代化选择框样式 */
.modern-select {
  @apply modern-input appearance-none cursor-pointer
         bg-white/90 backdrop-blur-sm;
}

/* 现代化复选框样式 */
.modern-checkbox {
  @apply w-5 h-5 rounded-lg border-2 border-gray-300
         text-blue-600 focus:ring-blue-500 focus:ring-2
         transition-all duration-200;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .modern-title {
    @apply text-2xl md:text-3xl;
  }

  .modern-subtitle {
    @apply text-base md:text-lg;
  }

  .btn-modern {
    @apply px-6 py-3 text-base;
  }

  .modern-container {
    @apply px-4;
  }

  .modern-grid {
    @apply gap-6;
  }

  .modern-card {
    @apply p-4;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .modern-card {
    @apply bg-gray-800/95 border-gray-700/50;
  }

  .modern-title {
    background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 50%, #d1d5db 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .modern-subtitle {
    @apply text-gray-300;
  }

  .modern-input {
    @apply bg-gray-800/90 border-gray-600 text-white;
  }

  .btn-modern-secondary {
    @apply bg-gray-800/90 text-gray-200 border-gray-600
           hover:bg-gray-700 hover:border-gray-500;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .modern-card {
    @apply border-2 border-gray-900;
  }

  .btn-primary {
    @apply bg-blue-700 hover:bg-blue-800;
  }

  .modern-input {
    @apply border-gray-900;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .animate-fadeInUp,
  .animate-slideInLeft,
  .animate-slideInRight,
  .animate-scaleIn,
  .animate-float,
  .animate-glow {
    animation: none;
  }

  .modern-card,
  .btn-modern,
  .component-card {
    transition: none;
  }
}

/* 添加网状小圆点动画 */
@keyframes dotPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.02);
  }
}

.dot-pattern {
  animation: dotPulse 4s ease-in-out infinite;
}

/* 更新光晕动画 */
@keyframes glowPulse {
  0%, 100% {
    opacity: 0.2;
    transform: translate(-50%, 0) scale(1);
  }
  50% {
    opacity: 0.3;
    transform: translate(-50%, 0) scale(1.05);
  }
}

.hero-glow {
  animation: glowPulse 6s ease-in-out infinite;
}

/* 添加径向渐变工具类 */
.bg-gradient-radial {
  background-image: radial-gradient(var(--tw-gradient-stops));
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-marquee {
  animation: marquee 30s linear infinite;
}

/* 自下而上滚动动画 */
@keyframes scrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.animate-scroll-up {
  animation: scrollUp 30s linear infinite;
}

.animate-pause {
  animation-play-state: paused;
}

/* 滚动动画效果 */
@keyframes scrollY {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

.scrolling-content {
  animation: scrollY 30s linear infinite;
}

.quotation-scroll-container:hover .scrolling-content {
  animation-play-state: paused;
}
