'use client'

import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import { useState, useRef, useEffect } from 'react'
import { Outfit } from 'next/font/google'
import { getCartItems } from '@/services/api'

const outfit = Outfit({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap'
})

export default function Header() {
  const { isAuthenticated, userId, logout } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const isHomePage = pathname === '/'
  const [showDropdown, setShowDropdown] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const [searchText, setSearchText] = useState('')
  const [cartItemCount, setCartItemCount] = useState(0)

  // 点击页面其他地方关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // 获取购物车商品种类数量
  useEffect(() => {
    const fetchCartItemCount = async () => {
      if (isAuthenticated && userId) {
        try {
          const cartItems = await getCartItems(userId)
          const itemCount = cartItems.length // 计算商品种类数量，而不是总数量
          setCartItemCount(itemCount)
        } catch (error) {
          console.error('Failed to fetch cart items:', error)
          setCartItemCount(0)
        }
      } else {
        setCartItemCount(0)
      }
    }

    fetchCartItemCount()
  }, [isAuthenticated, userId])

  const handleLogout = async () => {
    try {
      await logout()
      setShowDropdown(false)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const handleSearch = () => {
    if (!searchText.trim()) return
    
    router.push(`/search?q=${encodeURIComponent(searchText.trim())}`)
  }

  return (
    <div className={outfit.className}>
      {/* 顶部信息栏 */}
      <div className="bg-[#ef233c] text-white py-2 z-50 relative w-full">
        <div className="max-w-[1900px] mx-auto px-4 lg:px-14 flex justify-between items-center text-sm font-normal">
          <div>Welcome to China Electron !</div>
        </div>
      </div>

      {/* 主导航栏 */}
      <header className="bg-white text-gray-800 z-50 relative w-full">
        <div className="max-w-[1900px] mx-auto px-4 lg:px-14">
          <div className="flex items-center justify-between h-24">
            {/* Logo */}
            <Link href="/" className="flex items-center">
              <div className="flex items-center justify-center">
                <img
                  src="https://img.chinaelectron.com/logo.svg"
                  alt="Logo"
                  className="h-16 w-auto"
                />
              </div>
              {isHomePage && (
                <h1 style={{ position: 'absolute', width: '1px', height: '1px', padding: '0', margin: '-1px', overflow: 'hidden', clip: 'rect(0, 0, 0, 0)', whiteSpace: 'nowrap', borderWidth: '0' }}>
                  China Electron
                </h1>
              )}
            </Link>

            {/* 搜索框 */}
            <div className="flex-1 max-w-3xl mx-4 md:mx-8">
              <div className="relative">
                <input
                  type="text"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch()
                    }
                  }}
                  placeholder="What can we help you find?"
                  className="w-full px-6 py-4 pl-6 pr-16 rounded-lg
                           bg-gray-50 hover:bg-white
                           text-gray-700 placeholder-gray-400
                           border border-gray-200 focus:border-[#ef233c] focus:ring-2 focus:ring-[#ef233c]/20
                           transition-all duration-300 ease-out
                           focus:outline-none focus:shadow-lg
                           text-base font-medium"
                />
                <button
                  onClick={handleSearch}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 h-10 w-10
                           bg-[#ef233c] hover:bg-[#d41f35]
                           text-white rounded-lg
                           transition-all duration-300 ease-out flex items-center justify-center
                           hover:scale-105 hover:shadow-lg"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
                    <path strokeLinecap="round" strokeLinejoin="round"
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </div>

            {/* 右侧图标和用户部分 */}
            <div className="flex items-center space-x-4">
              {/* RFQ图标 */}
              <Link href="/rfq" className="hidden md:flex p-2 rounded-lg text-gray-700 hover:text-[#ef233c] hover:bg-gray-50 transition-all duration-300 group">
                <div className="relative">
                  <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <span className="sr-only">RFQ</span>
              </Link>

              {/* 购物车图标 */}
              <Link href="/cart" className="relative p-2 rounded-lg text-gray-700 hover:text-[#ef233c] hover:bg-gray-50 transition-all duration-300 group">
                <div className="relative">
                  <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z" />
                  </svg>
                  {cartItemCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-[#ef233c] text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-200">
                      {cartItemCount > 99 ? '99+' : cartItemCount}
                    </span>
                  )}
                </div>
                <span className="sr-only">Shopping Cart ({cartItemCount})</span>
              </Link>

              {isAuthenticated ? (
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="p-2 rounded-lg text-gray-700 hover:text-[#ef233c] hover:bg-gray-50 transition-all duration-300 group"
                  >
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    <span className="sr-only">User Account</span>
                  </button>

                  {/* Dropdown Menu */}
                  {showDropdown && (
                    <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white border border-gray-200 z-50">
                      <div className="py-1">
                        <button
                          onClick={() => {
                            router.push('/account')
                            setShowDropdown(false)
                          }}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 font-normal"
                        >
                          My Account
                        </button>
                        <button
                          onClick={handleLogout}
                          className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200 font-normal"
                        >
                          Log Out
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <Link href="/login" className="p-2 rounded-lg text-gray-700 hover:text-[#ef233c] hover:bg-gray-50 transition-all duration-300 group">
                  <div className="flex items-center">
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <span className="sr-only">Sign In</span>
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* 导航菜单 */}
      <div className="bg-white z-40 relative w-full border-b border-gray-200 shadow-sm">
        <div className="max-w-[1900px] mx-auto px-4 lg:px-14">
          <div className="flex items-center h-14 overflow-x-auto">
            <nav className="flex items-center space-x-8 md:space-x-12">
              <Link key="nav-components" href="/components" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <span>Components</span>
              </Link>
              <Link key="nav-manufacturers" href="/manufacturers" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
                <span>Manufacturers</span>
              </Link>
              {/* <Link key="nav-distributors" href="/distributors" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
                </svg>
                <span>Distributors</span>
              </Link> */}
              <Link key="nav-rfq" href="/rfq" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
                <span>RFQ</span>
              </Link>
              <Link key="nav-bom" href="/bom" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>BOM</span>
              </Link>
              <Link key="nav-dailyhot" href="/dailyhot" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                </svg>
                <span>Daily Hot</span>
              </Link>
              <Link key="nav-insights" href="/insights" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
                <span>Insights</span>
              </Link>
              <Link key="nav-spotlight" href="/spotlight" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83a2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z" />
                </svg>
                <span>Spotlight</span>
              </Link>
              <Link key="nav-aboutus" href="/about-us" className="text-gray-800 hover:text-[#ef233c] font-medium text-lg whitespace-nowrap transition-colors flex items-center space-x-2">
                <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>About us</span>
              </Link>
            </nav>
          </div>
        </div>
      </div>
    </div>
  )
}