import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import { ComponentDetail, FullData } from '@/types/component'
import ProductClientWrapper from '@/components/ClientComponents/ProductClientWrapper'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import { generateBreadcrumbs } from '@/utils/breadcrumbs'
import { buildDatasheetTranslatorUrl } from '@/utils/productUrl'
import ServerBreadcrumb from '@/components/Breadcrumb/ServerBreadcrumb'
import FAQAccordion from '@/components/ComponentDetail/FAQAccordion'
import SimplePDFViewer from '@/components/SimplePDFViewer'
import ProductJsonLd from '@/components/SEO/ProductJsonLd'
import SummarizerTabs from '@/components/ComponentDetail/SummarizerTabs'

interface ProductParameter {
  param_name: string
  param_value: string
}

interface CategoryNameInfo {
  name_cn: string
  name_en: string
  name_ru: string
}

// 添加summarizer相关的接口定义
interface SummarizerTableContent {
  key: string
  value: string[] | string
}

interface SummarizerTextContent {
  header?: string
  type: 'text'
  content: string[] | SummarizerItem[]
}

interface SummarizerTableItem {
  header: string
  type: 'table'
  content: SummarizerTableContent[]
}

type SummarizerItem = SummarizerTextContent | SummarizerTableItem

interface Summarizer {
  productCode: string
  results: SummarizerItem[]
}

interface ProductDetail {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand: {
    id: string
    name_en: string
    name_cn: string
    name_ru: string
    logo_url: string
    website: string
  }
  prices: {
    quantity: number
    price: number
  }[]
  stock: number
  parameters: {
    en: ProductParameter[]
    cn: ProductParameter[]
    ru: ProductParameter[]
  }
  category_path: string[]
  category_id: string
  category_names: {
    [key: string]: CategoryNameInfo
  }
  datasheet_url: string
  image_list: string[]
  description: string
  summarizer?: Summarizer // 添加summarizer字段
  rohs?: string | null
}



// 标签切换的summarizer数据组件
function SummarizerRenderer({ summarizer, productModel }: { summarizer?: Summarizer; productModel?: string }) {
  if (!summarizer || !summarizer.results || summarizer.results.length === 0) {
    return null;
  }



  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
        <h2 className="text-xl font-semibold text-blue-900 flex items-center gap-2">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {productModel ? `${productModel} Application Scenarios` : 'Application Scenarios'}
        </h2>
      </div>

      <SummarizerTabs items={summarizer.results} />
    </div>
  );
}

// 获取datasheet详情页URL的函数
async function getDatasheetPageUrl(productId: string): Promise<string | null> {
  try {
    const response = await fetch(
      `https://webapi.chinaelectron.com/datasheet/product?product_id=${encodeURIComponent(productId)}`,
      {
        headers: {
          'Accept': 'application/json',
        },
        next: { revalidate: 3600 }
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch datasheet URL: ${response.status}`);
    }

    const data = await response.json();

    if (data.exists && data.datasheet_url) {
      return data.datasheet_url;
    }

    return null;
  } catch (err) {
    console.error('Error fetching datasheet URL:', err);
    return null;
  }
}

// FAQ数据
const faqData = {
  sections: [
    {
      title: "Shipping & Logistics",
      questions: [
        {
          question: "When will my order be shipped?",
          answer: "For in-stock parts, orders placed between 8:00-16:00 will be dispatched on the same day."
        },
        {
          question: "How long does shipping take?",
          answer: "Once shipped, the transit time depends on the logistics provider you choose:\n\nDHL Express: 3-5 days\n\nFedEx International: 3-5 days\n\nYUNTU: 4-25 days\n\nChina Post Airmail: 15-45 days"
        },
        {
          question: "How much does shipping cost?",
          answer: "The shipping rate for your specific order can be found in your shopping cart."
        },
        {
          question: "What shipping options do you offer?",
          answer: "We provide shipping via DHL, FedEx, YUNTU, and China Post Airmail."
        },
        {
          question: "How can I track my order?",
          answer: "We will notify you by email with a tracking number once your order has shipped. You can also find the tracking number in your order history."
        }
      ]
    },
    {
      title: "Returns & Warranty",
      questions: [
        {
          question: "What is your return policy?",
          answer: "We only accept returns for quality-related issues.\n\nTimeframe: Return requests must be submitted within 30 days of delivery.\n\nAuthorization: Please contact customer service to receive a return authorization.\n\nCondition: Parts must be unused and in their original packaging.\n\nCost: The customer is responsible for the cost of return shipping."
        },
        {
          question: "Do you offer a warranty?",
          answer: "Yes. All purchases from China Electron come with a 30-day money-back return policy and a 90-day warranty against any manufacturing defects.\nPlease note: This warranty does not apply to defects caused by improper customer assembly, failure to follow instructions, product modification, or negligent or improper operation."
        }
      ]
    },
    {
      title: "Ordering & Payment",
      questions: [
        {
          question: "How do I place an order?",
          answer: "China Electron accepts online orders. The process is as follows:\nAdd to cart > Check out > Submit order > Complete payment > Delivery"
        },
        {
          question: "What payment methods do you accept?",
          answer: "We accept PayPal, Credit Card (Visa, Master, American Express, JCB, Discover), and Bank Transfer."
        }
      ]
    }
  ]
};

// 创建独立的Datasheet组件
async function DatasheetSection({ datasheetUrl, model, productId, productDetail }: { datasheetUrl?: string, model: string, productId: string, productDetail: any }) {
  if (!datasheetUrl) return null;

  // 通过新API获取datasheet详情页URL
  const datasheetPageUrl = await getDatasheetPageUrl(productId);

  // 构建SEO友好的数据表翻译器URL
  let datasheetTranslatorUrl = '';

  if (datasheetPageUrl) {
    // 从完整URL中提取文件名部分
    // 例如: https://www.chinaelectron.com/datasheet/electronic_components_sensors_special_Image-Sensor_onsemi-AR0220AT4B00XUEA2-DPBR_CMA101796142.pdf
    // 提取: electronic_components_sensors_special_Image-Sensor_onsemi-AR0220AT4B00XUEA2-DPBR_CMA101796142.pdf
    const urlParts = datasheetPageUrl.split('/');
    const fileName = urlParts[urlParts.length - 1];

    // 构建数据表翻译器URL
    datasheetTranslatorUrl = `/datasheettranslator/${fileName}`;
  } else {
    // 回退到使用buildDatasheetTranslatorUrl函数
    datasheetTranslatorUrl = buildDatasheetTranslatorUrl({
      product_id: productDetail.product_id,
      model: productDetail.model,
      brand: productDetail.brand,
      category_path: productDetail.category_path,
      category_names: productDetail.category_names
    });
  }

  return (
    <div id="datasheet-section" className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Datasheet
          </h2>
          <div className="flex items-center gap-2">
            <Link
              href={datasheetTranslatorUrl}
              className="inline-flex items-center gap-2 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
              Datasheet Translator
            </Link>
            <Link
              href={datasheetPageUrl || `/datasheet/${productId}`}
              className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
              Open Full View
            </Link>
          </div>
        </div>
      </div>
      <div className="p-6">
        <div className="aspect-[1.414/1] border border-gray-200 rounded-lg overflow-hidden">
          <SimplePDFViewer
            url={datasheetUrl}
            productName={model}
            productId={productId}
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
}

// 转换产品数据为组件需要的数据格式
function convertToComponentFormat(
  productDetail: ProductDetail
): { component: ComponentDetail, parsedData: FullData } {
  // 创建ComponentDetail对象
  const component: ComponentDetail = {
    id: productDetail.product_id,
    score: 1, // 默认值
    metadata: {
      id: productDetail.product_id,
      model: productDetail.model,
      name: productDetail.model,
      desc: productDetail.description || '',
      manufacture: productDetail.brand?.name_en || '',
      img: productDetail.image_list?.[0] || '',
      datasheet: productDetail.datasheet_url || '',
      cad: '',
      full_data: '', // 将在下面填充
      // 添加品牌信息，以便在组件中访问
      brand: productDetail.brand || {
        id: '',
        name_en: '',
        name_cn: '',
        name_ru: '',
        logo_url: '',
        website: ''
      },
      // 添加价格和库存数据到metadata
      prices: productDetail.prices || [],
      stock: productDetail.stock || 0,
      // 添加分类路径
      category_path: productDetail.category_path || [],
      // 添加分类名称
      category_names: productDetail.category_names || {},
      // 添加summarizer数据
      summarizer: productDetail.summarizer
    },
    values: []
  } as ComponentDetail;
  
  // 创建FullData对象并适配FullData接口的形式
  const parsedData = {
    name: productDetail.model,
    desc: productDetail.description || '',
    model: productDetail.model,
    location: '', // 默认值
    manufacture: productDetail.brand?.name_en || '',
    parameter: {
      languages: {
        english: {
          name: productDetail.model,
          desc: productDetail.description || '',
          parameters: {
            main_parameters: Object.fromEntries(
              (productDetail.parameters?.en || []).map(p => [p.param_name, p.param_value])
            ),
            secondary_parameters: {},
            common_parameters: {}
          },
          features: [],
          applications: []
        },
        chinese: {},
        russian: {}
      }
    },
    img: productDetail.image_list || [],
    datasheet: productDetail.datasheet_url ? [productDetail.datasheet_url] : [],
    cad: [],
    update_time: new Date().toISOString(),
    // 添加额外字段以便组件使用
    parameters: productDetail.parameters || { en: [], cn: [], ru: [] },
    // 确保价格和库存数据也在parsedData中
    prices: productDetail.prices || [],
    stock: productDetail.stock || 0,
    // 添加分类路径到parsedData
    category_path: productDetail.category_path || [],
    category_id: productDetail.category_id || '',
    // 添加分类名称到parsedData
    category_names: productDetail.category_names || {},
    // 添加summarizer数据到parsedData
    summarizer: productDetail.summarizer,
    // 添加rohs数据
    rohs: productDetail.rohs
  } as unknown as FullData;
  
  // 将parsedData转为字符串并保存到component.metadata.full_data
  component.metadata.full_data = JSON.stringify(parsedData);
  
  return { component, parsedData };
}

// 从新URL格式中提取产品ID
function extractProductIdFromUrl(urlPath: string): string {
  // console.log('Extracting product ID from URL path:', urlPath);
  
  // 针对CMA格式产品ID的精确匹配
  const cmaMatch = urlPath.match(/CMA\d{9,12}/);
  if (cmaMatch) {
    const productId = cmaMatch[0];
    // console.log('Direct CMA match found:', productId);
    return productId;
  }
  
  // 对于包含括号和特殊字符的复杂URL，先进行URL解码
  const decodedPath = decodeURIComponent(urlPath);
  // 尝试从解码后的URL中找CMA格式
  const decodedCmaMatch = decodedPath.match(/CMA\d{9,12}/);
  if (decodedCmaMatch) {
    const productId = decodedCmaMatch[0];
    // console.log('Found CMA product ID after URL decoding:', productId);
    return productId;
  }
  
  // 检查URL是否以CMA结尾
  if (/CMA\d+$/.test(urlPath)) {
    const parts = urlPath.split('-');
    const lastPart = parts[parts.length - 1];
    // console.log('Found CMA ID at end of URL:', lastPart);
    return lastPart;
  }
  
  // 分割路径并检查每个部分
  try {
    // 对于包含横杠的URL，分割并检查每个部分
    const parts = urlPath.split('-');
    // console.log('URL parts:', parts);
    
    // 从尾部开始查找CMA格式的部分
    for (let i = parts.length - 1; i >= 0; i--) {
      const part = parts[i];
      if (part && part.includes('CMA')) {
        const cmaMatch = part.match(/CMA\d+/);
        if (cmaMatch) {
          // console.log(`Found CMA ID in part ${i}:`, cmaMatch[0]);
          return cmaMatch[0];
        }
      }
    }
    
    // 检查最后一部分是否为产品ID格式
    if (parts.length > 0) {
      const lastPart = parts[parts.length - 1];
      // console.log('Last part of URL:', lastPart);
      if (/^CMA\d+$/.test(lastPart) || /^\d+$/.test(lastPart)) {
        // console.log('Last part is a valid product ID:', lastPart);
        return lastPart;
      }
    }
    
    // 如果我们有足够的部分，假设最后一个是产品ID（新URL格式）
    if (parts.length >= 6) {
      const productId = parts[parts.length - 1];
      // console.log('Using last part as product ID based on URL format:', productId);
      return productId;
    }
  } catch (error) {
    console.error('Error parsing URL parts:', error);
  }
  
  // 兼容旧格式 - 如果无法按新格式解析，直接返回原始路径
  // console.log('Using original path as product ID:', urlPath);
  return urlPath;
}

// 获取产品数据的函数，在服务端执行
async function getProductDetail(productId: string) {
  try {
    // 准备请求头
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    };

    const response = await fetch(
      `https://webapi.chinaelectron.com/products/${encodeURIComponent(productId)}`,
      { 
        headers,
        // 这确保每次访问都获取最新数据，适合SSR场景
        next: { revalidate: 3600 }
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch product details: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data || !data.product_id) {
      return null;
    }
    
    return data as ProductDetail;
  } catch (err) {
    console.error('Fetch error:', err);
    throw new Error('Failed to fetch product details');
  }
}



// 为元数据生成函数，用于SEO
export async function generateMetadata(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const urlPath = params.id;
  const productId = extractProductIdFromUrl(urlPath);
  const productDetail = await getProductDetail(productId);
  
  if (!productDetail) {
    return {
      title: 'Product Not Found',
      description: 'The requested product could not be found'
    };
  }
  
  const brandName = productDetail.brand?.name_en || 'Unknown Brand';

  // 构建canonical URL - 优先使用url字段，回退到手动构建
  const canonicalUrl = productDetail.url
    ? (productDetail.url.startsWith('http')
        ? productDetail.url
        : getCanonicalUrl(productDetail.url.startsWith('/') ? productDetail.url.slice(1) : productDetail.url))
    : getCanonicalUrl(`product/${urlPath}`);

  return {
    title: `${productDetail.model} | ${brandName} | Specs & Suppliers | China Electron`,
    description: `Find ${productDetail.model} by ${brandName} on China Electron. Get specifications, datasheets, pricing, and connect with suppliers for this electronic component raw material.`,
    keywords: `${productDetail.model}, ${brandName}, ${productDetail.category_names?.[productDetail.category_id]?.name_en || 'electronic component'}, ${productDetail.model} raw material, China Electron, component specs, B2B sourcing, ${productDetail.model} datasheet, ${productDetail.model} price`,
    openGraph: {
      images: productDetail.image_list?.[0] ? [productDetail.image_list[0]] : [],
    },
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

// 主页面组件 - 服务端渲染
export default async function ProductDetailPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const urlPath = params.id;
  const productId = extractProductIdFromUrl(urlPath);
  const productDetail = await getProductDetail(productId);

  // 如果产品不存在，返回404
  if (!productDetail) {
    notFound();
  }

  // 转换数据为组件格式
  const componentData = convertToComponentFormat(productDetail);

  // 生成面包屑
  const currentPath = `/product/${urlPath}`;
  const breadcrumbs = await generateBreadcrumbs(currentPath);

  // 构建产品的完整 URL - 优先使用url字段，回退到手动构建
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.chinaelectron.com';
  const productUrl = productDetail.url
    ? (productDetail.url.startsWith('http') ? productDetail.url : `${baseUrl}${productDetail.url}`)
    : `${baseUrl}/product/${urlPath}`;

  return (
    <div className="min-h-screen bg-white">
      {/* 服务端渲染的面包屑 */}
      <ServerBreadcrumb breadcrumbs={breadcrumbs} />

      {/* 添加 JSON-LD 结构化数据 */}
      <ProductJsonLd
        productDetail={productDetail}
        productUrl={productUrl}
        componentId={productDetail.product_id}
      />

      <div className="max-w mx-auto px-14 py-8">
          <Suspense fallback={
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner />
            </div>
          }>
            <ProductClientWrapper
              componentData={componentData}
              summarizerComponent={
                <>
                  {productDetail.summarizer ? <SummarizerRenderer summarizer={productDetail.summarizer} productModel={productDetail.model} /> : null}
                  <DatasheetSection
                    datasheetUrl={productDetail.datasheet_url}
                    model={productDetail.model}
                    productId={productDetail.product_id}
                    productDetail={productDetail}
                  />
                  <FAQAccordion sections={faqData.sections} />
                </>
              }
            />
          </Suspense>
      </div>
    </div>
  )
} 