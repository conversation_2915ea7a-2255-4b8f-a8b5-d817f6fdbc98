import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const metadata: Metadata = {
  title: 'Request for Quote (RFQ) | China Electron',
  description: 'Submit a Request for Quote (RFQ) for electronic components and raw materials. Get competitive pricing and lead times from verified suppliers on China Electron.',
  keywords: 'RFQ, request for quote, electronic component sourcing, component price inquiry, China Electron RFQ, procurement request',
  alternates: {
    canonical: getCanonicalUrl('rfq'),
  },
}

export default function RFQLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
} 