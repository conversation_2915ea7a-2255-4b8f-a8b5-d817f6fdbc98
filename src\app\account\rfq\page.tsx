'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { fetchRFQRequests, RFQRequest, mapStatusToUI, handleApiError } from '@/services/api'
import {
  FileText,
  Calendar,
  Package,
  Building,
  Mail,
  Globe,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

interface UiRFQItem {
  id: string
  model: string
  quantity: number
  requestDate: string
  status: 'pending' | 'quoted' | 'closed'
  contactName: string
  businessEmail: string
  companyName: string
  country: string
  brandName: string
}

export default function RFQListPage() {
  const { userId } = useAuth()
  const [rfqList, setRfqList] = useState<UiRFQItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadRFQData = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        const requests = await fetchRFQRequests(userId)
        // console.log('API Response:', requests) // 调试用

        // 转换API数据为UI显示格式
        const formattedRequests = requests.map((req: RFQRequest): UiRFQItem => ({
          id: req.id,
          model: req.model || 'N/A',
          quantity: req.quantity || 0,
          requestDate: formatDate(req.create_at),
          // 确保状态是有效的枚举值
          status: (req.status === 'pending' || req.status === 'quoted' || req.status === 'closed') 
            ? (req.status as 'pending' | 'quoted' | 'closed')
            : 'pending',
          contactName: req.contact_name || 'N/A',
          businessEmail: req.business_email || 'N/A',
          companyName: req.company_name || 'N/A',
          country: req.country || 'N/A',
          brandName: req.brand_name || 'N/A'
        }))
        
        setRfqList(formattedRequests)
      } catch (err) {
        console.error('Error details:', err)
        const errorMessage = handleApiError(err, '无法加载RFQ数据')
        setError(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }
    
    if (userId) {
      loadRFQData()
    }
  }, [userId])

  // 格式化日期为更友好的格式
  const formatDate = (dateString: string): string => {
    try {
      if (!dateString) return 'N/A'
      const date = new Date(dateString)
      return date.toISOString().split('T')[0] // 返回YYYY-MM-DD格式
    } catch (e) {
      return dateString || 'N/A'
    }
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          color: 'bg-amber-50 text-amber-700 border-amber-200',
          icon: Clock,
          label: 'Pending'
        }
      case 'quoted':
        return {
          color: 'bg-blue-50 text-blue-700 border-blue-200',
          icon: CheckCircle,
          label: 'Quoted'
        }
      case 'closed':
        return {
          color: 'bg-gray-50 text-gray-700 border-gray-200',
          icon: XCircle,
          label: 'Closed'
        }
      default:
        return {
          color: 'bg-gray-50 text-gray-700 border-gray-200',
          icon: AlertCircle,
          label: 'Unknown'
        }
    }
  }

  return (
    <div className="w-full">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <FileText className="w-6 h-6 text-[#DE2910]" />
          <div>
            <h2 className="text-xl font-bold text-gray-900">RFQ List</h2>
            <p className="text-sm text-gray-600">Manage your request for quotes</p>
          </div>
        </div>
        <div className="text-sm text-gray-500">
          {rfqList.length} {rfqList.length === 1 ? 'request' : 'requests'}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-xl mb-6 flex items-center space-x-2">
          <AlertCircle className="w-5 h-5 text-red-500" />
          <span>{error}</span>
        </div>
      )}

      {/* RFQ List */}
      <div className="bg-white/50 backdrop-blur-sm rounded-xl border border-white/20">
        {isLoading ? (
          <div className="flex flex-col justify-center items-center h-64 space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-[#DE2910]/20 border-t-[#DE2910]"></div>
            <p className="text-gray-600 font-medium">Loading your RFQ requests...</p>
          </div>
        ) : (
          <div className="p-6">
            {rfqList.length > 0 ? (
              <div className="space-y-4">
                {rfqList.map((rfq) => {
                  const statusConfig = getStatusConfig(rfq.status)
                  const StatusIcon = statusConfig.icon

                  return (
                    <div key={rfq.id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 hover:border-[#DE2910]/20">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                        {/* Left Section */}
                        <div className="flex-1 space-y-3">
                          <div className="flex items-start justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-10 h-10 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] rounded-lg flex items-center justify-center">
                                <FileText className="w-5 h-5 text-white" />
                              </div>
                              <div>
                                <h3 className="font-semibold text-gray-900 text-lg">{rfq.model}</h3>
                                <p className="text-sm text-gray-500">RFQ ID: {rfq.id}</p>
                              </div>
                            </div>
                            <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${statusConfig.color}`}>
                              <StatusIcon className="w-4 h-4 mr-1" />
                              {statusConfig.label}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="flex items-center space-x-2">
                              <Package className="w-4 h-4 text-gray-400" />
                              <div>
                                <p className="text-xs text-gray-500">Brand</p>
                                <p className="text-sm font-medium text-gray-900">{rfq.brandName}</p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Package className="w-4 h-4 text-gray-400" />
                              <div>
                                <p className="text-xs text-gray-500">Quantity</p>
                                <p className="text-sm font-medium text-gray-900">{rfq.quantity.toLocaleString()}</p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Building className="w-4 h-4 text-gray-400" />
                              <div>
                                <p className="text-xs text-gray-500">Company</p>
                                <p className="text-sm font-medium text-gray-900">{rfq.companyName}</p>
                              </div>
                            </div>

                            <div className="flex items-center space-x-2">
                              <Calendar className="w-4 h-4 text-gray-400" />
                              <div>
                                <p className="text-xs text-gray-500">Date</p>
                                <p className="text-sm font-medium text-gray-900">{rfq.requestDate}</p>
                              </div>
                            </div>
                          </div>

                          <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                            <div className="flex items-center space-x-4">
                              <div className="flex items-center space-x-1">
                                <Mail className="w-4 h-4 text-gray-400" />
                                <span className="text-sm text-gray-600">{rfq.contactName}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Globe className="w-4 h-4 text-gray-400" />
                                <span className="text-sm text-gray-600">{rfq.country}</span>
                              </div>
                            </div>
                            <div className="text-xs text-gray-400">
                              {rfq.businessEmail}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No RFQ requests found</h3>
                <p className="text-gray-500">You haven't submitted any RFQ requests yet.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
} 