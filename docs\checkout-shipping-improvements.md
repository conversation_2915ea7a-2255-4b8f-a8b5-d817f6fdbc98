# Checkout页面运费计算改进

## 概述

本次更新对checkout页面的运费计算功能进行了两个重要改进：
1. 使用真实的产品重量数据进行运费计算
2. 添加重试机制，提高运费API调用的可靠性

## 修改详情

### 1. 真实重量数据集成

#### 修改的文件
- `src/services/api.ts`
- `src/app/checkout/page.tsx`

#### 具体改进
- **CartItem接口扩展**: 添加了`weight?: number`字段来存储产品重量（单位：克）
- **数据库查询优化**: 修改`getCartItems`函数，通过JOIN查询从`products202503`表获取产品重量
- **重量计算逻辑**: 替换固定的0.1g重量，使用真实产品重量数据

#### 重量计算逻辑
```typescript
// 旧逻辑：固定重量
const estimatedWeight = items.reduce((total, item) => {
  return total + (item.cart_quantity * 0.1);
}, 0);

// 新逻辑：真实重量
const estimatedWeight = items.reduce((total, item) => {
  const itemWeight = item.weight || 0.1; // 如果没有重量数据，使用默认0.1g
  return total + (item.cart_quantity * itemWeight);
}, 0);
```

### 2. 重试机制实现

#### 新增功能
- **重试辅助函数**: 创建了`retryWithDelay`函数，支持可配置的重试次数和延迟
- **API调用重试**: 为`getLCSCExpressOptions`和`calculateShippingFee`函数添加重试机制
- **重试配置**: 最多重试3次，不使用指数退避策略（延迟为0）

#### 重试机制特点
```typescript
const retryWithDelay = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 0
): Promise<T> => {
  // 重试逻辑实现
  // - 最多重试3次
  // - 不使用延迟（delay = 0）
  // - 记录每次重试的错误信息
};
```

### 3. 日志和调试改进

#### 增强的日志记录
- 添加了详细的重量计算日志，包括每个商品的重量详情
- 记录重试过程中的错误信息
- 提供更详细的API调用参数日志

#### 示例日志输出
```javascript
console.log('📊 Weight calculation details:', {
  items: items.map(item => ({
    product_id: item.product_id,
    model: item.model_number,
    quantity: item.cart_quantity,
    weight: item.weight || 0.1,
    totalWeight: item.cart_quantity * (item.weight || 0.1)
  })),
  totalWeight: estimatedWeight
});
```

## 技术实现细节

### 数据库查询优化

修改了购物车数据获取的JOIN查询：

```sql
-- 新增的JOIN查询
SELECT 
  c.*, 
  s.stocks as available_stock,
  p.weight
FROM cart c
LEFT JOIN stocks s ON c.product_id = s.code
LEFT JOIN products202503 p ON c.product_id = p.product_id
WHERE c.user_id = ?
```

### 错误处理改进

- **渐进式降级**: 如果产品没有重量数据，使用默认值0.1g
- **重试失败处理**: 重试3次后仍失败，抛出详细的错误信息
- **用户友好的错误消息**: 为用户提供清晰的错误提示

## 兼容性考虑

### 向后兼容
- 如果产品表中没有重量数据，系统会自动使用默认重量0.1g
- 现有的购物车数据不受影响
- API接口保持向后兼容

### 数据完整性
- 重量字段为可选字段，不会影响现有功能
- 系统会优雅处理缺失的重量数据

## 性能影响

### 正面影响
- 更准确的运费计算
- 提高API调用成功率（通过重试机制）
- 减少用户因运费计算失败而流失

### 潜在影响
- 数据库查询增加一个JOIN操作（性能影响微小）
- 重试机制可能增加API调用时间（但提高了成功率）

## 测试验证

### 重试机制测试
已通过单元测试验证重试机制的正确性：
- ✅ 成功情况：第一次调用成功
- ✅ 失败后成功：第二次调用成功
- ✅ 全部失败：3次重试后抛出错误

### 重量计算测试
- 验证真实重量数据的正确获取
- 验证默认重量的降级处理
- 验证重量计算的准确性

## 后续改进建议

1. **重量数据完善**: 确保所有产品都有准确的重量数据
2. **缓存机制**: 考虑为运费计算结果添加短期缓存
3. **监控告警**: 添加运费API调用失败的监控和告警
4. **A/B测试**: 对比新旧运费计算的准确性和用户体验

## 部署注意事项

1. 确保数据库中的`products202503`表包含`weight`字段
2. 验证运费API的稳定性和响应时间
3. 监控checkout页面的错误率和转化率
4. 准备回滚方案（如果需要）

## 相关文件

- `src/services/api.ts` - API服务层修改
- `src/app/checkout/page.tsx` - Checkout页面修改
- `docs/购物车和订单功能缺失项.md` - 相关文档更新
