export default function SearchFilters() {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">筛选</h3>
      
      {/* 制造商筛选 */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-900 mb-2">制造商</h4>
        <div className="space-y-2">
          {['Quectel', 'STMicroelectronics', 'Texas Instruments'].map((manufacturer) => (
            <label key={manufacturer} className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-[#DE2910] rounded border-gray-300 
                         focus:ring-[#DE2910]"
              />
              <span className="ml-2 text-sm text-gray-600">{manufacturer}</span>
            </label>
          ))}
        </div>
      </div>

      {/* 价格范围筛选 */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-900 mb-2">价格范围</h4>
        <div className="flex gap-2">
          <input
            type="number"
            placeholder="最小"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md"
          />
          <span className="text-gray-500">-</span>
          <input
            type="number"
            placeholder="最大"
            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md"
          />
        </div>
      </div>

      {/* 库存状态筛选 */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-2">库存状态</h4>
        <div className="space-y-2">
          {['现货', '期货', '停产'].map((status) => (
            <label key={status} className="flex items-center">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-[#DE2910] rounded border-gray-300 
                         focus:ring-[#DE2910]"
              />
              <span className="ml-2 text-sm text-gray-600">{status}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  )
} 