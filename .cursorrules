已开发接口：
1、登录并获取token接口：https://auth.962692556.workers.dev/login
使用说明：
fetch('https://auth.962692556.workers.dev/login', {
    method: 'POST',
    headers: { 
        'Content-Type': 'application/json',
        'Origin': window.location.origin
    },
    body: JSON.stringify({ 
        username: username,
        password: password 
    })
})
返回示例：
{
    "success": true,
    "message": "Login successful",
    "data": {
        "token": "eyJhbG.....0DD7rmxi8s_Y0"
    }
}

2、登出并删除token接口：https://auth.962692556.workers.dev/logout
使用说明：
fetch('https://auth.962692556.workers.dev/logout', {
    method: 'POST',
    headers: { 
        'Content-Type': 'application/json',
        'Origin': window.location.origin
    },
    body: JSON.stringify({ 
        username: username,
        password: password 
    })
})
返回示例：
{
    "success": true,
    "message": 'Logout successful'
}


3、验证token鉴权接口：https://verify.962692556.workers.dev/
使用说明：
fetch('https://auth.962692556.workers.dev/verify', {
    headers: {
        'Authorization': 'Bearer ' + token,
        'Accept': 'application/json',
        'Origin': window.location.origin
    }
})
返回示例：
{"status":200,"verify":1} # 1：验证通过；0：验证不通过
