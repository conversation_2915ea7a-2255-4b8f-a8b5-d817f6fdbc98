# Get Express API - Cloudflare Worker

这个 Cloudflare Worker 脚本用于调用 LCSC 快递API获取国际运费报价。

## 部署步骤

### 1. 创建 Cloudflare Worker

```bash
# 使用 Wrangler CLI 创建新的 Worker
npx wrangler init get-express-worker

# 将 get-express.js 的内容复制到 src/index.js
```

### 2. 配置 wrangler.toml

```toml
name = "get-express-api"
main = "src/index.js"
compatibility_date = "2024-06-27"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "get-express-api"
```

### 3. 部署到 Cloudflare

```bash
# 部署到 Cloudflare
npx wrangler deploy

# 部署后会得到类似这样的URL:
# https://get-express-api.your-subdomain.workers.dev
```

## API 使用方法

### 请求格式

**URL**: `https://your-worker-domain.workers.dev/api/express`
**方法**: `POST`
**Content-Type**: `application/json`

### 请求参数

#### 必需参数
- `countryCode` (string): 国家代码，如 "DE", "US", "GB"
- `weight` (number): 包裹重量（克）
- `orderAmt` (number): 订单金额

#### 可选参数
- `province` (string): 省份/州，默认为空
- `city` (string): 城市，默认为空
- `postCode` (string): 邮政编码，默认为空
- `currencyCode` (string): 货币代码，默认为 "USD"
- `orderItemTotal` (number): 订单商品总数，默认为 1
- `vat` (boolean): 是否包含增值税，默认为 false
- `isFrom` (boolean): 是否来源标识，默认为 true
- `isHasBuzzer` (boolean): 是否包含蜂鸣器，默认为 false
- `isIncludeSpecialCategory` (boolean): 是否包含特殊类别，默认为 false

### 请求示例

#### JavaScript/Fetch
```javascript
const response = await fetch('https://your-worker-domain.workers.dev/api/express', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    countryCode: "DE",
    province: "Bayern",
    city: "Munich",
    postCode: "80331",
    currencyCode: "USD",
    weight: 8,
    orderAmt: 0.69,
    orderItemTotal: 2,
    vat: false
  })
});

const data = await response.json();
console.log(data);
```

#### cURL
```bash
curl --location 'https://your-worker-domain.workers.dev/api/express' \
--header 'Content-Type: application/json' \
--data '{
  "countryCode": "DE",
  "province": "Bayern", 
  "city": "Munich",
  "postCode": "80331",
  "currencyCode": "USD",
  "weight": 8,
  "orderAmt": 0.69,
  "orderItemTotal": 2,
  "vat": false
}'
```

#### Python
```python
import requests
import json

url = "https://your-worker-domain.workers.dev/api/express"
payload = {
    "countryCode": "DE",
    "province": "Bayern",
    "city": "Munich", 
    "postCode": "80331",
    "currencyCode": "USD",
    "weight": 8,
    "orderAmt": 0.69,
    "orderItemTotal": 2,
    "vat": False
}

response = requests.post(url, json=payload)
data = response.json()
print(json.dumps(data, indent=2))
```

### 响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    // LCSC API 返回的快递数据
    "expressOptions": [
      {
        "name": "DHL Express",
        "price": 25.50,
        "currency": "USD",
        "deliveryTime": "3-5 business days"
      }
    ]
  },
  "requestData": {
    "countryCode": "DE",
    "province": "Bayern",
    "city": "Munich",
    "postCode": "80331",
    "currencyCode": "USD",
    "weight": 8,
    "orderAmt": 0.69,
    "orderItemTotal": 2,
    "vat": false,
    "isFrom": true,
    "isHasBuzzer": false,
    "isIncludeSpecialCategory": false
  },
  "timestamp": "2025-06-28T10:30:00.000Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": "Missing required fields",
  "missingFields": ["countryCode", "weight"],
  "message": "Please provide: countryCode, weight, orderAmt",
  "timestamp": "2025-06-28T10:30:00.000Z"
}
```

## 常见国家代码

| 国家 | 代码 | 国家 | 代码 |
|------|------|------|------|
| 美国 | US | 德国 | DE |
| 英国 | GB | 法国 | FR |
| 日本 | JP | 加拿大 | CA |
| 澳大利亚 | AU | 意大利 | IT |
| 西班牙 | ES | 荷兰 | NL |
| 瑞士 | CH | 瑞典 | SE |
| 挪威 | NO | 丹麦 | DK |

## 错误处理

Worker 会处理以下错误情况：
1. 缺少必需参数
2. LCSC API 调用失败
3. 网络连接问题
4. 数据格式错误

## 注意事项

1. **Cookie 有效性**: 脚本中使用的 Cookie 可能会过期，需要定期更新
2. **速率限制**: LCSC API 可能有调用频率限制
3. **CORS**: Worker 已配置支持跨域请求
4. **缓存**: 响应设置为不缓存，确保获取最新运费信息

## 监控和日志

可以在 Cloudflare Dashboard 中查看：
- Worker 调用次数
- 错误日志
- 性能指标
- 请求分析

## 安全考虑

1. 考虑添加 API Key 验证
2. 实现请求频率限制
3. 添加 IP 白名单（如果需要）
4. 监控异常调用模式
