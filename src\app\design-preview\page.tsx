/**
 * Design Preview Page
 * 展示新的现代化设计风格
 */

export default function DesignPreview() {
  return (
    <div className="min-h-screen">
      {/* 现代化背景 */}
      <div className="fixed inset-0 z-[-2]">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50" />
        <div className="absolute inset-0" style={{ 
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(168, 85, 247, 0.05) 0%, transparent 50%)`,
          backgroundSize: '1000px 1000px'
        }} />
      </div>

      {/* 页面内容 */}
      <div className="relative">
        {/* 标题区域 */}
        <section className="modern-section">
          <div className="modern-container text-center">
            <h1 className="modern-title">
              现代化设计预览
            </h1>
            <p className="modern-subtitle">
              参考 Brator 汽车配件网站的现代化设计风格，为 China Electron 首页带来全新的视觉体验
            </p>
          </div>
        </section>

        {/* 卡片展示区域 */}
        <section className="modern-section">
          <div className="modern-container">
            <div className="modern-grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
              
              {/* 现代化卡片示例 1 */}
              <div className="modern-card animate-fadeInUp">
                <div className="mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">现代化设计</h3>
                  <p className="text-gray-600">采用现代化的渐变背景、卡片设计和动画效果</p>
                </div>
                <button className="btn-modern-primary w-full">
                  了解更多
                </button>
              </div>

              {/* 现代化卡片示例 2 */}
              <div className="modern-card animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
                <div className="mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">响应式布局</h3>
                  <p className="text-gray-600">完全响应式设计，在所有设备上都有完美的显示效果</p>
                </div>
                <button className="btn-modern-secondary w-full">
                  查看详情
                </button>
              </div>

              {/* 现代化卡片示例 3 */}
              <div className="modern-card animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
                <div className="mb-6">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-xl flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">用户体验</h3>
                  <p className="text-gray-600">流畅的动画效果和直观的交互设计提升用户体验</p>
                </div>
                <button className="btn-modern-primary w-full">
                  立即体验
                </button>
              </div>

            </div>
          </div>
        </section>

        {/* 特性展示区域 */}
        <section className="modern-section bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white">
          <div className="modern-container text-center">
            <h2 className="text-4xl font-bold mb-6 text-white">
              设计特性
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mt-12">
              
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎨</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">现代化视觉</h3>
                <p className="text-white/80">渐变色彩和现代化设计元素</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">流畅动画</h3>
                <p className="text-white/80">精心设计的动画和过渡效果</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">📱</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">响应式设计</h3>
                <p className="text-white/80">适配所有设备和屏幕尺寸</p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">性能优化</h3>
                <p className="text-white/80">优化的加载速度和用户体验</p>
              </div>

            </div>
          </div>
        </section>

        {/* 返回首页按钮 */}
        <section className="modern-section">
          <div className="modern-container text-center">
            <a href="/" className="btn-modern-primary inline-block">
              返回首页查看效果
            </a>
          </div>
        </section>

      </div>
    </div>
  )
}
