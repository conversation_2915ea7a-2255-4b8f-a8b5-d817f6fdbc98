/**
 * Spotlight Page
 * 
 * Displays latest articles and spotlight content
 * This page is server-side rendered for better SEO performance
 */

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import CategoryFilter from '@/components/Spotlight/CategoryFilter'
import { fetchBlogArticles } from '@/services/api'
import { BlogArticle } from '@/services/api'

// 安全的alt文本处理，确保只含ASCII字符
function safeAltText(text: string): string {
  // 移除非ASCII字符，确保客户端和服务器端渲染一致
  return text.replace(/[^\x00-\x7F]/g, '') || 'Image';
}

// 将标题转换为URL友好的格式
function slugifyTitle(title: string): string {
  // 将所有符号和空格替换为连字符，并将连续的连字符替换为单个连字符
  return title
    .replace(/[^\w\s]/g, '-') // 将所有非字母数字字符替换为连字符
    .replace(/\s+/g, '-')     // 将空格替换为连字符
    .replace(/-+/g, '-')      // 将连续的连字符替换为单个连字符
    .replace(/^-|-$/g, '')    // 移除开头和结尾的连字符
    .toLowerCase();           // 转为小写
}

// 将API的文章数据转换为UI所需格式
function transformArticleData(article: BlogArticle) {
  return {
    id: article.id,
    title: article.title,
    excerpt: article.description,
    image: article.cover_image,
    author: {
      name: article.author && article.author.name ? article.author.name : 'Unknown Author',
      avatar: article.author && article.author.avatar ? article.author.avatar : '',
      bio: article.author && article.author.bio ? article.author.bio : '',
      description: article.author && article.author.description ? article.author.description : ''
    },
    date: new Date(article.created_at).toISOString().split('T')[0],
    tags: article.tags ? article.tags.split(',') : [],
    category: article.category
  }
}

// 获取spotlight文章，支持过滤
async function getSpotlightArticles(searchParams?: { category?: string; page?: string }) {
  try {
    const page = searchParams?.page ? parseInt(searchParams.page) : 1
    const limit = 9 // 每页显示9篇文章
    
    // 转换category格式以匹配API
    let category = searchParams?.category
    if (category && category !== 'all') {
      // 将category从URL格式转换回API格式 (例如: 'industry-insights' -> 'Industry Insights')
      category = category
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
        .replace('And', '&')
    } else {
      category = undefined
    }
    
    try {
      // 从API获取文章
      const response = await fetchBlogArticles(page, limit, category)
      
      // 转换数据格式
      return {
        articles: response.data.map(transformArticleData),
        pagination: response.pagination
      }
    } catch (apiError) {
      console.error('API request failed:', apiError)
      // 返回空数据而不是抛出错误，确保页面能够渲染
      return { 
        articles: [], 
        pagination: { 
          total: 0, 
          page: page, 
          limit: limit, 
          pages: 0 
        },
        error: apiError instanceof Error ? apiError.message : 'Failed to fetch articles'
      }
    }
  } catch (error) {
    console.error('Error in getSpotlightArticles:', error)
    // 返回空数据而不是抛出错误，确保页面能够渲染
    return { 
      articles: [], 
      pagination: { 
        total: 0, 
        page: 1, 
        limit: 9, 
        pages: 0 
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export default async function SpotlightPage(props: {
  searchParams: Promise<{ category?: string; page?: string } | undefined>
}) {
  // 获取异步searchParams
  const searchParams = await props.searchParams;
  
  // 获取文章数据
  const { articles, pagination, error } = await getSpotlightArticles(searchParams);
  
  // 如果有文章，第一篇作为特色文章
  const featuredArticle = articles.length > 0 ? articles[0] : null
  
  // 其余文章
  const regularArticles = articles.slice(1)
  
  // 检查是否在过滤
  const isFiltering = !!(searchParams?.category && searchParams.category !== 'all')
  
  // 当前页码
  const currentPage = searchParams?.page ? parseInt(searchParams.page) : 1
  
  return (
    <div className="min-h-screen bg-white">
      {/* Banner with overlay text */}
      <div className="relative h-[300px] overflow-hidden bg-blue-900">
        {/* Background image - in production, would use a real image */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-800 to-blue-600">
          {/* Dynamic pattern overlay */}
          <div className="absolute inset-0" style={{ 
            backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
        
        {/* Spotlight text */}
        <div className="absolute inset-0 flex flex-col justify-center px-14">
          <div className="max-w-4xl">
            <h1 className="text-4xl font-bold text-white mb-4">Highlights Spotlight</h1>
            <p className="text-xl text-white/80">
              Everything You Need on Supply Chain Strategies, Risks & Tech!
            </p>
          </div>
        </div>
      </div>

      <div className="mx-auto px-14 py-12">
        {/* Category filter */}
        <CategoryFilter />
        
        {/* Error message */}
        {error && (
          <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <h3 className="text-lg font-medium text-red-800">Error loading articles</h3>
            </div>
            <p className="mt-2 text-sm text-red-700">{error}</p>
            <p className="mt-1 text-sm text-red-700">Showing cached or placeholder content. Please try again later.</p>
          </div>
        )}
        
        {/* Search results heading */}
        {isFiltering && (
          <div className="mb-8">
            <h2 className="text-3xl font-bold">
              {articles.length === 0 
                ? 'No results found' 
                : `Category Results (${pagination.total})`}
            </h2>
            <p className="text-gray-600 mt-2">
              {searchParams?.category && searchParams.category !== 'all' && `Category: ${searchParams.category.replace(/-/g, ' ')}`}
            </p>
          </div>
        )}
        
        {/* Featured article - larger layout */}
        {featuredArticle && !isFiltering && (
          <div className="mb-16">
            <div className="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden">
              <div className="flex flex-col md:flex-row">
                {/* Left: Image */}
                <div className="md:w-1/2 h-[300px] md:h-auto relative bg-gray-200">
                  {/* Article image */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Image 
                      src={featuredArticle.image || `https://dummyimage.com/800x600/333/fff.jpg&text=${encodeURIComponent('Featured Content')}`}
                      alt={safeAltText(featuredArticle.title)}
                      width={800}
                      height={600}
                      className="w-full h-full object-cover"
                      unoptimized
                      priority
                    />
                  </div>
                </div>
                
                {/* Right: Content */}
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center mb-5">
                    {/* Author avatar */}
                    <div className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                      {featuredArticle.author.avatar ? (
                        <Image 
                          src={featuredArticle.author.avatar}
                          alt={safeAltText(featuredArticle.author.name) || "Author"}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                          unoptimized
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
                          {featuredArticle.author.name && featuredArticle.author.name.charAt(0) || "A"}
                        </div>
                      )}
                    </div>
                    <div className="ml-3">
                      <div className="text-gray-700 font-medium">{featuredArticle.author.name || 'Unknown Author'}</div>
                      <div className="text-gray-500 text-sm">{featuredArticle.date}</div>
                    </div>
                  </div>
                  
                  <Link href={`/spotlight/${encodeURIComponent(slugifyTitle(featuredArticle.title))}-${featuredArticle.id}`} className="block group">
                    <h2 className="text-2xl font-bold mb-4 text-gray-900 group-hover:text-blue-600 transition-colors">
                      {featuredArticle.title}
                    </h2>
                  </Link>
                  
                  <p className="text-gray-600 mb-6">
                    {featuredArticle.excerpt}
                  </p>
                  
                  <div className="flex flex-wrap gap-2">
                    {featuredArticle.tags.slice(0, 5).map((tag, index) => (
                      <span key={index} className="inline-block bg-gray-100 text-gray-800 text-xs px-3 py-1 rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Latest heading - only show if not filtering */}
        {!isFiltering && <h2 className="text-3xl font-bold mb-8">Latest</h2>}
        
        {/* No results message */}
        {articles.length === 0 && (
          <div className="py-16 text-center">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-xl font-bold text-gray-700 mb-2">No articles found</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              We couldn't find any articles matching your criteria. Try adjusting your search or browse all articles.
            </p>
            <div className="mt-6">
              <Link 
                key="view-all-articles"
                href="/spotlight" 
                className="inline-block px-5 py-2.5 bg-blue-600 text-white font-medium rounded hover:bg-blue-700 transition-colors"
              >
                View All Articles
              </Link>
            </div>
          </div>
        )}
        
        {/* Grid of regular articles */}
        {regularArticles.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {isFiltering ? 
              // If filtering, show all matching articles in the grid
              articles.map((article) => (
                <div key={article.id} className="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden">
                  {/* Article image */}
                  <div className="h-48 relative bg-gray-200">
                    <Image 
                      src={article.image || `https://dummyimage.com/600x400/333/fff.jpg&text=${encodeURIComponent('Article Image')}`}
                      alt={safeAltText(article.title)}
                      width={600}
                      height={400}
                      className="w-full h-full object-cover"
                      unoptimized
                    />
                  </div>
                  
                  {/* Article content */}
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      {/* Author avatar */}
                      <div className="w-8 h-8 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                        {article.author.avatar ? (
                          <Image 
                            src={article.author.avatar}
                            alt={safeAltText(article.author.name) || "Author"}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                            unoptimized
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
                            {article.author.name && article.author.name.charAt(0) || "A"}
                          </div>
                        )}
                      </div>
                      <div className="ml-2">
                        <div className="text-gray-700 text-sm font-medium">{article.author.name || 'Unknown Author'}</div>
                        <div className="text-gray-500 text-xs">{article.date}</div>
                      </div>
                    </div>
                    
                    <Link href={`/spotlight/${encodeURIComponent(slugifyTitle(article.title))}-${article.id}`} className="block group">
                      <h3 className="text-xl font-bold mb-3 text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {article.title}
                      </h3>
                    </Link>
                    
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {article.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))
              :
              // If not filtering, show only the regular articles (exclude the featured one)
              regularArticles.map((article) => (
                <div key={article.id} className="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden">
                  {/* Article image */}
                  <div className="h-48 relative bg-gray-200">
                    <Image 
                      src={article.image || `https://dummyimage.com/600x400/333/fff.jpg&text=${encodeURIComponent('Article Image')}`}
                      alt={safeAltText(article.title)}
                      width={600}
                      height={400}
                      className="w-full h-full object-cover"
                      unoptimized
                    />
                  </div>
                  
                  {/* Article content */}
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      {/* Author avatar */}
                      <div className="w-8 h-8 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                        {article.author.avatar ? (
                          <Image 
                            src={article.author.avatar}
                            alt={safeAltText(article.author.name) || "Author"}
                            width={32}
                            height={32}
                            className="w-full h-full object-cover"
                            unoptimized
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
                            {article.author.name && article.author.name.charAt(0) || "A"}
                          </div>
                        )}
                      </div>
                      <div className="ml-2">
                        <div className="text-gray-700 text-sm font-medium">{article.author.name || 'Unknown Author'}</div>
                        <div className="text-gray-500 text-xs">{article.date}</div>
                      </div>
                    </div>
                    
                    <Link href={`/spotlight/${encodeURIComponent(slugifyTitle(article.title))}-${article.id}`} className="block group">
                      <h3 className="text-xl font-bold mb-3 text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {article.title}
                      </h3>
                    </Link>
                    
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {article.tags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))
            }
          </div>
        )}
        
        {/* Show pagination */}
        {pagination.pages > 1 && (
          <div className="mt-10 flex justify-center">
            <div className="inline-flex rounded-md shadow-sm">
              {/* Previous button */}
              <Link 
                href={currentPage > 1 ? `/spotlight?page=${currentPage - 1}${searchParams?.category ? `&category=${searchParams.category}` : ''}` : '#'}
                className={`px-3 py-2 rounded-l-md border border-gray-300 bg-white ${currentPage > 1 ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-400 cursor-not-allowed'}`}
              >
                Previous
              </Link>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                // If there are more than 5 pages, show a window around the current page
                let pageNum = i + 1
                if (pagination.pages > 5) {
                  if (currentPage <= 3) {
                    // Near the start, show first 5 pages
                    pageNum = i + 1
                  } else if (currentPage >= pagination.pages - 2) {
                    // Near the end, show last 5 pages
                    pageNum = pagination.pages - 4 + i
                  } else {
                    // In the middle, show current page and 2 on each side
                    pageNum = currentPage - 2 + i
                  }
                }
                
                return (
                  <Link
                    key={pageNum}
                    href={`/spotlight?page=${pageNum}${searchParams?.category ? `&category=${searchParams.category}` : ''}`}
                    className={`px-3 py-2 border-t border-b border-gray-300 ${
                      currentPage === pageNum
                        ? 'bg-blue-600 text-white font-medium'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </Link>
                )
              })}
              
              {/* Next button */}
              <Link
                href={currentPage < pagination.pages ? `/spotlight?page=${currentPage + 1}${searchParams?.category ? `&category=${searchParams.category}` : ''}` : '#'}
                className={`px-3 py-2 rounded-r-md border border-gray-300 bg-white ${currentPage < pagination.pages ? 'text-gray-700 hover:bg-gray-50' : 'text-gray-400 cursor-not-allowed'}`}
              >
                Next
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 