import React from 'react';

interface LoadingSpinnerProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

/**
 * 加载中动画组件
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  className = '',
  size = 'md'
}) => {
  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4 border-2';
      case 'lg':
        return 'w-10 h-10 border-4';
      case 'md':
      default:
        return 'w-6 h-6 border-3';
    }
  };
  
  const sizeClass = getSizeClass();
  
  return (
    <div className={`inline-block ${className}`}>
      <div className={`${sizeClass} rounded-full border-gray-300 border-t-blue-600 animate-spin`}></div>
    </div>
  );
};

export default LoadingSpinner; 