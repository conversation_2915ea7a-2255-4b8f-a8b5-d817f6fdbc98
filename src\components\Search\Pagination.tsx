export default function Pagination() {
  return (
    <div className="flex justify-center items-center space-x-2 py-8">
      <button className="px-4 py-2 text-sm text-gray-500 bg-white rounded-md border border-gray-300
                       hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
        上一页
      </button>
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((page) => (
          <button
            key={page}
            className={`px-4 py-2 text-sm rounded-md border
                      ${page === 1 
                        ? 'bg-[#DE2910] text-white border-[#DE2910]' 
                        : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50'
                      }`}
          >
            {page}
          </button>
        ))}
      </div>
      <button className="px-4 py-2 text-sm text-gray-500 bg-white rounded-md border border-gray-300
                       hover:bg-gray-50">
        下一页
      </button>
    </div>
  )
} 