# 为所有页面添加 Canonical URL 指南

本文档提供了如何为网站所有页面正确添加 Canonical URL 的指南。Canonical URL 是一种重要的 SEO 优化技术，有助于防止内容重复问题，并向搜索引擎明确指示页面的首选版本。

## 已完成的实现

我们已经完成了以下工作：

1. 创建了 `src/utils/canonicalUrl.ts` 工具函数，提供了 `getCanonicalUrl()` 函数用于生成规范链接
2. 更新了根布局文件 `src/app/layout.tsx`，为整个站点添加了基础的 canonical 链接
3. 为以下页面添加了特定的 canonical URL：
   - 首页 (`src/app/page.tsx`)
   - 制造商列表页 (`src/app/manufacturers/page.tsx`)
   - 制造商详情页 (`src/app/manufacturers/[id]/page.tsx`)
   - 产品详情页 (`src/app/product/[id]/page.tsx`)

## 实施步骤

为其余页面添加 canonical URL，请按照以下步骤操作：

1. 导入所需的工具函数和类型：
   ```tsx
   import { getCanonicalUrl } from '@/utils/canonicalUrl'
   import type { Metadata } from 'next'
   ```

2. 为静态页面添加 canonical URL：
   ```tsx
   export const generateMetadata = (): Metadata => {
     return {
       title: "页面标题",
       description: "页面描述...",
       // 其他元数据...
       alternates: {
         canonical: getCanonicalUrl('页面路径'),
       },
     };
   };
   ```

3. 为动态页面添加 canonical URL：
   ```tsx
   export async function generateMetadata(props: { params: Promise<{ [key: string]: string }> }): Promise<Metadata> {
     const params = await props.params;
     // 处理参数...
     
     return {
       title: "页面标题",
       description: "页面描述...",
       // 其他元数据...
       alternates: {
         canonical: getCanonicalUrl(`动态路径/${params.id}`),
       },
     };
   }
   ```

## 优先处理的页面

请优先为以下剩余的重要页面添加 canonical URL：

1. 博客列表页 (`src/app/blog/page.tsx`)
2. 博客文章页 (`src/app/blog/[id]/page.tsx`)
3. 分销商列表页 (`src/app/distributors/page.tsx`)
4. 分销商详情页 (`src/app/distributors/[id]/page.tsx`)
5. 搜索结果页 (`src/app/search/page.tsx`)
6. 各种静态内容页面 (如 About, Privacy Policy 等)

## 注意事项

1. **分页页面**：对于分页页面，通常建议将 canonical URL 指向第一页，除非每个分页都有完全独立的内容

2. **动态过滤/排序**：对于具有动态过滤或排序参数的页面，通常应将 canonical URL 指向无参数的基本页面

3. **多语言支持**：如果将来添加多语言支持，请确保在 `alternates` 对象中添加适当的 `languages` 配置

4. **定期检查**：使用工具定期检查网站的 canonical 链接是否正确实施

## 验证方法

实施后，可以通过以下方式验证 canonical URL 是否正确配置：

1. 使用浏览器开发者工具检查页面的 HTML 源代码，确认 `<link rel="canonical" href="...">` 标签是否存在
2. 使用 Google Search Console 检查页面的索引状态
3. 使用在线 SEO 工具验证 canonical 链接是否正确配置 