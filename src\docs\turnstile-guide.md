# Cloudflare Turnstile 验证码集成指南

本指南将帮助你理解和配置 Cloudflare Turnstile 验证码在项目中的集成。

## 什么是 Cloudflare Turnstile

Cloudflare Turnstile 是一种替代 CAPTCHA 的新一代验证系统，它可以有效防止机器人和自动化攻击，同时提供更好的用户体验。Turnstile 利用多种技术来确定访问者是人类还是机器人，包括密钥的私有通信渠道、交互分析以及 Cloudflare 的全球网络情报。

## 在项目中的实现

我们已经在以下位置实现了 Turnstile：

1. **登录页面** - 用户需要通过 Turnstile 验证才能登录
2. **API 路由** - `/api/verify-turnstile` 用于服务器端验证 Turnstile 令牌

## 配置步骤

### 1. 获取 Turnstile 密钥

1. 登录 [Cloudflare 仪表板](https://dash.cloudflare.com/)
2. 导航到 **Turnstile** 部分
3. 点击 **Add widget** 并填写你的网站信息
4. 选择验证码模式（推荐 "Managed" 模式，它会自动平衡安全和用户体验）
5. 复制生成的 Site Key 和 Secret Key

### 2. 配置环境变量

在项目根目录创建 `.env.local` 文件，并添加以下配置：

```
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_site_key
TURNSTILE_SECRET_KEY=your_secret_key
```

### 3. 使用 Turnstile 组件

我们的组件位于 `src/components/security/Turnstile.tsx`，可以在任何页面或表单中使用：

```jsx
import Turnstile from '@/components/security/Turnstile';
import { config } from '@/lib/config';

// 在组件内部
const [turnstileToken, setTurnstileToken] = useState(null);

// 验证码回调函数
const handleTurnstileVerify = (token) => {
  setTurnstileToken(token);
};

// 在表单中使用组件
<Turnstile
  sitekey={config.turnstile.siteKey}
  onVerify={handleTurnstileVerify}
  theme="auto"
  className="mt-4"
/>
```

### 4. 验证 Turnstile 令牌

在提交表单或执行操作前，应该先验证 Turnstile 令牌：

```javascript
// 客户端验证（通过我们的 API 路由）
const validateToken = async (token) => {
  const response = await fetch('/api/verify-turnstile', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ token }),
  });
  
  const data = await response.json();
  return data.success;
};

// 或者在提交表单时直接发送令牌给后端验证
const handleSubmit = async (e) => {
  e.preventDefault();
  
  if (!turnstileToken) {
    setError('请完成安全验证');
    return;
  }
  
  // 提交表单数据，包括 turnstileToken
  // ...
};
```

## 故障排除

### 验证码不显示

- 检查您的 Site Key 是否正确配置
- 确保域名在 Cloudflare Turnstile 仪表板中正确设置
- 检查浏览器控制台是否有错误信息

### 验证总是失败

- 确保 Secret Key 正确配置
- 检查服务器端的验证代码是否正确
- 验证 Turnstile 令牌的有效期（通常为 300 秒）

## 安全最佳实践

1. **不要在客户端直接验证令牌** - 始终在服务器端验证 Turnstile 令牌
2. **令牌单次使用** - 每个令牌只能验证一次，不能重复使用
3. **注意令牌过期** - Turnstile 令牌有 300 秒（5 分钟）的有效期
4. **保护你的 Secret Key** - 不要将 Secret Key 暴露在客户端代码中

## 参考资料

- [Cloudflare Turnstile 官方文档](https://developers.cloudflare.com/turnstile/)
- [Turnstile 客户端渲染](https://developers.cloudflare.com/turnstile/get-started/client-side-rendering/)
- [Turnstile 服务端验证](https://developers.cloudflare.com/turnstile/get-started/server-side-validation/) 