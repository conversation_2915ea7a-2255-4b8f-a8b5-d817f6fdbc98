import { generateBreadcrumbs } from '@/utils/breadcrumbs'
import ServerBreadcrumb from '@/components/Breadcrumb/ServerBreadcrumb'

export default async function TestBreadcrumbPage() {
  // 测试面包屑生成
  const breadcrumbs = await generateBreadcrumbs('/test-breadcrumb');

  return (
    <div className="min-h-screen bg-white">
      <ServerBreadcrumb breadcrumbs={breadcrumbs} />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-4">面包屑测试页面</h1>
        
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">生成的面包屑数据：</h2>
          <pre className="text-sm">
            {JSON.stringify(breadcrumbs, null, 2)}
          </pre>
        </div>
        
        <div className="mt-6">
          <p>这个页面用于测试服务端面包屑功能是否正常工作。</p>
          <p>如果你能看到上面的面包屑，说明功能正常。</p>
        </div>
      </div>
    </div>
  );
}
