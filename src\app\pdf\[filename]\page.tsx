'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import dynamic from 'next/dynamic'
import LoadingSpinner from '@/components/common/LoadingSpinner'

// 动态导入 PDFViewer 以减少编译负担
const PDFViewer = dynamic(() => import('@/components/PDFViewer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <LoadingSpinner />
    </div>
  )
})

export default function PDFPage() {
  const params = useParams()
  const [pdfUrl, setPdfUrl] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    try {
      // 获取token
      const token = localStorage.getItem('token')
      if (!token) {
        setError('无授权令牌')
        setLoading(false)
        return
      }

      // 直接构建PDF URL，不再通过服务器获取
      const filename = params.filename as string
      const directPdfUrl = `https://chinaelectron.com/datasheet/${encodeURIComponent(filename)}`
      
      // 设置PDF URL
      setPdfUrl(directPdfUrl)
      setLoading(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load PDF')
      setLoading(false)
    }
  }, [params.filename])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex justify-center items-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex justify-center items-center">
        <div className="text-red-500">{error}</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-screen-2xl mx-auto px-6 py-8">
        <div className="bg-white rounded-lg shadow-md">
          <PDFViewer url={pdfUrl} />
        </div>
      </div>
    </div>
  )
} 