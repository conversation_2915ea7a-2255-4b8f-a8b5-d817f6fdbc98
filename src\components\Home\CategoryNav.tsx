import Link from 'next/link'
import Image from 'next/image'

export default function CategoryNav() {
  const categories = [
    {
      title: 'Electronic Components',
      icon: 'https://dummyimage.com/48x48/0057b8/ffffff.png&text=EC',
      description: 'Search from millions of components'
    },
    {
      title: 'Technical Resources',
      icon: 'https://dummyimage.com/48x48/0057b8/ffffff.png&text=TR',
      description: 'Datasheets & CAD Models'
    },
    {
      title: 'Supply Chain Solutions',
      icon: 'https://dummyimage.com/48x48/0057b8/ffffff.png&text=SC',
      description: 'Inventory & Lead Time Data'
    },
    {
      title: 'Design Services',
      icon: 'https://dummyimage.com/48x48/0057b8/ffffff.png&text=DS',
      description: 'PCB & Engineering Support'
    }
  ]

  return (
    <section className="bg-white border-b border-[var(--neutral-200)]">
      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4">
          {categories.map((category, index) => (
            <Link 
              href="#" 
              key={index}
              className={`group flex items-center p-6 hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent
                         transition-all duration-300
                         ${index < categories.length - 1 ? 'border-r border-[var(--neutral-200)]' : ''}`}
            >
              <div className="flex-shrink-0 mr-4">
                <div className="w-12 h-12 rounded-lg bg-[var(--primary-50)] flex items-center justify-center
                              group-hover:scale-110 transition-transform duration-300
                              group-hover:shadow-[var(--shadow-glow)]">
                  <Image
                    src={category.icon}
                    alt={category.title}
                    width={24}
                    height={24}
                    className="text-[var(--primary-500)]"
                  />
                </div>
              </div>
              <div>
                <h3 className="text-sm font-semibold text-[var(--neutral-900)] group-hover:text-[var(--primary-500)]
                             transition-colors duration-300">
                  {category.title}
                </h3>
                <p className="text-xs text-[var(--neutral-700)] mt-1">
                  {category.description}
                </p>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
} 