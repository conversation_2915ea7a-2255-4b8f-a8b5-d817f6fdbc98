'use client'

import { useState } from 'react'

// 接口定义
interface SummarizerTableContent {
  key: string
  value: string[] | string
}

interface SummarizerTextContent {
  header?: string
  type: 'text'
  content: string[] | SummarizerItem[]
}

interface SummarizerTableItem {
  header: string
  type: 'table'
  content: SummarizerTableContent[]
}

type SummarizerItem = SummarizerTextContent | SummarizerTableItem

interface SummarizerTabsProps {
  items: SummarizerItem[]
}

export default function SummarizerTabs({ items }: SummarizerTabsProps) {
  const [activeTab, setActiveTab] = useState(0)

  if (!items || items.length === 0) {
    return null
  }

  // 渲染文本内容
  function renderTextContent(content: string[] | SummarizerItem[] | string, header?: string, parentHeader?: string) {
    if (!content) {
      return null;
    }

    // 如果header与parentHeader相同，不显示header
    const shouldShowHeader = header && header !== parentHeader;

    // 如果content不是数组，直接显示为文本
    if (!Array.isArray(content)) {
      return (
        <div className="mb-4">
          {shouldShowHeader && <h3 className="text-lg font-semibold mb-3">{header}</h3>}
          <p className="text-gray-700">{content}</p>
        </div>
      );
    }

    // 如果是空数组，返回null
    if (content.length === 0) {
      return null;
    }

    // 如果content是字符串数组，直接渲染
    if (typeof content[0] === 'string') {
      // 对于只有一个元素的数组，不使用列表，而是直接渲染为段落
      if (content.length === 1) {
        return (
          <div className="mb-4">
            {shouldShowHeader && <h3 className="text-lg font-semibold mb-3">{header}</h3>}
            <p className="text-gray-700">{content[0]}</p>
          </div>
        );
      }

      // 多个元素使用列表渲染
      return (
        <div className="mb-4">
          {shouldShowHeader && <h3 className="text-lg font-semibold mb-3">{header}</h3>}
          <ul className="list-disc pl-5 space-y-1">
            {(content as string[]).map((item, idx) => (
              <li key={idx} className="text-gray-700">{item}</li>
            ))}
          </ul>
        </div>
      );
    }

    // 如果content是对象数组，递归渲染
    return (
      <div className="mb-4">
        {shouldShowHeader && <h3 className="text-lg font-semibold mb-3">{header}</h3>}
        <div className="space-y-4 pl-4">
          {(content as SummarizerItem[]).map((item, idx) => (
            <div key={idx} className="border-l-2 border-gray-200 pl-4">
              {item.type === 'text' ? (
                renderTextContent(item.content, item.header, header)
              ) : (
                renderTableContent(item as SummarizerTableItem, header)
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 渲染表格内容
  function renderTableContent(item: SummarizerTableItem, parentHeader?: string) {
    if (!item.content || !Array.isArray(item.content) || item.content.length === 0) {
      return null;
    }

    // 如果item.header与parentHeader相同，不显示header
    const shouldShowHeader = item.header && item.header !== parentHeader;

    return (
      <div className="mb-4">
        {shouldShowHeader && <h3 className="text-lg font-semibold mb-3">{item.header}</h3>}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <tbody className="bg-white divide-y divide-gray-200">
              {item.content.map((row, idx) => (
                <tr key={idx}>
                  <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 bg-gray-50 w-1/4">
                    {row.key}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-700">
                    {Array.isArray(row.value) ? (
                      row.value.length === 1 ? (
                        <p>{row.value[0]}</p>
                      ) : (
                        <ul className="list-disc pl-5 space-y-1">
                          {row.value.map((val, valIdx) => (
                            <li key={valIdx}>{val}</li>
                          ))}
                        </ul>
                      )
                    ) : (
                      row.value
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  }



  return (
    <div className="w-full">
      {/* 标签导航 */}
      <div className="border-b border-gray-200">
        <div className="flex overflow-x-auto">
          {items.map((item, index) => {
            const isActive = activeTab === index

            return (
              <button
                key={index}
                onClick={() => setActiveTab(index)}
                className={`
                  flex-shrink-0 px-6 py-4 text-sm font-medium border-b-2 transition-colors
                  ${isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                {item.header || `Section ${index + 1}`}
              </button>
            )
          })}
        </div>
      </div>

      {/* 标签内容 */}
      <div className="p-6">
        {items.map((item, index) => (
          <div
            key={index}
            className={`${activeTab === index ? 'block' : 'hidden'}`}
          >
            {item.type === 'text' ? (
              renderTextContent(item.content, undefined, item.header)
            ) : (
              renderTableContent(item as SummarizerTableItem, item.header)
            )}
          </div>
        ))}
      </div>
    </div>
  )
}
