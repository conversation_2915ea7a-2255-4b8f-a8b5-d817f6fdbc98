# PDF解析 API文档
## 上传PDF创建解析任务
```
curl --request POST \
  --url http://*************:9875/api/process-url \
  --header 'content-type: application/json' \
  --data '{
  "url": "https://atta.szlcsc.com/upload/public/pdf/source/20180228/C97946_15198056954501377706.pdf"
}'
```
**响应示例**:
```json
{
	"success": true,
	"taskId": "460c79ea-e426-4a95-b3f5-aca608777581",
	"message": "PDF URL已提交，正在处理中"
}

```

**保留进任务过程的字段**：
```json
{
	"success": true,
	"taskId": "460c79ea-e426-4a95-b3f5-aca608777581",
	"message": "PDF URL已提交，正在处理中"
}
```



## 获取解析任务结果
```
curl --request GET \
  --url http://*************:9875/api/task-status/{taskId}
```
**响应示例**:
```json
{
	"id": "460c79ea-e426-4a95-b3f5-aca608777581",
	"url": "https://atta.szlcsc.com/upload/public/pdf/source/20180228/C97946_15198056954501377706.pdf",
	"status": "completed",
	"createdAt": "2025-04-10T15:06:21.328Z",
	"mineruId": "fdfc4c89-f186-4267-953c-e872679efb33",
	"fileUrl": "https://cdn-mineru.openxlab.org.cn/pdf/c31bc07a-c3de-413f-bef0-c67e4b43d2c3.zip",
	"filePath": "/www/wwwroot/pdf2md/data/pdf-extract/460c79ea-e426-4a95-b3f5-aca608777581/extracted/full.md",
	"extractDir": "/www/wwwroot/pdf2md/data/pdf-extract/460c79ea-e426-4a95-b3f5-aca608777581/extracted",
	"zipPath": "/www/wwwroot/pdf2md/data/pdf-extract/460c79ea-e426-4a95-b3f5-aca608777581/download.zip",
	"contentPreview": "# Chip Monolithic Ceramic Capacitor for General GRM31C5C1H104JA01_ (1206, C0G:EIA, 0.1uF, DC50V) _: packaging code  \n\n# 1.Scope  \n\nThis product specification is applied to Chip Monolithic Ceramic Capacitor used for General Electronic equipment.  \n\n# 2.MURATA Part NO. System  \n\n![](images/4e8b3fc9018e4f5bbb3563e0830296e4556dc18af9b3ca43db361bc07c453ea6.jpg)  \n\n# 3. Type & Dimensions  \n\n![](images/30635db3935a029fb1f61366b8ff8ca1f2ac83f1835853c3ff0a4893b070d028.jpg)  \n\n(Unit:mm)   \n\n\n<html><body><...",
	"updatedAt": "2025-04-10T15:06:28.826Z",
	"r2JsonName": "pdf-json/460c79ea-e426-4a95-b3f5-aca608777581-c31bc07a-c3de-413f-bef0-c67e4b43d2c3_content_list.json",
	"downloadUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581/download/markdown",
	"viewUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581",
	"zipDownloadUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581/download/zip",
	"jsonDownloadUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581/download/json",
	"r2JsonUrl": "https://164ebb2bf9333643b2c8232a628a286b.r2.cloudflarestorage.com(例如https://pub-xxx.r2.dev)/pdf-json/460c79ea-e426-4a95-b3f5-aca608777581-c31bc07a-c3de-413f-bef0-c67e4b43d2c3_content_list.json"
}
```

**保留进任务过程的字段**：
```json
{
  "id": "460c79ea-e426-4a95-b3f5-aca608777581",
  "url": "https://atta.szlcsc.com/upload/public/pdf/source/20180228/C97946_15198056954501377706.pdf",
  "status": "completed",
  "createdAt": "2025-04-10T15:06:21.328Z",
  "r2JsonName": "pdf-json/460c79ea-e426-4a95-b3f5-aca608777581-c31bc07a-c3de-413f-bef0-c67e4b43d2c3_content_list.json",
	"downloadUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581/download/markdown",
	"zipDownloadUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581/download/zip",
	"jsonDownloadUrl": "http://*************:9875/api/files/460c79ea-e426-4a95-b3f5-aca608777581/download/json",
}
```



## 提取简化r2JsonUrl的json文件的内容，并分析获得分类名称、分类对应的参数名和详细参数信息
```
curl -X POST https://extractjson.*********.workers.dev/ \
  -H "Content-Type: application/json" \
  -d '{"key": "path/to/file.json"}' //这个key是获取解析任务结果的响应示例里的r2JsonName
```
**响应示例**:
```json
{
    "data": [
        {
            "title": "ULTRAVOLTAASERIES HIGHVOLTAGEBIASINGSUPPLIES",
            "table_caption": "",
            "text": "The UltraVolt? AA series consists of high voltag......",
            "page_idx": 0
        },
        {
            "title": "PRODUCT HIGHLIGHTS",
            "table_caption": "",
            "text": "122%smallerthan standard A se.......LVD and RoHS)",
            "page_idx": 0
        },
    ],
    "category": "DC-DC电源芯片",
    "categoryParams": [
        "封装/规格",
        "品牌归属地",
        "功能类型",
    ],
    "parsedParams": {
        "languages": {
            "chinese": {
                "parameters": {
                    "main_parameters": {
                        "封装/规格": "1/16AA, 1/8AA, 1/4AA, 1/2AA, 1AA, 2AA, 4AA, 6AA",
                        "品牌归属地": "美国",
                        "功能类型": "高电压、微型PCB直插式、稳压DC-DC转换器",
                    },
                    "analysis": {
                        "key_features": [
                            "微型化封装（1/16AA至6AA）",
                        ],
                        "applications": [
                            "科研设备高压供电",
                        ],
                        "advantages": [
                            "体积仅为标准A系列1/8体积",
                        ]
                    }
                },
                "english": {
                    "parameters": {
                        "package/spec": "1/16AA, 1/8AA, 1/4AA, 1/2AA, 1AA, 2AA, 4AA, 6AA",
                        "brand origin": "United States",
                        "function type": "High-voltage, miniature PCB-mount, regulated DC-DC converter",
                    },
                    "analysis": {
                        "key_features": [
                            "Miniaturized packaging (1/16AA to 6AA)",
                        ],
                        "applications": [
                            "Research equipment power supply",
                        ],
                        "advantages": [
                            "1/8th the volume of standard A series",
                        ]
                    }
                },
                "russian": {
                    "parameters": {
                        "пакет/规格": "1/16AA, 1/8AA, 1/4AA, 1/2AA, 1AA, 2AA, 4AA, 6AA",
                        "производитель": "США",
                        "тип функции": "Высоковольтный миниатюрный DC-DC преобразователь",
                    },
                    "analysis": {
                        "ключевые особенности": [
                            "Миниатюрный корпус (1/16AA до 6AA)",
                        ],
                        "применение": [
                            "Исследовательское оборудование",
                        ],
                        "преимущества": [
                            "1/8-размер стандартной серии A",
                        ]
                    }
                }
            }
        }
    }
}
```
**保留进任务过程的字段**：
```json
{
	"category": "DC-DC电源芯片",
    "categoryParams": [
        "封装/规格",
        "品牌归属地",
        "功能类型",
    ],
    "parsedParams": {
        "languages": {
            "chinese": {
                "parameters": {
                    "main_parameters": {
                        "封装/规格": "1/16AA, 1/8AA, 1/4AA, 1/2AA, 1AA, 2AA, 4AA, 6AA",
                        "品牌归属地": "美国",
                        "功能类型": "高电压、微型PCB直插式、稳压DC-DC转换器",
                    },
                    "analysis": {
                        "key_features": [
                            "微型化封装（1/16AA至6AA）",
                        ],
                        "applications": [
                            "科研设备高压供电",
                        ],
                        "advantages": [
                            "体积仅为标准A系列1/8体积",
                        ]
                    }
                },
                "english": {
                    "parameters": {
                        "package/spec": "1/16AA, 1/8AA, 1/4AA, 1/2AA, 1AA, 2AA, 4AA, 6AA",
                        "brand origin": "United States",
                        "function type": "High-voltage, miniature PCB-mount, regulated DC-DC converter",
                    },
                    "analysis": {
                        "key_features": [
                            "Miniaturized packaging (1/16AA to 6AA)",
                        ],
                        "applications": [
                            "Research equipment power supply",
                        ],
                        "advantages": [
                            "1/8th the volume of standard A series",
                        ]
                    }
                },
                "russian": {
                    "parameters": {
                        "пакет/规格": "1/16AA, 1/8AA, 1/4AA, 1/2AA, 1AA, 2AA, 4AA, 6AA",
                        "производитель": "США",
                        "тип функции": "Высоковольтный миниатюрный DC-DC преобразователь",
                    },
                    "analysis": {
                        "ключевые особенности": [
                            "Миниатюрный корпус (1/16AA до 6AA)",
                        ],
                        "применение": [
                            "Исследовательское оборудование",
                        ],
                        "преимущества": [
                            "1/8-размер стандартной серии A",
                        ]
                    }
                }
            }
        }
    }
}
```


# Product API 文档

## 概述

这是一个基于Cloudflare Worker的API服务，提供了产品、类别和品牌相关的数据查询功能。API支持多语言（中文、英文和俄语）数据返回，并实现了完整的错误处理机制。

## 基础信息

### API 基础 URL

所有API请求都应该使用以下基础URL：

```
https://webapi.*********.workers.dev
```

### CORS 配置

API支持跨域请求，配置如下：

```javascript
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
};
```

### 错误处理

API实现了统一的错误处理机制，根据错误类型返回不同的HTTP状态码：

| 错误类型 | HTTP状态码 | 错误消息 |
|---------|-----------|--------|
| 资源未找到 | 404 | Resource not found |
| 数据库连接错误 | 503 | Database connection error |
| 参数错误 | 400 | 具体错误信息 |
| 其他错误 | 500 | Internal Server Error |

错误响应格式：

```json
{
  "error": "错误消息",
  "details": "详细错误信息"
}
```

## API 端点

### 1. 获取类别树

获取完整的类别层级结构。

- **URL**: `https://webapi.*********.workers.dev/categories/tree`
- **方法**: GET
- **参数**: 无

**响应示例**:

```json
[
  {
    "code": "category_code",
    "name_en": "Category Name",
    "name_cn": "类别名称",
    "name_ru": "Название категории",
    "children": [
      {
        "code": "subcategory_code",
        "name_en": "Subcategory Name",
        "name_cn": "子类别名称",
        "name_ru": "Название подкатегории",
        "children": []
      }
    ]
  }
]
```
**保留进任务过程的字段**：
```json
[
  {
    "code": "category_code",
    "name_en": "Category Name",
    "name_cn": "类别名称",
    "name_ru": "Название категории",
    "children": [
      {
        "code": "subcategory_code",
        "name_en": "Subcategory Name",
        "name_cn": "子类别名称",
        "name_ru": "Название подкатегории",
        "children": []
      }
    ]
  }
]
```

### 2. 获取类别详情

获取指定类别的详细信息。

- **URL**: `https://webapi.*********.workers.dev/categories/{code}`
- **方法**: GET
- **URL参数**: 
  - `code`: 类别代码

**响应示例**:

```json
{
  "code": "category_code",
  "parent_code": "parent_category_code",
  "level": 1,
  "name_cn": "类别名称",
  "name_en": "Category Name",
  "name_ru": "Название категории"
}
```
**保留进任务过程的字段**：
```json
{
  "code": "category_code",
  "parent_code": "parent_category_code",
  "level": 1,
  "name_cn": "类别名称",
  "name_en": "Category Name",
  "name_ru": "Название категории"
}
```
**错误响应**:

- 404: 类别不存在

### 3. 获取产品详情

获取指定产品的详细信息，包括品牌、价格、库存、参数等。

- **URL**: `https://webapi.*********.workers.dev/products/{productId}`
- **方法**: GET
- **URL参数**: 
  - `productId`: 产品ID

**响应示例**:

```json
{
  "product_id": "product_id",
  "model": "Model Name",
  "brand": {
    "id": "brand_id",
    "name_en": "Brand Name",
    "name_cn": "品牌名称",
    "name_ru": "Название бренда",
    "logo_url": "https://example.com/logo.png",
    "website": "https://brand-website.com"
  },
  "prices": [
    {
      "quantity": 1,
      "price": 10.5
    },
    {
      "quantity": 10,
      "price": 9.5
    }
  ],
  "stock": 100,
  "parameters": {
    "en": [
      {
        "param_name": "Parameter Name",
        "param_value": "Parameter Value"
      }
    ],
    "cn": [
      {
        "param_name": "参数名称",
        "param_value": "参数值"
      }
    ],
    "ru": [
      {
        "param_name": "Имя параметра",
        "param_value": "Значение параметра"
      }
    ]
  },
  "category_path": ["parent_category", "child_category"],
  "category_id": "CMC0039010005",
  "datasheet_url": "https://example.com/datasheet.pdf",
  "image_list": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
  "description": "Product description"
}
```

**保留进任务过程的字段**：
```json
{
  "product_id": "product_id",
  "model": "Model Name",
  "brand": {
    "id": "brand_id",
    "name_en": "Brand Name",
    "name_cn": "品牌名称",
    "name_ru": "Название бренда",
    "website": "https://brand-website.com"
  },
  "prices": [
    {
      "quantity": 1,
      "price": 10.5
    },
    {
      "quantity": 10,
      "price": 9.5
    }
  ],
  "stock": 100,
  "parameters": {
    "en": [
      {
        "param_name": "Parameter Name",
        "param_value": "Parameter Value"
      }
    ],
    "cn": [
      {
        "param_name": "参数名称",
        "param_value": "参数值"
      }
    ],
    "ru": [
      {
        "param_name": "Имя параметра",
        "param_value": "Значение параметра"
      }
    ]
  },
  "category_path": [
        "CMC0039",
        "CMC0039013",
        "CMC0039013004"
    ],
    "category_id": "CMC0039013004",
    "category_names": {
        "CMC0039": {
            "name_cn": "电子元件",
            "name_en": "electronic_components",
            "name_ru": "electronic_components"
        },
        "CMC0039013": {
            "name_cn": "音频器件",
            "name_en": "audio_devices",
            "name_ru": "audio_devices"
        },
        "CMC0039013004": {
            "name_cn": "蜂鸣器",
            "name_en": "Buzzer",
            "name_ru": "蜂鸣器"
        }
    },
  "datasheet_url": "https://example.com/datasheet.pdf",
}
```

**错误响应**:

- 404: 产品不存在

### 4. 搜索产品

根据类别和品牌搜索产品，支持分页。

- **URL**: `https://webapi.*********.workers.dev/products`
- **方法**: GET
- **查询参数**: 
  - `category` (可选): 类别代码
  - `brand` (可选): 品牌ID
  - `page` (可选): 页码，默认为1
  - `search` (可选): 搜索关键词，将在产品名称中搜索（支持中文、英文和俄语）

**响应示例**:

```json
{
  "total": 150,
  "page": 1,
  "keyword": "Wire-to-Board Pin Header",
  "results": [
    {
      "product_id": "product_id",
      "model": "Model Name",
      "brand_id": "brand_id",
      "brand": {
        "name_cn": "品牌名称",
        "name_en": "Brand Name",
        "name_ru": "Название бренда"
      },
      "price_key": "price_key",
      "stock_key": "stock_key",
      "datasheet_url": "https://example.com/datasheet.pdf",
      "image_list": ["https://example.com/image1.jpg"],
      "parameters_key": "parameters_key",
      "description": "Product description",
      "updated_at": "2023-01-01T00:00:00Z",
      "prices": [
        {
          "quantity": 1,
          "price": 10.5
        },
        {
          "quantity": 10,
          "price": 9.5
        }
      ],
      "stock": 100,
      "category_path": [
                "CMC0039",
                "CMC0039011",
                "CMC0039011003"
            ],
            "category_id": "CMC0039011003",
            "category_names": {
                "CMC0039": {
                    "name_cn": "电子元件",
                    "name_en": "electronic_components",
                    "name_ru": "electronic_components"
                },
                "CMC0039011": {
                    "name_cn": "数字器件",
                    "name_en": "digital_components",
                    "name_ru": "digital_components"
                },
                "CMC0039011003": {
                    "name_cn": "数字隔离器",
                    "name_en": "Digital Isolator",
                    "name_ru": "数字隔离器"
                }
            }
    }
  ]
}
```
**保留进任务过程的字段**：
```json
{
  "total": 150,
  "page": 1,
  "results": [
    {
      "product_id": "product_id",
      "model": "Model Name",
      "brand_id": "brand_id",
      "brand": {
        "name_cn": "品牌名称",
        "name_en": "Brand Name",
        "name_ru": "Название бренда"
      },
      "price_key": "price_key",
      "stock_key": "stock_key",
      "datasheet_url": "https://example.com/datasheet.pdf",
      "parameters_key": "parameters_key",
      "description": "Product description",
      "prices": [
        {
          "quantity": 1,
          "price": 10.5
        },
        {
          "quantity": 10,
          "price": 9.5
        }
      ],
      "stock": 100,
      "category_path": [
                "CMC0039",
                "CMC0039011",
                "CMC0039011003"
            ],
            "category_id": "CMC0039011003",
            "category_names": {
                "CMC0039": {
                    "name_cn": "电子元件",
                    "name_en": "electronic_components",
                    "name_ru": "electronic_components"
                },
                "CMC0039011": {
                    "name_cn": "数字器件",
                    "name_en": "digital_components",
                    "name_ru": "digital_components"
                },
                "CMC0039011003": {
                    "name_cn": "数字隔离器",
                    "name_en": "Digital Isolator",
                    "name_ru": "数字隔离器"
                }
            }
    }
  ]
}
```

# API文档更新 - 品牌列表接口

我需要更新API文档中的"获取品牌列表"部分，以反映新增的"domestic"字段功能。以下是更新后的文档内容：

### 5. 获取品牌列表

获取所有品牌或根据搜索条件筛选品牌。

- **URL**: `https://webapi.*********.workers.dev/brands`
- **方法**: GET
- **查询参数**:  
  - `search` (可选): 搜索关键词，将在品牌名称中搜索（支持中文、英文和俄语）
  - `domestic` (可选): 筛选品牌归属地，`true`表示中国品牌（数据库中存储为"chinese"），`false`表示国际品牌（数据库中存储为"international"）

**响应示例**:

```json
[
  {
    "id": "brand_id",
    "name_cn": "品牌名称",
    "name_en": "Brand Name",
    "name_ru": "Название бренда",
    "website": "https://brand-website.com",
    "logo_url": "https://example.com/logo.png",
    "domestic": "chinese"
  }
]
```

**保留进任务过程的字段**：
```json
[
  {
    "id": "brand_id",
    "name_cn": "品牌名称",
    "name_en": "Brand Name",
    "name_ru": "Название бренда",
    "website": "https://brand-website.com",
    "logo_url": "https://example.com/logo.png",
    "domestic": true
  }
]
```

**使用示例**:
- 获取所有品牌: `GET /brands`
- 获取所有中国品牌: `GET /brands?domestic=true`
- 获取所有国际品牌: `GET /brands?domestic=false`
- 搜索中国品牌: `GET /brands?search=关键词&domestic=true`

        
```
**保留进任务过程的字段**：
```json
[
  {
    "id": "brand_id",
    "name_cn": "品牌名称",
    "name_en": "Brand Name",
    "name_ru": "Название бренда",
    "website": "https://brand-website.com",
    "logo_url": "https://example.com/logo.png"
  }
]
```

### 6. 获取单个分销商

获取指定ID的分销商详细信息。

**请求方式**：GET

**URL**：`/distributors/{distributor_id}`

**参数**：
- `distributor_id`：分销商ID，路径参数

**示例请求**：
```bash
curl -X GET "https://webapi.*********.workers.dev/distributors/CMD1000001"
```

**成功响应**：
```json
{
  "distributor_id": "CMD1000001",
  "name_cn": "深圳市立嘉诚科技有限公司",
  "name_en": "Shenzhen Lijiacheng Technology Co., Ltd.",
  "name_ru": "Shenzhen Lijiacheng Technology Co., Ltd.",
  "category_id": "",
  "description_cn": "深圳市立嘉诚科技有限公司，诚信经营，只有原装，只做原装。",
  "description_en": "Shenzhen Lijiacheng Technology Co., Ltd., honest operation, only original, only original.",
  "description_ru": "Shenzhen Lijiacheng Technology Co., Ltd., honest operation, only original, only original.",
  "logo": "default.jpg",
  "img_list": [],
  "contact": {
    "电话": ["0755-83247658"],
    "手机": ["15307555560"],
    "QQ": ["1727880911"],
    "Email": ["<EMAIL>"],
    "地址": "华强北街道赛格电子市场2楼2715（只做原装，现货可看。有上传型号均为现货，可送货）"
  },
  "certification": ["诚信LV.2", "实体认证"],
  "address_cn": "华强北街道赛格电子市场2楼2715（只做原装，现货可看。有上传型号均为现货，可送货）",
  "address_en": "2/F, Seg Electronic Market, Huaqiangbei Street (Only original,现货can be viewed. Models uploaded are all in stock, can be delivered)",
  "address_ru": "2/F, Seg Electronic Market, Huaqiangbei Street (Only original,现货can be viewed. Models uploaded are all in stock, can be delivered)"
}
```

**错误响应**：
```json
{
  "error": "Resource not found",
  "details": "Distributor not found"
}
```

### 7. 批量获取分销商

批量获取多个分销商的详细信息。

**请求方式**：POST

**URL**：`/distributors/batch`

**请求体**：
```json
{
  "ids": ["CMD1000001", "CMD1000002", "CMD1000003"]
}
```

**示例请求**：
```bash
curl -X POST "https://webapi.*********.workers.dev/distributors/batch" \
  -H "Content-Type: application/json" \
  -d '{"ids": ["CMD1000001", "CMD1000002", "CMD1000003"]}'
```

**成功响应**：
```json
[
  {
    "distributor_id": "CMD1000001",
    "name_cn": "深圳市立嘉诚科技有限公司",
    "name_en": "Shenzhen Lijiacheng Technology Co., Ltd.",
    "name_ru": "Shenzhen Lijiacheng Technology Co., Ltd.",
    "category_id": "",
    "description_cn": "深圳市立嘉诚科技有限公司，诚信经营，只有原装，只做原装。",
    "description_en": "Shenzhen Lijiacheng Technology Co., Ltd., honest operation, only original, only original.",
    "description_ru": "Shenzhen Lijiacheng Technology Co., Ltd., honest operation, only original, only original.",
    "logo": "default.jpg",
    "img_list": [],
    "contact": {
      "电话": ["0755-83247658"],
      "手机": ["15307555560"],
      "QQ": ["1727880911"],
      "Email": ["<EMAIL>"],
      "地址": "华强北街道赛格电子市场2楼2715（只做原装，现货可看。有上传型号均为现货，可送货）"
    },
    "certification": ["诚信LV.2", "实体认证"],
    "address_cn": "华强北街道赛格电子市场2楼2715（只做原装，现货可看。有上传型号均为现货，可送货）",
    "address_en": "2/F, Seg Electronic Market, Huaqiangbei Street (Only original,现货can be viewed. Models uploaded are all in stock, can be delivered)",
    "address_ru": "2/F, Seg Electronic Market, Huaqiangbei Street (Only original,现货can be viewed. Models uploaded are all in stock, can be delivered)"
  },
  {
    "distributor_id": "CMD1000002",
    "name_cn": "深圳市福田区银鑫高科电子商行",
    "name_en": "Shenzhen Futian District Yin Xing Gao Ke Electronic Trade",
    "name_ru": "Shenzhen Futian District Yin Xing Gao Ke Electronic Trade",
    "category_id": "",
    "description_cn": "深圳银鑫高科电子创立于2003年，是台湾知名半导体品牌上市公司股东投的分销点...",
    "description_en": "Shenzhen Yin Xing Gao Ke Electronic was established in 2003...",
    "description_ru": "Shenzhen Yin Xing Gao Ke Electronic was established in 2003...",
    "logo": "default.jpg",
    "img_list": [],
    "contact": {
      "电话": ["+86-0755-82536613"],
      "手机": ["13570827735"],
      "QQ": ["602370927"],
      "Email": ["<EMAIL>"],
      "地址": "深圳市福田区华强北路赛格广场一楼1A082-1A083"
    },
    "certification": [],
    "address_cn": "深圳市福田区华强北路赛格广场一楼1A082-1A083",
    "address_en": "1A082-1A083, 1st Floor, Sai Ge Plaza, Huaqiangbei North Road, Futian District, Shenzhen",
    "address_ru": "1A082-1A083, 1st Floor, Sai Ge Plaza, Huaqiangbei North Road, Futian District, Shenzhen"
  },
  "..."
]
```

**错误响应**：
```json
{
  "error": "Invalid or empty distributor IDs",
  "details": "Invalid or empty distributor IDs"
}
```

### 8. 搜索分销商

根据关键词搜索分销商。

**请求方式**：GET

**URL**：`/distributors`

**参数**：
- search ：搜索关键词，可选
- page ：页码，默认为1，可选

**示例请求**：
```bash
curl -X GET "https://webapi.*********.workers.dev/distributors?search=深圳&page=2"
```

**成功响应**：
```json
{
  "total": 150,
  "page": 2,
  "limit": 30,
  "results": [
    {
      "distributor_id": "CMD1000031",
      "name_cn": "深圳市某某电子有限公司",
      "name_en": "Shenzhen XXX Electronics Co., Ltd.",
      "name_ru": "Shenzhen XXX Electronics Co., Ltd.",
      "logo": "default.jpg",
      "address_cn": "深圳市福田区某某路XX号",
      "address_en": "XX, XXX Road, Futian District, Shenzhen",
      "address_ru": "XX, XXX Road, Futian District, Shenzhen"
    },
    "..."
  ]
}
```

### 9. 获取分销商图片

获取分销商的图片。

**请求方式**：GET

**URL**：`/distributors/image/{image_name}`

**参数**：
- `image_name`：图片名称，路径参数

**示例请求**：
```bash
curl -X GET "https://webapi.*********.workers.dev/distributors/image/default.jpg"
```

**成功响应**：
直接返回图片数据，Content-Type 根据图片类型设置（如 image/jpeg, image/png 等）

**错误响应**：
```json
{
  "error": "Image not found",
  "details": "Image not found"
}
```

### 10. 通过品牌ID获取分销商

获取指定品牌ID的所有分销商信息。

**请求方式**：GET

**URL**：`/brand-distributors`

**参数**：
- `brand_id`：品牌ID，查询参数

**示例请求**：
```bash
curl -X GET "https://webapi.*********.workers.dev/brand-distributors?brand_id=BRD1000001"
```

**成功响应**：
```json
{
  "brand_id": "BRD1000001",
  "distributors": [
    {
      "distributor_id": "CMD1000001",
      "name_cn": "深圳市立嘉诚科技有限公司",
      "name_en": "Shenzhen Lijiacheng Technology Co., Ltd.",
      "name_ru": "Shenzhen Lijiacheng Technology Co., Ltd.",
      "category_id": "",
      "description_cn": "深圳市立嘉诚科技有限公司，诚信经营，只有原装，只做原装。",
      "description_en": "Shenzhen Lijiacheng Technology Co., Ltd., honest operation, only original, only original.",
      "description_ru": "Shenzhen Lijiacheng Technology Co., Ltd., honest operation, only original, only original.",
      "logo": "default.jpg",
      "img_list": [],
      "contact": {
        "电话": ["0755-83247658"],
        "手机": ["15307555560"],
        "QQ": ["1727880911"],
        "Email": ["<EMAIL>"],
        "地址": "华强北街道赛格电子市场2楼2715（只做原装，现货可看。有上传型号均为现货，可送货）"
      },
      "certification": ["诚信LV.2", "实体认证"],
      "address_cn": "华强北街道赛格电子市场2楼2715（只做原装，现货可看。有上传型号均为现货，可送货）",
      "address_en": "2/F, Seg Electronic Market, Huaqiangbei Street (Only original,现货can be viewed. Models uploaded are all in stock, can be delivered)",
      "address_ru": "2/F, Seg Electronic Market, Huaqiangbei Street (Only original,现货can be viewed. Models uploaded are all in stock, can be delivered)"
    },
    "..."
  ]
}
```

### 11. 通过分销商ID获取品牌

获取指定分销商ID的所有品牌信息。

**请求方式**：GET

**URL**：`/brand-distributors`

**参数**：
- `distributor_id`：分销商ID，查询参数

**示例请求**：
```bash
curl -X GET "https://webapi.*********.workers.dev/brand-distributors?distributor_id=CMD1000001"
```

**成功响应**：
```json
{
  "distributor_id": "CMD1000001",
  "brands": [
    {
      "id": "BRD1000001",
      "introduction_en": "Brand introduction in English",
      "name_cn": "品牌中文名称",
      "name_en": "Brand English Name",
      "website": "https://www.example.com",
      "name_ru": "Brand Russian Name",
      "introduction_ru": "Brand introduction in Russian",
      "logo_url": "brand_logo.jpg",
      "introduction_cn": "品牌中文介绍"
    },
    "..."
  ]
}
```

**错误响应**：
```json
{
  "error": "Missing required parameter: brand_id or distributor_id",
  "details": "Missing required parameter: brand_id or distributor_id"
}
```

### 注意事项

1. 所有API返回的JSON数据均使用UTF-8编码
2. 分销商图片可通过`/distributors/image/{image_name}`访问，其中`{image_name}`为分销商数据中的`logo`字段值
3. 品牌分销商映射API必须提供`brand_id`或`distributor_id`参数之一，否则会返回错误
4. 批量获取分销商API需要在请求体中提供分销商ID数组
5. 所有API均支持CORS，可以从任何域名发起请求

### 图片访问示例

分销商的logo图片可以通过以下URL访问：

```
https://webapi.*********.workers.dev/distributors/image/default.jpg
```

在前端可以这样使用：

```html
<img src="https://webapi.*********.workers.dev/distributors/image/default.jpg" alt="分销商Logo">
```











          
# 品牌详情 API 文档

## 接口信息

**接口URL**: `https://webapi.*********.workers.dev/brands/{id}`

**请求方法**: GET

**功能描述**: 根据品牌ID获取品牌的详细信息，包括名称、网站、介绍等多语言内容。

## 请求参数

| 参数名 | 类型 | 必填 | 描述 |
|-------|-----|------|------|
| id | string | 是 | 品牌ID，作为URL路径参数 |

## 响应格式

### 成功响应

当请求成功且找到相关品牌时，返回以下JSON格式数据：

```json
{
  "id": "1234",
  "name_cn": "意法半导体",
  "name_en": "STMicroelectronics",
  "name_ru": "СТМикроэлектроникс",
  "website": "https://www.st.com",
  "logo_url": "https://example.com/logos/st.png",
  "introduction_cn": "意法半导体是全球领先的半导体解决方案供应商...",
  "introduction_en": "STMicroelectronics is a global semiconductor leader...",
  "introduction_ru": "СТМикроэлектроникс является мировым лидером в области полупроводников..."
}
```

### 错误响应

当未找到指定ID的品牌时：

```json
{
  "error": "Brand not found",
  "details": "Brand not found"
}
```

当服务器内部错误时：

```json
{
  "error": "Internal Server Error",
  "details": "具体错误信息"
}
```

## 请求示例

### 使用 cURL

```bash
curl -X GET "https://webapi.*********.workers.dev/brands/1234"
```

### 使用 JavaScript (Fetch API)

```javascript
fetch("https://webapi.*********.workers.dev/brands/1234")
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('错误:', error));
```

## 字段说明

| 字段名 | 类型 | 描述 |
|-------|-----|------|
| id | string | 品牌唯一标识符 |
| name_cn | string | 品牌中文名称 |
| name_en | string | 品牌英文名称 |
| name_ru | string | 品牌俄文名称 |
| website | string | 品牌官方网站URL |
| logo_url | string | 品牌logo图片URL |
| introduction_cn | string | 品牌中文介绍 |
| introduction_en | string | 品牌英文介绍 |
| introduction_ru | string | 品牌俄文介绍 |

## 注意事项

1. 接口支持跨域请求（CORS）
2. 响应中的多语言字段（中文、英文和俄文）可能部分为空
3. 如果提供的品牌ID不存在，API将返回404错误


# 批量查询产品库是否有该型号
```
curl --location 'https://webapi.*********.workers.dev/products/models/check' \
--header 'Content-Type: application/json' \
--data '{
  "models": ["STM32F103C8T6", "ESP32-WROOM-32"],
  "product_ids": ["P12345", "P67890"]
}'

```

```json
响应示例：
{
    "ATMEGA644PA-AU": {
        "exists": false,
        "product_id": null,
        "brand_id": null,
        "brand_name_cn": null,
        "brand_name_en": null,
        "brand_name_ru": null,
        "domestic": null,
        "full":null
    },
    "CD4148WP": {
        "exists": true,
        "product_id": "CMA100590276",
        "brand_id": "CEB003110",
        "full": {
            "product_id": "CMA100590276",
            "model": "CD4148WP",
            "brand_id": "CEB003110",
            "brand": {
                "name_cn": "LIZ(丽智电子)",
                "name_en": "Liz Electronic",
                "name_ru": "Лиз Электроник",
                "logo_url": "https://brandimg.*********.workers.dev/1516867087889.jpg",
                "website": "http://www.lizgroup.com/"
            },
            "price_key": "CMA100590276",
            "stock_key": "CMA100590276",
            "datasheet_url": "https://datasheet.*********.workers.dev/CMA100520894_CD4148WP_规格书_wj217781.PDF",
            "image_list": [],
            "parameters_key": "CMA100590276",
            "description": "",
            "updated_at": "2025-03-19 02:00:58",
            "prices": [
                {
                    "quantity": 50,
                    "price": 0.08
                },
                {
                    "quantity": 500,
                    "price": 0.07
                },
                {
                    "quantity": 1500,
                    "price": 0.06
                },
                {
                    "quantity": 5000,
                    "price": 0.05
                },
                {
                    "quantity": 25000,
                    "price": 0.04
                }
            ],
            "stock": 0,
            "category_path": [
                "CMC0039",
                "CMC0039011",
                "CMC0039011003"
            ],
            "category_id": "CMC0039011003",
            "category_names": {
                "CMC0039": {
                    "name_cn": "电子元件",
                    "name_en": "electronic_components",
                    "name_ru": "electronic_components"
                },
                "CMC0039011": {
                    "name_cn": "数字器件",
                    "name_en": "digital_components",
                    "name_ru": "digital_components"
                },
                "CMC0039011003": {
                    "name_cn": "数字隔离器",
                    "name_en": "Digital Isolator",
                    "name_ru": "数字隔离器"
                }
            }
                
        }
    }
}
```


## 数据库结构

API使用Cloudflare D1数据库，主要表结构如下：

### categories 表

存储产品类别信息。

| 字段 | 描述 |
|-----|------|
| code | 类别代码 |
| parent_code | 父类别代码 |
| level | 类别层级 |
| name_cn | 中文名称 |
| name_en | 英文名称 |
| name_ru | 俄语名称 |

### products202503 表

存储产品基本信息。

| 字段 | 描述 |
|-----|------|
| product_id | 产品ID |
| model | 产品型号 |
| brand_id | 品牌ID |
| price_key | 价格键 |
| stock_key | 库存键 |
| datasheet_url | 数据表URL |
| image_list | 图片列表（JSON格式） |
| parameters_key | 参数键 |
| description | 产品描述 |
| updated_at | 更新时间 |

### brands 表

存储品牌信息。

| 字段 | 描述 |
|-----|------|
| id | 品牌ID |
| name_cn | 中文名称 |
| name_en | 英文名称 |
| name_ru | 俄语名称 |
| logo_url | 品牌logo URL |
| introduction_en | 英文简介 |
| introduction_cn | 中文简介 |
| introduction_ru | 俄语简介 |
| website | 品牌网站URL |
| created_at | 创建时间 |

### price 表

存储产品阶梯价格。

| 字段 | 描述 |
|-----|------|
| code | 价格键 |
| quantity | 数量 |
| price | 价格 |

### stocks 表

存储产品库存信息。

| 字段 | 描述 |
|-----|------|
| code | 库存键 |
| stocks | 库存数量 |

### parameters 表

存储产品参数信息。

| 字段 | 描述 |
|-----|------|
| code | 参数键 |
| languages | 语言代码 |
| param_name | 参数名称 |
| param_value | 参数值 |

### product_category_map 表

存储产品与类别的映射关系。

| 字段 | 描述 |
|-----|------|
| product_code | 产品代码 |
| category_code | 类别代码 |



# deepseek API
```
curl https://deepseek-worker.*********.workers.dev/ \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{
        "model": "deepseek-chat",
        "messages": [
          {"role": "system", "content": "你是一个有帮助的助手。"},
          {"role": "user", "content": "请介绍一下中国的历史。"}
        ],
        "temperature": 0.7,
        "stream": false
      }'
```
**响应示例**:
```json
{
    "id": "4cc0332d-eda5-420c-90b9-e62b93d8c507",
    "object": "chat.completion",
    "created": 1744297921,
    "model": "deepseek-chat",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "中国是一个拥有悠久历史和灿烂文化的国家，其历史可以追溯到数千年前。以下是中国历史的主要时期和特点的简要概述："
            },
            "logprobs": null,
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 14,
        "completion_tokens": 852,
        "total_tokens": 866,
        "prompt_tokens_details": {
            "cached_tokens": 0
        },
        "prompt_cache_hit_tokens": 0,
        "prompt_cache_miss_tokens": 14
    },
    "system_fingerprint": "fp_3d5141a69a_prod0225"
}
```
**保留进任务过程的字段**：
```json
{
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "中国是一个拥有悠久历史和灿烂文化的国家，其历史可以追溯到数千年前。以下是中国历史的主要时期和特点的简要概述："
            },
        }
    ]
}
```


# glm API 
```
curl https://glm-worker.*********.workers.dev/ \
  -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "model": "glm-4",
    "messages": [
      {"role": "system", "content": "你是一个有帮助的助手。"},
      {"role": "user", "content": "请介绍一下智谱AI。"}
    ],
    "temperature": 0.7,
    "top_p": 0.8,
    "max_tokens": 2048,
    "stream": false
  }'
```
**响应示例**:
```json
{
    "choices": [
        {
            "finish_reason": "stop",
            "index": 0,
            "message": {
                "content": "智谱AI（Zhipu AI）是一家专注于人工智能领域的中国公司，成立于2019年，总部位于北京。",
                "role": "assistant"
            }
        }
    ],
    "created": 1744297928,
    "id": "20250410231158babbf5ebdf1345e4",
    "model": "glm-4-flash",
    "request_id": "20250410231158babbf5ebdf1345e4",
    "usage": {
        "completion_tokens": 362,
        "prompt_tokens": 19,
        "total_tokens": 381
    }
}
```
**保留进任务过程的字段**：
```json
{
    "choices": [
        {
            "message": {
                "content": "智谱AI（Zhipu AI）是一家专注于人工智能领域的中国公司，成立于2019年，总部位于北京。",
                "role": "assistant"
            }
        }
    ]
}
```

# embeding API 
```
curl --location 'https://embedding.*********.workers.dev/' \
--header 'Content-Type: application/json' \
--data '{
  "model": "BAAI/bge-m3",
  "input": "测试文本内容",
  "encoding_format": "float"
}'
```

**响应示例**:
```json
{
    "object": "list",
    "data": [
        {
            "embedding": [
                -0.049343873,
                -0.008518142,
                -0.010184016,
            ],
            "index": 0,
            "object": "embedding"
        }
    ],
    "model": "BAAI/bge-m3",
    "usage": {
        "prompt_tokens": 6,
        "completion_tokens": 0,
        "total_tokens": 6
    }
}
```



# search Vectorize API
```
curl --location 'https://search-ai.*********.workers.dev/' \
--header 'Content-Type: application/json' \
--data '{
  "query": "我想找一款高性能笔记本电脑",
  "model": "BAAI/bge-m3",
  "topK": 3
}'
```

**响应示例**:
```json
{
  "query": "LM358",
  "embedding_model": "BAAI/bge-m3",
  "matches": [
    {
      "id": "CMA100126845",
      "score": 0.2405868,
      "metadata": {"parameter": "转换效率:未提供......"}
    },
    {
      "id": "CMA100225496",
      "score": 0.23674862,
      "metadata": {"parameter": "转换效率:未提供......"}
    },
    {
      "id": "CMA100205232",
      "score": 0.23674862,
      "metadata": {"parameter": "转换效率:未提供......"}
    },
    {
      "id": "CMA100140604",
      "score": 0.23674862,
      "metadata": {"parameter": "转换效率:未提供......"}
    },
    {
      "id": "CMA100095394",
      "score": 0.23674862,
      "metadata": {"parameter": "转换效率:未提供......"}
    }
  ],
  "usage": {
    "prompt_tokens": 5,
    "completion_tokens": 0,
    "total_tokens": 5
  }
}
```
**保留进任务过程的字段**：
```json
//只保留top1个
{
  "query": "LM358",
  "matches": [
    {
      "id": "CMA100126845",
      "score": 0.2405868,
      "metadata": {"parameter": "转换效率:未提供......"}
    }
  ],
}
```

# websearch API
```
curl --location 'https://glm-websearch.*********.workers.dev' \
--header 'Content-Type: application/json' \
--data '{
    "search_engine": "Search-Pro-Quark",
    "search_query": "LM358ADR参数"
}'
```

**响应示例**:
```json
{
    "created": 1744748251,
    "id": "202504160417299c6db0165bdc4dd5",
    "request_id": "202504160417299c6db0165bdc4dd5",
    "search_intent": [
        {
            "intent": "SEARCH_ALL",
            "keywords": "tmcp1a106mtrf 参数值",
            "query": "TMCP1A106MTRF参数值"
        }
    ],
    "search_result": [
        {
            "content": "爆款\n收藏 对比\n10uF ±20% 10V\n....20%\n故障率 -\n类型 模制",
            "icon": "",
            "link": "https://item.szlcsc.com/2061273.html?lcsc_vid=QllYXlxWFgBZBABeEwUKBVFWRAUMBgIHFVENVAcDQwQxVlNVTldXUVdWT1ldXjtW&spm=sc.it.tj.1-21",
            "media": "",
            "refer": "ref_1",
            "title": "TMCP1A106MTRF"
        },
        {
            "content": "10uF ±20% 10V\n..... 下载PDF",
            "icon": "",
            "link": "https://item.szlcsc.com/2061273.html?spm=sc.it.tj.1-14&lcsc_vid=FFYKVgFUEwNdBV1QRFkKA1wHFVZeVFwHRFVcBFVWEQUxVlNSR1ZaX1RQQVZeUTtW",
            "media": "",
            "refer": "ref_2",
            "title": "TMCP1A106MTRF"
        }
    ]
}
```
**保留进任务过程的字段**：
```json
{
    "search_result": [
        {
            "content": "爆款\n收藏 对比\n10uF ±20% 10V\n....20%\n故障率 -\n类型 模制",
            "icon": "",
            "link": "https://item.szlcsc.com/2061273.html?lcsc_vid=QllYXlxWFgBZBABeEwUKBVFWRAUMBgIHFVENVAcDQwQxVlNVTldXUVdWT1ldXjtW&spm=sc.it.tj.1-21",
            "media": "",
            "refer": "ref_1",
            "title": "TMCP1A106MTRF"
        },
        {
            "content": "10uF ±20% 10V\n..... 下载PDF",
            "icon": "",
            "link": "https://item.szlcsc.com/2061273.html?spm=sc.it.tj.1-14&lcsc_vid=FFYKVgFUEwNdBV1QRFkKA1wHFVZeVFwHRFVcBFVWEQUxVlNSR1ZaX1RQQVZeUTtW",
            "media": "",
            "refer": "ref_2",
            "title": "TMCP1A106MTRF"
        }
    ]
}
```

# reranke API
```
curl -X POST https://email-manage.*********.workers.dev \
  -H "Content-Type: application/json" \
  -d '{
    "query": "什么是人工智能?",
    "documents": [
      "人工智能是计算机科学的一个分支，致力于创建能够模拟人类智能的系统。",
      "机器学习是人工智能的一个子领域，专注于让计算机系统从数据中学习。",
      "深度学习是机器学习的一种方法，使用神经网络进行模式识别。",
      "自然语言处理是AI的一个应用领域，专注于让计算机理解人类语言。",
      "计算机视觉是AI的另一个应用领域，专注于让计算机理解和处理图像。"
    ],
    "model": "BAAI/bge-reranker-v2-m3",
    "top_n": 3,
    "return_documents": true,
    "max_chunks_per_doc": 1024,
    "overlap_tokens": 40
  }'
```

**响应示例**:
```json
{
    "id": "01963fda1ac07b9ebb43fe9350b4909c",
    "results": [
        {
            "document": {
                "text": "转换效率:未提供......"
            },
            "index": 9,
            "relevance_score": 0.85633236
        },
        {
            "document": {
                "text": "转换效率:未提供......"
            },
            "index": 2,
            "relevance_score": 0.83633236
        }
    ],
    "meta": {
        "billed_units": {
            "input_tokens": 10288,
            "output_tokens": 0,
            "search_units": 0,
            "classifications": 0
        },
        "tokens": {
            "input_tokens": 10288,
            "output_tokens": 0
        }
    }
}
```
# 搜索中国区域的产品 API
```
curl --location 'https://search-cn-pt.*********.workers.dev/?keyword={keyword}' \
--header 'Content-Type: application/json'
```
**响应示例**:
```json
{
    "keyword": "LM358AD",
    "textList": [
        "LM358AD\n品牌: ST(意法半导体)\n封装: SOIC-8\n放大器数: 双路\n最大电源宽度(Vdd-Vss): 32V\n增益带宽积(GBP): 1.1MHz\n输入失调电压(Vos): 3mV\n类目: 运算放大器\n编号: C5456123\n详细: 数据手册\n领券 \n包邮券\n嘉立创贴片惊喜价格(库存799)\nSMT扩展库\n\n1+: \n\n￥3.97\n\n10+: \n\n￥3.22\n\n30+: \n\n￥2.85\n\n100+: \n\n￥2.48\n\n500+: \n\n￥2.25\n现货最快4H发\n广东仓: 712\n江苏仓: 0\n个\n100个/管\n总额: ￥0\n加入购物车\n近期成交23单\n我要备货"
    ],
    "result": [
        {
            "text": "LM358AD",
            "model": "LM358AD",
            "brand": "ST(意法半导体)",
            "price": [
                {
                    "count": 1,
                    "price": 3.97
                },
                {
                    "count": 10,
                    "price": 3.22
                },
                {
                    "count": 30,
                    "price": 2.85
                },
                {
                    "count": 100,
                    "price": 2.48
                },
                {
                    "count": 500,
                    "price": 2.25
                }
            ],
            "stock": [
                712,
                0
            ]
        }
    ]
}
```
**保留进任务过程的字段**：
```json
{
    "keyword": "LM358AD",
    "result": [
        {
            "text": "LM358AD",
            "model": "LM358AD",
            "brand": "ST(意法半导体)",
            "price": [
                {
                    "count": 1,
                    "price": 3.97
                },
                {
                    "count": 10,
                    "price": 3.22
                },
                {
                    "count": 30,
                    "price": 2.85
                },
                {
                    "count": 100,
                    "price": 2.48
                },
                {
                    "count": 500,
                    "price": 2.25
                }
            ],
            "stock": [
                712,
                0
            ]
        }
    ]
}
```
# route API

```
curl --location 'https://agent-route.*********.workers.dev/' \
--header 'Content-Type: application/json' \
--data '{
  "apiType": "api-type-name",  // 必需，API 类型名称
  "pathParam": "optional-path-parameter",  // 可选，路径参数
  "queryParams": {  // 可选，查询参数
    "param1": "value1",
    "param2": "value2"
  },
  "data": {  // 可选，POST/PUT 请求的数据
    // 请求体数据
  }
}'
```

# 广告位管理API

```
URL :https://ads-manage.*********.workers.dev
- 列出所有键

- 请求: GET /api/ads
- 响应: { "keys": [...] }
- 获取值

- 请求: GET /api/ads/{key}
- 响应: { "key": "your_key", "value": "your_value" }
- 创建新值

- 请求: POST /api/ads/{key}
- 请求体: { "value": "your_value" } 或纯文本
- 响应: { "success": true, "message": "值已创建", "key": "your_key" }
- 更新值

- 请求: PUT /api/ads/{key}
- 请求体: { "value": "your_value" } 或纯文本
- 响应: { "success": true, "message": "值已更新", "key": "your_key" }
- 删除值

- 请求: DELETE /api/ads/{key}
- 响应: { "success": true, "message": "值已删除", "key": "your_key" }

```



          
# 电子元器件推荐 API 文档

这是一个用于获取和更新电子元器件推荐数据的 Cloudflare Worker API。该 API 从多个电子元器件网站获取热门搜索数据，使用智能模型提取元器件型号，并将其存储到数据库中。

## 基础 URL

所有 API 请求都应该发送到 Worker 的基础 URL，例如：`https://get-recommend.*********.workers.dev`

## API 端点

### 1. 获取最新推荐数据 `/latest-recommend`

获取数据库中最新一天的电子元器件推荐数据。

**请求方法:** GET

**CURL 示例:**
```bash
curl -X GET "https://get-recommend.*********.workers.dev/latest-recommend"
```

**响应示例:**
```json
{
  "date": "2023-05-20",
  "keywords": [
    "STM32F103C8T6",
    "LM358",
    "NE555",
    "ESP32",
    "MAX232",
    "74HC595"
  ]
}
```

**错误响应:**
```json
{
  "error": "没有找到任何推荐数据"
}
```

### 2. 更新推荐数据 `/update-recommend`

从多个电子元器件网站获取热门搜索数据，使用智能模型提取元器件型号，并将其存储到数据库中。

**请求方法:** GET

**CURL 示例:**
```bash
curl -X GET "https://get-recommend.*********.workers.dev/update-recommend"
```

**响应示例:**
```json
{
  "parts": [
    "STM32F103C8T6",
    "LM358",
    "NE555",
    "ESP32",
    "MAX232",
    "74HC595"
  ]
}
```

**错误响应:**
```json
{
  "error": "获取 lcsc.com 数据失败: 404"
}
```

## 工作流程说明

### 获取最新推荐数据流程
1. 查询数据库获取最新的数据日期
2. 查询该日期的所有关键词（元器件型号）
3. 返回日期和关键词列表

### 更新推荐数据流程
1. 并行从三个数据源获取数据：
   - dzsc.com（电子元器件商城）
   - hqew.com（华强电子网）
   - lcsc.com（立创商城）
2. 将获取到的数据合并
3. 使用智能大模型（GLM）从数据中提取电子元器件型号
4. 将提取的型号保存到数据库中
5. 返回处理结果

## 数据源说明

1. **dzsc.com**
   - 接口：`https://www.dzsc.com/ajax/iclock.aspx?act=gethotic`
   - 提供热门搜索的电子元器件型号

2. **hqew.com**
   - 接口：`https://product.hqew.com/home/<USER>
   - 返回 JSONP 格式的热门搜索关键词

3. **lcsc.com**
   - 接口：`https://wmsc.lcsc.com/ftps/wm/home/<USER>/recommend`
   - 提供立创商城推荐的电子元器件

## 智能处理

API 使用智能大模型（GLM）处理从多个数据源获取的原始数据，提取出标准化的电子元器件型号。处理流程如下：

1. 将三个数据源的数据合并
2. 使用 GLM 模型提取电子元器件型号
3. 规整 GLM 返回的数据为标准 JSON 格式
4. 将提取的型号保存到数据库

## 错误处理

当 API 请求失败时，将返回包含错误信息的 JSON 响应：

```json
{
  "error": "错误描述信息"
}
```

常见错误状态码：
- `404`: 未找到匹配的 API 路径
- `500`: 服务器内部错误，如数据源请求失败或数据库操作失败

## 注意事项

1. 所有 API 响应的 Content-Type 均为 `application/json`
2. API 支持跨域请求（CORS）
3. 数据库查询默认只返回最新一天的数据
4. 更新操作会从三个不同的数据源获取数据，可能需要较长时间处理



          
# LCSC API Worker 接口文档

这是一个用于获取LCSC电子元器件数据的Cloudflare Worker API。该API提供了多种端点，用于获取热门产品、折扣产品、闪购产品、特价产品以及品牌统计信息。

## 基础URL

所有API请求都应该发送到Worker的基础URL，例如：`https://get-hot.*********.workers.dev`

## API端点

### 1. 获取热门产品 `/hot`

获取LCSC网站上标记为热门的产品数据。

**请求方法:** GET

**CURL示例:**
```bash
curl -X GET "https://get-hot.*********.workers.dev/hot"
```

**响应示例:**
```json
{
  "success": true,
  "type": "hot",
  "pagesProcessed": [
    {
      "page": 1,
      "totalItems": 100,
      "totalPage": 5
    },
    ...
  ],
  "totalModels": 500,
  "batchesProcessed": [
    {
      "batchNumber": 1,
      "modelsProcessed": 100
    },
    ...
  ],
  "dbResults": {
    "success": 495,
    "failed": 5,
    "errors": [...]
  }
}
```

### 2. 获取折扣产品 `/discount`

获取LCSC网站上标记为折扣的产品数据。

**请求方法:** GET

**CURL示例:**
```bash
curl -X GET "https://get-hot.*********.workers.dev/discount"
```

**响应格式与热门产品相同，但`type`字段值为`"discount"`**

### 3. 获取闪购产品 `/flashsale`

获取LCSC网站上标记为闪购的产品数据。

**请求方法:** GET

**CURL示例:**
```bash
curl -X GET "https://get-hot.*********.workers.dev/flashsale"
```

**响应格式与热门产品相同，但`type`字段值为`"flashsale"`**

### 4. 获取特价产品 `/deals`

获取LCSC网站上标记为特价的产品数据。

**请求方法:** GET

**CURL示例:**
```bash
curl -X GET "https://get-hot.*********.workers.dev/deals"
```

**响应格式与热门产品相同，但`type`字段值为`"deals"`**

### 5. 获取最新数据 `/latest`

获取数据库中最新一天的产品数据，可以按类型筛选。

**请求方法:** GET

**查询参数:**
- `type` (可选): 产品类型，可选值为 `hot`, `discount`, `flashsale`, `deals`

**CURL示例:**
```bash
# 获取所有类型的最新数据
curl -X GET "https://get-hot.*********.workers.dev/latest"

# 获取特定类型的最新数据
curl -X GET "https://get-hot.*********.workers.dev/latest?type=hot"
```

**响应示例:**
```json
{
  "success": true,
  "type": "hot",
  "count": 495,
  "data": [
    {
      "product_id": "C123456",
      "model": "STM32F103C8T6",
      "brand_id": "1234",
      "brand_cn": "意法半导体",
      "brand_en": "STMicroelectronics",
      "brand_ru": "STMicroelectronics",
      "domestic": "chinese",
      "type": "hot",
      "created_at": "2023-05-20T12:34:56.789Z",
      "full": {
                "product_id": "CMA100025459",
                "model": "COS129U",
                "brand_id": "CEB000171",
                "brand": {
                    "name_cn": "COSINE(科山芯创)",
                    "name_en": "COSINE",
                    "name_ru": "Косайн",
                    "logo_url": "https://brandimg.*********.workers.dev/054CCD2C35325460BF2820F275935945.jpg",
                    "website": "http://www.cosine-ic.com/"
                },
                "price_key": "CMA100025459",
                "stock_key": "CMA100025459",
                "datasheet_url": "https://datasheet.*********.workers.dev/CMA100025459_COS129U_规格书_wj1225059.pdf",
                "image_list": [],
                "parameters_key": "CMA100025459",
                "description": "",
                "updated_at": "2025-03-19 02:01:00",
                "prices": [
                    {
                        "quantity": 1,
                        "price": 20.2
                    },
                    {
                        "quantity": 10,
                        "price": 19.31
                    },
                    {
                        "quantity": 30,
                        "price": 14.64
                    },
                    {
                        "quantity": 100,
                        "price": 15.15
                    }
                ],
                "stock": 434,
                "category_path": [
                    "CMC0048",
                    "CMC0048003",
                    "CMC0048003005"
                ],
                "category_id": "CMC0048003005",
                "category_names": {
                    "CMC0048": {
                        "name_cn": "特殊电子元件",
                        "name_en": "special_electronics",
                        "name_ru": "special_electronics"
                    },
                    "CMC0048003": {
                        "name_cn": "特殊转换器",
                        "name_en": "special_converters",
                        "name_ru": "special_converters"
                    },
                    "CMC0048003005": {
                        "name_cn": "仪表放大器",
                        "name_en": "Instrumentation Amplifier",
                        "name_ru": "仪表放大器"
                    }
                }
        },
    },
    ...
  ]
}
```

### 6. 获取品牌统计 `/brands`

获取数据库中最新一天的品牌统计信息。

**请求方法:** GET

**CURL示例:**
```bash
curl -X GET "https://get-hot.*********.workers.dev/brands"
```

**响应示例:**
```json
{
  "success": true,
  "date": "2023-05-20",
  "summary": {
    "total_brands": 150,
    "total_products": 2500,
    "domestic_stats": {
      "domestic": {
        "count": 45,
        "products": 750
      },
      "imported": {
        "count": 100,
        "products": 1700
      },
      "unknown": {
        "count": 5,
        "products": 50
      }
    }
  },
  "brands": [
    {
      "brand_id": "1234",
      "brand_cn": "意法半导体",
      "brand_en": "STMicroelectronics",
      "brand_ru": "STMicroelectronics",
      "domestic": "0",
      "product_count": 120,
      "distributor_count": 5
    },
    ...
  ]
}
```

## 错误响应

当API请求失败时，将返回包含错误信息的JSON响应：

```json
{
  "success": false,
  "error": "错误描述信息"
}
```

常见错误状态码：
- `404`: 未找到匹配的API路径
- `500`: 服务器内部错误，如数据库操作失败

## 注意事项

1. 所有API响应的Content-Type均为`application/json`
2. 热门、折扣、闪购和特价产品的API会从LCSC网站获取最新数据并存储到数据库中
3. `/latest`和`/brands`端点仅查询数据库中已存储的数据
4. 数据库查询默认只返回当天的数据

        






# distributors-portal API 文档

## 概述

distributors-portal API 是一个基于 Cloudflare Worker 的服务，提供了两个主要功能：
1. 抓取网页内容并提取分销商信息，存储到数据库中
2. 获取数据库中最新一天的分销商数据

## API 端点

### 1. 抓取并存储分销商数据

从指定网页抓取内容，提取分销商信息，并存储到数据库中。

- **URL**: `/`
- **方法**: POST
- **请求参数**: 无
- **响应格式**: JSON

**示例请求**:
```bash
curl --request POST \
  --url https://distributors-portal.*********.workers.dev/scrape \
  --header 'Content-Type: application/json'
```

**响应示例**:
```json
{
  "extracted_companies": ["公司1", "公司2", "公司3"],
  "matched_distributors_by_type": {
    "1": [
      {
        "distributor_id": "DIST001",
        "name_cn": "公司1",
        "description_cn": "公司1的描述"
      }
    ],
    "2": [
      {
        "distributor_id": "DIST002",
        "name_cn": "公司2",
        "description_cn": "公司2的描述"
      }
    ]
  },
  "storage_status": "数据已存储到distributors-portal表"
}
```

### 2. 获取最新分销商数据

获取数据库中最新一天的分销商数据。

- **URL**: `/latest`
- **方法**: GET
- **请求参数**: 无
- **响应格式**: JSON

**示例请求**:
```bash
curl --request GET \
  --url https://distributors-portal.*********.workers.dev/latest
```

**响应示例**:
```json
{
  "latest_date": "2023-05-15",
  "data": [
    {
      "distributor_id": "DIST001",
      "type": "1",
      "data": "2023-05-15"
    },
    {
      "distributor_id": "DIST002",
      "type": "2",
      "data": "2023-05-15"
    }
  ],
  "grouped_data": {
    "1": [
      {
        "distributor_id": "DIST001",
        "type": "1",
        "data": "2023-05-15"
      }
    ],
    "2": [
      {
        "distributor_id": "DIST002",
        "type": "2",
        "data": "2023-05-15"
      }
    ]
  }
}
```

## 错误响应

当请求处理失败时，API 将返回错误信息。

**错误响应示例**:
```json
{
  "error": "处理请求时出错",
  "message": "错误详细信息"
}
```

## 数据库表结构

API 使用的数据库表结构如下：

**表名**: `distributors-portal`

| 字段名 | 类型 | 描述 |
|-------|------|------|
| distributor_id | TEXT | 分销商ID，主键 |
| type | TEXT | 分销商类型，1表示floor1区域，2表示floor2区域 |
| data | TEXT | 数据创建日期，格式为YYYY-MM-DD |

## 注意事项

1. 抓取功能需要 POST 请求，获取最新数据功能需要 GET 请求
2. 数据库中的分销商信息按类型（type）分组存储
3. 每次抓取都会更新当天的数据记录




          
# LCSC 热门分类 API 文档

## 概述

该 API 提供了访问和更新 LCSC 热门分类数据的功能。API 支持获取最新分类数据和更新分类数据两个主要功能。

## API 端点

### 1. 获取最新分类数据

获取最新的分类数据，以树形结构返回，包含三级分类层次。

**URL**: `/api/latest-categories`

**方法**: `GET`

**请求示例**:

```bash
curl -X GET "https://popular-categories.*********.workers.dev/api/latest-categories"
```

**成功响应**:

```json
{
  "success": true,
  "latest_date": "2025-05-05",
  "total_categories": 85,
  "category_tree": {
    "CMC0001": {
      "code": "CMC0001",
      "parent_code": null,
      "level": 1,
      "name_cn": "电子元器件",
      "name_en": "Electronic Components",
      "name_ru": "电子元器件",
      "created_at": "2025-05-05T08:30:02.822Z",
      "children": {
        "CMC0001001": {
          "code": "CMC0001001",
          "parent_code": "CMC0001",
          "level": 2,
          "name_cn": "电阻",
          "name_en": "Resistors",
          "name_ru": "电阻",
          "created_at": "2025-05-05T08:30:02.822Z",
          "children": [
            {
              "code": "CMC0001001001",
              "parent_code": "CMC0001001",
              "level": 3,
              "name_cn": "贴片电阻",
              "name_en": "Chip Resistor",
              "name_ru": "贴片电阻",
              "created_at": "2025-05-05T08:30:02.822Z"
            },
            {
              "code": "CMC0001001003",
              "parent_code": "CMC0001001",
              "level": 3,
              "name_cn": "NTC热敏电阻",
              "name_en": "NTC Thermistor",
              "name_ru": "NTC热敏电阻",
              "created_at": "2025-05-05T08:30:02.822Z"
            }
          ]
        }
      }
    }
  }
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "未找到任何分类数据"
}
```

### 2. 更新分类数据

从 LCSC 网站获取最新的热门分类数据并更新到数据库。

**URL**: `/api/update-categories`

**方法**: `GET`

**请求示例**:

```bash
curl -X GET "https://popular-categories.*********.workers.dev/api/update-categories"
```

**成功响应**:

```json
{
  "success": true,
  "totalPages": 5,
  "totalCategories": 80,
  "lcscCategories": [
    {
      "categoryId": "123456",
      "categoryName": "贴片电阻"
    }
  ],
  "matchedCategories": [
    {
      "id": "123456",
      "name": "贴片电阻"
    }
  ],
  "databaseResults": [
    {
      "code": "CMC0001001001",
      "parent_code": "CMC0001001",
      "level": 3,
      "name_cn": "贴片电阻",
      "name_en": "Chip Resistor",
      "name_ru": "贴片电阻",
      "created_at": "2025-05-05T08:30:02.822Z"
    }
  ],
  "insertResults": [
    {
      "code": "CMC0001001001",
      "success": true,
      "result": {
        "success": true,
        "meta": {}
      }
    }
  ]
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "LCSC API 请求失败: 403 Forbidden"
}
```

## 数据结构

### 分类对象

| 字段 | 类型 | 描述 |
|------|------|------|
| code | string | 分类代码，唯一标识符 |
| parent_code | string | 父级分类代码 |
| level | number | 分类层级（1-3） |
| name_cn | string | 中文名称 |
| name_en | string | 英文名称 |
| name_ru | string | 俄文名称 |
| created_at | string | 创建时间（ISO 格式） |

## 错误代码

| 状态码 | 描述 |
|--------|------|
| 404 | 未找到匹配的 API 路径 |
| 500 | 服务器内部错误 |

## 注意事项

1. 更新分类数据接口会从 LCSC 网站获取 5 页热门分类数据
2. 分类数据以树形结构返回，包含三级分类层次
3. 所有时间戳均为 ISO 格式
4. 分类代码格式为 "CMCxxxxxxxx"，其中 x 为数字

## 示例应用

### 获取并显示最新分类数据

```bash
curl -X GET "https://popular-categories.*********.workers.dev/api/latest-categories"
```

### 更新分类数据

```bash
curl -X GET "https://popular-categories.*********.workers.dev/api/update-categories"
```

## 限制

1. API 请求频率限制为每分钟 60 次
2. 更新分类数据接口每天建议调用不超过 10 次，以避免对 LCSC 网站造成过大压力








          
# 新报价API文档

## 概述

本文档描述了新报价系统的API接口，用于获取和管理电子元器件的报价信息。

## 基础URL

```
https://newly-quota.*********.workers.dev
```

## API端点

### 1. 获取热门IC数据

获取并处理热门IC的最新报价信息，同时将数据保存到数据库。

**请求**

```
GET /api/hot-ic
```

**参数**

无需参数

**响应**

成功响应示例：
```json
[
  {
    "distributor_id": "12345",
    "name_cn": "某电子科技有限公司",
    "name_en": "Some Electronics Co., Ltd",
    "name_ru": "Некоторая электроника",
    "logo": "https://example.com/logo.png",
    "address_cn": "中国深圳市",
    "address_en": "Shenzhen, China",
    "address_ru": "Шэньчжэнь, Китай",
    "model": [
      {
        "model": "STM32F103C8T6",
        "product_id": "67890",
        "brand_id": "123",
        "brand_name_cn": "意法半导体",
        "brand_name_en": "STMicroelectronics",
        "brand_name_ru": "СТМикроэлектроникс",
        "domestic": false
      }
    ]
  }
]
```

错误响应示例：
```json
{
  "error": true,
  "message": "请求出错: 网络连接失败"
}
```

**CURL示例**

```bash
curl -X GET "https://newly-quota.*********.workers.dev/api/hot-ic" \
  -H "Accept: application/json"
```

### 2. 获取最新数据

获取数据库中最新一天的所有报价数据。

**请求**

```
GET /api/latest
```

**参数**

无需参数

**响应**

成功响应示例：
```json
{
  "error": false,
  "message": "获取成功",
  "latest_date": "2025-05-05",
  "data": [
    {
      "id": 1,
      "product_id": "67890",
      "model": "STM32F103C8T6",
      "brand_id": "123",
      "brand_name_cn": "意法半导体",
      "brand_name_en": "STMicroelectronics",
      "brand_name_ru": "СТМикроэлектроникс",
      "domestic": "chinese",
      "distributor_id": "12345",
      "name_cn": "某电子科技有限公司",
      "name_en": "Some Electronics Co., Ltd",
      "name_ru": "Некоторая электроника",
      "logo": "https://example.com/logo.png",
      "address_cn": "中国深圳市",
      "address_en": "Shenzhen, China",
      "address_ru": "Шэньчжэнь, Китай",
      "create_at": "2025-05-05T12:34:56.789Z"
    }
  ]
}
```

无数据响应示例：
```json
{
  "error": false,
  "message": "数据库中没有记录",
  "data": []
}
```

错误响应示例：
```json
{
  "error": true,
  "message": "获取最新数据出错: 数据库连接失败"
}
```

**CURL示例**

```bash
curl -X GET "https://newly-quota.*********.workers.dev/api/latest" \
  -H "Accept: application/json"
```

## 数据结构

### 分销商信息

| 字段 | 类型 | 描述 |
|------|------|------|
| distributor_id | string | 分销商ID |
| name_cn | string | 分销商中文名称 |
| name_en | string | 分销商英文名称 |
| name_ru | string | 分销商俄文名称 |
| logo | string | 分销商logo URL |
| address_cn | string | 分销商中文地址 |
| address_en | string | 分销商英文地址 |
| address_ru | string | 分销商俄文地址 |

### 型号信息

| 字段 | 类型 | 描述 |
|------|------|------|
| product_id | string | 产品ID |
| model | string | 型号 |
| brand_id | string | 品牌ID |
| brand_name_cn | string | 品牌中文名称 |
| brand_name_en | string | 品牌英文名称 |
| brand_name_ru | string | 品牌俄文名称 |
| domestic | boolean | 是否为国产品牌 |

## 错误码

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 404 | 请求的资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有API响应均为JSON格式
2. 所有API均支持跨域访问(CORS)
3. 数据更新频率为每天一次
4. `/api/hot-ic`接口会触发数据抓取和保存，执行时间较长
5. `/api/latest`接口仅查询数据库中已有数据，响应速度较快

## 更新日志

- 2025-05-05: 初始版本发布
- 2025-05-06: 添加`/api/latest`接口






          
# 验证码生成与验证 API 文档

## 概述

此 API 提供两个主要功能：
1. 生成一个有效期为10分钟的验证码
2. 验证用户提交的验证码是否匹配和有效

## API 端点

### 1. 生成验证码

**请求**

```
POST /api/generate
```

**请求头**

```
Content-Type: application/json
```

**请求体**

```json
{
  "identifier": "用户标识符"  // 可以是邮箱、手机号或用户ID等
}
```

**响应**

```json
{
  "success": true,
  "data": {
    "code": "123456",      // 6位数字验证码
    "expiresIn": 600       // 过期时间（秒）
  }
}
```

**错误响应**

```json
{
  "success": false,
  "error": "错误信息"
}
```

**CURL 示例**

```bash
curl -X POST https://verification-code.*********.workers.dev/api/generate \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>"}'
```

### 2. 验证验证码

**请求**

```
POST /api/verify
```

**请求头**

```
Content-Type: application/json
```

**请求体**

```json
{
  "identifier": "用户标识符",  // 与生成验证码时使用的标识符相同
  "code": "123456"           // 用户输入的验证码
}
```

**成功响应**

```json
{
  "success": true,
  "message": "Verification successful"
}
```

**错误响应**

```json
{
  "success": false,
  "error": "错误信息"  // 可能的错误：验证码不存在、已过期或不匹配
}
```

**CURL 示例**

```bash
curl -X POST https://verification-code.*********.workers.dev/api/verify \
  -H "Content-Type: application/json" \
  -d '{"identifier": "<EMAIL>", "code": "123456"}'
```

## 错误代码说明

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | Missing required field | 缺少必填字段 |
| 400 | Verification code not found or expired | 验证码不存在或已过期 |
| 400 | Verification code has expired | 验证码已过期 |
| 400 | Invalid verification code | 验证码不匹配 |
| 405 | Method not allowed | 请求方法不允许 |
| 500 | Internal Server Error | 服务器内部错误 |

## 注意事项

1. 验证码有效期为10分钟
2. 验证码为6位数字
3. 验证成功后，验证码将被删除（一次性使用）
4. API 支持跨域请求（CORS）
5. 请确保在生产环境中添加适当的安全措施





          
# 邮件管理系统 API 文档

## 概述

邮件管理系统是一个基于 Cloudflare Worker 的服务，支持多邮箱轮换发送，每个邮箱每天限额450次。系统提供了发送邮件和查询邮箱状态的API。

## API 端点

### 1. 发送邮件

用于发送电子邮件，支持HTML内容和附件。

- **URL**: `/api/send`
- **方法**: `POST`
- **请求头**:
  - `Content-Type: application/json`
  - `X-API-Key: <您的API密钥>`

- **请求体**:
```json
{
  "to": ["<EMAIL>", "<EMAIL>"],
  "subject": "邮件主题",
  "html": "<p>邮件HTML内容</p>",
  "body": "纯文本内容（可选，如果提供了html则可不提供）",
  "attachments": [
    {
      "filename": "文件名.pdf",
      "content": "Base64编码的文件内容",
      "contentType": "application/pdf"
    }
  ]
}
```

- **成功响应** (200 OK):
```json
{
  "success": true,
  "message": "Email sent successfully",
  "data": {
    "messageId": "消息ID",
    "from": "<EMAIL>",
    "to": ["<EMAIL>"]
  }
}
```

- **错误响应** (400, 401, 429, 500):
```json
{
  "success": false,
  "error": "错误信息"
}
```

### 2. 获取邮箱状态

用于查询所有配置邮箱的使用情况。

- **URL**: `/api/status`
- **方法**: `GET`
- **请求头**:
  - `X-API-Key: <您的API密钥>`

- **成功响应** (200 OK):
```json
{
  "success": true,
  "data": {
    "date": "2023-05-20",
    "accounts": [
      {
        "id": "account1",
        "email": "<EMAIL>",
        "used": 120,
        "remaining": 330,
        "limit": 450
      },
      {
        "id": "account2",
        "email": "<EMAIL>",
        "used": 45,
        "remaining": 405,
        "limit": 450
      }
    ]
  }
}
```

- **错误响应** (401, 500):
```json
{
  "success": false,
  "error": "错误信息"
}
```

## CURL 示例

### 发送邮件

```bash
curl -X POST "https://email-manage.*********.workers.dev/api/send" \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{
    "to": ["<EMAIL>"],
    "subject": "测试邮件",
    "html": "<h1>Hello World</h1><p>这是一封测试邮件</p>",
    "attachments": []
  }'
```

### 获取邮箱状态

```bash
curl -X GET "https://email-manage.*********.workers.dev/api/status" \
  -H "X-API-Key: your-api-key"
```

## 错误代码

- `400 Bad Request`: 请求参数不完整或格式错误
- `401 Unauthorized`: API密钥无效
- `405 Method Not Allowed`: 使用了不支持的HTTP方法
- `429 Too Many Requests`: 所有邮箱已达到发送限额
- `500 Internal Server Error`: 服务器内部错误

## 注意事项

1. 系统会自动选择使用量最少的邮箱发送邮件
2. 每个邮箱每天的发送限额为450封
3. 附件内容需要使用Base64编码
4. API密钥需要在环境变量中配置



          
# 认证服务 API 文档

## 基本信息

**基础 URL**: `https://auth.*********.workers.dev`

## API 端点

### 1. 用户注册

注册新用户，仅支持邮箱注册，并通过验证码验证邮箱真实性。

**请求**

```
POST /register
```

**请求头**

```
Content-Type: application/json
```

**请求体**

```json
{
  "email": "<EMAIL>",
  "password": "your_password"
}
```

**成功响应**

```json
{
  "success": true,
  "message": "验证码已发送到您的邮箱，请在10分钟内完成验证"
}
```

**错误响应**

```json
{
  "success": false,
  "message": "请输入有效的邮箱地址"
}
```

或

```json
{
  "success": false,
  "message": "该邮箱已被注册"
}
```

**CURL 示例**

```bash
curl -X POST https://auth.*********.workers.dev/register \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "your_password"}'
```

### 2. 验证码验证

验证用户注册时收到的验证码，验证成功后完成注册流程。

**请求**

```
POST /verify-code
```

**请求头**

```
Content-Type: application/json
```

**请求体**

```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**成功响应**

```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

**错误响应**

```json
{
  "success": false,
  "message": "验证码验证失败",
  "error": "验证码不存在或已过期"
}
```

**CURL 示例**

```bash
curl -X POST https://auth.*********.workers.dev/verify-code \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456"}'
```

### 3. 用户登录

用户登录并获取 JWT 令牌。

**请求**

```
POST /login
```

**请求头**

```
Content-Type: application/json
```

**请求体**

```json
{
  "username": "<EMAIL>",
  "password": "your_password"
}
```

**成功响应**

```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userID":"11ce669f-7b88-43fa-94ef-7c485..."
  }
}
```

**错误响应**

```json
{
  "success": false,
  "message": "User not found"
}
```

或

```json
{
  "success": false,
  "message": "Invalid password"
}
```

**CURL 示例**

```bash
curl -X POST https://auth.*********.workers.dev/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "your_password"}'
```

### 4. 验证令牌

验证 JWT 令牌的有效性。

**请求**

```
GET /verify
```

**请求头**

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**成功响应**

```json
{
  "success": true,
  "data": {
    "sub": "1",
    "username": "<EMAIL>",
    "role": "user",
    "origin": "https://example.com",
    "exp": 1234567890,
    "iat": 1234567890
  }
}
```

**错误响应**

```json
{
  "success": false,
  "error": "No token provided"
}
```

或

```json
{
  "success": false,
  "error": "Token has been revoked"
}
```

**CURL 示例**

```bash
curl -X GET https://auth.*********.workers.dev/verify \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 5. 用户登出

使当前 JWT 令牌失效。

**请求**
```
POST /logout
```

**请求头**

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**成功响应**

```json
{
  "success": true,
  "message": "Logout successful"
}
```

**错误响应**

```json
{
  "success": false,
  "message": "No token provided"
}
```

**CURL 示例**

```bash
curl -X POST https://auth.*********.workers.dev/logout \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

## 错误代码说明

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | Missing required field | 缺少必填字段 |
| 400 | Verification code not found or expired | 验证码不存在或已过期 |
| 400 | Invalid verification code | 验证码不匹配 |
| 401 | No token provided | 未提供令牌 |
| 401 | Invalid or expired token | 令牌无效或已过期 |
| 401 | Token has been revoked | 令牌已被撤销 |
| 401 | User not found | 用户不存在 |
| 401 | Invalid password | 密码错误 |
| 405 | Method not allowed | 请求方法不允许 |
| 500 | Internal Server Error | 服务器内部错误 |

## 注意事项

1. 验证码有效期为10分钟
2. 验证码为6位数字
3. 验证成功后，验证码将被删除（一次性使用）
4. 所有 API 支持跨域请求（CORS）
5. JWT 令牌默认有效期为1小时
6. 请确保在生产环境中添加适当的安全措施


# `/get-user-id` API 接口说明

## 接口概述

该接口用于通过提供的 JWT token 获取用户 ID 及基本信息，可用于验证用户是否已登录。

## 请求详情

- **URL**: `/get-user-id`
- **方法**: GET
- **认证**: 需要在请求头中提供 Bearer Token

### 请求头

| 参数名 | 必填 | 描述 |
|--------|------|------|
| Authorization | 是 | 格式为 "Bearer {token}"，其中 {token} 为登录时获取的 JWT token |

## 响应详情

### 成功响应

- **状态码**: 200 OK
- **内容类型**: application/json

```json
{
  "success": true,
  "data": {
    "userId": "用户ID",
    "username": "用户名/邮箱",
    "role": "用户角色"
  }
}
```

### 错误响应

#### 未提供 Token

- **状态码**: 401 Unauthorized
- **内容类型**: application/json

```json
{
  "success": false,
  "message": "No token provided"
}
```

#### Token 已被撤销

- **状态码**: 401 Unauthorized
- **内容类型**: application/json

```json
{
  "success": false,
  "message": "Token has been revoked"
}
```

#### Token 验证失败

- **状态码**: 401 Unauthorized
- **内容类型**: application/json

```json
{
  "success": false,
  "message": "Token verification failed"
}
```

## CURL 示例

```bash
curl -X GET "https://https://auth.*********.workers.dev/get-user-id" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************.signature"
```

## 使用场景

1. 前端应用启动时验证用户是否已登录
2. 获取当前登录用户的基本信息
3. 在需要用户ID的API请求前获取用户ID

## 注意事项

- Token 有效期为 1 小时，过期后需要重新登录获取新的 Token
- 如果用户登出，Token 将被撤销，此接口将返回错误
- 建议在应用启动时调用此接口验证用户登录状态

        


          
# 询价与报价 API 文档

## 基本信息

**基础 URL**: 根据您的部署环境而定

**认证方式**: Bearer Token

**响应格式**: 所有 API 返回 JSON 格式数据

**CORS**: 所有 API 支持跨域请求

## 认证说明

API 使用基于令牌的认证机制：

- 管理员令牌格式: `admin_[token内容]`
- 用户令牌格式: `user_[用户ID]`

所有非 GET 请求都需要提供有效的认证令牌。

## API 端点

### 1. 请求 (Requests) API

#### 1.1 获取请求列表

获取所有请求或根据查询参数筛选请求。

**请求**

```
GET /api/requests
```

**请求头**

```
Authorization: Bearer [token]
```

**查询参数**

- `user_id`: (可选) 用户ID，非管理员必须提供
- 其他字段: (可选) 任何 requests 表中的字段都可作为筛选条件

**成功响应**

```json
{
    "success": true,
    "meta": {
        "served_by": "v3-prod",
        "served_by_region": "WNAM",
        "served_by_primary": true,
        "timings": {
            "sql_duration_ms": 0.2967
        },
        "duration": 0.2967,
        "changes": 0,
        "last_row_id": 0,
        "changed_db": false,
        "size_after": 2676285440,
        "rows_read": 7,
        "rows_written": 0
    },
    "results": [
        {
            "id": "R1746746211479076",
            "bom_id": null,
            "user_id": "2e77ff8d-ab4b-46b5-a55e-79ef50485bb6",
            "type": "inquiry",
            "status": "pending",
            "contact_name": "qweqe",
            "business_email": "<EMAIL>",
            "company_name": "qwewqe",
            "country": "qweqwe",
            "quantity": 2,
            "model": "S4B-PH-K-S(LF)(SN)",
            "brand_id": "CEB002186",
            "brand_name": "JST Technology Co., Ltd.",
            "target_price": null,
            "delivery_time": null,
            "date_code": null,
            "distribution_id": null,
            "distribution_name": null,
            "create_at": "2025-05-08 23:16:52"
        }
    ]
}
```

**CURL 示例**

```bash
curl -X GET "https://requests-quota.*********.workers.dev/api/requests?user_id=user123" \
  -H "Authorization: Bearer user_user123"
```

#### 1.2 获取单个请求及其报价

获取特定请求的详细信息及其相关的所有报价。

**请求**

```
GET /api/requests/{requestId}
```

**请求头**

```
Authorization: Bearer [token]
```

**路径参数**

- `requestId`: 请求ID

**成功响应**

```json
{
  "request": {
    "id": "R1649876543123456",
    "user_id": "user123",
    "type": "inquiry",
    "status": "pending",
    "model": "STM32F103C8T6",
    "quantity": 1000,
    "create_at": "2023-04-14T10:15:23.000Z",
    ...
  },
  "quotes": [
    {
      "id": "Q1649876543654321",
      "request_id": "R1649876543123456",
      "user_id": "user123",
      "status": "pending",
      "prices": "[{\"quantity\":1000,\"price\":2.5}]",
      "create_at": "2023-04-14T11:30:45.000Z",
      ...
    }
  ]
}
```

**CURL 示例**

```bash
curl -X GET "https://requests-quota.*********.workers.dev/api/requests/R1649876543123456" \
  -H "Authorization: Bearer user_user123"
```

#### 1.3 创建新请求

创建一个新的请求记录。

**请求**

```
POST /api/requests
```

**请求头**

```
Authorization: Bearer [token]
Content-Type: application/json
```

**请求体**

```json
{
  "user_id": "user123",
  "type": "inquiry",
  "status": "pending",
  "contact_name": "张三",
  "business_email": "<EMAIL>",
  "company_name": "示例公司",
  "country": "China",
  "quantity": 1000,
  "model": "STM32F103C8T6",
  "target_price": 2.0,
  "delivery_time": "2023-05-15"
}
```

**成功响应**

```json
{
  "id": "R1649876543123456",
  "message": "请求创建成功"
}
```

**CURL 示例**

```bash
curl -X POST "https://requests-quota.*********.workers.dev/api/requests" \
  -H "Authorization: Bearer user_user123" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "type": "inquiry",
    "status": "pending",
    "contact_name": "张三",
    "business_email": "<EMAIL>",
    "company_name": "示例公司",
    "country": "China",
    "quantity": 1000,
    "model": "STM32F103C8T6",
    "target_price": 2.0,
    "delivery_time": "2023-05-15"
  }'
```

#### 1.4 更新请求

更新现有请求的信息。

**请求**

```
PUT /api/requests/{requestId}
```

**请求头**

```
Authorization: Bearer [token]
Content-Type: application/json
```

**路径参数**

- `requestId`: 请求ID

**请求体**

```json
{
  "user_id": "user123",
  "status": "processing",
  "quantity": 2000,
  "target_price": 1.8
}
```

**成功响应**

```json
{
  "message": "请求更新成功"
}
```

**CURL 示例**

```bash
curl -X PUT "https://requests-quota.*********.workers.dev/api/requests/R1649876543123456" \
  -H "Authorization: Bearer user_user123" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "status": "processing",
    "quantity": 2000,
    "target_price": 1.8
  }'
```

#### 1.5 删除请求

删除特定请求及其相关的所有报价。

**请求**

```
DELETE /api/requests/{requestId}
```

**请求头**

```
Authorization: Bearer [token]
```

**路径参数**

- `requestId`: 请求ID

**查询参数**

- `user_id`: (可选) 用户ID，非管理员必须提供

**成功响应**

```json
{
  "message": "请求及相关报价删除成功"
}
```

**CURL 示例**

```bash
curl -X DELETE "https://requests-quota.*********.workers.dev/api/requests/R1649876543123456?user_id=user123" \
  -H "Authorization: Bearer user_user123"
```

### 2. 报价 (Quotes) API

#### 2.1 获取报价列表

获取所有报价或根据查询参数筛选报价。

**请求**

```
GET /api/quotes
```

**请求头**

```
Authorization: Bearer [token]
```

**查询参数**

- `user_id`: (可选) 用户ID，非管理员必须提供
- `request_id`: (可选) 请求ID
- 其他字段: (可选) 任何 quote 表中的字段都可作为筛选条件

**成功响应**

```json
{
  "results": [
    {
      "id": "Q1649876543654321",
      "request_id": "R1649876543123456",
      "user_id": "user123",
      "status": "pending",
      "model": "STM32F103C8T6",
      "prices": "[{\"quantity\":1000,\"price\":2.5}]",
      "create_at": "2023-04-14T11:30:45.000Z",
      ...
    }
  ],
  "success": true
}
```

**CURL 示例**

```bash
curl -X GET "https://requests-quota.*********.workers.dev/api/quotes?request_id=R1649876543123456" \
  -H "Authorization: Bearer user_user123"
```

#### 2.2 获取单个报价

获取特定报价的详细信息。

**请求**

```
GET /api/quotes/{quoteId}
```

**请求头**

```
Authorization: Bearer [token]
```

**路径参数**

- `quoteId`: 报价ID

**成功响应**

```json
{
  "id": "Q1649876543654321",
  "request_id": "R1649876543123456",
  "user_id": "user123",
  "status": "pending",
  "model": "STM32F103C8T6",
  "prices": "[{\"quantity\":1000,\"price\":2.5}]",
  "create_at": "2023-04-14T11:30:45.000Z",
  ...
}
```

**CURL 示例**

```bash
curl -X GET "https://requests-quota.*********.workers.dev/api/quotes/Q1649876543654321" \
  -H "Authorization: Bearer user_user123"
```

#### 2.3 创建新报价

创建一个新的报价记录。

**请求**

```
POST /api/quotes
```

**请求头**

```
Authorization: Bearer [token]
Content-Type: application/json
```

**请求体**

```json
{
  "request_id": "R1649876543123456",
  "user_id": "user123",
  "type": "standard",
  "status": "pending",
  "model": "STM32F103C8T6",
  "prices": "[{\"quantity\":1000,\"price\":2.5},{\"quantity\":5000,\"price\":2.2}]",
  "shipping": "DHL",
  "quantity": 1000,
  "delivery_time": "2023-05-20",
  "date_code": "2023+"
}
```

**成功响应**

```json
{
  "id": "Q1649876543654321",
  "message": "报价创建成功"
}
```

**CURL 示例**

```bash
curl -X POST "https://requests-quota.*********.workers.dev/api/quotes" \
  -H "Authorization: Bearer user_user123" \
  -H "Content-Type: application/json" \
  -d '{
    "request_id": "R1649876543123456",
    "user_id": "user123",
    "type": "standard",
    "status": "pending",
    "model": "STM32F103C8T6",
    "prices": "[{\"quantity\":1000,\"price\":2.5},{\"quantity\":5000,\"price\":2.2}]",
    "shipping": "DHL",
    "quantity": 1000,
    "delivery_time": "2023-05-20",
    "date_code": "2023+"
  }'
```

#### 2.4 更新报价

更新现有报价的信息。

**请求**

```
PUT /api/quotes/{quoteId}
```

**请求头**

```
Authorization: Bearer [token]
Content-Type: application/json
```

**路径参数**

- `quoteId`: 报价ID

**请求体**

```json
{
  "user_id": "user123",
  "status": "accepted",
  "prices": "[{\"quantity\":1000,\"price\":2.4},{\"quantity\":5000,\"price\":2.1}]",
  "delivery_time": "2023-05-18"
}
```

**成功响应**

```json
{
  "message": "报价更新成功"
}
```

**CURL 示例**

```bash
curl -X PUT "https://requests-quota.*********.workers.dev/api/quotes/Q1649876543654321" \
  -H "Authorization: Bearer user_user123" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user123",
    "status": "accepted",
    "prices": "[{\"quantity\":1000,\"price\":2.4},{\"quantity\":5000,\"price\":2.1}]",
    "delivery_time": "2023-05-18"
  }'
```

#### 2.5 删除报价

删除特定报价。

**请求**

```
DELETE /api/quotes/{quoteId}
```

**请求头**

```
Authorization: Bearer [token]
```

**路径参数**

- `quoteId`: 报价ID

**查询参数**

- `user_id`: (可选) 用户ID，非管理员必须提供

**成功响应**

```json
{
  "message": "报价删除成功"
}
```

**CURL 示例**

```bash
curl -X DELETE "https://requests-quota.*********.workers.dev/api/quotes/Q1649876543654321?user_id=user123" \
  -H "Authorization: Bearer user_user123"
```

## 错误响应

所有 API 在发生错误时返回统一格式的错误信息：

```json
{
  "error": "错误描述信息"
}
```

常见错误状态码：

- `400 Bad Request`: 请求参数不完整或格式错误
- `401 Unauthorized`: 未提供授权令牌或令牌无效
- `403 Forbidden`: 无权访问请求的资源
- `404 Not Found`: 请求的资源不存在
- `405 Method Not Allowed`: 使用了不支持的HTTP方法
- `500 Internal Server Error`: 服务器内部错误

## 数据模型

### Requests 表字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | TEXT | 主键，格式为 "R" + 时间戳 + 6位随机数 |
| bom_id | TEXT | BOM ID |
| user_id | TEXT | 用户ID |
| type | TEXT | 请求类型 |
| status | TEXT | 请求状态 |
| contact_name | TEXT | 联系人姓名 |
| business_email | TEXT | 商务邮箱 |
| company_name | TEXT | 公司名称 |
| country | TEXT | 国家 |
| quantity | INTEGER | 数量 |
| model | TEXT | 型号 |
| brand_id | TEXT | 品牌ID |
| brand_name | TEXT | 品牌名称 |
| target_price | REAL | 目标价格 |
| delivery_time | TEXT | 交货时间 |
| date_code | TEXT | 生产日期代码 |
| distribution_id | TEXT | 分销商ID |
| distribution_name | TEXT | 分销商名称 |
| create_at | DATETIME | 创建时间 |

### Quote 表字段

| 字段名 | 类型 | 描述 |
|--------|------|------|
| id | TEXT | 主键，格式为 "Q" + 时间戳 + 6位随机数 |
| request_id | TEXT | 关联的请求ID |
| bom_id | TEXT | BOM ID |
| user_id | TEXT | 用户ID |
| type | TEXT | 报价类型 |
| status | TEXT | 报价状态 |
| model | TEXT | 型号 |
| prices | TEXT | 价格信息（JSON字符串） |
| shipping | TEXT | 运输方式 |
| quantity | INTEGER | 数量 |
| brand_id | TEXT | 品牌ID |
| brand_name | TEXT | 品牌名称 |
| target_price | REAL | 目标价格 |
| delivery_time | TEXT | 交货时间 |
| date_code | TEXT | 生产日期代码 |
| distribution_id | TEXT | 分销商ID |
| distribution_name | TEXT | 分销商名称 |
| create_at | DATETIME | 创建时间 |

## 注意事项

1. 所有非 GET 请求都需要提供有效的认证令牌
2. 非管理员用户只能访问和修改自己的请求和报价
3. 删除请求时会同时删除与之关联的所有报价
4. prices 字段存储为 JSON 字符串，包含不同数量级的价格信息






          
# Cloudflare Worker 广告管理 API 文档

## 概述

这个 Cloudflare Worker 脚本用于对 D1 数据库 `little-field-db` 的 `ads` 表进行增删改查操作。该 API 支持多字段条件查询，并提供完整的 CRUD 功能。

## 数据库表结构

`ads` 表包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| platform | TEXT | 平台 |
| page | TEXT | 页面 |
| page_location | TEXT | 页面位置 |
| tags | TEXT | 标签 |
| status | TEXT | 状态 |
| priority | INTEGER | 优先级 |
| img | TEXT | 图片链接 |
| img_alt | TEXT | 图片替代文本 |
| title | TEXT | 标题 |
| subtitle | TEXT | 副标题 |
| description | TEXT | 描述 |
| target_url | TEXT | 目标链接 |
| effective_time | TEXT | 生效时间 |
| create_at | DATETIME | 创建时间，默认为当前时间戳 |

## API 端点

### 1. 获取广告列表

- **URL**: `/api/ads`
- **方法**: `GET`
- **功能**: 获取广告列表，支持多字段条件查询
- **查询参数**:
  - 所有表字段均可作为查询条件（id, platform, page, page_location 等）
  - `page`: 页码（默认为 1）
  - `pageSize`: 每页记录数（默认为 10）
  - `sortBy`: 排序字段（默认为 id）
  - `sortOrder`: 排序方向（ASC 或 DESC，默认为 ASC）
- **响应示例**:
  ```json
  {
    "data": [
      {
        "id": 1,
        "platform": "web",
        "page": "home",
        "page_location": "banner",
        "tags": "promotion",
        "status": "active",
        "priority": 1,
        "img": "https://example.com/image.jpg",
        "img_alt": "促销广告",
        "title": "夏季大促",
        "subtitle": "全场5折起",
        "description": "限时优惠，不容错过",
        "target_url": "https://example.com/promotion",
        "effective_time": "2023-06-01",
        "create_at": "2023-05-15T10:30:00Z"
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "pageSize": 10,
      "totalPages": 5
    }
  }
  ```

### 2. 获取单个广告

- **URL**: `/api/ads/:id`
- **方法**: `GET`
- **功能**: 根据 ID 获取单个广告详情
- **参数**: 
  - `id`: 广告 ID（路径参数）
- **响应示例**:
  ```json
  {
    "id": 1,
    "platform": "web",
    "page": "home",
    "page_location": "banner",
    "tags": "promotion",
    "status": "active",
    "priority": 1,
    "img": "https://example.com/image.jpg",
    "img_alt": "促销广告",
    "title": "夏季大促",
    "subtitle": "全场5折起",
    "description": "限时优惠，不容错过",
    "target_url": "https://example.com/promotion",
    "effective_time": "2023-06-01",
    "create_at": "2023-05-15T10:30:00Z"
  }
  ```

### 3. 创建新广告

- **URL**: `/api/ads`
- **方法**: `POST`
- **功能**: 创建新的广告记录
- **请求体**: JSON 对象，包含广告信息
- **必填字段**: `platform`, `page`, `status`
- **请求体示例**:
  ```json
  {
    "platform": "mobile",
    "page": "product",
    "page_location": "bottom",
    "tags": "new,featured",
    "status": "active",
    "priority": 2,
    "img": "https://example.com/new-product.jpg",
    "img_alt": "新品推荐",
    "title": "全新上市",
    "subtitle": "限量发售",
    "description": "抢先体验最新产品",
    "target_url": "https://example.com/new-product",
    "effective_time": "2023-07-01"
  }
  ```
- **响应**: 返回创建的广告对象，包含自动生成的 ID 和创建时间

### 4. 更新广告

- **URL**: `/api/ads/:id`
- **方法**: `PUT`
- **功能**: 更新现有广告信息
- **参数**: 
  - `id`: 广告 ID（路径参数）
- **请求体**: JSON 对象，包含要更新的字段
- **请求体示例**:
  ```json
  {
    "status": "inactive",
    "priority": 3,
    "title": "更新后的标题"
  }
  ```
- **响应**: 返回更新后的广告对象

### 5. 删除广告

- **URL**: `/api/ads/:id`
- **方法**: `DELETE`
- **功能**: 删除指定广告
- **参数**: 
  - `id`: 广告 ID（路径参数）
- **响应示例**:
  ```json
  {
    "message": "Ad deleted successfully"
  }
  ```

## 错误处理

所有 API 端点在遇到错误时都会返回适当的 HTTP 状态码和错误信息：

- **400 Bad Request**: 请求参数错误或缺少必填字段
- **404 Not Found**: 请求的资源不存在
- **405 Method Not Allowed**: 不支持的 HTTP 方法
- **500 Internal Server Error**: 服务器内部错误

错误响应格式：
```json
{
  "error": "错误信息"
}
```

## CORS 支持

API 支持跨域资源共享 (CORS)，允许来自任何源的请求。




          
# 博客系统 API 文档

## 概述

这是一个基于 Cloudflare Worker 的博客系统 API，提供以下功能：

1. 对 blog 表、author 表、insights 表进行增删改查
2. 管理 insights 和 blog 之间的关联关系
3. 支持 Markdown 文件的上传和下载

## 数据库表结构

### blog 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| type | INTEGER | 博客类型，默认为0 |
| status | INTEGER | 博客状态，默认为0 |
| title | TEXT | 博客标题，必填 |
| subtitle | TEXT | 副标题 |
| description | TEXT | 描述 |
| cover_image | TEXT | 封面图片URL |
| content_markdown | TEXT | Markdown内容URL |
| tags | TEXT | 标签，多个标签可用逗号分隔 |
| category | TEXT | 分类 |
| location | TEXT | 位置 |
| created_at | DATETIME | 创建时间，默认为当前时间 |
| updated_at | DATETIME | 更新时间，默认为当前时间 |
| author_id | TEXT | 作者ID，必填 |

### author 表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| author_id | TEXT | 主键 |
| name | TEXT | 作者名称，必填 |
| avatar | TEXT | 头像URL |
| description | TEXT | 简短描述 |
| bio | TEXT | 详细介绍 |

### insights 表（专题）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| type | INTEGER | 专题类型，默认为0 |
| status | INTEGER | 专题状态，默认为0 |
| title | TEXT | 专题标题，必填 |
| subtitle | TEXT | 副标题 |
| description | TEXT | 描述 |
| cover_image | TEXT | 封面图片URL |
| tags | TEXT | 标签，多个标签可用逗号分隔 |
| created_at | DATETIME | 创建时间，默认为当前时间 |
| updated_at | DATETIME | 更新时间，默认为当前时间 |
| author_id | TEXT | 作者ID，必填 |

### insights_blog_map 表（专题-博客关联表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | INTEGER | 主键，自增 |
| insights_id | INTEGER | 专题ID |
| blog_id | INTEGER | 博客ID |

## API 端点

### 博客相关 API

#### 1. 获取博客列表

- **URL**: `/api/blogs`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码，默认为1
  - `limit`: 每页记录数，默认为10
  - `author_id`: 按作者ID筛选
  - `category`: 按分类筛选
  - `tag`: 按标签筛选（模糊匹配）
- **响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "type": 0,
      "status": 0,
      "title": "博客标题",
      "subtitle": "副标题",
      "description": "描述内容",
      "cover_image": "https://example.com/image.jpg",
      "content_markdown": "/api/markdown/1234567890-abcdef.md",
      "tags": "技术,编程",
      "category": "Web开发",
      "location": "北京",
      "created_at": "2023-05-20T08:00:00Z",
      "updated_at": "2023-05-20T08:00:00Z",
      "author_id": "author123"
    }
  ],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/blogs?page=1&limit=10&category=Web开发"
```

#### 2. 获取单个博客

- **URL**: `/api/blogs/:id`
- **方法**: `GET`
- **URL 参数**:
  - `id`: 博客ID
- **响应示例**:
```json
{
  "id": 1,
  "type": 0,
  "status": 0,
  "title": "博客标题",
  "subtitle": "副标题",
  "description": "描述内容",
  "cover_image": "https://example.com/image.jpg",
  "content_markdown": "/api/markdown/1234567890-abcdef.md",
  "tags": "技术,编程",
  "category": "Web开发",
  "location": "北京",
  "created_at": "2023-05-20T08:00:00Z",
  "updated_at": "2023-05-20T08:00:00Z",
  "author_id": "author123",
  "author": {
    "author_id": "author123",
    "name": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "description": "资深开发者",
    "bio": "10年Web开发经验..."
  },
  "insights": [
    {
      "id": 1,
      "title": "前端技术专题",
      "description": "关于前端技术的专题"
    }
  ]
}
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/blogs/1"
```

#### 3. 创建博客

- **URL**: `/api/blogs`
- **方法**: `POST`
- **Content-Type**: `application/json` 或 `multipart/form-data`（上传Markdown文件时）
- **请求体**:
```json
{
  "title": "新博客标题",
  "subtitle": "副标题",
  "description": "博客描述",
  "cover_image": "https://example.com/cover.jpg",
  "content_markdown": "https://example.com/content.md",
  "tags": "技术,编程",
  "category": "Web开发",
  "location": "上海",
  "type": 0,
  "status": 0,
  "author_id": "author123"
}
```
- **响应示例**:
```json
{
  "id": 2,
  "type": 0,
  "status": 0,
  "title": "新博客标题",
  "subtitle": "副标题",
  "description": "博客描述",
  "cover_image": "https://example.com/cover.jpg",
  "content_markdown": "https://example.com/content.md",
  "tags": "技术,编程",
  "category": "Web开发",
  "location": "上海",
  "created_at": "2023-05-21T10:00:00Z",
  "updated_at": "2023-05-21T10:00:00Z",
  "author_id": "author123"
}
```

**CURL 示例**:
```bash
curl -X POST "https://blog-manage.*********.workers.dev/api/blogs" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新博客标题",
    "subtitle": "副标题",
    "description": "博客描述",
    "cover_image": "https://example.com/cover.jpg",
    "tags": "技术,编程",
    "category": "Web开发",
    "author_id": "author123"
  }'
```

**使用 multipart/form-data 上传 Markdown 文件**:
```bash
curl -X POST "https://blog-manage.*********.workers.dev/api/blogs" \
  -F "title=新博客标题" \
  -F "subtitle=副标题" \
  -F "description=博客描述" \
  -F "author_id=author123" \
  -F "markdown_file=@/path/to/local/file.md"
```

#### 4. 更新博客

- **URL**: `/api/blogs/:id`
- **方法**: `PUT`
- **URL 参数**:
  - `id`: 博客ID
- **Content-Type**: `application/json` 或 `multipart/form-data`（上传Markdown文件时）
- **请求体**:
```json
{
  "title": "更新后的标题",
  "subtitle": "更新后的副标题",
  "description": "更新后的描述",
  "cover_image": "https://example.com/new-cover.jpg",
  "tags": "技术,编程,更新",
  "category": "前端开发",
  "status": 1
}
```
- **响应示例**:
```json
{
  "id": 1,
  "type": 0,
  "status": 1,
  "title": "更新后的标题",
  "subtitle": "更新后的副标题",
  "description": "更新后的描述",
  "cover_image": "https://example.com/new-cover.jpg",
  "content_markdown": "/api/markdown/1234567890-abcdef.md",
  "tags": "技术,编程,更新",
  "category": "前端开发",
  "location": "北京",
  "created_at": "2023-05-20T08:00:00Z",
  "updated_at": "2023-05-21T15:30:00Z",
  "author_id": "author123"
}
```

**CURL 示例**:
```bash
curl -X PUT "https://blog-manage.*********.workers.dev/api/blogs/1" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "更新后的标题",
    "subtitle": "更新后的副标题",
    "description": "更新后的描述",
    "status": 1
  }'
```

#### 5. 删除博客

- **URL**: `/api/blogs/:id`
- **方法**: `DELETE`
- **URL 参数**:
  - `id`: 博客ID
- **响应示例**:
```json
{
  "message": "博客已成功删除"
}
```

**CURL 示例**:
```bash
curl -X DELETE "https://blog-manage.*********.workers.dev/api/blogs/1"
```

### 作者相关 API

#### 1. 获取作者列表

- **URL**: `/api/authors`
- **方法**: `GET`
- **响应示例**:
```json
[
  {
    "author_id": "author123",
    "name": "张三",
    "avatar": "https://example.com/avatar1.jpg",
    "description": "资深开发者",
    "bio": "10年Web开发经验..."
  },
  {
    "author_id": "author456",
    "name": "李四",
    "avatar": "https://example.com/avatar2.jpg",
    "description": "UI设计师",
    "bio": "专注于用户体验设计..."
  }
]
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/authors"
```

#### 2. 获取单个作者

- **URL**: `/api/authors/:id`
- **方法**: `GET`
- **URL 参数**:
  - `id`: 作者ID
- **响应示例**:
```json
{
  "author_id": "author123",
  "name": "张三",
  "avatar": "https://example.com/avatar1.jpg",
  "description": "资深开发者",
  "bio": "10年Web开发经验..."
}
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/authors/author123"
```

#### 3. 创建作者

- **URL**: `/api/authors`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **请求体**:
```json
{
  "author_id": "author789",
  "name": "王五",
  "avatar": "https://example.com/avatar3.jpg",
  "description": "后端工程师",
  "bio": "专注于高性能服务端开发..."
}
```
- **响应示例**:
```json
{
  "author_id": "author789",
  "name": "王五",
  "avatar": "https://example.com/avatar3.jpg",
  "description": "后端工程师",
  "bio": "专注于高性能服务端开发..."
}
```

**CURL 示例**:
```bash
curl -X POST "https://blog-manage.*********.workers.dev/api/authors" \
  -H "Content-Type: application/json" \
  -d '{
    "author_id": "author789",
    "name": "王五",
    "avatar": "https://example.com/avatar3.jpg",
    "description": "后端工程师",
    "bio": "专注于高性能服务端开发..."
  }'
```

#### 4. 更新作者

- **URL**: `/api/authors/:id`
- **方法**: `PUT`
- **URL 参数**:
  - `id`: 作者ID
- **Content-Type**: `application/json`
- **请求体**:
```json
{
  "name": "王五（更新）",
  "avatar": "https://example.com/new-avatar.jpg",
  "description": "全栈工程师",
  "bio": "全栈开发专家，擅长前后端技术..."
}
```
- **响应示例**:
```json
{
  "author_id": "author789",
  "name": "王五（更新）",
  "avatar": "https://example.com/new-avatar.jpg",
  "description": "全栈工程师",
  "bio": "全栈开发专家，擅长前后端技术..."
}
```

**CURL 示例**:
```bash
curl -X PUT "https://blog-manage.*********.workers.dev/api/authors/author789" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "王五（更新）",
    "description": "全栈工程师",
    "bio": "全栈开发专家，擅长前后端技术..."
  }'
```

#### 5. 删除作者

- **URL**: `/api/authors/:id`
- **方法**: `DELETE`
- **URL 参数**:
  - `id`: 作者ID
- **响应示例**:
```json
{
  "message": "作者已成功删除"
}
```

**CURL 示例**:
```bash
curl -X DELETE "https://blog-manage.*********.workers.dev/api/authors/author789"
```

### 专题相关 API

#### 1. 获取专题列表

- **URL**: `/api/insights`
- **方法**: `GET`
- **查询参数**:
  - `page`: 页码，默认为1
  - `limit`: 每页记录数，默认为10
  - `author_id`: 按作者ID筛选
- **响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "type": 0,
      "status": 0,
      "title": "前端技术专题",
      "subtitle": "最新前端技术解析",
      "description": "深入探讨前端技术的发展与应用",
      "cover_image": "https://example.com/insight-cover.jpg",
      "tags": "前端,JavaScript,React",
      "created_at": "2023-05-15T08:00:00Z",
      "updated_at": "2023-05-15T08:00:00Z",
      "author_id": "author123"
    }
  ],
  "pagination": {
    "total": 5,
    "page": 1,
    "limit": 10,
    "pages": 1
  }
}
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/insights?page=1&limit=10"
```

#### 2. 获取单个专题

- **URL**: `/api/insights/:id`
- **方法**: `GET`
- **URL 参数**:
  - `id`: 专题ID
- **响应示例**:
```json
{
  "id": 1,
  "type": 0,
  "status": 0,
  "title": "前端技术专题",
  "subtitle": "最新前端技术解析",
  "description": "深入探讨前端技术的发展与应用",
  "cover_image": "https://example.com/insight-cover.jpg",
  "tags": "前端,JavaScript,React",
  "created_at": "2023-05-15T08:00:00Z",
  "updated_at": "2023-05-15T08:00:00Z",
  "author_id": "author123",
  "author": {
    "author_id": "author123",
    "name": "张三",
    "avatar": "https://example.com/avatar1.jpg",
    "description": "资深开发者",
    "bio": "10年Web开发经验..."
  },
  "blogs": [
    {
      "id": 1,
      "title": "React 18新特性解析",
      "description": "详细介绍React 18的新功能和改进",
      "created_at": "2023-05-16T10:00:00Z"
    }
  ]
}
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/insights/1"
```

#### 3. 创建专题

- **URL**: `/api/insights`
- **方法**: `POST`
- **Content-Type**: `application/json`
- **请求体**:
```json
{
  "title": "后端技术专题",
  "subtitle": "服务端开发最佳实践",
  "description": "探讨后端架构设计和性能优化",
  "cover_image": "https://example.com/backend-cover.jpg",
  "tags": "后端,架构,性能",
  "type": 0,
  "status": 0,
  "author_id": "author123"
}
```
- **响应示例**:
```json
{
  "id": 2,
  "type": 0,
  "status": 0,
  "title": "后端技术专题",
  "subtitle": "服务端开发最佳实践",
  "description": "探讨后端架构设计和性能优化",
  "cover_image": "https://example.com/backend-cover.jpg",
  "tags": "后端,架构,性能",
  "created_at": "2023-05-22T09:00:00Z",
  "updated_at": "2023-05-22T09:00:00Z",
  "author_id": "author123"
}
```

**CURL 示例**:
```bash
curl -X POST "https://blog-manage.*********.workers.dev/api/insights" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "后端技术专题",
    "subtitle": "服务端开发最佳实践",
    "description": "探讨后端架构设计和性能优化",
    "tags": "后端,架构,性能",
    "author_id": "author123"
  }'
```

#### 4. 更新专题

- **URL**: `/api/insights/:id`
- **方法**: `PUT`
- **URL 参数**:
  - `id`: 专题ID
- **Content-Type**: `application/json`
- **请求体**:
```json
{
  "title": "后端技术专题（更新）",
  "subtitle": "现代服务端开发最佳实践",
  "description": "深入探讨后端架构设计和性能优化的最新方法",
  "status": 1
}
```
- **响应示例**:
```json
{
  "id": 2,
  "type": 0,
  "status": 1,
  "title": "后端技术专题（更新）",
  "subtitle": "现代服务端开发最佳实践",
  "description": "深入探讨后端架构设计和性能优化的最新方法",
  "cover_image": "https://example.com/backend-cover.jpg",
  "tags": "后端,架构,性能",
  "created_at": "2023-05-22T09:00:00Z",
  "updated_at": "2023-05-22T10:30:00Z",
  "author_id": "author123"
}
```

**CURL 示例**:
```bash
curl -X PUT "https://blog-manage.*********.workers.dev/api/insights/2" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "后端技术专题（更新）",
    "subtitle": "现代服务端开发最佳实践",
    "description": "深入探讨后端架构设计和性能优化的最新方法",
    "status": 1
  }'
```

#### 5. 删除专题

- **URL**: `/api/insights/:id`
- **方法**: `DELETE`
- **URL 参数**:
  - `id`: 专题ID
- **响应示例**:
```json
{
  "message": "专题已成功删除"
}
```

**CURL 示例**:
```bash
curl -X DELETE "https://blog-manage.*********.workers.dev/api/insights/2"
```

### 专题-博客关联关系 API

#### 1. 获取专题下的博客列表

- **URL**: `/api/insights/:id/blogs`
- **方法**: `GET`
- **URL 参数**:
  - `id`: 专题ID
- **响应示例**:
```json
[
  {
    "id": 1,
    "title": "React 18新特性解析",
    "description": "详细介绍React 18的新功能和改进",
    "cover_image": "https://example.com/react18.jpg",
    "created_at": "2023-05-16T10:00:00Z",
    "author_id": "author123"
  },
  {
    "id": 3,
    "title": "Vue 3性能优化技巧",
    "description": "Vue 3应用性能优化的实用方法",
    "cover_image": "https://example.com/vue3.jpg",
    "created_at": "2023-05-18T14:00:00Z",
    "author_id": "author456"
  }
]
```

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/insights/1/blogs"
```

#### 2. 添加博客到专题

- **URL**: `/api/insights/:id/blogs`
- **方法**: `POST`
- **URL 参数**:
  - `id`: 专题ID
- **Content-Type**: `application/json`
- **请求体**:
```json
{
  "blog_id": 5
}
```
- **响应示例**:
```json
{
  "message": "博客已成功添加到专题",
  "insights_id": 1,
  "blog_id": 5
}
```

**CURL 示例**:
```bash
curl -X POST "https://blog-manage.*********.workers.dev/api/insights/1/blogs" \
  -H "Content-Type: application/json" \
  -d '{
    "blog_id": 5
  }'
```

#### 3. 从专题中移除博客

- **URL**: `/api/insights/:id/blogs/:blogId`
- **方法**: `DELETE`
- **URL 参数**:
  - `id`: 专题ID
  - `blogId`: 博客ID
- **响应示例**:
```json
{
  "message": "博客已成功从专题中移除"
}
```

**CURL 示例**:
```bash
curl -X DELETE "https://blog-manage.*********.workers.dev/api/insights/1/blogs/5"
```

### Markdown 文件 API

#### 1. 获取 Markdown 文件

- **URL**: `/api/markdown/:filename`
- **方法**: `GET`
- **URL 参数**:
  - `filename`: Markdown 文件名
- **响应**: Markdown 文件内容

**CURL 示例**:
```bash
curl -X GET "https://blog-manage.*********.workers.dev/api/markdown/1234567890-abcdef.md"
```

#### 2. 上传 Markdown 文件

- **URL**: `/api/markdown`
- **方法**: `POST`
- **Content-Type**: `multipart/form-data`
- **请求体**:
  - `file`: Markdown 文件
- **响应示例**:
```json
{
  "filename": "1234567890-abcdef.md",
  "url": "/api/markdown/1234567890-abcdef.md"
}
```

**CURL 示例**:
```bash
curl -X POST "https://blog-manage.*********.workers.dev/api/markdown" \
  -F "file=@/path/to/local/markdown.md"
```

## 错误处理

所有 API 在遇到错误时会返回相应的 HTTP 状态码和错误信息，例如：

```json
{
  "error": "博客不存在"
}
```

常见的错误状态码：
- 400: 请求参数错误
- 404: 资源不存在
- 500: 服务器内部错误

## 注意事项

1. 所有请求和响应的 Content-Type 默认为 `application/json`
2. 上传文件时需使用 `multipart/form-data` 格式
3. 创建博客和专题时，作者ID必须存在于作者表中
4. 删除博客时会同时删除相关的专题关联关系和存储的 Markdown 文件




## API 文档：邮箱订阅服务

**功能描述：** 此 API 用于接收用户提交的邮箱地址，并将其存储到 Cloudflare D1 数据库的 `subscribe` 表中。

**Worker 脚本：** <mcfile name="subscribe-worker.js" path="m:\little-field-6626\subscribe-worker.js"></mcfile>

### 端点 (Endpoint)

`POST https://subscribe.*********.workers.dev/`

**注意：** 请将 `https://subscribe.*********.workers.dev/` 替换为您 Cloudflare Worker 的实际部署 URL。

### 请求 (Request)

*   **方法 (Method):** `POST`
*   **头部 (Headers):**
    *   `Content-Type: application/json`
*   **请求体 (Body):**
    需要一个 JSON 对象，包含 `email` 字段。
    ```json
    {
      "email": "<EMAIL>"
    }
    ```

### 响应 (Responses)

#### 成功响应

*   **状态码 (Status Code):** `201 Created`
*   **内容 (Content):**
    ```json
    {
      "success": true,
      "message": "Successfully subscribed."
    }
    ```

#### 错误响应

1.  **请求方法错误**
    *   **状态码 (Status Code):** `405 Method Not Allowed`
    *   **内容 (Content):**
        ```json
        {
          "success": false,
          "error": "Expected POST request"
        }
        ```

2.  **缺少 Email**
    *   **状态码 (Status Code):** `400 Bad Request`
    *   **内容 (Content):**
        ```json
        {
          "success": false,
          "error": "Email is required"
        }
        ```

3.  **Email 格式无效**
    *   **状态码 (Status Code):** `400 Bad Request`
    *   **内容 (Content):**
        ```json
        {
          "success": false,
          "error": "Invalid email format"
        }
        ```

4.  **无效的 JSON 载荷**
    *   **状态码 (Status Code):** `400 Bad Request`
    *   **内容 (Content):**
        ```json
        {
          "success": false,
          "error": "Invalid JSON payload"
        }
        ```

5.  **数据库操作失败**
    *   **状态码 (Status Code):** `500 Internal Server Error`
    *   **内容 (Content):**
        ```json
        {
          "success": false,
          "error": "Failed to subscribe. Please try again."
        }
        ```

6.  **未预期的服务器错误**
    *   **状态码 (Status Code):** `500 Internal Server Error`
    *   **内容 (Content):**
        ```json
        {
          "success": false,
          "error": "An unexpected error occurred."
        }
        ```

### cURL 调用示例

请将 `https://subscribe.*********.workers.dev/` 替换为您的实际 Worker URL，并将 `<EMAIL>` 替换为您想订阅的邮箱地址。

```bash
curl -X POST \
  https://subscribe.*********.workers.dev/ \
  -H 'Content-Type: application/json' \
  -d '{
    "email": "<EMAIL>"
  }'
```

**示例成功响应：**
```
HTTP/1.1 201 Created
Content-Type: application/json
... (其他头部信息)

{"success":true,"message":"Successfully subscribed."}
```

**示例错误响应 (无效邮箱格式)：**
```
HTTP/1.1 400 Bad Request
Content-Type: application/json
... (其他头部信息)

{"success":false,"error":"Invalid email format"}
```

---
        
# BOM文件管理API说明

## API概述

此API用于管理电子元器件BOM（物料清单）相关的文件，提供上传、下载、列表查询和删除功能。

## 基本信息

- **基础URL**: `https://bom-files.*********.workers.dev`
- **认证方式**: 无需认证（如需添加认证，请修改代码）

## API端点

### 1. 获取文件列表

获取所有已上传的BOM文件列表。

**请求**:
```
GET /
```

**CURL示例**:
```bash
curl -X GET "https://bom-files.*********.workers.dev/"
```

**响应示例**:
```json
{
  "success": true,
  "files": [
    {
      "name": "bom_1686123456789_a1b2c3d4.xlsx",
      "size": 24680,
      "uploaded": "2023-06-07T12:34:56.789Z"
    },
    {
      "name": "bom_1686234567890_e5f6g7h8.csv",
      "size": 12345,
      "uploaded": "2023-06-08T15:45:23.456Z"
    }
  ]
}
```

### 2. 上传文件

上传一个新的BOM文件。

**请求**:
```
POST /[可选路径]
```

**CURL示例**:
```bash
curl -X POST "https://bom-files.*********.workers.dev/" \
  -H "Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  -H "Content-Disposition: attachment; filename=\"my_bom_file.xlsx\"" \
  --data-binary @/path/to/local/bom_file.xlsx
```

**响应示例**:
```json
{
  "success": true,
  "filePath": "bom_1686123456789_a1b2c3d4.xlsx",
  "url": "https://bom-files.*********.workers.dev/bom_1686123456789_a1b2c3d4.xlsx"
}
```

### 3. 获取特定文件

下载指定的BOM文件。

**请求**:
```
GET /{fileName}
```

**CURL示例**:
```bash
curl -X GET "https://bom-files.*********.workers.dev/bom_1686123456789_a1b2c3d4.xlsx" \
  -o downloaded_bom_file.xlsx
```

**响应**:
直接返回文件内容，响应头包含适当的Content-Type。

### 4. 删除文件

删除指定的BOM文件。

**请求**:
```
DELETE /{fileName}
```

**CURL示例**:
```bash
curl -X DELETE "https://bom-files.*********.workers.dev/bom_1686123456789_a1b2c3d4.xlsx"
```

**响应示例**:
```json
{
  "success": true,
  "message": "文件已成功删除: bom_1686123456789_a1b2c3d4.xlsx"
}
```

## 错误处理

所有错误响应都遵循以下格式：

```json
{
  "success": false,
  "error": "错误描述信息"
}
```

### 常见错误状态码:

- **400**: 请求参数错误（如未提供文件内容、未指定删除路径）
- **404**: 文件未找到
- **405**: 不支持的HTTP方法
- **500**: 服务器内部错误

## 使用注意事项

1. 上传文件时，建议设置`Content-Disposition`头以提供原始文件名
2. 上传的文件会自动生成唯一文件名，格式为`bom_{时间戳}_{随机字符串}{原扩展名}`
3. 支持的文件类型包括但不限于xlsx、csv、json等
4. API支持CORS，可以从任何源进行访问

## 前端集成示例

```javascript
// 上传BOM文件
async function uploadBomFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await fetch('https://bom-files.*********.workers.dev/', {
    method: 'POST',
    body: file,
    headers: {
      'Content-Disposition': `attachment; filename="${file.name}"`
    }
  });
  
  return await response.json();
}

// 获取文件列表
async function getBomFilesList() {
  const response = await fetch('https://bom-files.*********.workers.dev/');
  return await response.json();
}
```