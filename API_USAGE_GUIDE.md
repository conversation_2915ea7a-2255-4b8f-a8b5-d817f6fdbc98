# Cloudflare D1 Database API 使用指南

**Worker URL:** `https://api.chinaelectron.com`

**数据库名称:** `little-field-db`

**表数量:** 42个表

## 目录

- [基础信息](#基础信息)
- [数据库架构](#数据库架构)
- [单表操作](#单表操作)
- [批量操作](#批量操作)
- [多表联查](#多表联查)
- [自定义SQL查询](#自定义sql查询)
- [错误处理](#错误处理)
- [查询参数说明](#查询参数说明)
- [注意事项](#注意事项)

## 基础信息

### 获取 API 文档

```bash
curl -X GET "https://api.chinaelectron.com/api/docs"
```

### 获取数据库信息

```bash
curl -X GET "https://api.chinaelectron.com/api/info"
```

**返回示例:**

```json
{
  "success": true,
  "database_info": {
    "table_count": 29,
    "tables": [
      {
        "name": "products",
        "columns": [
          {"cid": 0, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1},
          {"cid": 1, "name": "model", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0},
          {"cid": 2, "name": "brand_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}
        ],
        "indexes": [],
        "row_count": 50000
      }
    ]
  }
}
```

## 数据库架构

### 完整表结构 (41张表)

#### 1. 产品相关表 (5张)

- **products** - 主产品表（已删除）

  - `product_id` (TEXT, PK) - 产品ID，格式如 CMA100101416
  - `model` (TEXT, NOT NULL) - 产品型号
  - `brand_id` (TEXT, NOT NULL) - 品牌ID
  - `price_key` (TEXT) - 价格键
  - `stock_key` (TEXT) - 库存键
  - `datasheet_key` (TEXT) - 数据表键
  - `image_keys` (TEXT) - 图片键
  - `parameters` (JSON) - 参数信息
  - `description` (TEXT) - 产品描述
  - `updated_at` (TIMESTAMP) - 更新时间
- **distributor_products** - 分销商产品表

  - `product_id` (TEXT, PK) - 产品ID
  - `distributor_id` (TEXT, PK) - 分销商ID
  - `model` (TEXT, NOT NULL) - 产品型号
  - `brand_id` (TEXT, NOT NULL) - 品牌ID
  - `price_key` (TEXT) - 价格键
  - `stock_key` (TEXT) - 库存键
  - `datasheet_key` (TEXT) - 数据表键
  - `image_keys` (TEXT) - 图片键
  - `parameters` (JSON) - 参数信息
  - `vector_data` (JSON) - 向量数据
  - `description` (TEXT) - 产品描述
  - `updated_at` (TIMESTAMP) - 更新时间
  - moq (INTEGER) - 最小起订量
  - spq (INTEGER) - 标准包装数
  - weight (REAL) - 重量
- **discount_products** - 折扣产品表

  - `product_id` (TEXT, PK) - 产品ID
  - `model` (TEXT) - 产品型号
  - `type` (TEXT) - 产品类型
  - `brand_id` (TEXT) - 品牌ID
  - `brand_cn` (TEXT) - 品牌中文名
  - `brand_en` (TEXT) - 品牌英文名
  - `brand_ru` (TEXT) - 品牌俄文名
  - `domestic` (TEXT) - 是否国产
  - `created_at` (DATETIME) - 创建时间
- **products202503** - 主产品表

  - `product_id` (TEXT, PK) - 产品ID
  - `model` (TEXT) - 产品型号
  - `brand_id` (TEXT) - 品牌ID
  - `price_key` (TEXT) - 价格键
  - `stock_key` (TEXT) - 库存键
  - `datasheet_url` (TEXT) - 数据表URL
  - `image_list` (TEXT) - 图片列表
  - `parameters_key` (TEXT) - 参数键
  - `description` (TEXT) - 产品描述
  - `updated_at` (TEXT) - 更新时间
  - `rohs` (INTEGER) - RoHS认证
  - `summarizer` (TEXT) - 摘要
- **component_mapping** - 产品编码映射表

  - `id` (INTEGER, PK) - 映射ID
  - `lcsc_code` (TEXT, NOT NULL) - LCSC编码
  - `ce_code` (TEXT, NOT NULL) - CE编码
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间
  - `UNIQUE(lcsc_code, ce_code)` - 唯一约束

#### 2. 品牌相关表 (3张)

- **brands** - 品牌表

  - `id` (TEXT, PK) - 品牌ID
  - `name_cn` (TEXT) - 中文名称
  - `name_en` (TEXT) - 英文名称
  - `name_ru` (TEXT) - 俄文名称
  - `introduction_cn` (TEXT) - 中文介绍
  - `introduction_en` (TEXT) - 英文介绍
  - `introduction_ru` (TEXT) - 俄文介绍
  - `website` (TEXT) - 官网
  - `logo_url` (TEXT) - Logo URL
  - `domestic` (TEXT) - 是否国产
  - `area` (TEXT) - 地区
- **brand_distributor_mapping** - 品牌分销商映射表

  - `id` (INTEGER, PK) - 映射ID
  - `brand_id` (TEXT, NOT NULL) - 品牌ID
  - `distributor_id` (TEXT, NOT NULL) - 分销商ID
  - `accountfor` (REAL) - 占比
- **brand_mapping** - 品牌映射表
- `id` (INTEGER, PK) - 映射ID
- `lcsc_brand_id` (TEXT, NOT NULL) - LCSC品牌ID
- `ce_brand_id` (TEXT, NOT NULL) - CE品牌ID
- `created_at` (DATETIME) - 创建时间
- `updated_at` (DATETIME) - 更新时间

#### 3. 分销商相关表 (2张)

- **distributors** - 分销商表

  - `distributor_id` (TEXT, PK) - 分销商ID
  - `name_cn` (TEXT) - 中文名称
  - `name_en` (TEXT) - 英文名称
  - `name_ru` (TEXT) - 俄文名称
  - `category_id` (TEXT) - 分类ID
  - `description_cn` (TEXT) - 中文描述
  - `description_en` (TEXT) - 英文描述
  - `description_ru` (TEXT) - 俄文描述
  - `logo` (TEXT) - Logo
  - `img_list` (TEXT) - 图片列表
  - `contact` (TEXT) - 联系方式
  - `certification` (TEXT) - 认证信息
  - `address_cn` (TEXT) - 中文地址
  - `address_en` (TEXT) - 英文地址
  - `address_ru` (TEXT) - 俄文地址
  - `website` (TEXT) - 官网
  - `established_date` (TEXT) - 成立日期
  - `membership_years` (INTEGER) - 会员年限
- **distributors-portal** - 分销商门户表

  - `distributor_id` (TEXT, PK) - 分销商ID
  - `type` (TEXT) - 类型
  - `data` (TEXT) - 数据

#### 4. 价格和库存表 (3张)

- **price** - 价格表

  - `id` (INTEGER, PK) - 价格ID
  - `code` (TEXT) - 产品代码 (对应product_id)
  - `quantity` (INTEGER) - 数量
  - `price` (REAL) - 价格
  - `created_at` (TEXT) - 创建时间
- **stocks** - 库存表

  - `code` (TEXT, PK) - 产品代码 (对应product_id)
  - `stocks` (INTEGER) - 库存数量
  - `created_at` (TEXT) - 创建时间
- **parameters** - 参数表

  - `id` (INTEGER, PK) - 参数ID
  - `code` (TEXT) - 产品代码
  - `languages` (TEXT) - 语言
  - `param_name` (TEXT) - 参数名称
  - `param_value` (TEXT) - 参数值
  - `created_at` (TEXT) - 创建时间

#### 5. 分类相关表 (4张)

- **categories** - 分类表

  - `code` (TEXT, PK) - 分类代码
  - `parent_code` (TEXT) - 父分类代码
  - `level` (INTEGER) - 分类级别
  - `name_cn` (TEXT) - 中文名称
  - `name_en` (TEXT) - 英文名称
  - `name_ru` (TEXT) - 俄文名称
  - `created_at` (TEXT) - 创建时间
- **product_category_map** - 产品分类映射表

  - `product_code` (TEXT, PK) - 产品代码
  - `category_code` (TEXT) - 分类代码
  - `created_at` (TEXT) - 创建时间
- **popular-categories** - 热门分类表

  - `code` (TEXT, PK) - 分类代码
  - `parent_code` (TEXT) - 父分类代码
  - `level` (INTEGER) - 分类级别
  - `name_cn` (TEXT) - 中文名称
  - `name_en` (TEXT) - 英文名称
  - `name_ru` (TEXT) - 俄文名称
  - `created_at` (TEXT) - 创建时间
- **category_distributor_mapping** - 分类分销商映射表

  - `id` (INTEGER, PK) - 映射ID
  - `category_id` (TEXT, NOT NULL) - 分类ID
  - `distributor_id` (TEXT, NOT NULL) - 分销商ID
  - `accountfor` (REAL) - 占比

#### 6. 用户、购物车和订单表 (11张)

- **users** - 用户表

  - `user_id` (TEXT, PK) - 用户ID
  - `email` (TEXT, NOT NULL) - 邮箱
  - `password_hash` (TEXT, NOT NULL) - 密码哈希
  - `role` (TEXT, NOT NULL) - 角色
  - `created_at` (TIMESTAMP) - 创建时间
- **user_addresses** - 用户地址表

  - `address_id` (INTEGER, PK) - 地址ID
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `address_type` (TEXT, NOT NULL) - 地址类型 ('shipping' 或 'billing')
  - `is_default` (BOOLEAN) - 是否默认地址
  - `first_name` (TEXT) - 名
  - `last_name` (TEXT) - 姓
  - `phone` (TEXT) - 电话号码
  - `street_address` (TEXT) - 街道地址
  - `apt_suite_building` (TEXT) - 公寓/套房/建筑物
  - `country_region` (TEXT) - 国家/地区
  - `postal_code` (TEXT) - 邮政编码
  - `state_province` (TEXT) - 州/省
  - `city` (TEXT) - 城市
  - `company_name` (TEXT) - 公司名称
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间
- **contact_information** - 联系人信息表

  - `contact_id` (INTEGER, PK) - 联系人ID
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `username` (TEXT) - 用户名
  - `email` (TEXT) - 邮箱
  - `is_default` (BOOLEAN) - 是否默认联系人
  - `first_name` (TEXT) - 名
  - `last_name` (TEXT) - 姓
  - `phone` (TEXT) - 电话号码
  - `job_role` (TEXT) - 职位角色
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间
- **compliance_statement** - 合规声明表

  - `compliance_id` (INTEGER, PK) - 合规声明ID
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `order_id` (TEXT) - 订单ID
  - `compliance_confirmed` (BOOLEAN) - 合规确认状态
  - `resell_components` (BOOLEAN) - 是否转售组件
  - `ultimate_consignee` (TEXT) - 最终收货人
  - `country` (TEXT) - 国家
  - `application_type` (TEXT) - 应用类型
  - `additional_information` (TEXT) - 附加信息
  - `is_default` (BOOLEAN) - 是否默认声明
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间
- **cart** - 购物车表

  - `cart_id` (INTEGER, PK) - 购物车ID
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `product_id` (TEXT, NOT NULL) - 产品ID
  - `model_number` (TEXT) - 产品型号
  - `brand` (TEXT) - 品牌
  - `description` (TEXT) - 产品描述
  - `unit_price` (REAL) - 单价
  - `stock_quantity` (INTEGER) - 库存数量
  - `cart_quantity` (INTEGER) - 购物车数量
  - `added_at` (DATETIME) - 添加时间
  - `updated_at` (DATETIME) - 更新时间
- **orders** - 订单表 (新版本)

  - `order_id` (TEXT, PK) - 订单ID
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `order_date` (DATETIME) - 订单日期
  - `order_total` (REAL) - 订单总金额
  - `order_status` (TEXT) - 订单状态
  - `payment_timeout` (DATETIME) - 支付超时时间
  - `payment_method` (TEXT) - 支付方式
- **billing_info** - 账单信息表

  - `billing_id` (INTEGER, PK) - 账单ID
  - `order_id` (TEXT) - 订单ID (外键)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `billing_address` (TEXT) - 账单地址
  - `address_line1` (TEXT) - 地址行1
  - `address_line2` (TEXT) - 地址行2
  - `address_line3` (TEXT) - 地址行3
  - `country` (TEXT) - 国家
  - `postal_code` (TEXT) - 邮政编码
- **shipping_info** - 物流信息表

  - `shipping_id` (INTEGER, PK) - 物流ID
  - `order_id` (TEXT) - 订单ID (外键)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `shipping_address` (TEXT) - 物流地址
  - `address_line1` (TEXT) - 地址行1
  - `address_line2` (TEXT) - 地址行2
  - `address_line3` (TEXT) - 地址行3
  - `country` (TEXT) - 国家
  - `postal_code` (TEXT) - 邮政编码
  - `shipping_method` (TEXT) - 物流方式

- **order_address** - 用户地址表
 - `shipping_id` (INTEGER, PK) - 物流ID
- `order_id` (TEXT) - 订单ID
- `shipping_method` (TEXT) - 物流方式
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `address_type` (TEXT, NOT NULL) - 地址类型 ('shipping' 或 'billing')
  - `first_name` (TEXT) - 名
  - `last_name` (TEXT) - 姓
  - `phone` (TEXT) - 电话号码
  - `street_address` (TEXT) - 街道地址
  - `apt_suite_building` (TEXT) - 公寓/套房/建筑物
  - `country_region` (TEXT) - 国家/地区
  - `postal_code` (TEXT) - 邮政编码
  - `state_province` (TEXT) - 州/省
  - `city` (TEXT) - 城市
  - `company_name` (TEXT) - 公司名称
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间

- **order_items** - 订单明细表

  - `item_id` (INTEGER, PK) - 明细ID
  - `order_id` (TEXT) - 订单ID (外键)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `ce_id` (TEXT) - CE产品ID
  - `mfr_number` (TEXT) - 制造商编号
  - `manufacturer` (TEXT) - 制造商
  - `description` (TEXT) - 产品描述
  - `quantity` (INTEGER) - 数量
  - `unit_price` (REAL) - 单价
  - `ext_price` (REAL) - 扩展价格
- **order_fees** - 订单费用表

  - `fee_id` (INTEGER, PK) - 费用ID
  - `order_id` (TEXT) - 订单ID (外键)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `merchandise_total` (REAL) - 商品总额
  - `freight` (REAL) - 运费
  - `handling_fee` (REAL) - 手续费
  - `total` (REAL) - 总计
- **payment** - 支付记录表

  - `id` (INTEGER, PK) - 支付记录ID
  - `order_id` (TEXT, NOT NULL) - 订单ID (外键)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `payment_method` (TEXT, NOT NULL) - 支付方式 (PayPal, Credit Card, etc.)
  - `transaction_number` (TEXT, NOT NULL) - 交易号/事务ID
  - `amount` (DECIMAL(10,2), NOT NULL) - 支付金额
  - `payment_date` (DATETIME) - 支付时间 (默认当前时间)
  - `status` (INTEGER) - 支付状态 (0=待处理, 1=成功, 2=失败, 3=退款)
  - `remarks` (TEXT) - 备注信息
  - `created_at` (DATETIME) - 创建时间 (默认当前时间)
  - `updated_at` (DATETIME) - 更新时间 (默认当前时间)

#### 7. 询价和报价表 (3张)

- **requests** - 询价请求表

  - `id` (TEXT, PK) - 请求ID
  - `bom_id` (TEXT) - BOM ID
  - `user_id` (TEXT) - 用户ID
  - `type` (TEXT) - 请求类型
  - `status` (TEXT) - 状态
  - `contact_name` (TEXT) - 联系人姓名
  - `business_email` (TEXT) - 商务邮箱
  - `company_name` (TEXT) - 公司名称
  - `country` (TEXT) - 国家
  - `quantity` (INTEGER) - 数量
  - `model` (TEXT) - 型号
  - `brand_id` (TEXT) - 品牌ID
  - `brand_name` (TEXT) - 品牌名称
  - `target_price` (REAL) - 目标价格
  - `delivery_time` (TEXT) - 交货时间
  - `date_code` (TEXT) - 日期代码
  - `distribution_id` (TEXT) - 分销商ID
  - `distribution_name` (TEXT) - 分销商名称
  - `create_at` (DATETIME) - 创建时间
- **quote** - 报价表

  - `id` (TEXT, PK) - 报价ID
  - `request_id` (TEXT) - 请求ID
  - `bom_id` (TEXT) - BOM ID
  - `user_id` (TEXT) - 用户ID
  - `type` (TEXT) - 报价类型
  - `status` (TEXT) - 状态
  - `model` (TEXT) - 型号
  - `prices` (TEXT) - 价格信息
  - `shipping` (TEXT) - 运费信息
  - `quantity` (INTEGER) - 数量
  - `brand_id` (TEXT) - 品牌ID
  - `brand_name` (TEXT) - 品牌名称
  - `target_price` (REAL) - 目标价格
  - `delivery_time` (TEXT) - 交货时间
  - `date_code` (TEXT) - 日期代码
  - `distribution_id` (TEXT) - 分销商ID
  - `distribution_name` (TEXT) - 分销商名称
  - `create_at` (DATETIME) - 创建时间
- **newly_quota** - 新报价表

  - `id` (INTEGER, PK) - 报价ID
  - `product_id` (TEXT) - 产品ID
  - `model` (TEXT, NOT NULL) - 型号
  - `brand_id` (TEXT) - 品牌ID
  - `brand_name_cn` (TEXT) - 品牌中文名
  - `brand_name_en` (TEXT) - 品牌英文名
  - `brand_name_ru` (TEXT) - 品牌俄文名
  - `domestic` (TEXT) - 是否国产
  - `distributor_id` (TEXT) - 分销商ID
  - `name_cn` (TEXT) - 中文名称
  - `name_en` (TEXT) - 英文名称
  - `name_ru` (TEXT) - 俄文名称
  - `logo` (TEXT) - Logo
  - `address_cn` (TEXT) - 中文地址
  - `address_en` (TEXT) - 英文地址
  - `address_ru` (TEXT) - 俄文地址
  - `create_at` (DATETIME) - 创建时间

#### 8. BOM管理表 (2张)

- **bom_main** - BOM主记录表

  - `bom_id` (TEXT, PK) - BOM ID (uuid格式)
  - `bom_name` (TEXT, NOT NULL) - BOM名称
  - `status` (INTEGER) - 状态 (默认0)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `file_url` (TEXT) - 文件URL
  - `item_count` (INTEGER) - 项目总数 (默认0)
  - `success_count` (INTEGER) - 成功匹配数 (默认0)
  - `exact_match_count` (INTEGER) - 精确匹配数 (默认0)
  - `partial_match_count` (INTEGER) - 部分匹配数 (默认0)
  - `no_match_count` (INTEGER) - 无匹配数 (默认0)
  - `est_total_price` (REAL) - 预估总价 (默认0)
  - `each_price` (REAL) - 单价 (默认0)
  - `priority_matching` (INTEGER) - 优先匹配 (默认0)
  - `quantity_multiplier` (INTEGER) - 数量倍数 (默认1)
  - `create_at` (DATETIME) - 创建时间 (默认当前时间)
  - `update_at` (DATETIME) - 更新时间 (默认当前时间)
- **bom_items** - BOM明细表

  - `bom_items_id` (TEXT, PK) - BOM明细ID (uuid格式)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `bom_id` (INTEGER, NOT NULL) - BOM ID (外键关联bom_main表)
  - `product_code` (TEXT) - 产品代码
  - `customer_part_number` (TEXT) - 客户零件号
  - `match_status` (INTEGER) - 匹配状态 (默认0)
  - `requested_qty` (INTEGER) - 请求数量 (默认0)
  - `order_qty` (INTEGER) - 订单数量 (默认0)
  - `stock_availability` (TEXT) - 库存可用性
  - `target_price` (REAL) - 目标价格
  - `lead_time` (TEXT) - 交货时间
  - `unit_price` (REAL) - 单价 (默认0)
  - `ext_price` (REAL) - 扩展价格 (默认0)
  - `offer_availability` (TEXT) - 报价可用性
  - `packaging_choice` (TEXT) - 包装选择
  - `notes` (TEXT) - 备注
  - `create_at` (DATETIME) - 创建时间 (默认当前时间)
  - `update_at` (DATETIME) - 更新时间 (默认当前时间)

#### 9. 内容管理表 (5张)

- **blog** - 博客表

  - `id` (INTEGER, PK) - 博客ID
  - `type` (INTEGER) - 类型
  - `status` (INTEGER) - 状态
  - `title` (TEXT, NOT NULL) - 标题
  - `subtitle` (TEXT) - 副标题
  - `description` (TEXT) - 描述
  - `cover_image` (TEXT) - 封面图片
  - `content_markdown` (TEXT) - Markdown内容
  - `tags` (TEXT) - 标签
  - `category` (TEXT) - 分类
  - `location` (TEXT) - 位置
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间
  - `author_id` (TEXT, NOT NULL) - 作者ID
  - `view_count` (INTEGER) - 浏览次数
- **insights** - 洞察表

  - `id` (INTEGER, PK) - 洞察ID
  - `type` (INTEGER) - 类型
  - `status` (INTEGER) - 状态
  - `title` (TEXT, NOT NULL) - 标题
  - `subtitle` (TEXT) - 副标题
  - `description` (TEXT) - 描述
  - `cover_image` (TEXT) - 封面图片
  - `tags` (TEXT) - 标签
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间
  - `author_id` (TEXT, NOT NULL) - 作者ID
- **author** - 作者表

  - `author_id` (TEXT, PK) - 作者ID
  - `name` (TEXT, NOT NULL) - 姓名
  - `avatar` (TEXT) - 头像
  - `description` (TEXT) - 描述
  - `bio` (TEXT) - 简介
- **insights_blog_map** - 洞察博客映射表

  - `id` (INTEGER, PK) - 映射ID
  - `insights_id` (INTEGER) - 洞察ID
  - `blog_id` (INTEGER) - 博客ID
- **ads** - 广告表

  - `id` (INTEGER, PK) - 广告ID
  - `platform` (TEXT) - 平台
  - `page` (TEXT) - 页面
  - `page_location` (TEXT) - 页面位置
  - `tags` (TEXT) - 标签
  - `status` (TEXT) - 状态
  - `priority` (INTEGER) - 优先级
  - `img` (TEXT) - 图片
  - `img_alt` (TEXT) - 图片描述
  - `title` (TEXT) - 标题
  - `subtitle` (TEXT) - 副标题
  - `description` (TEXT) - 描述
  - `target_url` (TEXT) - 目标URL
  - `effective_time` (TEXT) - 有效时间
  - `create_at` (DATETIME) - 创建时间

#### 10. 系统管理表 (4张)

- **recommend** - 推荐表

  - `id` (INTEGER, PK) - 推荐ID
  - `keyword` (TEXT, NOT NULL) - 关键词
  - `date` (DATE, NOT NULL) - 日期
  - `created_at` (DATETIME) - 创建时间
- **subscribe** - 订阅表

  - `id` (INTEGER, PK) - 订阅ID
  - `email` (TEXT) - 邮箱
  - `create_at` (DATETIME) - 创建时间
- **tdkh** - TDK管理表

  - `id` (INTEGER, PK) - TDK ID
  - `platform` (TEXT, NOT NULL) - 平台
  - `url` (TEXT, NOT NULL) - URL
  - `title` (TEXT) - 标题
  - `description` (TEXT) - 描述
  - `keywords` (TEXT) - 关键词
  - `h1` (TEXT) - H1标签
  - `note` (TEXT) - 备注
  - `create_at` (DATETIME) - 创建时间
  - `update_at` (DATETIME) - 更新时间
- **product_index** - 产品索引表

  - `id` (INTEGER, PK) - 索引ID
  - `first_text` (TEXT) - 首字符
  - `product_id` (TEXT) - 产品ID
  - `model` (TEXT) - 型号
  - `url` (TEXT) - URL

### 表统计总结

- **产品相关表**: 5张 (products, distributor_products, discount_products, products202503, component_mapping)
- **品牌相关表**: 2张 (brands, brand_distributor_mapping)
- **分销商相关表**: 2张 (distributors, distributors-portal)
- **价格和库存表**: 3张 (price, stocks, parameters)
- **分类相关表**: 4张 (categories, product_category_map, popular-categories, category_distributor_mapping)
- **用户、购物车和订单表**: 11张 (users, user_addresses, contact_information, compliance_statement, cart, orders, billing_info, shipping_info, order_items, order_fees, payment)
- **询价和报价表**: 3张 (requests, quote, newly_quota)
- **BOM管理表**: 2张 (bom_main, bom_items)
- **内容管理表**: 5张 (blog, insights, author, insights_blog_map, ads)
- **系统管理表**: 4张 (recommend, subscribe, tdkh, product_index)

**总计**: 41张表

## 单表操作

### 1. 查询数据 (GET)

#### 基础查询

```bash
# 查询所有产品
curl -X GET "https://api.chinaelectron.com/api/table/products"

# 查询所有品牌
curl -X GET "https://api.chinaelectron.com/api/table/brands"

# 查询所有分销商
curl -X GET "https://api.chinaelectron.com/api/table/distributors"
```

#### 带条件查询

```bash
# 查询特定品牌的产品
curl -X GET "https://api.chinaelectron.com/api/table/products?where_brand_id=TI"

# 查询特定型号的产品
curl -X GET "https://api.chinaelectron.com/api/table/products?where_model=LM358"

# 查询国产品牌
curl -X GET "https://api.chinaelectron.com/api/table/brands?where_domestic=true"

# 查询特定用户的订单
curl -X GET "https://api.chinaelectron.com/api/table/orders?where_user_id=user123"

# 查询已完成的订单
curl -X GET "https://api.chinaelectron.com/api/table/orders?where_order_status=completed"

# 查询用户购物车
curl -X GET "https://api.chinaelectron.com/api/table/cart?where_user_id=user_001&order_by=added_at&order_dir=DESC"

# 查询用户地址
curl -X GET "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&order_by=created_at&order_dir=DESC"

# 查询用户默认收货地址
curl -X GET "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&where_address_type=shipping&where_is_default=1"

# 查询用户默认账单地址
curl -X GET "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&where_address_type=billing&where_is_default=1"

# 查询用户联系人信息
curl -X GET "https://api.chinaelectron.com/api/table/contact_information?where_user_id=user_001&order_by=created_at&order_dir=DESC"

# 查询用户默认联系人
curl -X GET "https://api.chinaelectron.com/api/table/contact_information?where_user_id=user_001&where_is_default=1"

# 查询用户合规声明
curl -X GET "https://api.chinaelectron.com/api/table/compliance_statement?where_user_id=user_001&order_by=created_at&order_dir=DESC"

# 查询特定订单的合规声明
curl -X GET "https://api.chinaelectron.com/api/table/compliance_statement?where_order_id=ORD20250627001"

# 查询用户默认合规声明模板
curl -X GET "https://api.chinaelectron.com/api/table/compliance_statement?where_user_id=user_001&where_is_default=1"

# 查询用户支付记录
curl -X GET "https://api.chinaelectron.com/api/table/payment?where_user_id=user_001&order_by=payment_date&order_dir=DESC"

# 查询特定订单的支付记录
curl -X GET "https://api.chinaelectron.com/api/table/payment?where_order_id=ORD20250627001"

# 查询成功的支付记录
curl -X GET "https://api.chinaelectron.com/api/table/payment?where_status=1&order_by=payment_date&order_dir=DESC"

# 查询PayPal支付记录
curl -X GET "https://api.chinaelectron.com/api/table/payment?where_payment_method=PayPal&order_by=payment_date&order_dir=DESC"

# 查询特定交易号的支付记录
curl -X GET "https://api.chinaelectron.com/api/table/payment?where_transaction_number=9XF7978138827292X"

# 查询组件编码映射
curl -X GET "https://api.chinaelectron.com/api/table/component_mapping"

# 查询特定LCSC编码的映射
curl -X GET "https://api.chinaelectron.com/api/table/component_mapping?where_lcsc_code=C15849"

# 查询特定CE编码的映射
curl -X GET "https://api.chinaelectron.com/api/table/component_mapping?where_ce_code=CMA100101416"

# 按创建时间排序查询映射
curl -X GET "https://api.chinaelectron.com/api/table/component_mapping?order_by=created_at&order_dir=DESC&limit=100"

# 查询用户的BOM列表
curl -X GET "https://api.chinaelectron.com/api/table/bom_main?where_user_id=user_001&order_by=create_at&order_dir=DESC"

# 查询特定BOM的详细信息
curl -X GET "https://api.chinaelectron.com/api/table/bom_main?where_bom_id=1"

# 查询BOM的明细项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&order_by=create_at&order_dir=ASC"

# 查询用户特定BOM的明细项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_user_id=user_001&where_bom_id=1"

# 查询匹配成功的BOM项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_match_status=1"

# 查询无匹配的BOM项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_match_status=0"

# 查询特定产品代码的BOM项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_product_code=CMA100101416"

# 按扩展价格排序查询BOM项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&order_by=ext_price&order_dir=DESC"

# 查询高价值BOM项目（扩展价格大于1.0）
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_ext_price=>1.0"

# 查询库存不足的BOM项目
curl -X GET "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_stock_availability=Limited Stock"
```

#### 分页和排序

```bash
# 按更新时间倒序查询产品，分页显示
curl -X GET "https://api.chinaelectron.com/api/table/products?order_by=updated_at&order_dir=DESC&limit=50&offset=0"

# 按创建时间查询订单
curl -X GET "https://api.chinaelectron.com/api/table/orders?order_by=created_at&order_dir=DESC&limit=20&offset=0"

# 按价格查询价格表
curl -X GET "https://api.chinaelectron.com/api/table/price?order_by=price&order_dir=ASC&limit=100"
```

**返回示例:**

```json
{
  "success": true,
  "data": [
    {
      "product_id": "CMA100101416",
      "model": "LM358",
      "brand_id": "TI",
      "price_key": "CMA100101416",
      "stock_key": "CMA100101416",
      "description": "双运算放大器",
      "updated_at": "2025-06-27 10:30:00"
    },
    {
      "product_id": "CMA100101417",
      "model": "LM324",
      "brand_id": "TI",
      "price_key": "CMA100101417",
      "stock_key": "CMA100101417",
      "description": "四运算放大器",
      "updated_at": "2025-06-27 11:00:00"
    }
  ],
  "count": 2,
  "meta": {
    "duration": 0.123,
    "rows_read": 2,
    "rows_written": 0
  }
}
```

### 2. 插入数据 (POST)

#### 单条插入

```bash
# 插入新产品
curl -X POST "https://api.chinaelectron.com/api/table/products" \
  -H "Content-Type: application/json" \
  -d '{
    "product_id": "CMA100101418",
    "model": "LM741",
    "brand_id": "TI",
    "price_key": "CMA100101418",
    "stock_key": "CMA100101418",
    "description": "单运算放大器"
  }'

# 插入新品牌
curl -X POST "https://api.chinaelectron.com/api/table/brands" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "ANALOG",
    "name_cn": "亚德诺",
    "name_en": "Analog Devices",
    "website": "https://www.analog.com",
    "domestic": "false",
    "area": "美国"
  }'

# 插入新用户
curl -X POST "https://api.chinaelectron.com/api/table/users" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "email": "<EMAIL>",
    "password_hash": "hashed_password",
    "role": "buyer"
  }'

# 添加用户地址
curl -X POST "https://api.chinaelectron.com/api/table/user_addresses" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "address_type": "shipping",
    "is_default": true,
    "first_name": "张",
    "last_name": "工程师",
    "phone": "+86-138-0013-8000",
    "street_address": "深圳湾科技生态园10栋A座",
    "apt_suite_building": "1001室",
    "country_region": "中国",
    "postal_code": "518057",
    "state_province": "广东省",
    "city": "深圳市",
    "company_name": "科技有限公司"
  }'

# 添加账单地址
curl -X POST "https://api.chinaelectron.com/api/table/user_addresses" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "address_type": "billing",
    "is_default": true,
    "first_name": "张",
    "last_name": "工程师",
    "phone": "+86-138-0013-8000",
    "street_address": "深圳湾科技生态园10栋A座",
    "apt_suite_building": "1001室",
    "country_region": "中国",
    "postal_code": "518057",
    "state_province": "广东省",
    "city": "深圳市",
    "company_name": "科技有限公司"
  }'

# 添加联系人信息
curl -X POST "https://api.chinaelectron.com/api/table/contact_information" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "username": "engineer_zhang",
    "email": "<EMAIL>",
    "is_default": true,
    "first_name": "张",
    "last_name": "工程师",
    "phone": "+86-138-0013-8000",
    "job_role": "采购工程师"
  }'

# 添加备用联系人
curl -X POST "https://api.chinaelectron.com/api/table/contact_information" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "username": "manager_li",
    "email": "<EMAIL>",
    "is_default": false,
    "first_name": "李",
    "last_name": "经理",
    "phone": "+86-139-0013-9000",
    "job_role": "采购经理"
  }'

# 添加合规声明
curl -X POST "https://api.chinaelectron.com/api/table/compliance_statement" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "order_id": "ORD20250627001",
    "compliance_confirmed": true,
    "resell_components": false,
    "ultimate_consignee": "深圳科技有限公司",
    "country": "中国",
    "application_type": "工业自动化设备",
    "additional_information": "用于生产线控制系统，非军用用途",
    "is_default": true
  }'

# 添加默认合规声明模板
curl -X POST "https://api.chinaelectron.com/api/table/compliance_statement" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "compliance_confirmed": true,
    "resell_components": false,
    "ultimate_consignee": "深圳科技有限公司",
    "country": "中国",
    "application_type": "消费电子产品",
    "additional_information": "用于消费电子产品制造，符合出口管制要求",
    "is_default": false
  }'

# 添加商品到购物车
curl -X POST "https://api.chinaelectron.com/api/table/cart" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "product_id": "CMA100101416",
    "model_number": "LM358",
    "brand": "Texas Instruments",
    "description": "双运算放大器",
    "unit_price": 0.25,
    "stock_quantity": 10000,
    "cart_quantity": 100
  }'

# 创建新订单
curl -X POST "https://api.chinaelectron.com/api/table/orders" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD20250627001",
    "user_id": "user_001",
    "order_date": "2025-06-27 14:30:00",
    "order_total": 299.99,
    "order_status": "pending",
    "payment_timeout": "2025-06-28 14:30:00",
    "payment_method": "credit_card"
  }'

# 添加订单明细
curl -X POST "https://api.chinaelectron.com/api/table/order_items" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD20250627001",
    "user_id": "user_001",
    "ce_id": "CMA100101416",
    "mfr_number": "LM358",
    "manufacturer": "Texas Instruments",
    "description": "双运算放大器",
    "quantity": 100,
    "unit_price": 0.25,
    "ext_price": 25.00
  }'

# 添加账单信息
curl -X POST "https://api.chinaelectron.com/api/table/billing_info" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD20250627001",
    "user_id": "user_001",
    "billing_address": "深圳市南山区科技园",
    "address_line1": "深圳湾科技生态园",
    "address_line2": "10栋A座",
    "address_line3": "1001室",
    "country": "中国",
    "postal_code": "518057"
  }'

# 添加物流信息
curl -X POST "https://api.chinaelectron.com/api/table/shipping_info" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD20250627001",
    "user_id": "user_001",
    "shipping_address": "深圳市南山区科技园",
    "address_line1": "深圳湾科技生态园",
    "address_line2": "10栋A座",
    "address_line3": "1001室",
    "country": "中国",
    "postal_code": "518057",
    "shipping_method": "顺丰快递"
  }'

# 添加订单费用
curl -X POST "https://api.chinaelectron.com/api/table/order_fees" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD20250627001",
    "user_id": "user_001",
    "merchandise_total": 275.00,
    "freight": 20.00,
    "handling_fee": 4.99,
    "total": 299.99
  }'

# 添加支付记录
curl -X POST "https://api.chinaelectron.com/api/table/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "order_id": "ORD20250627001",
    "user_id": "user_001",
    "payment_method": "PayPal",
    "transaction_number": "9XF7978138827292X",
    "amount": 299.99,
    "payment_date": "2025-06-27 15:30:00",
    "status": 1,
    "remarks": "PayPal payment completed successfully"
  }'

# 添加组件编码映射
curl -X POST "https://api.chinaelectron.com/api/table/component_mapping" \
  -H "Content-Type: application/json" \
  -d '{
    "lcsc_code": "C15849",
    "ce_code": "CMA100101416"
  }'

# 批量添加组件编码映射
curl -X POST "https://api.chinaelectron.com/api/table/component_mapping" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "lcsc_code": "C15849",
      "ce_code": "CMA100101416"
    },
    {
      "lcsc_code": "C7950",
      "ce_code": "CMA100101417"
    },
    {
      "lcsc_code": "C7379",
      "ce_code": "CMA100101418"
    }
  ]'

# 创建BOM主记录
curl -X POST "https://api.chinaelectron.com/api/table/bom_main" \
  -H "Content-Type: application/json" \
  -d '{
    "bom_name": "智能家居控制器BOM",
    "status": 0,
    "user_id": "user_001",
    "file_url": "https://example.com/bom/smart_home_controller.xlsx",
    "item_count": 25,
    "success_count": 20,
    "exact_match_count": 18,
    "partial_match_count": 2,
    "no_match_count": 5,
    "est_total_price": 125.50,
    "each_price": 5.02,
    "priority_matching": 1,
    "quantity_multiplier": 100
  }'

# 添加BOM明细项目
curl -X POST "https://api.chinaelectron.com/api/table/bom_items" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "bom_id": 1,
    "product_code": "CMA100101416",
    "customer_part_number": "U1",
    "match_status": 1,
    "requested_qty": 2,
    "order_qty": 2,
    "stock_availability": "In Stock",
    "target_price": 0.25,
    "lead_time": "2-3 weeks",
    "unit_price": 0.22,
    "ext_price": 0.44,
    "offer_availability": "Available",
    "packaging_choice": "Tape & Reel",
    "notes": "双运算放大器，用于信号处理"
  }'

# 批量添加BOM明细项目
curl -X POST "https://api.chinaelectron.com/api/table/bom_items" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "user_id": "user_001",
      "bom_id": 1,
      "product_code": "CMA100101417",
      "customer_part_number": "U2",
      "match_status": 1,
      "requested_qty": 1,
      "order_qty": 1,
      "stock_availability": "In Stock",
      "target_price": 0.30,
      "lead_time": "2-3 weeks",
      "unit_price": 0.28,
      "ext_price": 0.28,
      "offer_availability": "Available",
      "packaging_choice": "Tube",
      "notes": "四运算放大器，用于多路信号处理"
    },
    {
      "user_id": "user_001",
      "bom_id": 1,
      "product_code": "CMA100101418",
      "customer_part_number": "U3",
      "match_status": 2,
      "requested_qty": 4,
      "order_qty": 4,
      "stock_availability": "Limited Stock",
      "target_price": 0.15,
      "lead_time": "4-6 weeks",
      "unit_price": 0.18,
      "ext_price": 0.72,
      "offer_availability": "Limited",
      "packaging_choice": "Cut Tape",
      "notes": "比较器，部分匹配替代料"
    }
  ]'
```

**返回示例:**

```json
{
  "success": true,
  "inserted_id": "CMA100101418",
  "changes": 1
}
```

#### 批量插入（通过单表接口）

```bash
# 批量插入产品
curl -X POST "https://api.chinaelectron.com/api/table/products" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "product_id": "CMA100101419",
      "model": "LM393",
      "brand_id": "TI",
      "description": "双比较器"
    },
    {
      "product_id": "CMA100101420",
      "model": "LM339",
      "brand_id": "TI",
      "description": "四比较器"
    }
  ]'

# 批量插入价格信息
curl -X POST "https://api.chinaelectron.com/api/table/price" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "code": "CMA100101416",
      "quantity": 100,
      "price": 0.25
    },
    {
      "code": "CMA100101416",
      "quantity": 1000,
      "price": 0.20
    }
  ]'
```

### 3. 更新数据 (PUT)

```bash
# 更新产品信息
curl -X PUT "https://api.chinaelectron.com/api/table/products?where_product_id=CMA100101416" \
  -H "Content-Type: application/json" \
  -d '{
    "description": "双运算放大器 - 更新版本",
    "price_key": "CMA100101416"
  }'

# 更新品牌信息
curl -X PUT "https://api.chinaelectron.com/api/table/brands?where_id=TI" \
  -H "Content-Type: application/json" \
  -d '{
    "website": "https://www.ti.com.cn",
    "introduction_cn": "德州仪器是全球领先的半导体公司"
  }'

# 更新订单状态
curl -X PUT "https://api.chinaelectron.com/api/table/orders?where_order_id=order_001" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "shipped"
  }'

# 更新库存信息
curl -X PUT "https://api.chinaelectron.com/api/table/stocks?where_code=CMA100101416" \
  -H "Content-Type: application/json" \
  -d '{
    "stocks": 5000
  }'

# 更新购物车商品数量
curl -X PUT "https://api.chinaelectron.com/api/table/cart?where_cart_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "cart_quantity": 200,
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新支付记录状态
curl -X PUT "https://api.chinaelectron.com/api/table/payment?where_transaction_number=9XF7978138827292X" \
  -H "Content-Type: application/json" \
  -d '{
    "status": 1,
    "remarks": "Payment confirmed and processed",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新支付记录备注
curl -X PUT "https://api.chinaelectron.com/api/table/payment?where_order_id=ORD20250627001" \
  -H "Content-Type: application/json" \
  -d '{
    "remarks": "Payment processed successfully via PayPal",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新购物车商品价格
curl -X PUT "https://api.chinaelectron.com/api/table/cart?where_user_id=user_001&where_product_id=CMA100101416" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_price": 0.22,
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新用户地址信息
curl -X PUT "https://api.chinaelectron.com/api/table/user_addresses?where_address_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+86-138-0013-8001",
    "street_address": "深圳湾科技生态园11栋B座",
    "apt_suite_building": "2001室",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 设置默认地址（先取消其他默认地址，再设置新的默认地址）
curl -X PUT "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&where_address_type=shipping&where_is_default=1" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": false,
    "updated_at": "2025-06-27 15:30:00"
  }'

curl -X PUT "https://api.chinaelectron.com/api/table/user_addresses?where_address_id=2" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": true,
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新联系人信息
curl -X PUT "https://api.chinaelectron.com/api/table/contact_information?where_contact_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+86-138-0013-8001",
    "email": "<EMAIL>",
    "job_role": "高级采购工程师",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 设置默认联系人（先取消其他默认联系人，再设置新的默认联系人）
curl -X PUT "https://api.chinaelectron.com/api/table/contact_information?where_user_id=user_001&where_is_default=1" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": false,
    "updated_at": "2025-06-27 15:30:00"
  }'

curl -X PUT "https://api.chinaelectron.com/api/table/contact_information?where_contact_id=2" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": true,
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新合规声明信息
curl -X PUT "https://api.chinaelectron.com/api/table/compliance_statement?where_compliance_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "ultimate_consignee": "深圳科技有限公司（更新）",
    "application_type": "工业自动化设备（升级版）",
    "additional_information": "用于生产线控制系统升级，非军用用途，符合最新出口管制要求",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 确认合规声明
curl -X PUT "https://api.chinaelectron.com/api/table/compliance_statement?where_compliance_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "compliance_confirmed": true,
    "updated_at": "2025-06-27 15:30:00"
  }'

# 设置默认合规声明模板
curl -X PUT "https://api.chinaelectron.com/api/table/compliance_statement?where_user_id=user_001&where_is_default=1" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": false,
    "updated_at": "2025-06-27 15:30:00"
  }'

curl -X PUT "https://api.chinaelectron.com/api/table/compliance_statement?where_compliance_id=2" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": true,
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新组件编码映射
curl -X PUT "https://api.chinaelectron.com/api/table/component_mapping?where_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "ce_code": "CMA100101419",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新特定LCSC编码的映射
curl -X PUT "https://api.chinaelectron.com/api/table/component_mapping?where_lcsc_code=C15849" \
  -H "Content-Type: application/json" \
  -d '{
    "ce_code": "CMA100101420",
    "updated_at": "2025-06-27 15:30:00"
  }'

# 更新BOM主记录信息
curl -X PUT "https://api.chinaelectron.com/api/table/bom_main?where_bom_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "bom_name": "智能家居控制器BOM v2.0",
    "status": 1,
    "item_count": 28,
    "success_count": 25,
    "exact_match_count": 22,
    "partial_match_count": 3,
    "no_match_count": 3,
    "est_total_price": 145.75,
    "each_price": 5.21,
    "update_at": "2025-06-27 15:30:00"
  }'

# 更新BOM明细项目信息
curl -X PUT "https://api.chinaelectron.com/api/table/bom_items?where_bom_items_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "match_status": 1,
    "order_qty": 5,
    "unit_price": 0.20,
    "ext_price": 1.00,
    "stock_availability": "In Stock",
    "offer_availability": "Available",
    "packaging_choice": "Tape & Reel",
    "notes": "价格已更新，库存充足",
    "update_at": "2025-06-27 15:30:00"
  }'

# 批量更新BOM项目的匹配状态
curl -X PUT "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_match_status=0" \
  -H "Content-Type: application/json" \
  -d '{
    "match_status": 2,
    "notes": "已找到替代料",
    "update_at": "2025-06-27 15:30:00"
  }'

# 更新BOM项目的价格信息
curl -X PUT "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_product_code=CMA100101416" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_price": 0.18,
    "ext_price": 0.36,
    "lead_time": "1-2 weeks",
    "update_at": "2025-06-27 15:30:00"
  }'

# 更新BOM项目的订单数量
curl -X PUT "https://api.chinaelectron.com/api/table/bom_items?where_bom_items_id=2" \
  -H "Content-Type: application/json" \
  -d '{
    "order_qty": 10,
    "ext_price": 2.80,
    "update_at": "2025-06-27 15:30:00"
  }'
```

**返回示例:**

```json
{
  "success": true,
  "changes": 1
}
```

### 4. 删除数据 (DELETE)

```bash
# 删除特定产品
curl -X DELETE "https://api.chinaelectron.com/api/table/products?where_product_id=CMA100101418"

# 删除过期的价格记录
curl -X DELETE "https://api.chinaelectron.com/api/table/price?where_code=CMA100101999"

# 删除已取消的订单
curl -X DELETE "https://api.chinaelectron.com/api/table/orders?where_status=cancelled&where_user_id=user_001"

# 删除特定分类的产品映射
curl -X DELETE "https://api.chinaelectron.com/api/table/product_category_map?where_category_code=obsolete_category"

# 删除购物车中的商品
curl -X DELETE "https://api.chinaelectron.com/api/table/cart?where_cart_id=1"

# 清空用户购物车
curl -X DELETE "https://api.chinaelectron.com/api/table/cart?where_user_id=user_001"

# 删除购物车中特定商品
curl -X DELETE "https://api.chinaelectron.com/api/table/cart?where_user_id=user_001&where_product_id=CMA100101416"

# 删除用户地址
curl -X DELETE "https://api.chinaelectron.com/api/table/user_addresses?where_address_id=3"

# 删除用户所有非默认地址
curl -X DELETE "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&where_is_default=0"

# 删除联系人信息
curl -X DELETE "https://api.chinaelectron.com/api/table/contact_information?where_contact_id=3"

# 删除用户所有非默认联系人
curl -X DELETE "https://api.chinaelectron.com/api/table/contact_information?where_user_id=user_001&where_is_default=0"

# 删除合规声明
curl -X DELETE "https://api.chinaelectron.com/api/table/compliance_statement?where_compliance_id=3"

# 删除特定订单的合规声明
curl -X DELETE "https://api.chinaelectron.com/api/table/compliance_statement?where_order_id=ORD20250627999"

# 删除用户所有非默认合规声明
curl -X DELETE "https://api.chinaelectron.com/api/table/compliance_statement?where_user_id=user_001&where_is_default=0"

# 删除特定组件编码映射
curl -X DELETE "https://api.chinaelectron.com/api/table/component_mapping?where_id=1"

# 删除特定LCSC编码的映射
curl -X DELETE "https://api.chinaelectron.com/api/table/component_mapping?where_lcsc_code=C15849"

# 删除特定CE编码的映射
curl -X DELETE "https://api.chinaelectron.com/api/table/component_mapping?where_ce_code=CMA100101416"

# 批量删除过期的映射记录（假设有过期逻辑）
curl -X DELETE "https://api.chinaelectron.com/api/table/component_mapping?where_created_at=2025-01-01"

# 删除特定BOM主记录
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_main?where_bom_id=1"

# 删除用户的特定BOM
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_main?where_user_id=user_001&where_bom_id=2"

# 删除用户所有已取消的BOM
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_main?where_user_id=user_001&where_status=-1"

# 删除特定BOM的明细项目
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1"

# 删除特定BOM明细项目
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_items?where_bom_items_id=5"

# 删除BOM中无匹配的项目
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_items?where_bom_id=1&where_match_status=0"

# 删除用户特定BOM的明细项目
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_items?where_user_id=user_001&where_bom_id=1&where_product_code=CMA100101999"

# 批量删除过期的BOM记录（假设有过期逻辑）
curl -X DELETE "https://api.chinaelectron.com/api/table/bom_main?where_create_at<2025-01-01"
```

**返回示例:**

```json
{
  "success": true,
  "deleted": 1
}
```

## 批量操作

### 1. 批量插入

```bash
# 批量插入产品
curl -X POST "https://api.chinaelectron.com/api/batch/insert/products" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "product_id": "CMA100101421",
      "model": "AD8066",
      "brand_id": "ANALOG",
      "description": "双高速运算放大器"
    },
    {
      "product_id": "CMA100101422",
      "model": "AD8067",
      "brand_id": "ANALOG",
      "description": "双高速运算放大器"
    }
  ]'

# 批量插入价格信息
curl -X POST "https://api.chinaelectron.com/api/batch/insert/price" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "code": "CMA100101416",
      "quantity": 1,
      "price": 0.50
    },
    {
      "code": "CMA100101416",
      "quantity": 10,
      "price": 0.45
    },
    {
      "code": "CMA100101416",
      "quantity": 100,
      "price": 0.35
    }
  ]'

# 批量插入分销商产品
curl -X POST "https://api.chinaelectron.com/api/batch/insert/distributor_products" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "product_id": "CMA100101416",
      "distributor_id": "DIGIKEY",
      "model": "LM358",
      "brand_id": "TI"
    },
    {
      "product_id": "CMA100101417",
      "distributor_id": "DIGIKEY",
      "model": "LM324",
      "brand_id": "TI"
    }
  ]'

# 批量插入订单明细
curl -X POST "https://api.chinaelectron.com/api/batch/insert/order_items" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "order_id": "ORD20250627001",
      "user_id": "user_001",
      "ce_id": "CMA100101416",
      "mfr_number": "LM358",
      "manufacturer": "Texas Instruments",
      "description": "双运算放大器",
      "quantity": 100,
      "unit_price": 0.25,
      "ext_price": 25.00
    },
    {
      "order_id": "ORD20250627001",
      "user_id": "user_001",
      "ce_id": "CMA100101417",
      "mfr_number": "LM324",
      "manufacturer": "Texas Instruments",
      "description": "四运算放大器",
      "quantity": 50,
      "unit_price": 0.30,
      "ext_price": 15.00
    }
  ]'

# 批量添加商品到购物车
curl -X POST "https://api.chinaelectron.com/api/batch/insert/cart" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "user_id": "user_001",
      "product_id": "CMA100101416",
      "model_number": "LM358",
      "brand": "Texas Instruments",
      "description": "双运算放大器",
      "unit_price": 0.25,
      "stock_quantity": 10000,
      "cart_quantity": 100
    },
    {
      "user_id": "user_001",
      "product_id": "CMA100101417",
      "model_number": "LM324",
      "brand": "Texas Instruments",
      "description": "四运算放大器",
      "unit_price": 0.30,
      "stock_quantity": 8000,
      "cart_quantity": 50
    }
  ]'

# 批量添加用户地址
curl -X POST "https://api.chinaelectron.com/api/batch/insert/user_addresses" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "user_id": "user_001",
      "address_type": "shipping",
      "is_default": false,
      "first_name": "李",
      "last_name": "经理",
      "phone": "+86-139-0013-9000",
      "street_address": "北京市朝阳区建国门外大街",
      "apt_suite_building": "国贸大厦A座1501室",
      "country_region": "中国",
      "postal_code": "100022",
      "state_province": "北京市",
      "city": "北京市",
      "company_name": "北京科技公司"
    },
    {
      "user_id": "user_001",
      "address_type": "billing",
      "is_default": false,
      "first_name": "王",
      "last_name": "总监",
      "phone": "+86-021-6888-9999",
      "street_address": "上海市浦东新区陆家嘴环路",
      "apt_suite_building": "上海中心大厦88层",
      "country_region": "中国",
      "postal_code": "200120",
      "state_province": "上海市",
      "city": "上海市",
      "company_name": "上海贸易公司"
    }
  ]'

# 批量添加联系人信息
curl -X POST "https://api.chinaelectron.com/api/batch/insert/contact_information" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "user_id": "user_001",
      "username": "director_wang",
      "email": "<EMAIL>",
      "is_default": false,
      "first_name": "王",
      "last_name": "总监",
      "phone": "+86-021-6888-9999",
      "job_role": "采购总监"
    },
    {
      "user_id": "user_001",
      "username": "assistant_liu",
      "email": "<EMAIL>",
      "is_default": false,
      "first_name": "刘",
      "last_name": "助理",
      "phone": "+86-020-8888-6666",
      "job_role": "采购助理"
    }
  ]'

# 批量添加合规声明
curl -X POST "https://api.chinaelectron.com/api/batch/insert/compliance_statement" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "user_id": "user_001",
      "compliance_confirmed": true,
      "resell_components": false,
      "ultimate_consignee": "北京科技公司",
      "country": "中国",
      "application_type": "通信设备",
      "additional_information": "用于5G基站设备制造，符合出口管制要求",
      "is_default": false
    },
    {
      "user_id": "user_001",
      "compliance_confirmed": true,
      "resell_components": true,
      "ultimate_consignee": "上海贸易公司",
      "country": "中国",
      "application_type": "汽车电子",
      "additional_information": "用于新能源汽车电子控制系统，转售给下游制造商",
      "is_default": false
    }
  ]'

# 批量添加组件编码映射
curl -X POST "https://api.chinaelectron.com/api/batch/insert/component_mapping" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "lcsc_code": "C15849",
      "ce_code": "CMA100101416"
    },
    {
      "lcsc_code": "C7950",
      "ce_code": "CMA100101417"
    },
    {
      "lcsc_code": "C7379",
      "ce_code": "CMA100101418"
    },
    {
      "lcsc_code": "C25804",
      "ce_code": "CMA100101419"
    },
    {
      "lcsc_code": "C7452",
      "ce_code": "CMA100101420"
    }
  ]'

# 批量创建BOM主记录
curl -X POST "https://api.chinaelectron.com/api/batch/insert/bom_main" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "bom_name": "智能门锁控制器BOM",
      "status": 0,
      "user_id": "user_001",
      "file_url": "https://example.com/bom/smart_lock.xlsx",
      "item_count": 15,
      "priority_matching": 1,
      "quantity_multiplier": 50
    },
    {
      "bom_name": "温度传感器模块BOM",
      "status": 0,
      "user_id": "user_001",
      "file_url": "https://example.com/bom/temp_sensor.xlsx",
      "item_count": 8,
      "priority_matching": 0,
      "quantity_multiplier": 200
    }
  ]'

# 批量添加BOM明细项目
curl -X POST "https://api.chinaelectron.com/api/batch/insert/bom_items" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "user_id": "user_001",
      "bom_id": 2,
      "product_code": "CMA100101416",
      "customer_part_number": "U1",
      "match_status": 1,
      "requested_qty": 1,
      "order_qty": 1,
      "target_price": 0.25,
      "unit_price": 0.22,
      "ext_price": 0.22
    },
    {
      "user_id": "user_001",
      "bom_id": 2,
      "product_code": "CMA100101417",
      "customer_part_number": "U2",
      "match_status": 1,
      "requested_qty": 2,
      "order_qty": 2,
      "target_price": 0.30,
      "unit_price": 0.28,
      "ext_price": 0.56
    }
  ]'
```

**返回示例:**

```json
{
  "success": true,
  "inserted_count": 2,
  "results": [
    {"inserted_id": "CMA100101421", "changes": 1},
    {"inserted_id": "CMA100101422", "changes": 1}
  ]
}
```

### 2. 批量更新

```bash
# 批量更新产品信息
curl -X POST "https://api.chinaelectron.com/api/batch/update/products" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "where": {"product_id": "CMA100101416"},
      "data": {"description": "双运算放大器 - 工业级", "price_key": "CMA100101416"}
    },
    {
      "where": {"product_id": "CMA100101417"},
      "data": {"description": "四运算放大器 - 汽车级"}
    }
  ]'

# 批量更新库存
curl -X POST "https://api.chinaelectron.com/api/batch/update/stocks" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "where": {"code": "CMA100101416"},
      "data": {"stocks": 10000}
    },
    {
      "where": {"code": "CMA100101417"},
      "data": {"stocks": 8000}
    }
  ]'

# 批量更新订单状态
curl -X POST "https://api.chinaelectron.com/api/batch/update/orders" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "where": {"order_id": "order_001"},
      "data": {"status": "processing"}
    },
    {
      "where": {"order_id": "order_002"},
      "data": {"status": "shipped"}
    }
  ]'

# 批量更新组件编码映射
curl -X POST "https://api.chinaelectron.com/api/batch/update/component_mapping" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "where": {"lcsc_code": "C15849"},
      "data": {"ce_code": "CMA100101421"}
    },
    {
      "where": {"lcsc_code": "C7950"},
      "data": {"ce_code": "CMA100101422"}
    }
  ]'

# 批量更新BOM主记录
curl -X POST "https://api.chinaelectron.com/api/batch/update/bom_main" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "where": {"bom_id": 1},
      "data": {
        "status": 1,
        "success_count": 22,
        "exact_match_count": 20,
        "partial_match_count": 2,
        "no_match_count": 3,
        "est_total_price": 156.80
      }
    },
    {
      "where": {"bom_id": 2},
      "data": {
        "status": 1,
        "success_count": 7,
        "exact_match_count": 6,
        "partial_match_count": 1,
        "no_match_count": 1,
        "est_total_price": 45.20
      }
    }
  ]'

# 批量更新BOM明细项目
curl -X POST "https://api.chinaelectron.com/api/batch/update/bom_items" \
  -H "Content-Type: application/json" \
  -d '[
    {
      "where": {"bom_items_id": 1},
      "data": {
        "match_status": 1,
        "unit_price": 0.19,
        "ext_price": 0.38,
        "stock_availability": "In Stock"
      }
    },
    {
      "where": {"bom_items_id": 2},
      "data": {
        "match_status": 1,
        "unit_price": 0.26,
        "ext_price": 0.52,
        "lead_time": "1-2 weeks"
      }
    }
  ]'
```

**返回示例:**

```json
{
  "success": true,
  "updated_count": 2,
  "results": [
    {"changes": 1, "where": {"product_id": "CMA100101416"}},
    {"changes": 1, "where": {"product_id": "CMA100101417"}}
  ]
}
```

### 3. 批量删除

```bash
# 批量删除过期产品
curl -X POST "https://api.chinaelectron.com/api/batch/delete/products" \
  -H "Content-Type: application/json" \
  -d '[
    {"product_id": "CMA100101999"},
    {"product_id": "CMA100101998"}
  ]'

# 批量删除过期价格记录
curl -X POST "https://api.chinaelectron.com/api/batch/delete/price" \
  -H "Content-Type: application/json" \
  -d '[
    {"code": "CMA100101999", "quantity": 100},
    {"code": "CMA100101998", "quantity": 1000}
  ]'

# 批量删除已取消的询价请求
curl -X POST "https://api.chinaelectron.com/api/batch/delete/requests" \
  -H "Content-Type: application/json" \
  -d '[
    {"id": "req_001", "status": "cancelled"},
    {"id": "req_002", "status": "cancelled"}
  ]'

# 批量删除组件编码映射
curl -X POST "https://api.chinaelectron.com/api/batch/delete/component_mapping" \
  -H "Content-Type: application/json" \
  -d '[
    {"lcsc_code": "C15849"},
    {"lcsc_code": "C7950"},
    {"ce_code": "CMA100101999"}
  ]'

# 批量删除BOM主记录
curl -X POST "https://api.chinaelectron.com/api/batch/delete/bom_main" \
  -H "Content-Type: application/json" \
  -d '[
    {"bom_id": 10},
    {"bom_id": 11},
    {"user_id": "user_001", "status": -1}
  ]'

# 批量删除BOM明细项目
curl -X POST "https://api.chinaelectron.com/api/batch/delete/bom_items" \
  -H "Content-Type: application/json" \
  -d '[
    {"bom_items_id": 25},
    {"bom_items_id": 26},
    {"bom_id": 5, "match_status": 0}
  ]'
```

**返回示例:**

```json
{
  "success": true,
  "deleted_count": 2,
  "results": [
    {"deleted": 1, "where": {"product_id": "CMA100101999"}},
    {"deleted": 1, "where": {"product_id": "CMA100101998"}}
  ]
}
```

## 多表联查

### 产品和品牌联查示例

```bash
# 查询产品及其品牌信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "p.product_id",
      "p.model",
      "p.description",
      "b.name_cn as brand_name_cn",
      "b.name_en as brand_name_en",
      "b.domestic",
      "b.area"
    ],
    "from": "products p",
    "joins": [
      {
        "type": "INNER",
        "table": "brands b",
        "on": "p.brand_id = b.id"
      }
    ],
    "where": {
      "b.domestic": "true"
    },
    "orderBy": "p.updated_at DESC",
    "limit": 100,
    "offset": 0
  }'
```

### 产品价格库存联查示例

```bash
# 查询产品及其价格和库存信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "p.product_id",
      "p.model",
      "p.description",
      "b.name_en as brand_name",
      "pr.quantity",
      "pr.price",
      "s.stocks"
    ],
    "from": "products p",
    "joins": [
      {
        "type": "INNER",
        "table": "brands b",
        "on": "p.brand_id = b.id"
      },
      {
        "type": "LEFT",
        "table": "price pr",
        "on": "p.product_id = pr.code"
      },
      {
        "type": "LEFT",
        "table": "stocks s",
        "on": "p.product_id = s.code"
      }
    ],
    "where": {
      "pr.quantity": {"gte": 100},
      "s.stocks": {"gt": 0}
    },
    "orderBy": ["pr.price ASC", "s.stocks DESC"],
    "limit": 50
  }'
```

### 用户订单联查示例

```bash
# 查询用户及其订单信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "u.user_id",
      "u.email",
      "u.role",
      "o.order_id",
      "o.status",
      "o.total_amount",
      "o.created_at"
    ],
    "from": "users u",
    "joins": [
      {
        "type": "INNER",
        "table": "orders o",
        "on": "u.user_id = o.user_id"
      }
    ],
    "where": {
      "u.role": "buyer",
      "o.total_amount": {"gt": 1000}
    },
    "orderBy": "o.created_at DESC",
    "limit": 50,
    "offset": 0
  }'
```

### 分销商产品联查示例

```bash
# 查询分销商及其产品信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "d.distributor_id",
      "d.name_en as distributor_name",
      "d.website",
      "dp.product_id",
      "dp.model",
      "b.name_en as brand_name",
      "b.domestic"
    ],
    "from": "distributors d",
    "joins": [
      {
        "type": "INNER",
        "table": "distributor_products dp",
        "on": "d.distributor_id = dp.distributor_id"
      },
      {
        "type": "INNER",
        "table": "brands b",
        "on": "dp.brand_id = b.id"
      }
    ],
    "where": {
      "d.membership_years": {"gte": 5},
      "b.domestic": "false"
    },
    "orderBy": ["d.membership_years DESC", "dp.model ASC"],
    "limit": 100
  }'
```

### 复杂询价报价联查示例

```bash
# 查询询价请求及相关报价信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "r.id as request_id",
      "r.contact_name",
      "r.company_name",
      "r.model",
      "r.quantity",
      "r.target_price",
      "q.id as quote_id",
      "q.prices",
      "q.shipping",
      "q.status as quote_status"
    ],
    "from": "requests r",
    "joins": [
      {
        "type": "LEFT",
        "table": "quote q",
        "on": "r.id = q.request_id"
      }
    ],
    "where": {
      "r.status": "pending",
      "r.create_at": {"gte": "2025-06-01"}
    },
    "orderBy": ["r.create_at DESC"],
    "limit": 50
  }'
```

### 组件编码映射联查示例

```bash
# 查询产品及其LCSC编码映射
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "p.product_id",
      "p.model",
      "p.description",
      "b.name_en as brand_name",
      "cm.lcsc_code",
      "cm.created_at as mapping_created"
    ],
    "from": "products p",
    "joins": [
      {
        "type": "INNER",
        "table": "brands b",
        "on": "p.brand_id = b.id"
      },
      {
        "type": "LEFT",
        "table": "component_mapping cm",
        "on": "p.product_id = cm.ce_code"
      }
    ],
    "where": {
      "cm.lcsc_code": {"ne": null}
    },
    "orderBy": ["cm.created_at DESC"],
    "limit": 100
  }'

# 查询LCSC编码对应的产品详细信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "cm.lcsc_code",
      "cm.ce_code",
      "p.model",
      "p.description",
      "b.name_en as brand_name",
      "pr.price",
      "pr.quantity as price_quantity",
      "s.stocks"
    ],
    "from": "component_mapping cm",
    "joins": [
      {
        "type": "INNER",
        "table": "products p",
        "on": "cm.ce_code = p.product_id"
      },
      {
        "type": "INNER",
        "table": "brands b",
        "on": "p.brand_id = b.id"
      },
      {
        "type": "LEFT",
        "table": "price pr",
        "on": "p.product_id = pr.code"
      },
      {
        "type": "LEFT",
        "table": "stocks s",
        "on": "p.product_id = s.code"
      }
    ],
    "where": {
      "cm.lcsc_code": "C15849"
    },
    "orderBy": ["pr.quantity ASC"]
  }'
```

### BOM管理联查示例

```bash
# 查询BOM主记录及其明细统计
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "bm.bom_id",
      "bm.bom_name",
      "bm.status",
      "bm.user_id",
      "bm.item_count",
      "bm.success_count",
      "bm.exact_match_count",
      "bm.partial_match_count",
      "bm.no_match_count",
      "bm.est_total_price",
      "COUNT(bi.bom_items_id) as actual_item_count",
      "SUM(bi.ext_price) as actual_total_price"
    ],
    "from": "bom_main bm",
    "joins": [
      {
        "type": "LEFT",
        "table": "bom_items bi",
        "on": "bm.bom_id = bi.bom_id"
      }
    ],
    "where": {
      "bm.user_id": "user_001"
    },
    "groupBy": ["bm.bom_id", "bm.bom_name", "bm.status", "bm.user_id", "bm.item_count", "bm.success_count", "bm.exact_match_count", "bm.partial_match_count", "bm.no_match_count", "bm.est_total_price"],
    "orderBy": "bm.create_at DESC",
    "limit": 50
  }'

# 查询BOM明细及其产品信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "bi.bom_items_id",
      "bi.bom_id",
      "bi.customer_part_number",
      "bi.match_status",
      "bi.requested_qty",
      "bi.order_qty",
      "bi.unit_price",
      "bi.ext_price",
      "bi.stock_availability",
      "p.product_id",
      "p.model",
      "p.description",
      "b.name_en as brand_name",
      "s.stocks as current_stock",
      "pr.price as current_price"
    ],
    "from": "bom_items bi",
    "joins": [
      {
        "type": "LEFT",
        "table": "products p",
        "on": "bi.product_code = p.product_id"
      },
      {
        "type": "LEFT",
        "table": "brands b",
        "on": "p.brand_id = b.id"
      },
      {
        "type": "LEFT",
        "table": "stocks s",
        "on": "p.product_id = s.code"
      },
      {
        "type": "LEFT",
        "table": "price pr",
        "on": "p.product_id = pr.code AND pr.quantity = 1"
      }
    ],
    "where": {
      "bi.bom_id": 1
    },
    "orderBy": ["bi.ext_price DESC"],
    "limit": 100
  }'

# 查询用户BOM汇总统计
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "bm.user_id",
      "COUNT(DISTINCT bm.bom_id) as total_boms",
      "SUM(bm.item_count) as total_items",
      "SUM(bm.success_count) as total_success",
      "SUM(bm.exact_match_count) as total_exact_match",
      "SUM(bm.partial_match_count) as total_partial_match",
      "SUM(bm.no_match_count) as total_no_match",
      "SUM(bm.est_total_price) as total_estimated_value",
      "AVG(bm.est_total_price) as avg_bom_value"
    ],
    "from": "bom_main bm",
    "where": {
      "bm.user_id": "user_001",
      "bm.status": {"gte": 0}
    },
    "groupBy": ["bm.user_id"]
  }'
```

**返回示例:**

```json
{
  "success": true,
  "data": [
    {
      "distributor_id": "DIGIKEY",
      "distributor_name": "Digi-Key Electronics",
      "website": "https://www.digikey.com",
      "product_id": "CMA100101416",
      "model": "LM358",
      "brand_name": "Texas Instruments",
      "domestic": "false"
    },
    {
      "distributor_id": "MOUSER",
      "distributor_name": "Mouser Electronics",
      "website": "https://www.mouser.com",
      "product_id": "CMA100101421",
      "model": "AD8066",
      "brand_name": "Analog Devices",
      "domestic": "false"
    }
  ],
  "count": 2,
  "query": "SELECT d.distributor_id, d.name_en as distributor_name, d.website, dp.product_id, dp.model, b.name_en as brand_name, b.domestic FROM distributors d INNER JOIN distributor_products dp ON d.distributor_id = dp.distributor_id INNER JOIN brands b ON dp.brand_id = b.id WHERE d.membership_years >= ? AND b.domestic = ? ORDER BY d.membership_years DESC, dp.model ASC LIMIT 100 OFFSET 0",
  "meta": {
    "duration": 0.456,
    "rows_read": 150,
    "rows_written": 0
  }
}
```

## 自定义SQL查询

### 产品统计查询

```bash
# 统计各品牌的产品数量
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT b.name_en as brand_name, b.domestic, COUNT(p.product_id) as product_count FROM brands b LEFT JOIN products p ON b.id = p.brand_id GROUP BY b.id, b.name_en, b.domestic ORDER BY product_count DESC",
    "params": []
  }'

# 查询特定品牌的产品总数
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT COUNT(*) as total_products FROM products WHERE brand_id = ?",
    "params": ["TI"]
  }'
```

### 价格分析查询

```bash
# 查询产品的价格区间分布
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT code, MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price, COUNT(*) as price_tiers FROM price WHERE quantity >= ? GROUP BY code HAVING COUNT(*) > 1 ORDER BY avg_price DESC",
    "params": [100]
  }'

# 查询库存充足的产品及其最低价格
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT p.product_id, p.model, b.name_en as brand, s.stocks, MIN(pr.price) as min_price FROM products p INNER JOIN brands b ON p.brand_id = b.id INNER JOIN stocks s ON p.product_id = s.code LEFT JOIN price pr ON p.product_id = pr.code WHERE s.stocks > ? GROUP BY p.product_id, p.model, b.name_en, s.stocks ORDER BY min_price ASC",
    "params": [1000]
  }'
```

### 业务统计查询

```bash
# 统计各分销商的产品数量
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT d.name_en as distributor_name, d.membership_years, COUNT(dp.product_id) as product_count FROM distributors d LEFT JOIN distributor_products dp ON d.distributor_id = dp.distributor_id GROUP BY d.distributor_id, d.name_en, d.membership_years ORDER BY product_count DESC",
    "params": []
  }'

# 查询最近的询价请求统计
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT DATE(create_at) as date, COUNT(*) as request_count, AVG(quantity) as avg_quantity, AVG(target_price) as avg_target_price FROM requests WHERE create_at >= ? GROUP BY DATE(create_at) ORDER BY date DESC",
    "params": ["2025-06-01"]
  }'
```

### 复杂分析查询

```bash
# 分析品牌在不同分销商的分布情况
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT b.name_en as brand_name, b.domestic, COUNT(DISTINCT dp.distributor_id) as distributor_count, COUNT(dp.product_id) as total_products FROM brands b INNER JOIN distributor_products dp ON b.id = dp.brand_id GROUP BY b.id, b.name_en, b.domestic HAVING distributor_count > ? ORDER BY distributor_count DESC, total_products DESC",
    "params": [2]
  }'

# 分析组件编码映射统计
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT COUNT(*) as total_mappings, COUNT(DISTINCT lcsc_code) as unique_lcsc_codes, COUNT(DISTINCT ce_code) as unique_ce_codes FROM component_mapping",
    "params": []
  }'

# 查询重复的LCSC编码映射
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT lcsc_code, COUNT(*) as mapping_count FROM component_mapping GROUP BY lcsc_code HAVING COUNT(*) > 1 ORDER BY mapping_count DESC",
    "params": []
  }'

# 查询最近添加的编码映射
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT cm.lcsc_code, cm.ce_code, p.model, b.name_en as brand_name, cm.created_at FROM component_mapping cm LEFT JOIN products p ON cm.ce_code = p.product_id LEFT JOIN brands b ON p.brand_id = b.id WHERE cm.created_at >= ? ORDER BY cm.created_at DESC",
    "params": ["2025-06-01"]
  }'
```

### BOM统计查询

```bash
# 统计用户BOM数量和状态分布
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT user_id, COUNT(*) as total_boms, SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as pending_boms, SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as completed_boms, SUM(CASE WHEN status = -1 THEN 1 ELSE 0 END) as cancelled_boms, AVG(est_total_price) as avg_bom_value FROM bom_main GROUP BY user_id ORDER BY total_boms DESC",
    "params": []
  }'

# 查询BOM匹配率统计
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT bom_id, bom_name, item_count, success_count, exact_match_count, partial_match_count, no_match_count, ROUND((success_count * 100.0 / item_count), 2) as success_rate, ROUND((exact_match_count * 100.0 / item_count), 2) as exact_match_rate FROM bom_main WHERE item_count > 0 ORDER BY success_rate DESC",
    "params": []
  }'

# 查询高价值BOM项目
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT bi.bom_id, bm.bom_name, bi.customer_part_number, bi.product_code, p.model, b.name_en as brand_name, bi.order_qty, bi.unit_price, bi.ext_price FROM bom_items bi INNER JOIN bom_main bm ON bi.bom_id = bm.bom_id LEFT JOIN products p ON bi.product_code = p.product_id LEFT JOIN brands b ON p.brand_id = b.id WHERE bi.ext_price > ? ORDER BY bi.ext_price DESC",
    "params": [10.0]
  }'

# 查询BOM项目匹配状态分布
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT bom_id, COUNT(*) as total_items, SUM(CASE WHEN match_status = 0 THEN 1 ELSE 0 END) as no_match, SUM(CASE WHEN match_status = 1 THEN 1 ELSE 0 END) as exact_match, SUM(CASE WHEN match_status = 2 THEN 1 ELSE 0 END) as partial_match, SUM(ext_price) as total_value FROM bom_items WHERE bom_id = ? GROUP BY bom_id",
    "params": [1]
  }'

# 查询用户最活跃的BOM（按更新时间）
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT bm.bom_id, bm.bom_name, bm.user_id, bm.item_count, bm.est_total_price, bm.create_at, bm.update_at, COUNT(bi.bom_items_id) as item_count_actual FROM bom_main bm LEFT JOIN bom_items bi ON bm.bom_id = bi.bom_id WHERE bm.user_id = ? GROUP BY bm.bom_id, bm.bom_name, bm.user_id, bm.item_count, bm.est_total_price, bm.create_at, bm.update_at ORDER BY bm.update_at DESC LIMIT ?",
    "params": ["user_001", 10]
  }'

# 查询库存不足的BOM项目
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT bi.bom_id, bm.bom_name, bi.customer_part_number, bi.product_code, p.model, bi.order_qty, s.stocks, bi.stock_availability FROM bom_items bi INNER JOIN bom_main bm ON bi.bom_id = bm.bom_id LEFT JOIN products p ON bi.product_code = p.product_id LEFT JOIN stocks s ON p.product_id = s.code WHERE (s.stocks < bi.order_qty OR bi.stock_availability LIKE '%Limited%' OR bi.stock_availability LIKE '%Out%') AND bi.match_status = 1 ORDER BY bi.ext_price DESC",
    "params": []
  }'
```

**返回示例:**

```json
{
  "success": true,
  "data": [
    {
      "brand_name": "Texas Instruments",
      "domestic": "false",
      "product_count": 15420
    },
    {
      "brand_name": "Analog Devices",
      "domestic": "false",
      "product_count": 8750
    },
    {
      "brand_name": "STMicroelectronics",
      "domestic": "false",
      "product_count": 6890
    }
  ],
  "count": 3,
  "meta": {
    "duration": 0.234,
    "rows_read": 500,
    "rows_written": 0
  }
}
```

## 错误处理

### 常见错误响应

#### 400 Bad Request

```json
{
  "error": "WHERE condition is required for UPDATE"
}
```

#### 404 Not Found

```json
{
  "error": "Route not found"
}
```

#### 500 Internal Server Error

```json
{
  "error": "Internal server error",
  "message": "no such table: non_existent_table"
}
```

## 查询参数说明

### WHERE 条件参数

- `where_{field}`: 等值查询
- 示例: `where_brand_id=TI&where_domestic=true`

### 排序参数

- `order_by`: 排序字段
- `order_dir`: 排序方向 (ASC/DESC，默认ASC)
- 示例: `order_by=updated_at&order_dir=DESC`

### 分页参数

- `limit`: 限制返回条数 (默认100，最大1000)
- `offset`: 偏移量 (默认0)
- 示例: `limit=50&offset=100`

### 多表联查操作符

- `gt`: 大于 - `{"price": {"gt": 100}}`
- `lt`: 小于 - `{"stocks": {"lt": 1000}}`
- `gte`: 大于等于 - `{"quantity": {"gte": 100}}`
- `lte`: 小于等于 - `{"price": {"lte": 50}}`
- `like`: 模糊匹配 - `{"model": {"like": "%LM%"}}`
- `ne`: 不等于 - `{"status": {"ne": "cancelled"}}`

### 常用字段说明

- **产品相关**: `product_id`, `model`, `brand_id`, `description`
  - `product_id`: 格式如 `CMA100101416`（唯一标识符）
  - `model`: 产品型号，如 `LM358`、`LM324`
- **组件编码映射相关**: `id`, `lcsc_code`, `ce_code`, `created_at`, `updated_at`
  - `lcsc_code`: LCSC平台的产品编码，如 `C15849`
  - `ce_code`: China Electron平台的产品编码，对应 `product_id`
- **品牌相关**: `id`, `name_cn`, `name_en`, `domestic`, `area`
- **分销商相关**: `distributor_id`, `name_cn`, `name_en`, `membership_years`
- **价格相关**: `code`, `quantity`, `price`, `created_at`
  - `code`: 关联产品ID，格式与 `product_id` 相同
- **库存相关**: `code`, `stocks`, `created_at`
  - `code`: 关联产品ID，格式与 `product_id` 相同
- **用户相关**: `user_id`, `email`, `role`, `created_at`
- **用户地址相关**: `address_id`, `user_id`, `address_type`, `is_default`, `first_name`, `last_name`, `phone`, `street_address`, `country_region`, `postal_code`, `city`
- **联系人信息相关**: `contact_id`, `user_id`, `username`, `email`, `is_default`, `first_name`, `last_name`, `phone`, `job_role`
- **合规声明相关**: `compliance_id`, `user_id`, `order_id`, `compliance_confirmed`, `resell_components`, `ultimate_consignee`, `country`, `application_type`, `is_default`
- **购物车相关**: `cart_id`, `user_id`, `product_id`, `model_number`, `brand`, `cart_quantity`, `unit_price`, `added_at`
- **订单相关**: `order_id`, `user_id`, `order_status`, `order_total`, `order_date`, `payment_method`
- **订单明细相关**: `item_id`, `order_id`, `ce_id`, `mfr_number`, `manufacturer`, `quantity`, `unit_price`
- **账单信息相关**: `billing_id`, `order_id`, `billing_address`, `country`, `postal_code`
- **物流信息相关**: `shipping_id`, `order_id`, `shipping_address`, `shipping_method`
- **订单费用相关**: `fee_id`, `order_id`, `merchandise_total`, `freight`, `handling_fee`, `total`

### 重要说明

- **产品ID格式**: 所有 `product_id` 都采用类似 `CMA100101416` 的格式
- **关联关系**: 价格表和库存表中的 `code` 字段直接对应产品表的 `product_id`
- **数据一致性**: 确保在操作价格和库存数据时使用正确的产品ID格式

## 注意事项

1. **CORS支持**: 所有 API 都支持 CORS，可以从浏览器直接调用
2. **安全限制**: 更新和删除操作必须提供 WHERE 条件，防止误操作
3. **批量限制**: 批量操作建议单次不超过 1000 条记录
4. **SQL安全**: 自定义 SQL 查询请谨慎使用，避免执行危险操作
5. **时间格式**: 所有时间字段建议使用 ISO 8601 格式 (YYYY-MM-DD HH:MM:SS)
6. **字符编码**: 所有请求和响应都使用 UTF-8 编码
7. **数据类型**:
   - TEXT 字段支持中文、英文、俄文
   - JSON 字段存储结构化数据
   - TIMESTAMP 字段自动处理时区
8. **性能优化**:
   - 使用索引字段进行查询可提高性能
   - 大量数据查询建议使用分页
   - 复杂联查建议限制返回字段数量

## 常见使用场景

### 1. 产品搜索

```bash
# 按型号搜索产品
curl -X GET "https://api.chinaelectron.com/api/table/products?where_model=LM358"

# 按品牌搜索产品
curl -X GET "https://api.chinaelectron.com/api/table/products?where_brand_id=TI&limit=50"

# 按产品ID搜索
curl -X GET "https://api.chinaelectron.com/api/table/products?where_product_id=CMA100101416"
```

### 2. 库存管理

```bash
# 查询低库存产品
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT * FROM stocks WHERE stocks < ?",
    "params": [100]
  }'
```

### 3. 价格分析

```bash
# 查询产品价格梯度
curl -X GET "https://api.chinaelectron.com/api/table/price?where_code=CMA100101416&order_by=quantity"
```

### 4. 组件编码映射管理

```bash
# 根据LCSC编码查找CE编码
curl -X GET "https://api.chinaelectron.com/api/table/component_mapping?where_lcsc_code=C15849"

# 根据CE编码查找LCSC编码
curl -X GET "https://api.chinaelectron.com/api/table/component_mapping?where_ce_code=CMA100101416"

# 批量导入编码映射
curl -X POST "https://api.chinaelectron.com/api/batch/insert/component_mapping" \
  -H "Content-Type: application/json" \
  -d '[
    {"lcsc_code": "C15849", "ce_code": "CMA100101416"},
    {"lcsc_code": "C7950", "ce_code": "CMA100101417"}
  ]'

# 查询编码映射统计
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT COUNT(*) as total_mappings FROM component_mapping",
    "params": []
  }'
```

### 4. 合规声明管理

```bash
# 查询用户所有合规声明
curl -X GET "https://api.chinaelectron.com/api/table/compliance_statement?where_user_id=user_001&order_by=created_at&order_dir=DESC"

# 查询用户默认合规声明模板
curl -X GET "https://api.chinaelectron.com/api/table/compliance_statement?where_user_id=user_001&where_is_default=1"

# 添加新合规声明
curl -X POST "https://api.chinaelectron.com/api/table/compliance_statement" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "order_id": "ORD20250627001",
    "compliance_confirmed": true,
    "resell_components": false,
    "ultimate_consignee": "深圳科技有限公司",
    "country": "中国",
    "application_type": "工业自动化设备",
    "additional_information": "用于生产线控制系统，非军用用途",
    "is_default": false
  }'

# 确认合规声明
curl -X PUT "https://api.chinaelectron.com/api/table/compliance_statement?where_compliance_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "compliance_confirmed": true
  }'

# 查询合规声明统计信息
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT application_type, COUNT(*) as statement_count, SUM(CASE WHEN compliance_confirmed = 1 THEN 1 ELSE 0 END) as confirmed_count FROM compliance_statement WHERE user_id = ? GROUP BY application_type",
    "params": ["user_001"]
  }'

# 查询订单及其合规声明信息
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "o.order_id",
      "o.order_date",
      "o.order_total",
      "o.order_status",
      "cs.compliance_id",
      "cs.compliance_confirmed",
      "cs.resell_components",
      "cs.ultimate_consignee",
      "cs.country",
      "cs.application_type",
      "cs.additional_information"
    ],
    "from": "orders o",
    "joins": [
      {
        "type": "LEFT",
        "table": "compliance_statement cs",
        "on": "o.order_id = cs.order_id"
      }
    ],
    "where": {
      "o.user_id": "user_001"
    },
    "orderBy": "o.order_date DESC"
  }'

# 使用默认合规声明模板创建订单合规声明
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "INSERT INTO compliance_statement (user_id, order_id, compliance_confirmed, resell_components, ultimate_consignee, country, application_type, additional_information, is_default) SELECT user_id, ?, compliance_confirmed, resell_components, ultimate_consignee, country, application_type, additional_information, 0 FROM compliance_statement WHERE user_id = ? AND is_default = 1",
    "params": ["ORD20250627002", "user_001"]
  }'
```

### 5. 联系人信息管理

```bash
# 查询用户所有联系人
curl -X GET "https://api.chinaelectron.com/api/table/contact_information?where_user_id=user_001&order_by=created_at&order_dir=DESC"

# 查询用户默认联系人
curl -X GET "https://api.chinaelectron.com/api/table/contact_information?where_user_id=user_001&where_is_default=1"

# 添加新联系人
curl -X POST "https://api.chinaelectron.com/api/table/contact_information" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "username": "engineer_zhang",
    "email": "<EMAIL>",
    "is_default": true,
    "first_name": "张",
    "last_name": "工程师",
    "phone": "+86-138-0013-8000",
    "job_role": "采购工程师"
  }'

# 设置默认联系人
curl -X PUT "https://api.chinaelectron.com/api/table/contact_information?where_contact_id=2" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": true
  }'

# 查询联系人统计信息
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT job_role, COUNT(*) as contact_count FROM contact_information WHERE user_id = ? GROUP BY job_role",
    "params": ["user_001"]
  }'

# 查询用户完整信息（包含默认联系人）
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "u.user_id",
      "u.email as user_email",
      "u.role",
      "ci.contact_id",
      "ci.username",
      "ci.email as contact_email",
      "ci.first_name",
      "ci.last_name",
      "ci.phone",
      "ci.job_role",
      "ci.created_at"
    ],
    "from": "users u",
    "joins": [
      {
        "type": "LEFT",
        "table": "contact_information ci",
        "on": "u.user_id = ci.user_id"
      }
    ],
    "where": {
      "u.user_id": "user_001",
      "ci.is_default": true
    }
  }'
```

### 6. 用户地址管理

```bash
# 查询用户所有地址
curl -X GET "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&order_by=created_at&order_dir=DESC"

# 查询用户默认收货地址
curl -X GET "https://api.chinaelectron.com/api/table/user_addresses?where_user_id=user_001&where_address_type=shipping&where_is_default=1"

# 添加新地址
curl -X POST "https://api.chinaelectron.com/api/table/user_addresses" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "address_type": "shipping",
    "is_default": false,
    "first_name": "张",
    "last_name": "工程师",
    "phone": "+86-138-0013-8000",
    "street_address": "深圳湾科技生态园10栋A座",
    "apt_suite_building": "1001室",
    "country_region": "中国",
    "postal_code": "518057",
    "state_province": "广东省",
    "city": "深圳市",
    "company_name": "科技有限公司"
  }'

# 设置默认地址
curl -X PUT "https://api.chinaelectron.com/api/table/user_addresses?where_address_id=2" \
  -H "Content-Type: application/json" \
  -d '{
    "is_default": true
  }'

# 查询地址统计信息
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT address_type, COUNT(*) as address_count FROM user_addresses WHERE user_id = ? GROUP BY address_type",
    "params": ["user_001"]
  }'

# 查询用户地址详细信息（用于订单创建）
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "u.user_id",
      "u.email",
      "ua.address_id",
      "ua.address_type",
      "ua.is_default",
      "ua.first_name",
      "ua.last_name",
      "ua.phone",
      "ua.street_address",
      "ua.apt_suite_building",
      "ua.country_region",
      "ua.postal_code",
      "ua.state_province",
      "ua.city",
      "ua.company_name"
    ],
    "from": "users u",
    "joins": [
      {
        "type": "INNER",
        "table": "user_addresses ua",
        "on": "u.user_id = ua.user_id"
      }
    ],
    "where": {
      "u.user_id": "user_001",
      "ua.is_default": true
    },
    "orderBy": "ua.address_type"
  }'
```

### 7. 购物车管理

```bash
# 查询用户购物车
curl -X GET "https://api.chinaelectron.com/api/table/cart?where_user_id=user_001&order_by=added_at&order_dir=DESC"

# 添加商品到购物车
curl -X POST "https://api.chinaelectron.com/api/table/cart" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user_001",
    "product_id": "CMA100101416",
    "model_number": "LM358",
    "brand": "Texas Instruments",
    "description": "双运算放大器",
    "unit_price": 0.25,
    "stock_quantity": 10000,
    "cart_quantity": 100
  }'

# 更新购物车商品数量
curl -X PUT "https://api.chinaelectron.com/api/table/cart?where_cart_id=1" \
  -H "Content-Type: application/json" \
  -d '{
    "cart_quantity": 200
  }'

# 查询购物车统计信息
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT COUNT(*) as item_count, SUM(cart_quantity * unit_price) as total_amount FROM cart WHERE user_id = ?",
    "params": ["user_001"]
  }'

# 查询购物车详细信息（包含产品信息）
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "c.cart_id",
      "c.product_id",
      "c.model_number",
      "c.brand",
      "c.description",
      "c.unit_price",
      "c.cart_quantity",
      "c.added_at",
      "s.stocks as available_stock",
      "p.updated_at as product_updated"
    ],
    "from": "cart c",
    "joins": [
      {
        "type": "LEFT",
        "table": "stocks s",
        "on": "c.product_id = s.code"
      },
      {
        "type": "LEFT",
        "table": "products p",
        "on": "c.product_id = p.product_id"
      }
    ],
    "where": {
      "c.user_id": "user_001"
    },
    "orderBy": "c.added_at DESC"
  }'

# 从购物车创建订单（转换购物车为订单明细）
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "INSERT INTO order_items (order_id, user_id, ce_id, mfr_number, manufacturer, description, quantity, unit_price, ext_price) SELECT ?, user_id, product_id, model_number, brand, description, cart_quantity, unit_price, (cart_quantity * unit_price) FROM cart WHERE user_id = ?",
    "params": ["ORD20250627002", "user_001"]
  }'

# 使用用户默认地址创建订单账单和物流信息
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "INSERT INTO billing_info (order_id, user_id, billing_address, address_line1, address_line2, address_line3, country, postal_code) SELECT ?, user_id, CONCAT(first_name, \" \", last_name), street_address, apt_suite_building, CONCAT(city, \", \", state_province), country_region, postal_code FROM user_addresses WHERE user_id = ? AND address_type = \"billing\" AND is_default = 1",
    "params": ["ORD20250627002", "user_001"]
  }'

curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "INSERT INTO shipping_info (order_id, user_id, shipping_address, address_line1, address_line2, address_line3, country, postal_code, shipping_method) SELECT ?, user_id, CONCAT(first_name, \" \", last_name), street_address, apt_suite_building, CONCAT(city, \", \", state_province), country_region, postal_code, \"顺丰快递\" FROM user_addresses WHERE user_id = ? AND address_type = \"shipping\" AND is_default = 1",
    "params": ["ORD20250627002", "user_001"]
  }'

# 创建订单时自动生成合规声明
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "INSERT INTO compliance_statement (user_id, order_id, compliance_confirmed, resell_components, ultimate_consignee, country, application_type, additional_information, is_default) SELECT user_id, ?, compliance_confirmed, resell_components, ultimate_consignee, country, application_type, additional_information, 0 FROM compliance_statement WHERE user_id = ? AND is_default = 1",
    "params": ["ORD20250627003", "user_001"]
  }'
```

### 8. 订单管理

```bash
# 查询用户订单历史
curl -X GET "https://api.chinaelectron.com/api/table/orders?where_user_id=user123&order_by=order_date&order_dir=DESC"

# 查询待支付订单
curl -X GET "https://api.chinaelectron.com/api/table/orders?where_order_status=pending&order_by=payment_timeout"

# 查询订单明细
curl -X GET "https://api.chinaelectron.com/api/table/order_items?where_order_id=ORD20250627001"

# 更新订单状态
curl -X PUT "https://api.chinaelectron.com/api/table/orders?where_order_id=ORD20250627001" \
  -H "Content-Type: application/json" \
  -d '{
    "order_status": "paid"
  }'

# 查询订单统计信息
curl -X POST "https://api.chinaelectron.com/api/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "sql": "SELECT order_status, COUNT(*) as order_count, SUM(order_total) as total_amount FROM orders WHERE user_id = ? GROUP BY order_status",
    "params": ["user123"]
  }'

# 查询订单详细信息（包含明细）
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "o.order_id",
      "o.order_date",
      "o.order_total",
      "o.order_status",
      "oi.ce_id",
      "oi.mfr_number",
      "oi.manufacturer",
      "oi.description",
      "oi.quantity",
      "oi.unit_price",
      "oi.ext_price"
    ],
    "from": "orders o",
    "joins": [
      {
        "type": "LEFT",
        "table": "order_items oi",
        "on": "o.order_id = oi.order_id"
      }
    ],
    "where": {
      "o.user_id": "user123"
    },
    "orderBy": "o.order_date DESC",
    "limit": 50
  }'

# 查询订单完整信息（包含账单和物流）
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "o.order_id",
      "o.order_date",
      "o.order_total",
      "o.order_status",
      "o.payment_method",
      "bi.billing_address",
      "bi.country as billing_country",
      "si.shipping_address",
      "si.country as shipping_country",
      "si.shipping_method",
      "of.merchandise_total",
      "of.freight",
      "of.handling_fee",
      "of.total"
    ],
    "from": "orders o",
    "joins": [
      {
        "type": "LEFT",
        "table": "billing_info bi",
        "on": "o.order_id = bi.order_id"
      },
      {
        "type": "LEFT",
        "table": "shipping_info si",
        "on": "o.order_id = si.order_id"
      },
      {
        "type": "LEFT",
        "table": "order_fees of",
        "on": "o.order_id = of.order_id"
      }
    ],
    "where": {
      "o.order_id": "ORD20250627001"
    }
  }'

# 查询订单完整信息（包含支付记录）
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "o.order_id",
      "o.order_date",
      "o.order_total",
      "o.order_status",
      "o.payment_method",
      "p.transaction_number",
      "p.payment_method as actual_payment_method",
      "p.amount as payment_amount",
      "p.payment_date",
      "p.status as payment_status",
      "p.remarks as payment_remarks",
      "bi.billing_address",
      "si.shipping_address",
      "of.total as fee_total"
    ],
    "from": "orders o",
    "joins": [
      {
        "type": "LEFT",
        "table": "payment p",
        "on": "o.order_id = p.order_id"
      },
      {
        "type": "LEFT",
        "table": "billing_info bi",
        "on": "o.order_id = bi.order_id"
      },
      {
        "type": "LEFT",
        "table": "shipping_info si",
        "on": "o.order_id = si.order_id"
      },
      {
        "type": "LEFT",
        "table": "order_fees of",
        "on": "o.order_id = of.order_id"
      }
    ],
    "where": {
      "o.order_id": "ORD20250627001"
    }
  }'

# 查询用户所有订单及支付记录
curl -X POST "https://api.chinaelectron.com/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": [
      "o.order_id",
      "o.order_date",
      "o.order_total",
      "o.order_status",
      "p.transaction_number",
      "p.payment_method",
      "p.payment_date",
      "p.status as payment_status"
    ],
    "from": "orders o",
    "joins": [
      {
        "type": "LEFT",
        "table": "payment p",
        "on": "o.order_id = p.order_id"
      }
    ],
    "where": {
      "o.user_id": "user_001"
    },
    "orderBy": "o.order_date DESC"
  }'
```
