/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // 警告: 这只是临时解决方案
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.chinaelectron.com',
      },
      {
        protocol: 'https',
        hostname: 'dummyimage.com',
      },
      {
        protocol: 'https',
        hostname: 'brandimg.chinaelectron.com',
      },
      {
        protocol: 'https',
        hostname: 'example.com',
      },
      {
        protocol: 'https',
        hostname: 'blog-manage.chinaelectron.com',
      },
      {
        protocol: 'http',
        hostname: 'example.com',
      },
      {
        protocol: 'https',
        hostname: '*.cloudfront.net',
      }
    ],
    unoptimized: true
  },
  // 添加缓存配置
  headers: async () => {
    return [
      {
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      {
        source: '/sitemaps/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ]
  },
  // 添加路由配置
  async rewrites() {
    return {
      beforeFiles: [
        // 处理sitemap路由
        {
          source: '/sitemap.xml',
          destination: '/sitemaps/sitemap.xml',
        },
        // 处理品牌筛选路由
        {
          source: '/manufacturers/:filter',
          destination: '/manufacturers/:filter',
        }
      ]
    }
  }
}

module.exports = nextConfig