'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { buildProductUrl } from '@/utils/productUrl'

interface SearchResultCardProps {
  result: {
    id: string
    score: number
    metadata: {
      cad: string
      datasheet: string
      desc: string
      img: string
      manufacture: string
      model: string
      name: string
      url?: string // 添加url字段
    }
  }
}

// 将搜索结果转换为产品对象，以便使用buildProductUrl函数
function convertResultToProduct(result: SearchResultCardProps['result']) {
  return {
    product_id: result.id,
    model: result.metadata.model,
    url: result.metadata.url, // 添加url字段
    brand_en: result.metadata.manufacture,
    brand_cn: '',
    description: result.metadata.desc,
    datasheet_url: result.metadata.datasheet,
    // 添加默认分类，以便生成URL
    category_path: ['component', 'electronic', 'general'],
    category_names: {
      component: {
        name_cn: '元器件',
        name_en: 'component',
        name_ru: 'компонент'
      },
      electronic: {
        name_cn: '电子',
        name_en: 'electronic',
        name_ru: 'электронный'
      },
      general: {
        name_cn: '通用',
        name_en: 'general',
        name_ru: 'общий'
      }
    }
  }
}

export default function SearchResultCard({ result }: SearchResultCardProps) {
  const [imgSrc, setImgSrc] = useState('')
  
  // 转换搜索结果为产品对象
  const productData = convertResultToProduct(result)

  useEffect(() => {
    const fetchImage = async () => {
      try {
        const token = localStorage.getItem('token')
        const response = await fetch(result.metadata.img, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'image/*',
            'Origin': window.location.origin
          }
        })
        if (response.ok) {
          const blob = await response.blob()
          setImgSrc(URL.createObjectURL(blob))
        }
      } catch (error) {
        console.error('Error loading image:', error)
      }
    }

    if (result.metadata.img) {
      fetchImage()
    }

    return () => {
      // 清理 blob URL
      if (imgSrc) {
        URL.revokeObjectURL(imgSrc)
      }
    }
  }, [result.metadata.img])

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:border-[#DE2910]/20 
                    hover:shadow-lg hover:shadow-[#DE2910]/10 transition-all duration-300">
      <div className="p-6">
        <div className="flex gap-6">
          {/* Product Image */}
          <div className="relative w-32 h-32 flex-shrink-0 bg-gray-50 rounded-lg overflow-hidden">
            {imgSrc ? (
              <img
                src={imgSrc}
                alt={result.metadata.name}
                className="w-full h-full object-contain p-2"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                Loading...
              </div>
            )}
          </div>

          {/* 产品信息 */}
          <div className="flex-grow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  <Link href={result.metadata.url || buildProductUrl(productData)} className="hover:text-[#DE2910]">
                    {result.metadata.name}
                  </Link>
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {result.metadata.desc}
                </p>
              </div>
              <div className="text-sm text-gray-500">
                Score: {result.score.toFixed(2)}
              </div>
            </div>

            {/* 产品详细信息 */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">制造商：</span>
                <span className="text-gray-900">{result.metadata.manufacture}</span>
              </div>
              <div>
                <span className="text-gray-500">型号：</span>
                <span className="text-gray-900 font-mono">{result.metadata.model}</span>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex gap-4 mt-4">
              {result.metadata.datasheet && (
                <a
                  href={result.metadata.datasheet}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-[#DE2910] hover:text-[#FF6B45] flex items-center gap-1"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  Datasheet
                </a>
              )}
              {result.metadata.cad && (
                <a
                  href={result.metadata.cad}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-[#DE2910] hover:text-[#FF6B45] flex items-center gap-1"
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                          d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  CAD
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 