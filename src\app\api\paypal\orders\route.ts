import { NextRequest, NextResponse } from 'next/server'

// PayPal配置
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID || 'Aaw539l1Q1uQs0ceDD3u_trL3a987pfyhVDBieEiZwOFJ9vioqbArb2Z0yKJuibdqDSGRPgqj3h3m3Mg'
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET || 'EISkpM6MAPZvkC5xLqd-8o_skXxPcXFRSt2LPixPYBq_N5jxCt5pv5Xi1sU8MXZRonlPRtA7fXhwYuoi'
// 强制使用沙盒环境，因为我们使用的是沙盒凭据
const PAYPAL_BASE_URL = 'https://api-m.paypal.com'

// 获取PayPal访问令牌
async function getPayPalAccessToken(): Promise<string> {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64')
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials'
  })

  if (!response.ok) {
    throw new Error('Failed to get PayPal access token')
  }

  const data = await response.json()
  return data.access_token
}

// 创建PayPal订单
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { order_id, amount, currency = 'USD' } = body

    console.log('PayPal order creation request:', { order_id, amount, currency })

    if (!order_id || !amount) {
      console.error('Missing required fields:', { order_id, amount })
      return NextResponse.json(
        { error: 'Missing required fields: order_id, amount' },
        { status: 400 }
      )
    }

    // 获取访问令牌
    console.log('Getting PayPal access token...')
    const accessToken = await getPayPalAccessToken()
    console.log('PayPal access token obtained successfully')

    // 获取基础URL - 优先使用环境变量，否则使用请求的origin
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL ||
                   request.headers.get('origin') ||
                   `https://${request.headers.get('host')}`

    console.log('Using base URL:', baseUrl)

    // 创建PayPal订单
    const paypalOrderData = {
      intent: 'CAPTURE',
      purchase_units: [
        {
          reference_id: order_id,
          amount: {
            currency_code: currency,
            value: amount.toFixed(2)
          },
          description: `Order ${order_id} - China Electron Components`
        }
      ],
      application_context: {
        brand_name: 'China Electron',
        landing_page: 'NO_PREFERENCE',
        user_action: 'PAY_NOW',
        return_url: `${baseUrl}/orders/${order_id}?payment=success`,
        cancel_url: `${baseUrl}/orders/${order_id}?payment=cancelled`
      }
    }

    console.log('PayPal order data:', JSON.stringify(paypalOrderData, null, 2))
    console.log('PayPal API URL:', `${PAYPAL_BASE_URL}/v2/checkout/orders`)

    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `${order_id}-${Date.now()}`
      },
      body: JSON.stringify(paypalOrderData)
    })

    console.log('PayPal API response status:', response.status)

    if (!response.ok) {
      const errorData = await response.text()
      console.error('PayPal order creation failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      })
      return NextResponse.json(
        { error: 'Failed to create PayPal order', details: errorData },
        { status: response.status }
      )
    }

    const paypalOrder = await response.json()
    console.log('PayPal order created:', JSON.stringify(paypalOrder, null, 2))

    return NextResponse.json({
      id: paypalOrder.id,
      status: paypalOrder.status,
      links: paypalOrder.links
    })

  } catch (error) {
    console.error('Error creating PayPal order:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
