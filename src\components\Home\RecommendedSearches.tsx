'use client'

import { useRouter } from 'next/navigation'
import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'

interface RecommendedSearchesProps {
  keywords: string[]
  isLoading: boolean
}

// 创建一个客户端专用的组件实现
export function ClientRecommendedSearches({ keywords, isLoading }: RecommendedSearchesProps) {
  const router = useRouter()
  const [isMobile, setIsMobile] = useState(false)

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 640)
    }
    
    // 初始检查
    checkScreenSize()
    
    // 监听窗口大小变化
    window.addEventListener('resize', checkScreenSize)
    
    // 清理
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  const handleClick = (keyword: string) => {
    router.push(`/search?q=${encodeURIComponent(keyword)}`)
  }

  // 为保持UI一致，确保至少显示8个按钮，不足时用占位符填充
  const displayKeywords = [...keywords]
  if (displayKeywords.length < 8) {
    const placeholders = Array(8 - displayKeywords.length).fill('')
    displayKeywords.push(...placeholders)
  }

  // 在移动端只显示4个，否则全部显示
  const visibleKeywords = isMobile 
    ? displayKeywords.slice(0, 4) 
    : displayKeywords

  return (
    <section className="relative w-screen ml-[calc(-50vw+50%)]">
      <div className="w-full bg-white/80 backdrop-blur-sm border-t border-gray-300/60 border-b border-gray-100/50 shadow-sm" style={{boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04)'}}>
        <div className="max-w-[1900px] mx-auto px-4 lg:px-14">
          <div className="py-4 flex flex-col">
            <div className="mb-4">
              <h3 className="text-gray-800 font-semibold text-lg flex items-center">
                <span className="mr-3 text-blue-600">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </span>
                Recommended Searches for You
              </h3>
            </div>

          {isLoading ? (
            // 现代化加载骨架屏
            <div className="flex flex-wrap gap-3">
              {Array(isMobile ? 4 : 8).fill(0).map((_, index) => (
                <div
                  key={index}
                  className="h-10 w-24 sm:w-32 bg-gradient-to-r from-gray-100 to-gray-200 animate-pulse rounded-lg"
                />
              ))}
            </div>
          ) : (
            // 现代化搜索标签
            <div className="flex flex-wrap gap-3">
              {visibleKeywords.map((keyword, index) => (
                keyword && (
                  <button
                    key={index}
                    onClick={() => handleClick(keyword)}
                    className="group px-4 py-2.5 text-sm font-medium rounded-lg
                             bg-white/80 backdrop-blur-sm border border-gray-200/50
                             hover:bg-white hover:border-blue-300/50 hover:text-blue-600
                             hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-0.5
                             transition-all duration-300 ease-out text-gray-700
                             relative overflow-hidden"
                  >
                    <span className="relative z-10">{keyword}</span>
                    {/* 悬停时的渐变背景 */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50
                                  opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </button>
                )
              ))}
            </div>
          )}
          </div>
        </div>
      </div>
    </section>
  )
}

// 创建一个服务器端加载器，通过动态导入来懒加载客户端组件
import dynamic from 'next/dynamic'

// 使用动态导入，确保客户端组件不在服务器端执行
const DynamicRecommendedSearches = dynamic(
  () => Promise.resolve(ClientRecommendedSearches),
  { 
    ssr: false,
    loading: () => (
      <section className="relative w-screen ml-[calc(-50vw+50%)]">
        <div className="w-full bg-white/80 backdrop-blur-sm border-t border-gray-300/60 border-b border-gray-100/50 shadow-sm" style={{boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04)'}}>
          <div className="max-w-[1900px] mx-auto px-4 lg:px-14">
            <div className="py-3 flex flex-col">
              <div className="mb-3">
                <h3 className="text-gray-800 font-medium text-base">
                  Recommended Searches for You
                </h3>
              </div>
              <div className="flex flex-wrap gap-2 sm:gap-3">
                {Array(8).fill(0).map((_, index) => (
                  <div
                    key={index}
                    className="h-8 w-20 sm:w-32 bg-gray-100 animate-pulse rounded-lg"
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>
    )
  }
)

// 默认导出动态组件，外部使用时不需要改变引用方式
export default function RecommendedSearches(props: RecommendedSearchesProps) {
  return <DynamicRecommendedSearches {...props} />
} 