#!/usr/bin/env node

/**
 * 转换正常的country-state.json为我们的数据格式
 * 
 * 使用方法：
 * node scripts/convert-country-data.js
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const INPUT_FILE = path.join(__dirname, 'country-state.json');
const OUTPUT_FILE = path.join(__dirname, '../src/data/countries-states-complete.ts');

/**
 * 转换为我们的数据格式
 */
function convertToOurFormat(countries) {
  console.log('🔄 转换数据格式...');
  
  const converted = countries.map(country => {
    const result = {
      code: country.countryCode,
      name: country.countryNameEn,
      nameCn: country.countryNameCn || country.countryNameEn
    };
    
    // 添加省州数据
    if (country.provinceList && country.provinceList.length > 0) {
      result.states = country.provinceList.map(province => ({
        code: province.provinceCode,
        name: province.provinceNameEn,
        nameCn: province.provinceNameCn || province.provinceNameEn
      }));
    }
    
    return result;
  }).filter(country => country.code && country.name);
  
  console.log(`✅ 转换完成: ${converted.length} 个国家`);
  return converted;
}

/**
 * 生成TypeScript文件
 */
function generateTypeScriptFile(countries) {
  console.log('📝 生成TypeScript文件...');
  
  // 分离有省州数据和无省州数据的国家
  const countriesWithStates = countries.filter(c => c.states && c.states.length > 0);
  const countriesWithoutStates = countries.filter(c => !c.states || c.states.length === 0);
  
  const content = `// 完整的全球国家和省/州数据
// 自动生成时间: ${new Date().toISOString()}
// 数据来源: scripts/country-state.json
// 总计: ${countries.length} 个国家，其中 ${countriesWithStates.length} 个有省州数据

export interface Country {
  code: string;        // 国家代码
  name: string;        // 英文名称
  nameCn?: string;     // 中文名称
  states?: State[];    // 省州列表
}

export interface State {
  code: string;        // 省州代码
  name: string;        // 英文名称
  nameCn?: string;     // 中文名称
}

// 有预定义省/州的国家 (${countriesWithStates.length}个)
export const countriesWithStates: Country[] = ${JSON.stringify(countriesWithStates, null, 2)};

// 其他国家 (${countriesWithoutStates.length}个)
export const otherCountries: Country[] = ${JSON.stringify(countriesWithoutStates, null, 2)};

// 合并所有国家
export const allCountries: Country[] = [...countriesWithStates, ...otherCountries];

// 根据国家代码获取省/州列表
export const getStatesByCountry = (countryCode: string): State[] => {
  const country = countriesWithStates.find(c => c.code === countryCode);
  return country?.states || [];
};

// 检查国家是否有预定义的省/州
export const hasStates = (countryCode: string): boolean => {
  return countriesWithStates.some(c => c.code === countryCode);
};

// 根据国家代码获取国家信息
export const getCountryByCode = (countryCode: string): Country | undefined => {
  return allCountries.find(c => c.code === countryCode);
};

// 获取国家的中文名称
export const getCountryNameCn = (countryCode: string): string => {
  const country = getCountryByCode(countryCode);
  return country?.nameCn || country?.name || countryCode;
};

// 获取省州的中文名称
export const getStateNameCn = (countryCode: string, stateCode: string): string => {
  const states = getStatesByCountry(countryCode);
  const state = states.find(s => s.code === stateCode);
  return state?.nameCn || state?.name || stateCode;
};
`;
  
  return content;
}

/**
 * 主函数
 */
async function main() {
  console.log('🌍 转换国家省州数据');
  console.log('==================');
  console.log(`📁 输入文件: ${INPUT_FILE}`);
  console.log(`📁 输出文件: ${OUTPUT_FILE}`);
  console.log('');
  
  try {
    // 检查输入文件是否存在
    if (!fs.existsSync(INPUT_FILE)) {
      throw new Error(`输入文件不存在: ${INPUT_FILE}`);
    }
    
    // 读取原始数据
    console.log('📖 读取原始数据...');
    const rawData = fs.readFileSync(INPUT_FILE, 'utf8');
    const countries = JSON.parse(rawData);
    
    if (!Array.isArray(countries)) {
      throw new Error('数据格式错误：期望数组格式');
    }
    
    console.log(`✅ 成功读取 ${countries.length} 个国家的数据`);
    
    // 转换数据
    const convertedCountries = convertToOurFormat(countries);
    
    // 生成TypeScript文件
    const tsContent = generateTypeScriptFile(convertedCountries);
    
    // 确保目录存在
    const tsDir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(tsDir)) {
      fs.mkdirSync(tsDir, { recursive: true });
    }
    
    // 备份现有文件
    if (fs.existsSync(OUTPUT_FILE)) {
      const backupFile = OUTPUT_FILE + '.backup.' + Date.now();
      fs.copyFileSync(OUTPUT_FILE, backupFile);
      console.log(`📋 已备份现有文件: ${path.basename(backupFile)}`);
    }
    
    fs.writeFileSync(OUTPUT_FILE, tsContent, 'utf8');
    console.log(`✅ TypeScript文件已保存: ${OUTPUT_FILE}`);
    
    // 统计信息
    console.log('');
    console.log('📊 统计信息:');
    console.log(`📍 总国家数: ${convertedCountries.length}`);
    console.log(`🏛️  有省州数据: ${convertedCountries.filter(c => c.states && c.states.length > 0).length} 个国家`);
    console.log(`🌐 无省州数据: ${convertedCountries.filter(c => !c.states || c.states.length === 0).length} 个国家`);
    
    const totalStates = convertedCountries.reduce((sum, country) => {
      return sum + (country.states ? country.states.length : 0);
    }, 0);
    console.log(`🗺️  总省州数: ${totalStates}`);
    
    console.log('');
    console.log('🎉 转换完成！');
    console.log('');
    console.log('📋 使用方法:');
    console.log('1. 在AddressForm中导入: import { allCountries, getStatesByCountry } from "@/data/countries-states-complete"');
    console.log('2. 替换现有的countries数据');
    console.log('3. 现在支持中英文双语显示');
    
  } catch (error) {
    console.error('');
    console.error('❌ 转换失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
