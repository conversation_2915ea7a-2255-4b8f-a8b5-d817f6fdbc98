/**
 * Cloudflare Workers script for LCSC product synchronization
 * Checks LCSC codes and syncs product information to local database
 */

export default {
  async fetch(request, env, ctx) {
    try {
      const url = new URL(request.url);
      const lcscCode = url.searchParams.get('lcscCode');
      
      if (!lcscCode) {
        return new Response(JSON.stringify({ error: 'Missing lcscCode parameter' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const result = await processLcscCode(lcscCode, env);
      
      return new Response(JSON.stringify(result), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('Error processing request:', error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
};

/**
 * Process LCSC code and sync product information
 */
async function processLcscCode(lcscCode, env) {
  const { DB, lcsc } = env;

  // Initialize result tracking
  const result = {
    product_id: null,
    status: null,
    lcsc_code: lcscCode,
    details: {
      new_product: false,
      new_brand: false,
      new_mapping: false,
      updated_tables: [],
      brand_info: null,
      category_info: null,
      price_count: 0,
      parameter_count: 0,
      stock_info: null
    }
  };

  // Step 1: Check if CE code exists in component_mapping
  const mappingResult = await DB.prepare(
    'SELECT ce_code FROM component_mapping WHERE lcsc_code = ?'
  ).bind(lcscCode).first();

  let ceCode = mappingResult?.ce_code;
  let isNewProduct = !ceCode;

  if (ceCode) {
    // Step 2.1: Supplement existing product info
    const existingProduct = await DB.prepare(
      'SELECT product_id FROM products202503 WHERE product_id = ?'
    ).bind(ceCode).first();

    if (existingProduct) {
      // Product already exists, return existing CE code
      result.product_id = ceCode;
      result.status = 'exists';
      return result;
    }
  }

  // Step 2.1.1 or 2.2.1: Fetch product data from LCSC API
  const lcscData = await fetchLcscProductData(lcscCode);

  if (!lcscData || lcscData.code !== 200) {
    throw new Error('Failed to fetch product data from LCSC API');
  }

  const productData = lcscData.result;

  // Generate CE code if not exists
  if (!ceCode) {
    ceCode = await generateNextCeCode(DB);
    result.details.new_mapping = true;

    // Create component mapping
    await DB.prepare(`
      INSERT INTO component_mapping (lcsc_code, ce_code, created_at, updated_at)
      VALUES (?, ?, datetime('now'), datetime('now'))
    `).bind(lcscCode, ceCode).run();

    result.details.updated_tables.push('component_mapping');
  }

  // Process brand
  const brandResult = await processBrand(DB, productData.brandId, productData.brandNameEn, productData.isAsianBrand);
  result.details.brand_info = brandResult;
  if (brandResult.is_new) {
    result.details.new_brand = true;
    result.details.updated_tables.push('brands', 'brand_mapping');
  }

  // Process category
  const categoryCode = await processCategory(DB, productData.catalogId);
  result.details.category_info = { category_code: categoryCode };

  // Insert/Update product
  await insertProduct(DB, ceCode, productData, brandResult.brand_id);
  result.details.new_product = true;
  result.details.updated_tables.push('products202503');

  // Insert category mapping
  await insertCategoryMapping(DB, ceCode, categoryCode);
  result.details.updated_tables.push('product_category_map');

  // Insert prices
  const priceCount = await insertPrices(DB, ceCode, productData.productPriceList);
  result.details.price_count = priceCount;
  result.details.updated_tables.push('price');

  // Insert stock
  const stockInfo = await insertStock(DB, ceCode, productData.stockNumber);
  result.details.stock_info = stockInfo;
  result.details.updated_tables.push('stocks');

  // Insert parameters
  const parameterCount = await insertParameters(DB, ceCode, productData.paramVOList);
  result.details.parameter_count = parameterCount;
  result.details.updated_tables.push('parameters');

  // Save LCSC data to R2
  await saveLcscDataToR2(lcsc, lcscCode, lcscData);

  result.product_id = ceCode;
  result.status = isNewProduct ? 'created' : 'updated';

  return result;
}

/**
 * Fetch product data from LCSC API
 */
async function fetchLcscProductData(productCode) {
  const response = await fetch(`https://wmsc.lcsc.com/ftps/wm/product/detail?productCode=${productCode}`, {
    method: 'GET',
    headers: {
      'Cookie': 'wmsc_cart_key=90CD1C5752AB78615BC188255B7EFC82CE0346844A0CD5349910DE1EF2DF18691D5BA85C3DFF4502',
      'User-Agent': 'PostmanRuntime/7.44.1',
      'Accept': '*/*',
      'Accept-Encoding': 'gzip, deflate, br',
      'Connection': 'keep-alive',
      'Host': 'wmsc.lcsc.com'
    }
  });

  if (!response.ok) {
    throw new Error(`LCSC API request failed: ${response.status}`);
  }

  return await response.json();
}

/**
 * Generate next CE code (CMA + 9 digits)
 */
async function generateNextCeCode(DB) {
  const result = await DB.prepare(`
    SELECT product_id FROM products202503 
    WHERE product_id LIKE 'CMA%' 
    ORDER BY product_id DESC 
    LIMIT 1
  `).first();
  
  let nextNumber = 200000001; // Default starting number
  
  if (result?.product_id) {
    const currentNumber = parseInt(result.product_id.substring(3));
    nextNumber = currentNumber + 1;
  }
  
  return `CMA${nextNumber.toString().padStart(9, '0')}`;
}

/**
 * Process brand information
 */
async function processBrand(DB, lcscBrandId, brandNameEn, isAsianBrand) {
  // Check if brand mapping exists
  const brandMapping = await DB.prepare(
    'SELECT ce_brand_id FROM brand_mapping WHERE lcsc_brand_id = ?'
  ).bind(lcscBrandId).first();

  if (brandMapping) {
    return {
      brand_id: brandMapping.ce_brand_id,
      is_new: false,
      brand_name: brandNameEn || '',
      domestic: isAsianBrand ? 'chinese' : 'international'
    };
  }

  // Generate new brand ID
  const newBrandId = await generateNextBrandId(DB);

  // Insert brand
  await DB.prepare(`
    INSERT INTO brands (id, name_cn, name_en, name_ru, introduction_cn, introduction_en, introduction_ru, website, logo_url, domestic, area)
    VALUES (?, '', ?, '', '', '', '', '', '', ?, '')
  `).bind(
    newBrandId,
    brandNameEn || '',
    isAsianBrand ? 'chinese' : 'international'
  ).run();

  // Create brand mapping
  await DB.prepare(`
    INSERT INTO brand_mapping (lcsc_brand_id, ce_brand_id, created_at, updated_at)
    VALUES (?, ?, datetime('now'), datetime('now'))
  `).bind(lcscBrandId, newBrandId).run();

  return {
    brand_id: newBrandId,
    is_new: true,
    brand_name: brandNameEn || '',
    domestic: isAsianBrand ? 'chinese' : 'international'
  };
}

/**
 * Generate next brand ID (CEB + 6 digits)
 */
async function generateNextBrandId(DB) {
  const result = await DB.prepare(`
    SELECT id FROM brands 
    WHERE id LIKE 'CEB%' 
    ORDER BY id DESC 
    LIMIT 1
  `).first();
  
  let nextNumber = 200001; // Default starting number
  
  if (result?.id) {
    const currentNumber = parseInt(result.id.substring(3));
    nextNumber = currentNumber + 1;
  }
  
  return `CEB${nextNumber.toString().padStart(6, '0')}`;
}

/**
 * Process category mapping
 */
async function processCategory(DB, catalogId) {
  const categoryMapping = await DB.prepare(
    'SELECT ce_category_id FROM category_mapping WHERE lcsc_category_id = ?'
  ).bind(catalogId).first();
  
  return categoryMapping?.ce_category_id || 'CMC0016015001';
}

/**
 * Insert product information
 */
async function insertProduct(DB, ceCode, productData, brandId) {
  await DB.prepare(`
    INSERT OR REPLACE INTO products202503 
    (product_id, model, brand_id, price_key, stock_key, datasheet_url, image_list, parameters_key, description, updated_at, rohs, summarizer)
    VALUES (?, ?, ?, ?, ?, ?, '[]', ?, ?, datetime('now'), ?, '')
  `).bind(
    ceCode,
    productData.productModel || '',
    brandId,
    ceCode,
    ceCode,
    productData.pdfUrl || '',
    ceCode,
    productData.productIntroEn || '',
    productData.isEnvironment ? 1 : 0
  ).run();
}

/**
 * Insert category mapping
 */
async function insertCategoryMapping(DB, ceCode, categoryCode) {
  await DB.prepare(`
    INSERT OR REPLACE INTO product_category_map (product_code, category_code, created_at)
    VALUES (?, ?, datetime('now'))
  `).bind(ceCode, categoryCode).run();
}

/**
 * Insert price information
 */
async function insertPrices(DB, ceCode, priceList) {
  // Delete existing prices
  await DB.prepare('DELETE FROM price WHERE code = ?').bind(ceCode).run();

  let priceCount = 0;
  // Insert new prices
  for (const priceItem of priceList || []) {
    await DB.prepare(`
      INSERT INTO price (code, quantity, price, created_at)
      VALUES (?, ?, ?, datetime('now'))
    `).bind(ceCode, priceItem.ladder, priceItem.usdPrice).run();
    priceCount++;
  }

  return priceCount;
}

/**
 * Insert stock information
 */
async function insertStock(DB, ceCode, stockNumber) {
  await DB.prepare(`
    INSERT OR REPLACE INTO stocks (code, stocks, created_at)
    VALUES (?, ?, datetime('now'))
  `).bind(ceCode, stockNumber || 0).run();

  return {
    stock_quantity: stockNumber || 0
  };
}

/**
 * Insert parameter information
 */
async function insertParameters(DB, ceCode, paramList) {
  // Delete existing parameters
  await DB.prepare('DELETE FROM parameters WHERE code = ?').bind(ceCode).run();

  let parameterCount = 0;
  // Insert new parameters
  for (const param of paramList || []) {
    // Insert English parameter
    if (param.paramNameEn && param.paramValueEn) {
      await DB.prepare(`
        INSERT INTO parameters (code, languages, param_name, param_value, created_at)
        VALUES (?, 'english', ?, ?, datetime('now'))
      `).bind(ceCode, param.paramNameEn, param.paramValueEn).run();
      parameterCount++;
    }

    // Insert Chinese parameter
    if (param.paramName && param.paramValue) {
      await DB.prepare(`
        INSERT INTO parameters (code, languages, param_name, param_value, created_at)
        VALUES (?, 'chinese', ?, ?, datetime('now'))
      `).bind(ceCode, param.paramName, param.paramValue).run();
      parameterCount++;
    }
  }

  return parameterCount;
}

/**
 * Save LCSC data to R2 storage
 */
async function saveLcscDataToR2(r2Bucket, lcscCode, lcscData) {
  const fileName = `${lcscCode}.json`;
  await r2Bucket.put(fileName, JSON.stringify(lcscData, null, 2), {
    httpMetadata: {
      contentType: 'application/json'
    }
  });
}
