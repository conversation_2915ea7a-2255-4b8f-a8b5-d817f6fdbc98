# 部署和配置文档

## 项目概览

ChinaElectron是一个基于Next.js 14的现代化电子元器件B2B平台，采用Cloudflare生态系统进行部署和托管。

### 技术栈
- **前端**: Next.js 14 + TypeScript + Tailwind CSS
- **后端**: Cloudflare Workers
- **数据库**: Cloudflare D1 (SQLite)
- **CDN**: Cloudflare CDN
- **安全**: Cloudflare Turnstile
- **包管理**: npm/pnpm

## 环境要求

### 开发环境
- **Node.js**: >= 18.18.0
- **npm**: >= 8.0.0 或 **pnpm**: >= 7.0.0
- **Git**: 最新版本

### 生产环境
- **Cloudflare账户**: 用于Workers和D1部署
- **域名**: 用于自定义域名配置
- **SSL证书**: Cloudflare自动提供

## 项目结构

```
ChinaElectron/
├── src/                    # 源代码目录
│   ├── app/               # Next.js App Router页面
│   ├── components/        # React组件
│   ├── contexts/          # React Context
│   ├── services/          # API服务
│   ├── utils/             # 工具函数
│   ├── types/             # TypeScript类型
│   └── lib/               # 库和配置
├── public/                # 静态资源
├── docs/                  # 项目文档
├── scripts/               # 构建脚本
├── db-script.js           # Cloudflare Workers数据库API
├── get-express.js         # 快递API Worker
├── wrangler.toml          # Cloudflare Workers配置
├── next.config.js         # Next.js配置
├── package.json           # 项目依赖
├── tsconfig.json          # TypeScript配置
└── tailwind.config.ts     # Tailwind CSS配置
```

## 环境变量配置

### 前端环境变量 (.env.local)
```bash
# Cloudflare Turnstile配置
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key

# 网站基础配置
NEXT_PUBLIC_SITE_URL=https://www.chinaelectron.com

# API端点配置
NEXT_PUBLIC_API_BASE_URL=https://webapi.chinaelectron.com
NEXT_PUBLIC_AUTH_BASE_URL=https://auth.chinaelectron.com
NEXT_PUBLIC_BLOG_BASE_URL=https://blog-manage.chinaelectron.com

# 开发环境配置
NODE_ENV=development
```

### Cloudflare Workers环境变量
```toml
# wrangler.toml
[vars]
API_VERSION = "1.0.0"
MAX_QUERY_LIMIT = "1000"
CORS_ORIGIN = "*"
```

## 本地开发环境搭建

### 1. 克隆项目
```bash
git clone https://github.com/your-org/chinaelectron.git
cd chinaelectron
```

### 2. 安装依赖
```bash
# 使用npm
npm install

# 或使用pnpm (推荐)
pnpm install
```

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

### 4. 启动开发服务器
```bash
# 启动Next.js开发服务器
npm run dev

# 或使用pnpm
pnpm dev
```

访问 http://localhost:7988 查看应用

### 5. 启动Cloudflare Workers本地开发
```bash
# 安装Wrangler CLI
npm install -g wrangler

# 登录Cloudflare
wrangler auth login

# 启动本地Workers开发服务器
wrangler dev db-script.js --local
```

## Cloudflare D1数据库配置

### 1. 创建D1数据库
```bash
# 创建新的D1数据库
wrangler d1 create little-field-db

# 获取数据库ID并更新wrangler.toml
```

### 2. 数据库配置文件 (wrangler.toml)
```toml
name = "d1-database-api"
main = "db-script.js"
compatibility_date = "2024-06-27"
compatibility_flags = ["nodejs_compat"]

# D1数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "your-database-id-here"

# 环境变量
[vars]
API_VERSION = "1.0.0"
MAX_QUERY_LIMIT = "1000"

# 开发环境配置
[env.dev]
[[env.dev.d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "your-database-id-here"

# 生产环境配置
[env.production]
[[env.production.d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "your-database-id-here"
```

### 3. 数据库初始化
```bash
# 执行数据库迁移脚本
wrangler d1 execute little-field-db --file=./scripts/init-db.sql

# 导入初始数据
wrangler d1 execute little-field-db --file=./scripts/seed-data.sql
```

## Next.js配置

### next.config.js
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true, // 临时解决方案
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.chinaelectron.com',
      },
      {
        protocol: 'https',
        hostname: 'brandimg.chinaelectron.com',
      },
      {
        protocol: 'https',
        hostname: 'blog-manage.chinaelectron.com',
      },
      {
        protocol: 'https',
        hostname: '*.cloudfront.net',
      }
    ],
    unoptimized: true
  },
  // 缓存配置
  headers: async () => {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0',
          },
        ],
      },
    ]
  },
  // 重定向配置
  redirects: async () => {
    return [
      {
        source: '/old-path',
        destination: '/new-path',
        permanent: true,
      },
    ]
  },
  // 重写配置
  rewrites: async () => {
    return [
      {
        source: '/api/proxy/:path*',
        destination: 'https://external-api.com/:path*',
      },
    ]
  }
}

module.exports = nextConfig
```

### TypeScript配置 (tsconfig.json)
```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

## 第三方服务集成

### 1. Cloudflare Turnstile配置

#### 获取Turnstile密钥
1. 登录 [Cloudflare仪表板](https://dash.cloudflare.com/)
2. 导航到 **Turnstile** 部分
3. 点击 **Add widget** 创建新的验证码
4. 选择验证模式（推荐"Managed"模式）
5. 复制Site Key和Secret Key

#### 环境变量配置
```bash
# .env.local
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_site_key_here
TURNSTILE_SECRET_KEY=your_secret_key_here
```

#### 组件使用
```tsx
import Turnstile from '@/components/security/Turnstile'

<Turnstile
  onVerify={(token) => handleVerification(token)}
  onError={(error) => handleError(error)}
  theme="auto"
  size="normal"
/>
```

### 2. LCSC快递API集成

#### Worker配置 (get-express.js)
```javascript
export default {
  async fetch(request, env, ctx) {
    // LCSC API集成逻辑
    // 处理快递费用查询
    // 返回运费信息
  }
}
```

#### 部署快递API Worker
```bash
# 部署快递API Worker
wrangler deploy get-express.js --name get-express-api

# 配置自定义域名
wrangler route add "express-api.chinaelectron.com/*" get-express-api
```

### 3. 邮件服务配置

#### SMTP配置
```javascript
// src/lib/email.ts
export const emailConfig = {
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
}
```

## 构建和部署

### 1. 前端构建部署

#### 构建生产版本
```bash
# 构建Next.js应用
npm run build

# 启动生产服务器（本地测试）
npm run start
```

#### 部署到Vercel
```bash
# 安装Vercel CLI
npm install -g vercel

# 登录Vercel
vercel login

# 部署项目
vercel --prod
```

#### 部署到Cloudflare Pages
```bash
# 使用Wrangler部署到Pages
wrangler pages deploy ./out

# 或通过Git集成自动部署
```

### 2. Cloudflare Workers部署

#### 数据库API Worker
```bash
# 部署数据库API Worker
wrangler deploy db-script.js --env production

# 配置自定义域名
wrangler route add "db-api.chinaelectron.com/*" d1-database-api
```

#### 快递API Worker
```bash
# 部署快递API Worker
wrangler deploy get-express.js --name get-express-api --env production
```

### 3. 域名和SSL配置

#### 自定义域名配置
```bash
# 添加自定义域名到Worker
wrangler route add "api.chinaelectron.com/*" your-worker-name

# 配置DNS记录
# A记录: api.chinaelectron.com -> Cloudflare IP
# CNAME记录: www.chinaelectron.com -> chinaelectron.com
```

#### SSL证书配置
- Cloudflare自动提供免费SSL证书
- 支持通配符证书
- 自动续期

## 监控和日志

### 1. Cloudflare Analytics
- Workers分析
- 网站流量分析
- 性能监控
- 错误跟踪

### 2. 日志配置
```javascript
// Workers日志
console.log('Info:', data)
console.error('Error:', error)
console.warn('Warning:', warning)

// Next.js日志
import { logger } from '@/lib/logger'
logger.info('Application started')
logger.error('Error occurred', error)
```

### 3. 错误监控
```javascript
// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
})

// React错误边界
class ErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    console.error('React Error:', error, errorInfo)
  }
}
```

## 性能优化

### 1. 缓存策略
```javascript
// Next.js缓存配置
export const revalidate = 3600 // 1小时

// Cloudflare缓存配置
const cacheHeaders = {
  'Cache-Control': 'public, max-age=3600, s-maxage=86400',
  'CDN-Cache-Control': 'max-age=86400',
}
```

### 2. 图片优化
```javascript
// Next.js Image组件
import Image from 'next/image'

<Image
  src="/image.jpg"
  alt="Description"
  width={800}
  height={600}
  priority={true}
  placeholder="blur"
/>
```

### 3. 代码分割
```javascript
// 动态导入
const DynamicComponent = dynamic(() => import('./Component'), {
  loading: () => <LoadingSpinner />,
  ssr: false
})
```

## 安全配置

### 1. 内容安全策略 (CSP)
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'"
  }
]
```

### 2. CORS配置
```javascript
// Cloudflare Workers CORS
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
}
```

### 3. 环境变量安全
- 敏感信息使用Cloudflare Secrets
- 前端环境变量使用NEXT_PUBLIC_前缀
- 定期轮换API密钥

## 备份和恢复

### 1. 数据库备份
```bash
# 导出D1数据库
wrangler d1 export little-field-db --output backup.sql

# 定期备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
wrangler d1 export little-field-db --output "backup_${DATE}.sql"
```

### 2. 代码备份
- Git版本控制
- 定期推送到远程仓库
- 标签管理发布版本

### 3. 配置备份
- 环境变量备份
- Cloudflare配置导出
- DNS记录备份

## 故障排除

### 1. 常见问题

#### 构建失败
```bash
# 清理缓存
npm run clean
rm -rf .next node_modules
npm install

# 检查TypeScript错误
npm run type-check
```

#### Workers部署失败
```bash
# 检查wrangler配置
wrangler whoami
wrangler auth login

# 验证配置文件
wrangler validate
```

#### 数据库连接问题
```bash
# 测试数据库连接
wrangler d1 execute little-field-db --command "SELECT 1"

# 检查绑定配置
wrangler d1 list
```

### 2. 调试工具
- Chrome DevTools
- Cloudflare Workers调试器
- Next.js调试模式
- Wrangler日志查看

### 3. 性能分析
- Lighthouse性能测试
- Cloudflare Analytics
- Next.js Bundle Analyzer
- Core Web Vitals监控
