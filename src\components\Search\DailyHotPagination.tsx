'use client'

import { useRouter } from 'next/navigation'
import ProductTable from './ProductTable'

interface Product {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_id: string
  brand: {
    name_cn: string | null
    name_en: string | null
    name_ru: string | null
    logo_url?: string
    website?: string
  }
  price_key: string
  stock_key: string
  datasheet_url: string
  parameters_key: string
  parameters?: {
    english?: {
      param_name: string;
      param_value: string;
    }[];
    chinese?: {
      param_name: string;
      param_value: string;
    }[];
    russian?: {
      param_name: string;
      param_value: string;
    }[];
  };
  description: string
  prices: { quantity: number; price: number }[]
  stock: number
  image_list?: string[]
  category_path?: string[]
  category_names?: {
    [key: string]: {
      name_cn: string
      name_en: string
      name_ru: string
    }
  }
  category_id?: string
}

interface DailyHotPaginationProps {
  products: Product[]
  totalResults: number
  currentPage: number
}

export function DailyHotPagination({ products, totalResults, currentPage }: DailyHotPaginationProps) {
  const router = useRouter();

  // 处理页面导航
  const navigateToPage = (page: number) => {
    router.push(`/dailyhot?page=${page}`);
  };

  return (
    <ProductTable 
      products={products} 
      totalResults={totalResults} 
      currentPage={currentPage}
      navigateToPage={navigateToPage}
    />
  );
} 