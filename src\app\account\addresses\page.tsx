'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import {
  getUserAddresses,
  addUserAddress,
  updateUserAddress,
  deleteUserAddress,
  setDefaultAddress,
  AddressInfo
} from '@/services/api'
import AddressForm from '@/components/Account/AddressForm'
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Star,
  Home,
  Building,
  AlertCircle
} from 'lucide-react'

export default function AddressesPage() {
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()
  
  const [addresses, setAddresses] = useState<AddressInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingAddress, setEditingAddress] = useState<AddressInfo | null>(null)
  const [submitting, setSubmitting] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | null; text: string }>({
    type: null,
    text: ''
  })

  // 获取地址列表
  const fetchAddresses = async () => {
    if (!userId) return
    
    try {
      setLoading(true)
      const addressData = await getUserAddresses(userId)
      setAddresses(addressData)
    } catch (error) {
      console.error('Error fetching addresses:', error)
      setMessage({ type: 'error', text: 'Failed to load addresses' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!authLoading && isAuthenticated && userId) {
      fetchAddresses()
    } else if (!authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [authLoading, isAuthenticated, userId, router])

  // 处理保存地址（添加或编辑）
  const handleSaveAddress = async (addressData: Omit<AddressInfo, 'address_id' | 'created_at' | 'updated_at'>) => {
    if (!userId) return

    try {
      setSubmitting(true)

      const addressWithUserId = {
        ...addressData,
        user_id: userId
      }

      let result
      if (editingAddress) {
        // 编辑现有地址
        result = await updateUserAddress(editingAddress.address_id, addressWithUserId)
      } else {
        // 添加新地址
        result = await addUserAddress(addressWithUserId)
      }

      if (result.success) {
        setMessage({ type: 'success', text: result.message })
        setShowAddForm(false)
        setEditingAddress(null)
        await fetchAddresses()
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save address' })
    } finally {
      setSubmitting(false)
    }
  }

  // 处理删除地址
  const handleDeleteAddress = async (addressId: number) => {
    if (!confirm('Are you sure you want to delete this address?')) return

    try {
      const result = await deleteUserAddress(addressId)
      if (result.success) {
        setMessage({ type: 'success', text: result.message })
        await fetchAddresses()
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to delete address' })
    }
  }

  // 处理设置默认地址
  const handleSetDefault = async (addressId: number, addressType: 'shipping' | 'billing') => {
    if (!userId) return

    try {
      const result = await setDefaultAddress(addressId, userId, addressType)
      if (result.success) {
        setMessage({ type: 'success', text: result.message })
        await fetchAddresses()
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update default address' })
    }
  }

  // 处理编辑地址
  const handleEditAddress = (address: AddressInfo) => {
    setEditingAddress(address)
    setShowAddForm(true)
  }

  // 处理取消表单
  const handleCancelForm = () => {
    setShowAddForm(false)
    setEditingAddress(null)
  }

  // 按类型分组地址
  const shippingAddresses = addresses.filter(addr => addr.address_type === 'shipping')
  const billingAddresses = addresses.filter(addr => addr.address_type === 'billing')

  if (authLoading || loading) {
    return (
      <div className="w-full">
        <div className="flex flex-col justify-center items-center h-64 space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-[#DE2910]/20 border-t-[#DE2910]"></div>
          <p className="text-gray-600 font-medium">Loading your addresses...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="w-full">
      {/* Page Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <MapPin className="w-6 h-6 text-[#DE2910]" />
          <div>
            <h2 className="text-xl font-bold text-gray-900">Address Book</h2>
            <p className="text-sm text-gray-600">Manage your shipping and billing addresses</p>
          </div>
        </div>

        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center space-x-2 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] text-white px-4 py-2 rounded-xl font-medium hover:from-[#FF3B20] hover:to-[#FF8055] hover:scale-105 transition-all duration-300 shadow-lg"
        >
          <Plus className="w-4 h-4" />
          <span>Add Address</span>
        </button>
      </div>

      {/* 消息显示 */}
      {message.text && (
        <div className={`p-4 rounded-xl mb-6 flex items-center space-x-2 ${
          message.type === 'success'
            ? 'bg-green-50 text-green-700 border border-green-200'
            : 'bg-red-50 text-red-700 border border-red-200'
        }`}>
          {message.type === 'success' ? (
            <Star className="w-5 h-5 text-green-500" />
          ) : (
            <AlertCircle className="w-5 h-5 text-red-500" />
          )}
          <span>{message.text}</span>
        </div>
      )}

      <div className="bg-white/50 backdrop-blur-sm rounded-xl border border-white/20 p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 收货地址 */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <Home className="w-5 h-5 text-[#DE2910]" />
              <h3 className="text-lg font-semibold text-gray-900">Shipping Addresses</h3>
            </div>

            {shippingAddresses.length === 0 ? (
              <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
                <MapPin className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h4 className="text-md font-medium text-gray-600 mb-2">No shipping addresses</h4>
                <p className="text-sm text-gray-500 mb-4">Add a shipping address to get started</p>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="text-[#DE2910] hover:text-[#FF6B45] font-medium text-sm"
                >
                  Add shipping address
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {shippingAddresses.map((address) => (
                  <div key={address.address_id} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-300 hover:border-[#DE2910]/20">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="w-6 h-6 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] rounded-md flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Home className="w-3 h-3 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900 text-sm">{address.first_name} {address.last_name}</h4>
                            {address.is_default && (
                              <span className="inline-flex items-center px-1.5 py-0.5 bg-amber-50 text-amber-700 text-xs rounded border border-amber-200">
                                <Star className="w-2.5 h-2.5 fill-current mr-1" />
                                Default
                              </span>
                            )}
                          </div>
                          <div className="text-gray-600 text-xs space-y-0.5">
                            {address.company_name && <div className="font-medium text-gray-700">{address.company_name}</div>}
                            <div>{address.street_address}{address.apt_suite_building ? `, ${address.apt_suite_building}` : ''}</div>
                            <div>{address.city}, {address.state_province} {address.postal_code}</div>
                            <div className="flex items-center justify-between">
                              <span>{address.country_region}</span>
                              <span className="text-gray-500">📞 {address.phone}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={() => handleEditAddress(address)}
                          className="p-1.5 text-gray-400 hover:text-[#DE2910] transition-colors rounded hover:bg-gray-50"
                          title="Edit address"
                        >
                          <Edit className="w-3.5 h-3.5" />
                        </button>
                        <button
                          onClick={() => handleDeleteAddress(address.address_id)}
                          className="p-1.5 text-gray-400 hover:text-red-600 transition-colors rounded hover:bg-gray-50"
                          title="Delete address"
                        >
                          <Trash2 className="w-3.5 h-3.5" />
                        </button>
                      </div>
                    </div>

                    {!address.is_default && (
                      <div className="pt-2 border-t border-gray-100">
                        <button
                          onClick={() => handleSetDefault(address.address_id, 'shipping')}
                          className="text-[#DE2910] hover:text-[#FF6B45] text-xs font-medium"
                        >
                          Set as default
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 账单地址 */}
          <div>
            <div className="flex items-center space-x-2 mb-6">
              <Building className="w-5 h-5 text-[#DE2910]" />
              <h3 className="text-lg font-semibold text-gray-900">Billing Addresses</h3>
            </div>

            {billingAddresses.length === 0 ? (
              <div className="bg-white rounded-xl border border-gray-200 p-8 text-center">
                <Building className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h4 className="text-md font-medium text-gray-600 mb-2">No billing addresses</h4>
                <p className="text-sm text-gray-500 mb-4">Add a billing address for checkout</p>
                <button
                  onClick={() => setShowAddForm(true)}
                  className="text-[#DE2910] hover:text-[#FF6B45] font-medium text-sm"
                >
                  Add billing address
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {billingAddresses.map((address) => (
                  <div key={address.address_id} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all duration-300 hover:border-[#DE2910]/20">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex items-start space-x-3 flex-1">
                        <div className="w-6 h-6 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] rounded-md flex items-center justify-center flex-shrink-0 mt-0.5">
                          <Building className="w-3 h-3 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h4 className="font-medium text-gray-900 text-sm">{address.first_name} {address.last_name}</h4>
                            {address.is_default && (
                              <span className="inline-flex items-center px-1.5 py-0.5 bg-amber-50 text-amber-700 text-xs rounded border border-amber-200">
                                <Star className="w-2.5 h-2.5 fill-current mr-1" />
                                Default
                              </span>
                            )}
                          </div>
                          <div className="text-gray-600 text-xs space-y-0.5">
                            {address.company_name && <div className="font-medium text-gray-700">{address.company_name}</div>}
                            <div>{address.street_address}{address.apt_suite_building ? `, ${address.apt_suite_building}` : ''}</div>
                            <div>{address.city}, {address.state_province} {address.postal_code}</div>
                            <div className="flex items-center justify-between">
                              <span>{address.country_region}</span>
                              <span className="text-gray-500">📞 {address.phone}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={() => handleEditAddress(address)}
                          className="p-1.5 text-gray-400 hover:text-[#DE2910] transition-colors rounded hover:bg-gray-50"
                          title="Edit address"
                        >
                          <Edit className="w-3.5 h-3.5" />
                        </button>
                        <button
                          onClick={() => handleDeleteAddress(address.address_id)}
                          className="p-1.5 text-gray-400 hover:text-red-600 transition-colors rounded hover:bg-gray-50"
                          title="Delete address"
                        >
                          <Trash2 className="w-3.5 h-3.5" />
                        </button>
                      </div>
                    </div>

                    {!address.is_default && (
                      <div className="pt-2 border-t border-gray-100">
                        <button
                          onClick={() => handleSetDefault(address.address_id, 'billing')}
                          className="text-[#DE2910] hover:text-[#FF6B45] text-xs font-medium"
                        >
                          Set as default
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 地址表单弹窗 */}
        {(showAddForm || editingAddress) && (
          <AddressForm
            address={editingAddress}
            onSave={handleSaveAddress}
            onCancel={handleCancelForm}
            isLoading={submitting}
          />
        )}
      </div>
    </div>
  )
}
