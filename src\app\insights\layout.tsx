/**
 * Insights Layout
 * 
 * This layout is applied to all pages under the /insights route
 * It provides consistent structure and styling for all insights pages
 */

import { ReactNode } from 'react'
import Link from 'next/link'
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

interface InsightsLayoutProps {
  children: ReactNode
}

export const metadata: Metadata = {
  title: 'Industry Insights | China Electron',
  description: 'Read the latest industry insights, market trends, and analysis on electronic components and raw materials from China Electron experts.',
  keywords: 'industry insights, electronic component trends, market analysis, China Electron insights, component industry news',
  alternates: {
    canonical: getCanonicalUrl('insights'),
  },
}

export default function InsightsLayout({ children }: InsightsLayoutProps) {
  return (
    <main className="min-h-screen flex flex-col">
      {/* Content area */}
      <div className="flex-grow">
        {children}
      </div>
    </main>
  )
} 