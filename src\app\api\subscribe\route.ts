import { NextRequest, NextResponse } from 'next/server';

// 存储IP地址的上次请求时间
const ipRequestTimes = new Map<string, number[]>();
// 存储邮箱的上次请求时间
const emailRequestTimes = new Map<string, string>();

// 限制参数 
const RATE_LIMIT_WINDOW = 60 * 1000; // 1分钟内
const MAX_REQUESTS_PER_IP = 5; // 每IP最多请求次数
const EMAIL_COOLDOWN = 24 * 60 * 60 * 1000; // 同一邮箱24小时内只能订阅一次

/**
 * 验证邮箱格式
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 检查IP请求频率
 */
function checkIpRateLimit(ip: string): boolean {
  const now = Date.now();
  const requestTimes = ipRequestTimes.get(ip) || [];
  
  // 清理过期的请求记录
  const recentRequests = requestTimes.filter(time => now - time < RATE_LIMIT_WINDOW);
  
  // 检查是否超过频率限制
  if (recentRequests.length >= MAX_REQUESTS_PER_IP) {
    return false;
  }
  
  // 更新请求记录
  recentRequests.push(now);
  ipRequestTimes.set(ip, recentRequests);
  
  return true;
}

/**
 * 检查邮箱冷却时间
 */
function checkEmailCooldown(email: string): boolean {
  const now = Date.now();
  const lastRequestTime = emailRequestTimes.get(email);
  
  if (lastRequestTime && (now - parseInt(lastRequestTime)) < EMAIL_COOLDOWN) {
    return false;
  }
  
  emailRequestTimes.set(email, now.toString());
  return true;
}

/**
 * POST 处理订阅请求
 */
export async function POST(req: NextRequest) {
  // 获取客户端IP
  const ip = req.headers.get('x-forwarded-for') || 'unknown';
  
  // 检查IP请求频率
  if (!checkIpRateLimit(ip)) {
    return NextResponse.json(
      { success: false, error: 'Too many requests. Please try again later.' },
      { status: 429 }
    );
  }
  
  try {
    // 解析请求体
    const { email } = await req.json();
    
    // 验证请求数据
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      );
    }
    
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' },
        { status: 400 }
      );
    }
    
    // 检查邮箱冷却时间
    if (!checkEmailCooldown(email)) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'This email was recently subscribed. Please try again later.' 
        },
        { status: 429 }
      );
    }
    
    // 调用实际的订阅API
    const response = await fetch('https://subscribe.chinaelectron.com/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
      },
      body: JSON.stringify({ email })
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.error || 'Failed to subscribe');
    }
    
    // 返回成功响应
    return NextResponse.json(
      { success: true, message: 'Successfully subscribed.' },
      { status: 201 }
    );
    
  } catch (error) {
    console.error('Subscription error:', error);
    
    // 返回错误响应
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'An unexpected error occurred' 
      },
      { status: 500 }
    );
  }
} 