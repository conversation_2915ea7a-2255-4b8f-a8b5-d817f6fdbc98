import { NextRequest, NextResponse } from 'next/server'

const DB_API_BASE_URL = 'https://api.chinaelectron.com/api'

// 检查并更新订单支付状态
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params
    const body = await request.json()
    const { paypal_transaction_id, force_update = false } = body

    if (!orderId) {
      return NextResponse.json(
        { error: 'Missing orderId parameter' },
        { status: 400 }
      )
    }

    // 首先检查当前订单状态
    const checkResponse = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!checkResponse.ok) {
      return NextResponse.json(
        { error: 'Failed to check order status' },
        { status: checkResponse.status }
      )
    }

    const checkResult = await checkResponse.json()
    const orders = checkResult.data || []

    if (orders.length === 0) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    const order = orders[0]
    
    // 如果订单已经是已支付状态且不强制更新，直接返回
    if (order.order_status === 'PAID' && !force_update) {
      return NextResponse.json({
        success: true,
        message: 'Order is already paid',
        order_status: order.order_status,
        payment_method: order.payment_method,
        already_paid: true
      })
    }

    // 如果订单状态不是已支付，尝试更新
    if (order.order_status !== 'PAID' || force_update) {
      // 首先检查是否已有支付记录
      let paymentRecordExists = false
      if (paypal_transaction_id) {
        try {
          const paymentCheckResponse = await fetch(`${DB_API_BASE_URL}/table/payment?where_transaction_number=${paypal_transaction_id}`)
          if (paymentCheckResponse.ok) {
            const paymentResult = await paymentCheckResponse.json()
            paymentRecordExists = (paymentResult.data || []).length > 0
          }
        } catch (error) {
          console.error('Error checking payment record:', error)
        }
      }

      // 如果没有支付记录且有交易ID，创建支付记录
      if (!paymentRecordExists && paypal_transaction_id) {
        try {
          const paymentData = {
            order_id: orderId,
            user_id: order.user_id,
            payment_method: 'PayPal',
            transaction_number: paypal_transaction_id,
            amount: order.order_total,
            payment_date: new Date().toISOString(),
            status: 1, // 成功状态
            remarks: 'Payment record created via status update API',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }

          const paymentResponse = await fetch(`${DB_API_BASE_URL}/table/payment`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(paymentData)
          })

          if (paymentResponse.ok) {
            console.log('Payment record created successfully')
          } else {
            console.error('Failed to create payment record:', await paymentResponse.text())
          }
        } catch (error) {
          console.error('Error creating payment record:', error)
        }
      }

      // 更新订单状态
      const updateData: any = {
        order_status: 'PAID',
        payment_method: 'PayPal'
      }

      const updateResponse = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      })

      if (!updateResponse.ok) {
        const errorText = await updateResponse.text()
        console.error('Failed to update order status:', errorText)
        return NextResponse.json(
          {
            error: 'Failed to update order status',
            details: errorText,
            current_status: order.order_status
          },
          { status: updateResponse.status }
        )
      }

      const updateResult = await updateResponse.json()

      return NextResponse.json({
        success: true,
        message: 'Order status updated successfully',
        order_status: 'PAID',
        payment_method: 'PayPal',
        updated: true,
        payment_record_created: !paymentRecordExists && !!paypal_transaction_id,
        data: updateResult
      })
    }

    return NextResponse.json({
      success: true,
      message: 'No update needed',
      order_status: order.order_status,
      payment_method: order.payment_method,
      updated: false
    })

  } catch (error) {
    console.error('Error checking/updating payment status:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

// 获取订单支付状态
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params

    if (!orderId) {
      return NextResponse.json(
        { error: 'Missing orderId parameter' },
        { status: 400 }
      )
    }

    const response = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`)

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch order status' },
        { status: response.status }
      )
    }

    const result = await response.json()
    const orders = result.data || []

    if (orders.length === 0) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    const order = orders[0]
    
    return NextResponse.json({
      order_id: order.order_id,
      order_status: order.order_status,
      payment_method: order.payment_method,
      is_paid: order.order_status === 'PAID',
      order_total: order.order_total,
      updated_at: order.updated_at
    })

  } catch (error) {
    console.error('Error fetching payment status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
