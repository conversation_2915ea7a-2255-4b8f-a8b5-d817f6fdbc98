// 首页相关类型定义

// 广告项目接口
export interface AdItem {
  id: string;
  title: string;
  image_url: string;
  link_url: string;
  alt_text: string;
  start_date: string;
  end_date: string;
  status: string;
  priority: number;
  page_location?: string;
}

// 广告数据接口
export interface AdsData {
  key: string;
  value: {
    homepage_ads: {
      side_ads: AdItem[];
      bottom_ads: AdItem[];
    }
  }
}

// 推荐搜索接口
export interface RecommendedSearches {
  date: string;
  keywords: string[];
}

// 热门商品接口
export interface HotItem {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_en: string
  brand_cn: string
  domestic: string
  type?: string
  full?: {
    url?: string // 添加url字段到full对象
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_en: string
      name_cn: string
      name_ru: string
      logo_url?: string
    }
    prices?: {
      quantity: number
      price: number
    }[]
    stock?: number
  }
}

// 推荐搜索数据接口
export interface RecommendedSearchData {
  date: string
  keywords: string[]
}

// 品牌接口
export interface Brand {
  brand_id: string
  brand_cn: string
  brand_en: string
  brand_ru: string
  domestic: string
  product_count: number
  distributor_count: number
}

// 品牌响应接口
export interface BrandsResponse {
  success: boolean
  date: string
  summary: {
    total_brands: number
    total_products: number
    domestic_stats: {
      domestic: {
        count: number
        products: number
      }
      imported: {
        count: number
        products: number
      }
      unknown: {
        count: number
        products: number
      }
    }
  }
  brands: Brand[]
}

// 清仓商品接口
export interface LiquidationItem {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_en: string
  brand_cn: string
  domestic: string
  type: string
  full?: {
    url?: string // 添加url字段到full对象
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_en: string
      name_cn: string
      name_ru: string
      logo_url?: string
    }
    prices?: {
      quantity: number
      price: number
    }[]
    stock?: number
  }
}

// 分类项目接口
export interface CategoryItem {
  code: string;
  parent_code: string | null;
  level: number;
  name_cn: string;
  name_en: string;
  name_ru: string;
  created_at: string;
  children?: Record<string, CategoryItem> | CategoryItem[];
}

// 分类响应接口
export interface CategoryResponse {
  success: boolean;
  latest_date: string;
  total_categories: number;
  category_tree: Record<string, CategoryItem>;
}

// 报价项目接口
export interface QuotationItem {
  id: number;
  product_id: string;
  model: string;
  url?: string; // 添加url字段
  brand_id: string;
  brand_name_cn: string;
  brand_name_en: string;
  brand_name_ru: string;
  domestic: string;
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  logo: string;
  address_cn: string;
  address_en: string;
  address_ru: string;
  create_at: string;
  full?: {
    url?: string; // 添加url字段到full对象
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_cn: string
      name_en: string
      name_ru: string
      logo_url?: string
    }
    prices?: {
      quantity: number
      price: number
    }[]
    stock?: number
  }
}

// 报价响应接口
export interface QuotationResponse {
  error: boolean;
  message: string;
  latest_date?: string;
  data: QuotationItem[];
}

// 带有分销商信息的报价项目接口
export interface QuotationItemWithDistributor extends QuotationItem {
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  logo: string;
  address_cn: string;
  address_en: string;
  address_ru: string;
  create_at: string;
}

// 活跃分销商接口
export interface ActiveDistributor {
  id: string;
  name_en: string;
  logo: string;
  description: string;
  count?: number;
  latest_time?: number;
}

// 专题集合接口
export interface TopicCollection {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle: string;
  description: string;
  cover_image: string;
  tags: string;
  created_at: string;
  updated_at: string;
  author_id: string;
}

// 分页数据接口
export interface PaginationData {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// 洞察响应接口
export interface InsightsResponse {
  data: TopicCollection[];
  pagination: PaginationData;
}

// 博客文章接口
export interface BlogArticle {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle?: string;
  description: string;
  cover_image: string;
  content_markdown: string;
  tags: string;
  category: string;
  location?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
}

// 博客响应接口
export interface BlogsResponse {
  data: BlogArticle[];
  pagination: PaginationData;
}

// 详细广告项目接口
export interface DetailedAdItem {
  id: number;
  platform: string;
  page: string;
  page_location: string;
  tags: string;
  status: string;
  priority: number;
  img: string;
  img_alt: string;
  title: string;
  subtitle: string;
  description: string;
  target_url: string;
  effective_time: string;
  create_at: string;
} 