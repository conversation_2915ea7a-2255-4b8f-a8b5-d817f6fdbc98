'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'
import { getOrderDetails } from '@/services/api'
import PayPalButton from '@/components/PayPalButton'

// 使用内联SVG图标替代lucide-react
const ClockIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <circle cx="12" cy="12" r="10" strokeWidth={2} />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6l4 2" />
  </svg>
)

const PackageIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.27 6.96L12 12.01l8.73-5.05M12 22.08V12" />
  </svg>
)

const MapPinIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const CreditCardIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
)

const ArrowLeftIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 12H5m7-7l-7 7 7 7" />
  </svg>
)

const DownloadIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m7-10v12m-4-4l4 4 4-4" />
  </svg>
)

const PrinterIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
  </svg>
)

// 订单状态显示函数
function getOrderStatusDisplay(status: string) {
  const statusMap: { [key: string]: { text: string; color: string; bgColor: string } } = {
    'pending': { text: 'Pending', color: 'text-yellow-700', bgColor: 'bg-yellow-100' },
    'confirmed': { text: 'Confirmed', color: 'text-blue-700', bgColor: 'bg-blue-100' },
    'processing': { text: 'Processing', color: 'text-purple-700', bgColor: 'bg-purple-100' },
    'shipped': { text: 'Shipped', color: 'text-green-700', bgColor: 'bg-green-100' },
    'delivered': { text: 'Delivered', color: 'text-green-700', bgColor: 'bg-green-100' },
    'cancelled': { text: 'Cancelled', color: 'text-red-700', bgColor: 'bg-red-100' },
    'refunded': { text: 'Refunded', color: 'text-gray-700', bgColor: 'bg-gray-100' }
  }
  
  return statusMap[status] || { text: status, color: 'text-gray-700', bgColor: 'bg-gray-100' }
}

export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isAuthenticated, userId, isLoading: authLoading } = useAuth()
  const [orderDetails, setOrderDetails] = useState<any>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [paymentSuccess, setPaymentSuccess] = useState(false)

  useEffect(() => {
    if (authLoading) return // 等待认证状态加载完成

    if (!isAuthenticated || !userId) {
      router.push('/login')
      return
    }

    // 检查支付状态参数
    const paymentStatus = searchParams.get('payment')
    if (paymentStatus === 'success') {
      setPaymentSuccess(true)
      setMessage({
        type: 'success',
        text: 'Payment completed successfully! Your order has been updated.'
      })
    } else if (paymentStatus === 'cancelled') {
      setMessage({
        type: 'error',
        text: 'Payment was cancelled. You can try again below.'
      })
    }

    const fetchOrderDetails = async () => {
      try {
        setIsLoading(true)
        const orderId = params.id as string
        const details = await getOrderDetails(orderId)
        setOrderDetails(details)
      } catch (error) {
        console.error('Failed to fetch order details:', error)
        setMessage({
          type: 'error',
          text: error instanceof Error ? error.message : 'Failed to load order details'
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrderDetails()
  }, [params.id, isAuthenticated, userId, authLoading, router, searchParams])

  // 检查并更新支付状态
  const checkPaymentStatus = async (transactionId?: string) => {
    try {
      const orderId = params.id as string

      // 首先尝试使用专门的支付状态API
      const response = await fetch(`/api/orders/${orderId}/payment-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paypal_transaction_id: transactionId,
          force_update: false
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Payment status check result:', result)

        if (result.success && result.order_status === 'PAID') {
          // 刷新订单详情以显示最新状态
          await refreshOrderDetails()
          return true
        }
      }

      return false
    } catch (error) {
      console.error('Failed to check payment status:', error)
      return false
    }
  }

  // 刷新订单详情
  const refreshOrderDetails = async () => {
    try {
      const orderId = params.id as string
      const details = await getOrderDetails(orderId)

      if (details) {
        setOrderDetails(details)

        // 检查订单状态是否已更新
        if (details.order.order_status === 'PAID') {
          setPaymentSuccess(true)
          setMessage({
            type: 'success',
            text: 'Payment completed successfully! Your order status has been updated.'
          })
        }
      }
    } catch (error) {
      console.error('Failed to refresh order details:', error)
    }
  }

  // 支付成功处理
  const handlePaymentSuccess = async (details: any) => {
    console.log('Payment successful:', details)
    setPaymentSuccess(true)
    setMessage({
      type: 'success',
      text: 'Payment completed successfully! Updating order status...'
    })

    // 获取交易ID
    const transactionId = details?.purchase_units?.[0]?.payments?.captures?.[0]?.id

    // 立即尝试检查和更新支付状态
    const statusUpdated = await checkPaymentStatus(transactionId)

    if (!statusUpdated) {
      // 如果立即检查失败，等待2秒后再试
      setTimeout(async () => {
        const retrySuccess = await checkPaymentStatus(transactionId)
        if (!retrySuccess) {
          // 如果还是失败，使用普通的刷新方法
          await refreshOrderDetails()
        }
      }, 2000)

      // 最后一次尝试（10秒后）
      setTimeout(async () => {
        await checkPaymentStatus(transactionId)
      }, 10000)
    }
  }

  // 支付错误处理
  const handlePaymentError = (error: any) => {
    console.error('Payment error:', error)
    setMessage({
      type: 'error',
      text: 'Payment failed. Please try again or contact support.'
    })
  }

  // 支付取消处理
  const handlePaymentCancel = () => {
    console.log('Payment cancelled by user')
    setMessage({
      type: 'error',
      text: 'Payment was cancelled. You can try again below.'
    })
  }

  if (authLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#DE2910] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading order details...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !userId) {
    return null
  }

  if (message?.type === 'error' || !orderDetails) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-600 mb-4">
            {message?.text || 'Order not found'}
          </h2>
          <Link
            href="/account/orders"
            className="inline-flex items-center gap-2 bg-[#DE2910] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#DE2910]/90 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            Back to Orders
          </Link>
        </div>
      </div>
    )
  }

  const { order, items, fees, shipping_address, billing_address } = orderDetails
  const statusDisplay = getOrderStatusDisplay(order.order_status)

  // 检查是否可以支付（订单状态为待支付）
  const canPay = order.order_status === 'PENDING_PAYMENT' || order.order_status === 'pending'

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">

        {/* 消息提示 */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg border ${
            message.type === 'success'
              ? 'bg-green-50 border-green-200 text-green-700'
              : 'bg-red-50 border-red-200 text-red-700'
          }`}>
            <p>{message.text}</p>
          </div>
        )}
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Link
              href="/account/orders"
              className="flex items-center gap-2 text-gray-600 hover:text-[#DE2910] transition-colors"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              Back to Orders
            </Link>
            <div className="h-6 w-px bg-gray-300"></div>
            <h1 className="text-3xl font-bold text-gray-900">Order Details</h1>
          </div>
          
          <div className="flex items-center gap-3">
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              <DownloadIcon />
              Download CSV
            </button>
            <button className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              <PrinterIcon />
              Reorder
            </button>
          </div>
        </div>

        {/* 订单基本信息 */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <p className="text-sm text-gray-500 mb-1">Order No.</p>
              <p className="font-semibold text-gray-900">{order.order_id}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Order Date</p>
              <p className="font-semibold text-gray-900">
                {new Date(order.order_date).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Order Total</p>
              <p className="font-semibold text-gray-900">${order.order_total.toFixed(2)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500 mb-1">Order Status</p>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${statusDisplay.color} ${statusDisplay.bgColor}`}>
                {statusDisplay.text}
              </span>
            </div>
          </div>
          
          {order.payment_timeout && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <div className="flex items-center gap-2 text-amber-600">
                <ClockIcon className="w-4 h-4" />
                <span className="text-sm">
                  Payment Timeout: {new Date(order.payment_timeout).toLocaleString()}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Shipping 和 Billing Information 横向排列 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Shipping Information */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-4">
              <MapPinIcon className="w-5 h-5 text-[#DE2910]" />
              <h2 className="text-xl font-semibold text-gray-900">Shipping Information</h2>
            </div>
            
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Shipping Address</p>
                {shipping_address ? (
                  <div className="space-y-1">
                    <div className="font-medium text-gray-900">
                      {shipping_address.first_name} {shipping_address.last_name}
                    </div>
                    {shipping_address.company_name && (
                      <div className="text-gray-600">{shipping_address.company_name}</div>
                    )}
                    <div className="text-gray-600">{shipping_address.street_address}</div>
                    {shipping_address.apt_suite_building && (
                      <div className="text-gray-600">{shipping_address.apt_suite_building}</div>
                    )}
                    <div className="text-gray-600">
                      {shipping_address.city}, {shipping_address.state_province} {shipping_address.postal_code}
                    </div>
                    <div className="text-gray-600">{shipping_address.country_region}</div>
                    <div className="text-gray-600">{shipping_address.phone}</div>
                  </div>
                ) : (
                  <div className="text-gray-500">No shipping address information</div>
                )}
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Shipping Method</p>
                <p className="font-medium text-gray-900">{orderDetails.shipping_info?.shipping_method || 'Not specified'}</p>
              </div>
            </div>
          </div>

          {/* Billing Information */}
          <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-4">
              <CreditCardIcon className="w-5 h-5 text-[#DE2910]" />
              <h2 className="text-xl font-semibold text-gray-900">Billing Information</h2>
            </div>
            
            <div className="space-y-3">
              <div>
                <p className="text-sm text-gray-500">Billing Address</p>
                {billing_address ? (
                  <div className="space-y-1">
                    <div className="font-medium text-gray-900">
                      {billing_address.first_name} {billing_address.last_name}
                    </div>
                    {billing_address.company_name && (
                      <div className="text-gray-600">{billing_address.company_name}</div>
                    )}
                    <div className="text-gray-600">{billing_address.street_address}</div>
                    {billing_address.apt_suite_building && (
                      <div className="text-gray-600">{billing_address.apt_suite_building}</div>
                    )}
                    <div className="text-gray-600">
                      {billing_address.city}, {billing_address.state_province} {billing_address.postal_code}
                    </div>
                    <div className="text-gray-600">{billing_address.country_region}</div>
                    <div className="text-gray-600">{billing_address.phone}</div>
                  </div>
                ) : (
                  <div className="text-gray-500">No billing address information</div>
                )}
              </div>
              
              <div>
                <p className="text-sm text-gray-500">Payment Method</p>
                <p className="font-medium text-gray-900">{order.payment_method || 'Not specified'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* 订单明细表格 */}
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <PackageIcon className="w-5 h-5 text-[#DE2910]" />
              <h2 className="text-xl font-semibold text-gray-900">
                In Stock Items ({items.length})
              </h2>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">CE#</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Mfr #</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Mfr</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Description</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Order Qty</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Unit Price</th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900">Ext Price</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item: any, index: number) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-4">
                      <div className="font-medium text-blue-600">{item.ce_id}</div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="font-medium text-gray-900">{item.mfr_number}</div>
                    </td>
                    <td className="py-4 px-4 text-gray-600">{item.manufacturer}</td>
                    <td className="py-4 px-4 text-gray-600 max-w-xs">
                      <div className="max-w-xs overflow-hidden" title={item.description}>
                        <div className="text-sm leading-tight" style={{
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}>
                          {item.description}
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4 text-right font-medium">{item.quantity}</td>
                    <td className="py-4 px-4 text-right font-medium">$ {item.unit_price.toFixed(4)}</td>
                    <td className="py-4 px-4 text-right font-semibold text-gray-900">
                      $ {item.ext_price.toFixed(2)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧银行转账信息 */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m-3-6h6" />
                </svg>
                Bank Transfer Information
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Deutsche Bank */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3 text-sm">Deutsche Bank AG, Hong Kong</h4>
                  <div className="space-y-2 text-xs">
                    <div>
                      <span className="font-medium text-gray-700">Account Number:</span>
                      <div className="text-gray-900 font-mono">*************</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Account Name:</span>
                      <div className="text-gray-900">Shenzhen Yexingxin Information Technology Co., Ltd.</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">SWIFT/BIC:</span>
                      <div className="text-gray-900 font-mono">DEUTHKHHXXX</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="font-medium text-gray-700">Bank Code:</span>
                        <div className="text-gray-900">054</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Branch Code:</span>
                        <div className="text-gray-900">895</div>
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Bank Address:</span>
                      <div className="text-gray-900">57/F, International Commerce Centre, 1 Austin Road West, Kowloon, Hong Kong</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Payment Method:</span>
                      <div className="text-gray-900">SWIFT/CHATS Payment</div>
                    </div>
                  </div>
                </div>

                {/* DBS Bank */}
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3 text-sm">DBS Bank (Hong Kong) Limited</h4>
                  <div className="space-y-2 text-xs">
                    <div>
                      <span className="font-medium text-gray-700">Account Number:</span>
                      <div className="text-gray-900 font-mono">**************</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Account Name:</span>
                      <div className="text-gray-900">Shenzhen Yexingxin Information Technology Co., Ltd.</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">SWIFT/BIC:</span>
                      <div className="text-gray-900 font-mono">DHBKHKHH (DHBKHKHHXXX)</div>
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      <div>
                        <span className="font-medium text-gray-700">Sort Code:</span>
                        <div className="text-gray-900">016</div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Branch Code:</span>
                        <div className="text-gray-900">478</div>
                      </div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Bank Address:</span>
                      <div className="text-gray-900">11th Floor, The Center, 99 Queen's Road Central, Central, Hong Kong</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">Payment Method:</span>
                      <div className="text-gray-900">SWIFT(T/T) or CHATS(HK Local)</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Important Notes */}
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start gap-2">
                  <svg className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <div>
                    <p className="text-xs font-medium text-yellow-800">Important Notes:</p>
                    <p className="text-xs text-yellow-700 mt-1">
                      Please include <strong>[Buyer's Name, Invoice/Contract Number: {order.order_id}, and Product Name]</strong> in the memo or note section when making the payment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧费用明细 */}
          <div>
            {/* 费用明细 */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <div className="space-y-3">
                {/* 商品总额 */}
                <div className="flex justify-between py-2">
                  <span className="text-gray-700">Merchandise Total:</span>
                  <span className="font-semibold">
                    $ {fees.length > 0 ? fees[0]?.merchandise_total?.toFixed(2) || '0.00' : items.reduce((sum: number, item: any) => sum + item.ext_price, 0).toFixed(2)}
                  </span>
                </div>

                {/* 运费 */}
                {fees.length > 0 && fees[0]?.freight !== undefined && (
                  <div className="flex justify-between py-2">
                    <span className="text-gray-700">Freight:</span>
                    <span className="font-semibold">
                      $ {fees[0].freight.toFixed(2)}
                    </span>
                  </div>
                )}

                {/* 手续费 */}
                {fees.length > 0 && fees[0]?.handling_fee !== undefined && (
                  <div className="flex justify-between py-2">
                    <span className="text-gray-700">Handling Fee:</span>
                    <span className="font-semibold">
                      $ {fees[0].handling_fee.toFixed(2)}
                    </span>
                  </div>
                )}

                <hr className="border-gray-200 my-3" />
                <div className="flex justify-between py-2 text-lg font-bold">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-[#DE2910]">$ {order.order_total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* 支付按钮 */}
            {canPay && !paymentSuccess && (
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 mt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Complete Payment</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Choose your payment method to complete this order.
                </p>
                <PayPalButton
                  orderId={order.order_id}
                  amount={order.order_total}
                  currency="USD"
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  onCancel={handlePaymentCancel}
                  disabled={false}
                />
              </div>
            )}

            {/* 支付成功提示 */}
            {paymentSuccess && (
              <div className="bg-green-50 border border-green-200 rounded-2xl p-6 mt-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">Payment Successful!</h3>
                      <p className="text-sm text-green-700 mt-1">
                        Your payment has been processed successfully. {order.order_status === 'PAID' ? 'Order status updated.' : 'Updating order status...'}
                      </p>
                    </div>
                  </div>
                  {order.order_status !== 'PAID' && (
                    <div className="ml-4 flex gap-2">
                      <button
                        onClick={refreshOrderDetails}
                        className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                      >
                        Refresh Status
                      </button>
                      <button
                        onClick={() => checkPaymentStatus()}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                      >
                        Force Update
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
