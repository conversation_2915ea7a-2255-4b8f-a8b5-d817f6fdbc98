-- 迁移脚本：将 order_items 表中的 lcsc_id 字段改为 ce_id
-- 适用于 Cloudflare D1 数据库

-- 1. 创建新的临时表，使用 ce_id 字段
CREATE TABLE order_items_new (
  item_id INTEGER PRIMARY KEY,
  order_id TEXT,
  user_id TEXT NOT NULL,
  ce_id TEXT,
  mfr_number TEXT,
  manufacturer TEXT,
  description TEXT,
  quantity INTEGER,
  unit_price REAL,
  ext_price REAL
);

-- 2. 复制现有数据到新表
INSERT INTO order_items_new (
  item_id, order_id, user_id, ce_id, mfr_number, 
  manufacturer, description, quantity, unit_price, ext_price
)
SELECT 
  item_id, order_id, user_id, lcsc_id, mfr_number, 
  manufacturer, description, quantity, unit_price, ext_price
FROM order_items;

-- 3. 删除旧表
DROP TABLE order_items;

-- 4. 重命名新表
ALTER TABLE order_items_new RENAME TO order_items;

-- 验证迁移结果
SELECT COUNT(*) as total_records FROM order_items;
SELECT * FROM order_items LIMIT 5;
