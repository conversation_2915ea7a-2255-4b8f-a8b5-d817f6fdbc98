'use client'

import { ComponentDetail, FullData } from '@/types/component'
import ComponentOverview from '@/components/ComponentDetail/ComponentOverview'
import Specifications from '@/components/ComponentDetail/Specifications'
import AssistInquiry from '@/components/ComponentDetail/AssistInquiry'
import AddToCart from '@/components/ComponentDetail/AddToCart'
import { useAuth } from '@/contexts/AuthContext'
import { ReactNode } from 'react'

// 产品内容组件接口
interface ProductClientWrapperProps {
  componentData: {
    component: ComponentDetail;
    parsedData: FullData;
  };
  summarizerComponent?: ReactNode; // 添加summarizer组件属性
}

export default function ProductClientWrapper({ 
  componentData, 
  summarizerComponent, 
}: ProductClientWrapperProps) {
  const { userId, token } = useAuth();
  const productId = componentData.component.id;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div className="lg:col-span-2 space-y-6">
        <ComponentOverview component={componentData.component} parsedData={componentData.parsedData} />
        <Specifications 
          component={componentData.component} 
          parsedData={componentData.parsedData} 
        />
        {/* 在Specifications后面渲染summarizer组件 */}
        {summarizerComponent}
      </div>
      <div className="space-y-6">
        <AddToCart
          component={componentData.component}
          parsedData={componentData.parsedData}
        />
        <AssistInquiry
          component={componentData.component}
          parsedData={componentData.parsedData}
          userId={userId}
          token={token}
        />
      </div>
    </div>
  )
} 