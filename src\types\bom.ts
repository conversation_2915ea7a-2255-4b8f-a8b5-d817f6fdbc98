/**
 * BOM (Bill of Materials) 相关类型定义
 */

// BOM文件上传响应
export interface BOMUploadResponse {
  success: boolean;
  bom_id: string;
  file_info: {
    url: string;
    filename: string;
    originalName: string;
  };
  message: string;
}

// BOM文件解析响应
export interface BOMParseResponse {
  success: boolean;
  headers: string[];
  preview_data: string[][];
  data_rows: string[][];
  total_rows: number;
  suggested_mapping: {
    description: string;
    selection: number[][];
    mapping_info: Record<string, string>;
  };
}

// BOM处理响应
export interface BOMProcessResponse {
  success: boolean;
  bom_id: string;
  statistics: {
    item_count: number;
    success_count: number;
    exact_match_count: number;
    partial_match_count: number;
    no_match_count: number;
    est_total_price: number;
    each_price: number;
  };
  bom_items: BOMItem[];
  message: string;
}

// BOM详情响应
export interface BOMDetailResponse {
  success: boolean;
  bom_id: string;
  bom_main: BOMMain;
  bom_items: BOMItem[];
}

// BOM主记录
export interface BOMMain {
  bom_id: string;
  bom_name: string;
  status: number;
  user_id: string;
  file_url: string;
  item_count: number;
  success_count: number;
  exact_match_count: number;
  partial_match_count: number;
  no_match_count: number;
  est_total_price: number;
  each_price: number;
  priority_matching: number;
  quantity_multiplier: number;
  create_at: string;
  update_at: string;
}

// BOM项目
export interface BOMItem {
  bom_items_id: string;
  user_id: string;
  bom_id: string;
  product_code: string;
  customer_part_number: string;
  match_status: number;
  requested_qty: number;
  order_qty: number;
  stock_availability: string;
  target_price: number;
  lead_time: string;
  unit_price: number;
  ext_price: number;
  offer_availability: string;
  packaging_choice: string;
  notes: string;
  create_at: string;
  update_at: string;
}

// 列映射类型枚举
export enum ColumnType {
  NONE = 0,
  QUANTITY = 1,
  CE_PART_NUMBER = 2,
  MANUFACTURER = 3,
  MANUFACTURER_PART_NUMBER = 4,
  DESCRIPTION = 5,
  PACKAGE = 6,
  CUSTOMER_PART_NUMBER = 7,
  TARGET_PRICE = 8,
  TARGET_LEAD_TIME = 9
}

// 列映射选项
export const COLUMN_TYPE_OPTIONS = [
  { value: ColumnType.NONE, label: 'Select Type' },
  { value: ColumnType.QUANTITY, label: 'Quantity' },
  { value: ColumnType.CE_PART_NUMBER, label: 'CE Part Number' },
  { value: ColumnType.MANUFACTURER, label: 'Manufacturer' },
  { value: ColumnType.MANUFACTURER_PART_NUMBER, label: 'Manufacturer Part Number' },
  { value: ColumnType.DESCRIPTION, label: 'Description' },
  { value: ColumnType.PACKAGE, label: 'Package' },
  { value: ColumnType.CUSTOMER_PART_NUMBER, label: 'Customer Part Number' },
  { value: ColumnType.TARGET_PRICE, label: 'Target Price(USD)' },
  { value: ColumnType.TARGET_LEAD_TIME, label: 'Target lead Time(Business days)' }
];

// 优先匹配类型
export type PriorityMatchType = 'in_stock' | 'exact';

// BOM处理请求参数
export interface BOMProcessRequest {
  bomId: string;
  userId: string;
  startLine: number;
  selection: number[][];
  priorityMatch: PriorityMatchType;
  coefficientTotal: number;
}

// BOM列表响应
export interface BOMListResponse {
  success: boolean;
  boms: BOMMain[];
  total: number;
  page: number;
  limit: number;
}

// 匹配状态
export enum MatchStatus {
  NO_MATCH = 0,
  EXACT_MATCH = 1,
  PARTIAL_MATCH = 2
}

// 库存状态
export type StockStatus = 'In Stock' | 'Out of Stock' | 'Backordered' | 'Discontinued';

// BOM状态
export enum BOMStatus {
  DRAFT = 0,
  PROCESSING = 1,
  COMPLETED = 2,
  FAILED = 3
}
