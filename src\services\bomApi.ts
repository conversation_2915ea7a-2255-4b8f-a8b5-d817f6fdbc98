/**
 * BOM (Bill of Materials) API 服务
 */

import {
  BOMUploadResponse,
  BOMParseResponse,
  BOMProcessResponse,
  BOMDetailResponse,
  BOMListResponse,
  BOMProcessRequest
} from '@/types/bom';

// BOM API 基础URL
const BOM_API_BASE_URL = 'https://bom-processor.chinaelectron.com';

/**
 * 上传BOM文件
 * @param file - 要上传的文件
 * @param bomName - BOM项目名称
 * @param userId - 用户ID
 * @returns Promise<BOMUploadResponse>
 */
export const uploadBOMFile = async (
  file: File,
  bomName: string,
  userId: string
): Promise<BOMUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('bomName', bomName);
  formData.append('userId', userId);

  const response = await fetch(`${BOM_API_BASE_URL}/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * 解析BOM文件
 * @param fileUrl - 文件URL
 * @param startLine - 起始行号（默认为2，跳过表头）
 * @returns Promise<BOMParseResponse>
 */
export const parseBOMFile = async (
  fileUrl: string,
  startLine: number = 2
): Promise<BOMParseResponse> => {
  const response = await fetch(`${BOM_API_BASE_URL}/parse-excel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      fileUrl,
      startLine,
    }),
  });

  if (!response.ok) {
    throw new Error(`Parse failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * 处理BOM数据
 * @param params - BOM处理参数
 * @returns Promise<BOMProcessResponse>
 */
export const processBOM = async (
  params: BOMProcessRequest
): Promise<BOMProcessResponse> => {
  const response = await fetch(`${BOM_API_BASE_URL}/process-bom`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`Process failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * 获取BOM详情
 * @param bomId - BOM ID
 * @returns Promise<BOMDetailResponse>
 */
export const getBOMDetail = async (bomId: string): Promise<BOMDetailResponse> => {
  const response = await fetch(`${BOM_API_BASE_URL}/bom-detail?bomId=${bomId}`, {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error(`Get detail failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * 获取用户的BOM列表
 * @param userId - 用户ID
 * @param page - 页码（默认为1）
 * @param limit - 每页记录数（默认为20）
 * @returns Promise<BOMListResponse>
 */
export const getBOMList = async (
  userId: string,
  page: number = 1,
  limit: number = 20
): Promise<BOMListResponse> => {
  const params = new URLSearchParams({
    userId,
  });

  const response = await fetch(`${BOM_API_BASE_URL}/user-boms?${params}`, {
    method: 'GET',
  });

  if (!response.ok) {
    throw new Error(`Get list failed: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();

  // 根据API文档，响应结构为 { success, user_id, bom_count, bom_list }
  return {
    success: data.success,
    boms: data.bom_list || [],
    total: data.bom_count || 0,
    page: page,
    limit: limit
  };
};

/**
 * 删除BOM项目
 * @param bomId - BOM ID
 * @param userId - 用户ID
 * @returns Promise<{success: boolean; message: string}>
 */
export const deleteBOM = async (
  bomId: string,
  userId: string
): Promise<{success: boolean; message: string}> => {
  const response = await fetch(`${BOM_API_BASE_URL}/delete-bom`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      bomId,
      userId,
    }),
  });

  if (!response.ok) {
    throw new Error(`Delete failed: ${response.status} ${response.statusText}`);
  }

  return response.json();
};

/**
 * 验证文件类型是否为CSV
 * @param file - 文件对象
 * @returns boolean
 */
export const validateCSVFile = (file: File): boolean => {
  const allowedTypes = ['text/csv', 'application/csv'];
  const allowedExtensions = ['.csv'];
  
  const hasValidType = allowedTypes.includes(file.type);
  const hasValidExtension = allowedExtensions.some(ext => 
    file.name.toLowerCase().endsWith(ext)
  );
  
  return hasValidType || hasValidExtension;
};

/**
 * 格式化文件大小
 * @param bytes - 字节数
 * @returns 格式化的文件大小字符串
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 验证列映射配置
 * @param selection - 列映射选择数组
 * @returns {isValid: boolean; errors: string[]}
 */
export const validateColumnMapping = (selection: number[][]): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  // 检查是否选择了Quantity（必选）
  const hasQuantity = selection.some(([, type]) => type === 1);
  if (!hasQuantity) {
    errors.push('Quantity is required');
  }
  
  // 检查是否选择了CE Part Number、Manufacturer Part Number或Description中的至少一个
  const hasRequiredField = selection.some(([, type]) => [2, 4, 5].includes(type));
  if (!hasRequiredField) {
    errors.push('At least one of CE Part Number, Manufacturer Part Number, or Description is required');
  }
  
  // 检查是否有重复的列类型选择（除了0-不选择）
  const selectedTypes = selection
    .filter(([, type]) => type !== 0)
    .map(([, type]) => type);
  
  const uniqueTypes = new Set(selectedTypes);
  if (selectedTypes.length !== uniqueTypes.size) {
    errors.push('Each column type can only be selected once');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
