<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>D1 Database API 测试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #555;
        }
        input, textarea, select, button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007cba;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ D1 Database API 测试工具</h1>
        
        <!-- API 基础配置 -->
        <div class="section">
            <h3>⚙️ API 配置</h3>
            <input type="text" id="apiBase" placeholder="API 基础URL" value="http://localhost:8787">
            <button onclick="testConnection()">测试连接</button>
            <div id="connectionResult" class="result" style="display: none;"></div>
        </div>

        <div class="grid">
            <!-- 数据库信息 -->
            <div class="section">
                <h3>📊 数据库信息</h3>
                <button onclick="getDatabaseInfo()">获取数据库信息</button>
                <div id="dbInfoResult" class="result" style="display: none;"></div>
            </div>

            <!-- 表查询 -->
            <div class="section">
                <h3>🔍 表查询</h3>
                <input type="text" id="queryTable" placeholder="表名" value="users">
                <input type="text" id="queryWhere" placeholder="WHERE 条件 (如: status=active)" value="">
                <input type="number" id="queryLimit" placeholder="限制条数" value="10">
                <button onclick="queryTable()">查询数据</button>
                <div id="queryResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="grid">
            <!-- 插入数据 -->
            <div class="section">
                <h3>➕ 插入数据</h3>
                <input type="text" id="insertTable" placeholder="表名" value="users">
                <textarea id="insertData" placeholder="JSON 数据" rows="4">{"name": "Test User", "email": "<EMAIL>"}</textarea>
                <button onclick="insertData()">插入数据</button>
                <div id="insertResult" class="result" style="display: none;"></div>
            </div>

            <!-- 批量插入 -->
            <div class="section">
                <h3>📦 批量插入</h3>
                <input type="text" id="batchInsertTable" placeholder="表名" value="products">
                <textarea id="batchInsertData" placeholder="JSON 数组" rows="4">[{"name": "Product1", "price": 99.99}, {"name": "Product2", "price": 149.99}]</textarea>
                <button onclick="batchInsert()">批量插入</button>
                <div id="batchInsertResult" class="result" style="display: none;"></div>
            </div>
        </div>

        <!-- 多表联查 -->
        <div class="section">
            <h3>🔗 多表联查</h3>
            <textarea id="joinQuery" placeholder="联查配置 JSON" rows="8">{
  "select": ["u.name", "u.email", "o.total"],
  "from": "users u",
  "joins": [
    {
      "type": "INNER",
      "table": "orders o",
      "on": "u.user_id = o.user_id"
    }
  ],
  "where": {
    "u.status": "active"
  },
  "limit": 20
}</textarea>
            <button onclick="executeJoin()">执行联查</button>
            <div id="joinResult" class="result" style="display: none;"></div>
        </div>

        <!-- 自定义 SQL -->
        <div class="section">
            <h3>🛠️ 自定义 SQL</h3>
            <textarea id="customSql" placeholder="SQL 查询" rows="3">SELECT COUNT(*) as total FROM users WHERE status = ?</textarea>
            <textarea id="sqlParams" placeholder="参数 JSON 数组" rows="2">["active"]</textarea>
            <button onclick="executeCustomSql()">执行 SQL</button>
            <div id="sqlResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function getApiBase() {
            return document.getElementById('apiBase').value.replace(/\/$/, '');
        }

        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.textContent = JSON.stringify(data, null, 2);
        }

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { data, isError: !response.ok };
            } catch (error) {
                return { data: { error: error.message }, isError: true };
            }
        }

        async function testConnection() {
            const { data, isError } = await makeRequest(`${getApiBase()}/api/info`);
            showResult('connectionResult', data, isError);
        }

        async function getDatabaseInfo() {
            const { data, isError } = await makeRequest(`${getApiBase()}/api/info`);
            showResult('dbInfoResult', data, isError);
        }

        async function queryTable() {
            const table = document.getElementById('queryTable').value;
            const where = document.getElementById('queryWhere').value;
            const limit = document.getElementById('queryLimit').value;
            
            let url = `${getApiBase()}/api/table/${table}?limit=${limit}`;
            
            if (where) {
                const [field, value] = where.split('=');
                if (field && value) {
                    url += `&where_${field.trim()}=${value.trim()}`;
                }
            }
            
            const { data, isError } = await makeRequest(url);
            showResult('queryResult', data, isError);
        }

        async function insertData() {
            const table = document.getElementById('insertTable').value;
            const jsonData = document.getElementById('insertData').value;
            
            try {
                const data = JSON.parse(jsonData);
                const { data: result, isError } = await makeRequest(`${getApiBase()}/api/table/${table}`, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                showResult('insertResult', result, isError);
            } catch (error) {
                showResult('insertResult', { error: 'Invalid JSON: ' + error.message }, true);
            }
        }

        async function batchInsert() {
            const table = document.getElementById('batchInsertTable').value;
            const jsonData = document.getElementById('batchInsertData').value;
            
            try {
                const data = JSON.parse(jsonData);
                const { data: result, isError } = await makeRequest(`${getApiBase()}/api/batch/insert/${table}`, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });
                showResult('batchInsertResult', result, isError);
            } catch (error) {
                showResult('batchInsertResult', { error: 'Invalid JSON: ' + error.message }, true);
            }
        }

        async function executeJoin() {
            const jsonQuery = document.getElementById('joinQuery').value;
            
            try {
                const query = JSON.parse(jsonQuery);
                const { data, isError } = await makeRequest(`${getApiBase()}/api/join/`, {
                    method: 'POST',
                    body: JSON.stringify(query)
                });
                showResult('joinResult', data, isError);
            } catch (error) {
                showResult('joinResult', { error: 'Invalid JSON: ' + error.message }, true);
            }
        }

        async function executeCustomSql() {
            const sql = document.getElementById('customSql').value;
            const paramsText = document.getElementById('sqlParams').value;
            
            try {
                const params = paramsText ? JSON.parse(paramsText) : [];
                const { data, isError } = await makeRequest(`${getApiBase()}/api/query/`, {
                    method: 'POST',
                    body: JSON.stringify({ sql, params })
                });
                showResult('sqlResult', data, isError);
            } catch (error) {
                showResult('sqlResult', { error: 'Invalid JSON: ' + error.message }, true);
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
