import Link from 'next/link'

async function getFirstTexts() {
  try {
    const response = await fetch('https://webapi.chinaelectron.com/product-index/first-text', { next: { revalidate: 86400 } })
    const data = await response.json()
    return data.results?.map((item: any) => item.first_text) || []
  } catch (error) {
    console.error('Error fetching first texts:', error)
    return []
  }
}

export default async function Footer() {
  const firstTexts = await getFirstTexts()

  const footerLinks = {
    Components: [
      { name: 'Components', href: '/components' },
      { name: 'Manufacturers', href: '/manufacturers' },
      { name: 'RFQ', href: '/rfq' },
      { name: 'BOM', href: '/bom' },
      { name: 'Daily Hot', href: '/dailyhot' },
    ],
    Company: [
      { name: 'About Us', href: '/about-us' },
      { name: 'Privacy Policy', href: '/privacy-policy' },
      { name: 'Terms of Service', href: '/terms-of-service' },
    ],
  }

  return (
    <footer className="bg-[#0A051F] border-t border-white/[0.08] pt-16 pb-8 w-full">
      <div className="max-w-[1900px] mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-12 mb-12">
          {/* Logo and Description */}
          <div className="md:col-span-1">
            <Link href="/" className="flex items-center">
              <img
                src="https://img.chinaelectron.com/logo-white.svg"
                alt="Logo"
                className="h-24 w-auto"
              />
            </Link>
            <p className="mt-4 text-[#A1A1A9] text-sm">
              Professional electronic components sourcing platform for engineers and procurement
            </p>

            {/* Contact Information */}
            <div className="mt-6">
              <p className="text-[#A1A1A9] text-sm mb-2">
                <span className="text-white">Email: </span>
                <a href="mailto:<EMAIL>" className="hover:text-white transition-colors duration-200">
                  <EMAIL>
                </a>
              </p>
              <p className="text-[#A1A1A9] text-sm mb-2">
                <span className="text-white">Discord: </span>
                <a href="https://discord.gg/tZkeYQ3AKx" target="_blank" rel="noopener noreferrer" className="hover:text-white transition-colors duration-200">
                  Join our community
                </a>
              </p>
            </div>
          </div>

          {/* Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-white font-semibold mb-4">{category}</h3>
              <ul className="space-y-3">
                {links.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-[#A1A1A9] hover:text-white text-sm transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}

          {/* Discord QR Code */}
          <div className="md:col-span-1">
            <h3 className="text-white font-semibold mb-4">Join Our Discord</h3>
            <div className="bg-white p-2 rounded-md inline-block">
              <img
                src="https://img.chinaelectron.com/discord-qr.png"
                alt="Discord QR Code"
                className="w-[120px] h-[120px]"
              />
            </div>
            <p className="mt-2 text-[#A1A1A9] text-xs">
              Scan to join our Discord community
            </p>
            <a
              href="https://discord.gg/tZkeYQ3AKx"
              target="_blank"
              rel="noopener noreferrer"
              className="mt-1 inline-block text-blue-400 hover:text-blue-300 text-xs transition-colors duration-200"
            >
              https://discord.gg/tZkeYQ3AKx
            </a>
          </div>

          {/* WhatsApp QR Code */}
          <div className="md:col-span-1">
            <h3 className="text-white font-semibold mb-4">Contact via WhatsApp</h3>
            <div className="bg-white p-2 rounded-md inline-block">
              <img
                src="/WhatsApp.png"
                alt="WhatsApp QR Code"
                className="w-[120px] h-[120px]"
              />
            </div>
            <p className="mt-2 text-[#A1A1A9] text-xs">
              Scan to contact us on WhatsApp
            </p>
          </div>
        </div>

        {/* Product Index First Text Row */}
        <div className="py-6 border-t border-white/[0.08] overflow-x-auto">
          <div className="flex flex-wrap gap-4">
            {firstTexts.map((text: string, index: number) => (
              <Link 
                key={index} 
                href={`/product_index/${text}`}
                className="text-[#A1A1A9] hover:text-white text-sm transition-colors duration-200"
              >
                {text}
              </Link>
            ))}
          </div>
        </div>

        {/* Friendly Links */}
        <div className="py-6 border-t border-white/[0.08]">
          <h3 className="text-white font-semibold mb-4">Friendly Links</h3>
          <div className="flex flex-wrap gap-6">
            <a
              href="https://lscsc.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#A1A1A9] hover:text-white text-sm transition-colors duration-200"
            >
              LSCSC
            </a>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="py-6 border-t border-white/[0.08]">
          <div className="flex flex-wrap items-center justify-center gap-4">
            {/* SSL Encryption */}
            <div className="flex items-center">
              <a
                href="https://www.trustlogo.com/ttb_searcher/trustlogo?v_querytype=W&v_shortname=CL1&v_search=https://chinaelectron.com&x=6&y=5"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:opacity-80 transition-opacity"
              >
                <img
                  src="https://img.chinaelectron.com/sectigo_trust_seal_lg_140x54.png"
                  alt="SSL Encrypted Payment"
                  className="h-auto w-auto max-h-8"
                />
              </a>
            </div>

            {/* Payment Methods */}
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_paypal_icon.png"
                alt="PayPal"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_visa_icon.png"
                alt="Visa"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_double_icon.png"
                alt="Mastercard"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_amex_icon.png"
                alt="American Express"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_jcb_icon.png"
                alt="JCB"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_dinersclub_icon.png"
                alt="Diners Club"
                className="h-8 w-auto"
              />
            </div>

            {/* Shipping Methods */}
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_dhl_icon.png"
                alt="DHL"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_fedex_icon.png"
                alt="FedEx"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_ems_icon.png"
                alt="EMS"
                className="h-8 w-auto"
              />
            </div>
            <div className="flex items-center">
              <img
                src="/footer_icon/foot_sf_icon.png"
                alt="SF Express"
                className="h-8 w-auto"
              />
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="pt-8 border-t border-white/[0.08] flex flex-col md:flex-row justify-between items-center">
          <p className="text-[#A1A1A9] text-sm">
            © {new Date().getFullYear()} China Electron. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
} 