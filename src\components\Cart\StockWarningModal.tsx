'use client'

import { useState } from 'react'
import { CartItem } from '@/services/api'

interface StockWarningModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  insufficientStockItems: CartItem[]
  validItemsCount: number
}

export default function StockWarningModal({
  isOpen,
  onClose,
  onConfirm,
  insufficientStockItems,
  validItemsCount
}: StockWarningModalProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* 背景遮罩 */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={onClose}></div>
      
      {/* Modal内容 */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-2xl shadow-xl max-w-2xl w-full mx-auto transform transition-all">
          {/* 头部 */}
          <div className="px-8 pt-8 pb-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-14 h-14 bg-yellow-100 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
              <div className="ml-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  Stock Insufficient
                </h3>
                <p className="text-base text-gray-600 mt-1">
                  Some items in your cart have insufficient stock
                </p>
              </div>
            </div>
          </div>

          {/* 内容 */}
          <div className="px-8 pb-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
              <p className="text-base text-yellow-800 mb-4 font-medium">
                The following items will be excluded from checkout:
              </p>
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {insufficientStockItems.map((item, index) => (
                  <div key={index} className="flex items-center justify-between text-base bg-white rounded-md p-3 border border-yellow-100">
                    <span className="text-gray-700 truncate flex-1 mr-4 font-medium">
                      {item.model_number}
                    </span>
                    <span className="text-red-600 font-semibold whitespace-nowrap">
                      Need: {item.cart_quantity}, Available: {item.available_stock || item.stock_quantity}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {validItemsCount > 0 ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-5">
                <p className="text-base text-green-800">
                  <span className="font-semibold">{validItemsCount} items</span> with sufficient stock will proceed to checkout.
                </p>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 rounded-lg p-5">
                <p className="text-base text-red-800 font-semibold">
                  All selected items have insufficient stock. Please adjust quantities or refresh your cart.
                </p>
              </div>
            )}
          </div>

          {/* 底部按钮 */}
          <div className="px-8 pb-8">
            <div className="flex space-x-4">
              <button
                onClick={onClose}
                className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium text-base"
              >
                Cancel
              </button>
              {validItemsCount > 0 && (
                <button
                  onClick={onConfirm}
                  className="flex-1 px-6 py-3 bg-[#DE2910] text-white rounded-lg hover:bg-[#DE2910]/90 transition-colors font-medium text-base"
                >
                  Continue ({validItemsCount} items)
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
