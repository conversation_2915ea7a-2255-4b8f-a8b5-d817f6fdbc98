'use client'

import { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { parseBOMFile, processBOM, validateColumnMapping, getBOMDetail } from '@/services/bomApi'
import { BOMParseResponse, COLUMN_TYPE_OPTIONS, ColumnType, PriorityMatchType } from '@/types/bom'
import { AlertCircle, CheckCircle, ArrowLeft, ArrowRight } from 'lucide-react'

interface BOMMappingPageProps {
  params: Promise<{
    bomId: string
  }>
}

export default function BOMMappingPage({ params }: BOMMappingPageProps) {
  const router = useRouter()
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const resolvedParams = use(params)
  const [parseData, setParseData] = useState<BOMParseResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [error, setError] = useState('')
  const [selection, setSelection] = useState<number[][]>([])
  const [quantityMultiple, setQuantityMultiple] = useState(1)
  const [priorityMatch, setPriorityMatch] = useState<PriorityMatchType>('in_stock')
  const [startLine, setStartLine] = useState(2)
  const [fileUrl, setFileUrl] = useState<string | null>(null)
  const [selectedStartRow, setSelectedStartRow] = useState(2)

  const bomId = resolvedParams.bomId

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return

    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    loadBOMInfo()
  }, [isAuthenticated, authLoading, bomId])

  const loadBOMInfo = async () => {
    try {
      setLoading(true)
      setError('')

      // 首先获取BOM详情以获取文件URL
      const bomDetail = await getBOMDetail(bomId)
      if (bomDetail.success && bomDetail.bom_main.file_url) {
        setFileUrl(bomDetail.bom_main.file_url)
        // 获取到文件URL后，解析文件
        await loadParseData(bomDetail.bom_main.file_url)
      } else {
        setError('Failed to get BOM information')
        setLoading(false)
      }
    } catch (err) {
      console.error('Load BOM info error:', err)
      setError('Failed to load BOM information')
      setLoading(false)
    }
  }

  const loadParseData = async (url: string) => {
    try {
      const response = await parseBOMFile(url, startLine)
      setParseData(response)

      // 初始化列映射选择
      if (response.suggested_mapping?.selection) {
        setSelection(response.suggested_mapping.selection)
      } else {
        // 如果没有建议映射，初始化为全部不选择，但给第一列设置为Quantity
        const initialSelection = response.headers.map((_, index) => [index, index === 0 ? 1 : 0])
        setSelection(initialSelection)
      }
      setLoading(false)
    } catch (err) {
      console.error('Parse error:', err)
      setError(err instanceof Error ? err.message : 'Failed to parse file')
      setLoading(false)
    }
  }

  const handleColumnTypeChange = (columnIndex: number, typeValue: number) => {
    const newSelection = [...selection]
    newSelection[columnIndex] = [columnIndex, typeValue]
    setSelection(newSelection)
  }

  const handleStartRowChange = (newStartRow: number) => {
    setSelectedStartRow(newStartRow)
    setStartLine(newStartRow)
    // 不需要重新解析文件，只是更新起始行标记
    // 实际的起始行处理会在提交时进行
  }



  const handleCreateBOM = async () => {
    if (!userId) {
      setError('User not authenticated')
      return
    }

    // 验证列映射
    const validation = validateColumnMapping(selection)
    if (!validation.isValid) {
      setError(validation.errors.join(', '))
      return
    }

    setProcessing(true)
    setError('')

    try {
      const processRequest = {
        bomId,
        userId,
        startLine,
        selection,
        priorityMatch,
        coefficientTotal: quantityMultiple
      }

      const response = await processBOM(processRequest)

      if (response.success) {
        // 处理成功，跳转到BOM详情页面
        router.push(`/bom/detail/${bomId}`)
      } else {
        setError('BOM processing failed')
      }
    } catch (err) {
      console.error('Process error:', err)
      setError(err instanceof Error ? err.message : 'Failed to process BOM')
    } finally {
      setProcessing(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{authLoading ? 'Checking authentication...' : 'Loading BOM data...'}</p>
        </div>
      </div>
    )
  }

  if (error && !parseData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/bom')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to Upload
          </button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w mx-auto px-14">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">BOM Mapping</h1>
          <p className="text-gray-600 text-sm">A preview of the uploaded data is shown below and columns have been mapped based on file contents.</p>
          <div className="mt-3 text-sm">
            <p className="text-red-600">• Please select the row where part information begins.</p>
            <p className="text-red-600">• At least two column types are required: <span className="font-medium">Quantity</span> and <span className="font-medium">others</span> (CE Part Number / Mfr.# / Description)</p>
            <p className="text-gray-600">• The more column types you choose, the more accurate the data will be.</p>
          </div>
        </div>

        {/* 步骤指示器 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-green-600 text-white rounded-full text-sm font-medium">
              <CheckCircle className="h-4 w-4" />
            </div>
            <span className="ml-2 text-sm font-medium text-green-600">Upload BOM File</span>
          </div>
          <div className="w-16 h-0.5 bg-green-600 mx-4"></div>
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">
              2
            </div>
            <span className="ml-2 text-sm font-medium text-blue-600">Column Mapping</span>
          </div>
          <div className="w-16 h-0.5 bg-gray-300 mx-4"></div>
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full text-sm font-medium">
              3
            </div>
            <span className="ml-2 text-sm font-medium text-gray-500">BOM Details</span>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200">
          {/* 列映射表格 */}

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-800 text-white">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium">Row</th>
                  {parseData?.headers.slice(0, 6).map((header, index) => (
                    <th key={index} className="px-4 py-3 text-left text-sm font-medium">
                      Column {index + 1}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {/* 列类型选择行 */}
                <tr className="bg-gray-100 border-b">
                  <td className="px-4 py-3">
                    <span className="text-sm text-gray-500">Start Row</span>
                  </td>
                  {parseData?.headers.slice(0, 6).map((header, index) => (
                    <td key={index} className="px-4 py-3">
                      <select
                        value={selection[index]?.[1] || 0}
                        onChange={(e) => handleColumnTypeChange(index, parseInt(e.target.value))}
                        className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                      >
                        {COLUMN_TYPE_OPTIONS.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </td>
                  ))}
                </tr>

                {/* 表头行 */}
                <tr className="bg-white border-b font-medium">
                  <td className="px-4 py-3 text-sm text-gray-500">Row #</td>
                  {parseData?.headers.slice(0, 6).map((header, index) => (
                    <td key={index} className="px-4 py-3 text-sm">
                      {header}
                    </td>
                  ))}
                </tr>

                {/* 数据行 */}
                {parseData?.preview_data.slice(1, 9).map((row, rowIndex) => (
                  <tr key={rowIndex} className={`border-b ${rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="px-4 py-2 text-sm text-gray-700">
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="startRow"
                          className="mr-2"
                          checked={selectedStartRow === rowIndex + 2}
                          onChange={() => handleStartRowChange(rowIndex + 2)}
                        />
                        <span>{rowIndex + 2}</span>
                      </div>
                    </td>
                    {row.slice(0, 6).map((cell, cellIndex) => (
                      <td key={cellIndex} className="px-4 py-2 text-sm text-gray-700 max-w-xs truncate">
                        {cell || '-'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 底部控制区域 */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-6">
                <div className="flex items-center space-x-2">
                  <label className="text-sm font-medium text-gray-700">Quantity Multiple</label>
                  <input
                    type="number"
                    min="1"
                    step="0.1"
                    value={quantityMultiple}
                    onChange={(e) => setQuantityMultiple(parseFloat(e.target.value) || 1)}
                    className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  />
                </div>

              </div>
              <button
                onClick={handleCreateBOM}
                disabled={processing}
                className="px-6 py-2 bg-blue-600 text-white text-sm font-medium rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {processing ? 'Processing...' : 'Create BOM'}
              </button>
            </div>
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="p-6 border-t border-gray-200">
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}


        </div>


      </div>
      </div>
    </>
  )
}
