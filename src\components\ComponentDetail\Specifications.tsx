import { ComponentDetail, FullData } from '@/types/component'
import { ReactNode } from 'react'

interface SpecificationsProps {
  component: ComponentDetail
  parsedData: FullData
  summarizerContent?: ReactNode
}

interface ParameterItem {
  param_name: string;
  param_value: string;
}

export default function Specifications({ component, parsedData }: SpecificationsProps) {
  // 获取产品型号
  const productModel = (component.metadata as any)?.model || component.metadata.name || 'Unknown Product'

  // 获取参数列表，优先使用产品详情API返回的parameters.english
  // 如果在parsedData中找不到，则尝试使用旧的路径
  const parameters: ParameterItem[] =
    (parsedData as any)?.parameters?.english ||
    parsedData?.parameter?.languages?.english?.parameters?.main_parameters?.map?.(
      (key: string, value: string) => ({ param_name: key, param_value: value })
    ) || [];

  // 渲染表格行
  const renderTableRow = (label: string, value: string | React.ReactNode, key?: string) => {
    const displayValue = value === undefined || value === null ? 'N/A' : value

    return (
      <tr key={key || label} className="border-t border-gray-100">
        <td className="py-3 w-1/3 text-gray-600">{label}</td>
        <td className="py-3 text-gray-900 font-medium">{displayValue}</td>
      </tr>
    )
  }

  // 判断是否有参数可以显示
  const hasParameters = parameters && parameters.length > 0;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
        <h2 className="text-xl font-semibold text-blue-900 flex items-center gap-2">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          {productModel} Specifications
        </h2>
      </div>
      <div className="p-6">
      
      {!hasParameters ? (
        <div className="text-gray-500 italic">No specification details available</div>
      ) : (
        <div className="mb-4">
          <table className="w-full border-separate border-spacing-0">
            <tbody>
              {parameters.map((param: ParameterItem, index: number) => (
                renderTableRow(param.param_name, param.param_value, `param-${index}`)
              ))}
            </tbody>
          </table>
        </div>
      )}
      </div>
    </div>
  )
} 