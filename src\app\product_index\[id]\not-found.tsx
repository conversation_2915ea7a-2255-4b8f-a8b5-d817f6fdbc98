import Link from 'next/link';
import { Metadata } from 'next';

// 定义首字母API响应类型
interface FirstTextResponse {
  total: number;
  results: { first_text: string }[];
}

// 生成元数据
export const metadata: Metadata = {
  title: 'Product Index Not Found | China Electron',
  description: 'The requested product index was not found. Browse our available product indices.',
  alternates: {
    canonical: 'https://www.chinaelectron.com/product_index/A',
  }
};

// 获取所有首字母列表
async function getAllFirstTexts(): Promise<string[]> {
  try {
    const response = await fetch(
      'https://webapi.chinaelectron.com/product-index/first-text',
      { 
        cache: 'force-cache', // 使用强制缓存，确保服务端和客户端获取相同的数据
        next: { revalidate: 86400 } // 每天重新验证一次
      }
    );
    
    if (!response.ok) {
      throw new Error(`Failed to fetch first texts: ${response.status}`);
    }
    
    const data: FirstTextResponse = await response.json();
    // 确保结果是排序的，这样服务端和客户端渲染的顺序一致
    return data.results.map(item => item.first_text).sort();
  } catch (error) {
    console.error('Error fetching first texts:', error);
    return [];
  }
}

export default async function NotFound() {
  // 获取所有首字母
  const firstTexts = await getAllFirstTexts();
  
  // 确保稳定排序
  const sortedFirstTexts = [...firstTexts];
  
  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <h2 className="text-3xl font-bold mb-4">Product Index Not Found</h2>
      <p className="text-gray-600 mb-8">
        The product index you are looking for does not exist or has no products.
      </p>
      
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">Browse by First Character</h3>
        <div className="flex flex-wrap justify-center gap-2 max-w-4xl mx-auto">
          {sortedFirstTexts.map(text => (
            <Link 
              key={text}
              href={`/product_index/${encodeURIComponent(text)}`}
              className="px-3 py-1 bg-white border rounded-md hover:bg-gray-100"
            >
              {text}
            </Link>
          ))}
        </div>
      </div>
      
      <Link 
        href="/"
        className="inline-block px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        Return to Home
      </Link>
    </div>
  );
} 