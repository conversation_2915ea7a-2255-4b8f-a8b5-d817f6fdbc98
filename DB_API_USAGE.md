# Cloudflare D1 数据库 API 使用指南

这是一个功能完整的 Cloudflare Workers 脚本，用于对 D1 数据库进行批量操作和多表联查。

## 🚀 部署步骤

### 1. 配置 wrangler.toml
确保 `wrangler.toml` 文件中的数据库配置正确：
```toml
[[d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "your-database-id"
```

### 2. 部署到 Cloudflare Workers
```bash
# 部署到开发环境
wrangler deploy --env dev

# 部署到生产环境
wrangler deploy --env production
```

### 3. 本地开发
```bash
# 启动本地开发服务器
wrangler dev

# 使用本地数据库
wrangler dev --local
```

## 📚 API 接口文档

### 基础信息
- **基础URL**: `https://your-worker.your-subdomain.workers.dev`
- **Content-Type**: `application/json`
- **支持 CORS**: 是

### 1. 数据库信息
```http
GET /api/info
```
返回数据库中所有表的结构信息、字段详情和行数统计。

### 2. 单表操作

#### 查询数据
```http
GET /api/table/{tableName}?where_field=value&limit=50&offset=0
```

**查询参数**:
- `where_{field}`: WHERE 条件
- `order_by`: 排序字段
- `order_dir`: 排序方向 (ASC/DESC)
- `limit`: 限制条数 (默认100)
- `offset`: 偏移量 (默认0)

**示例**:
```bash
# 查询活跃用户，按创建时间排序
GET /api/table/users?where_status=active&order_by=created_at&order_dir=DESC&limit=20

# 查询特定品牌的产品
GET /api/table/products?where_brand_id=BRAND123&limit=100
```

#### 插入数据
```http
POST /api/table/{tableName}
Content-Type: application/json

# 单条插入
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "status": "active"
}

# 批量插入
[
  {"name": "User1", "email": "<EMAIL>"},
  {"name": "User2", "email": "<EMAIL>"}
]
```

#### 更新数据
```http
PUT /api/table/{tableName}?where_id=123
Content-Type: application/json

{
  "name": "Updated Name",
  "status": "inactive"
}
```

#### 删除数据
```http
DELETE /api/table/{tableName}?where_id=123&where_status=inactive
```

### 3. 批量操作

#### 批量插入
```http
POST /api/batch/insert/{tableName}
Content-Type: application/json

[
  {"name": "Product1", "price": 99.99},
  {"name": "Product2", "price": 149.99},
  {"name": "Product3", "price": 199.99}
]
```

#### 批量更新
```http
POST /api/batch/update/{tableName}
Content-Type: application/json

[
  {
    "where": {"id": 1},
    "data": {"price": 89.99, "status": "sale"}
  },
  {
    "where": {"id": 2},
    "data": {"price": 129.99, "status": "sale"}
  }
]
```

#### 批量删除
```http
POST /api/batch/delete/{tableName}
Content-Type: application/json

[
  {"id": 1, "status": "deleted"},
  {"id": 2, "status": "deleted"}
]
```

### 4. 多表联查

```http
POST /api/join/
Content-Type: application/json

{
  "select": ["u.name", "u.email", "o.total", "p.name as product_name"],
  "from": "users u",
  "joins": [
    {
      "type": "INNER",
      "table": "orders o",
      "on": "u.user_id = o.user_id"
    },
    {
      "type": "LEFT",
      "table": "products p",
      "on": "o.product_id = p.product_id"
    }
  ],
  "where": {
    "u.status": "active",
    "o.total": {"gt": 100},
    "p.category": {"like": "%electronics%"}
  },
  "orderBy": "o.created_at DESC",
  "limit": 50,
  "offset": 0
}
```

**WHERE 条件操作符**:
- `gt`: 大于
- `lt`: 小于
- `gte`: 大于等于
- `lte`: 小于等于
- `like`: 模糊匹配
- `ne`: 不等于
- 数组: IN 查询

### 5. 自定义 SQL 查询

```http
POST /api/query/
Content-Type: application/json

{
  "sql": "SELECT COUNT(*) as total, brand_id FROM products WHERE price > ? GROUP BY brand_id ORDER BY total DESC",
  "params": [100]
}
```

## 🔧 实际使用示例

### JavaScript/Fetch 示例

```javascript
const API_BASE = 'https://your-worker.your-subdomain.workers.dev';

// 1. 获取数据库信息
async function getDatabaseInfo() {
  const response = await fetch(`${API_BASE}/api/info`);
  return await response.json();
}

// 2. 查询用户数据
async function getUsers(limit = 50) {
  const response = await fetch(`${API_BASE}/api/table/users?limit=${limit}&order_by=created_at`);
  return await response.json();
}

// 3. 创建新用户
async function createUser(userData) {
  const response = await fetch(`${API_BASE}/api/table/users`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(userData)
  });
  return await response.json();
}

// 4. 批量插入产品
async function batchInsertProducts(products) {
  const response = await fetch(`${API_BASE}/api/batch/insert/products`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(products)
  });
  return await response.json();
}

// 5. 多表联查：获取用户订单详情
async function getUserOrderDetails() {
  const joinQuery = {
    select: ['u.name', 'u.email', 'o.order_id', 'o.total', 'o.created_at'],
    from: 'users u',
    joins: [
      {
        type: 'INNER',
        table: 'orders o',
        on: 'u.user_id = o.user_id'
      }
    ],
    where: {
      'u.status': 'active',
      'o.total': { 'gt': 50 }
    },
    orderBy: 'o.created_at DESC',
    limit: 100
  };

  const response = await fetch(`${API_BASE}/api/join/`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(joinQuery)
  });
  return await response.json();
}
```

### cURL 示例

```bash
# 获取数据库信息
curl -X GET "https://your-worker.your-subdomain.workers.dev/api/info"

# 查询产品数据
curl -X GET "https://your-worker.your-subdomain.workers.dev/api/table/products?where_brand_id=BRAND123&limit=20"

# 插入新产品
curl -X POST "https://your-worker.your-subdomain.workers.dev/api/table/products" \
  -H "Content-Type: application/json" \
  -d '{"name":"New Product","price":99.99,"brand_id":"BRAND123"}'

# 多表联查
curl -X POST "https://your-worker.your-subdomain.workers.dev/api/join/" \
  -H "Content-Type: application/json" \
  -d '{
    "select": ["p.name", "b.name as brand_name", "p.price"],
    "from": "products p",
    "joins": [{"type": "INNER", "table": "brands b", "on": "p.brand_id = b.id"}],
    "where": {"p.price": {"gt": 100}},
    "limit": 50
  }'
```

## ⚠️ 注意事项

1. **安全性**: 生产环境建议添加身份验证
2. **限制**: 默认查询限制为100条，最大1000条
3. **性能**: 大量数据操作建议分批处理
4. **错误处理**: 所有接口都返回标准的错误格式

## 📋 数据库表结构

### component_mapping 表
用于存储 LCSC 编码和 CE 编码的映射关系。

**表结构**:
```sql
CREATE TABLE IF NOT EXISTS component_mapping (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    lcsc_code TEXT NOT NULL,
    ce_code TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(lcsc_code, ce_code)
);
```

**字段说明**:
- `id`: 主键，自增长
- `lcsc_code`: LCSC 编码，不能为空
- `ce_code`: CE 编码，不能为空
- `created_at`: 创建时间，默认当前时间
- `updated_at`: 更新时间，默认当前时间
- 唯一约束：`(lcsc_code, ce_code)` 组合唯一

**使用示例**:

```javascript
// 1. 查询映射关系
const mappings = await fetch(`${API_BASE}/api/table/component_mapping?where_lcsc_code=C123456&limit=10`);

// 2. 添加新的映射关系
const newMapping = {
  lcsc_code: "C123456",
  ce_code: "CMA100101416"
};
await fetch(`${API_BASE}/api/table/component_mapping`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(newMapping)
});

// 3. 批量插入映射关系
const mappings = [
  { lcsc_code: "C123456", ce_code: "CMA100101416" },
  { lcsc_code: "C789012", ce_code: "CMA100101417" }
];
await fetch(`${API_BASE}/api/batch/insert/component_mapping`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(mappings)
});

// 4. 通过 LCSC 编码查找 CE 编码
const joinQuery = {
  select: ['cm.ce_code', 'cm.lcsc_code'],
  from: 'component_mapping cm',
  where: {
    'cm.lcsc_code': 'C123456'
  },
  limit: 1
};
const result = await fetch(`${API_BASE}/api/join/`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(joinQuery)
});
```

## 🛠️ 自定义扩展

你可以根据需要修改 `db-script.js` 来添加：
- 身份验证中间件
- 数据验证
- 缓存机制
- 日志记录
- 更复杂的查询逻辑
