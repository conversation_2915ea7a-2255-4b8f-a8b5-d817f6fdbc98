'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { verifyTurnstileToken } from '@/services/turnstileService'
import { config } from '@/lib/config'

interface AuthContextType {
  isAuthenticated: boolean
  token: string | null
  userId: string | null
  login: (username: string, password: string, turnstileToken?: string | null) => Promise<void>
  logout: () => Promise<void>
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [token, setToken] = useState<string | null>(null)
  const [userId, setUserId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // 初始化时检查 localStorage 中是否有 token 和 userId
  useEffect(() => {
    const initializeAuth = () => {
      if (typeof window !== 'undefined') {
        const storedToken = localStorage.getItem('token')
        const storedUserId = localStorage.getItem('userId')
        if (storedToken) {
          setToken(storedToken)
          setIsAuthenticated(true)
          if (storedUserId) {
            setUserId(storedUserId)
          }
        }
      }
      setIsLoading(false)
    }
    initializeAuth()
  }, [])

  const login = async (username: string, password: string, turnstileToken?: string | null) => {
    try {
      // 如果提供了 Turnstile token，先进行验证
      if (turnstileToken) {
        // 客户端不直接验证 Turnstile token，通常这是在服务器端进行的
        // 这里我们假设 Turnstile token 有效，并在登录请求中一同提交
        // console.log('Turnstile token provided:', turnstileToken.substring(0, 10) + '...');
      }

      const response = await fetch('https://auth.chinaelectron.com/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin
        },
        body: JSON.stringify({ 
          username, 
          password,
          // 如果有 Turnstile token，也一并提交
          ...(turnstileToken ? { cf_turnstile_response: turnstileToken } : {})
        })
      })

      const data = await response.json()

      if (data.success) {
        // 保存 token 到 localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('token', data.data.token)
          
          // 检查 API 响应中是否有 userID（注意大小写）
          // API 返回的是 userID（大写ID），而不是 userId（小写id）
          // console.log('Login response data:', data.data); // 调试日志
          
          if (data.data.userId) {
            // 保存 userID 到 localStorage
            localStorage.setItem('userId', data.data.userId)
            setUserId(data.data.userId)
            // console.log('UserID saved:', data.data.userId); // 调试日志
          } else {
            console.warn('No userID found in login response:', data.data);
          }
        }
        
        setToken(data.data.token)
        setIsAuthenticated(true)
        router.push('/')
      } else {
        throw new Error(data.message || '登录失败')
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error
    }
  }

  const logout = async () => {
    try {
      const response = await fetch('https://auth.chinaelectron.com/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin
        }
      })

      const data = await response.json()

      if (typeof window !== 'undefined') {
        localStorage.removeItem('token')
        localStorage.removeItem('userId')
      }
      
      setToken(null)
      setUserId(null)
      setIsAuthenticated(false)
      router.push('/login')

      if (!data.success) {
        console.warn('Logout API returned error:', data.message)
      }
    } catch (error) {
      if (typeof window !== 'undefined') {
        localStorage.removeItem('token')
        localStorage.removeItem('userId')
      }
      
      setToken(null)
      setUserId(null)
      setIsAuthenticated(false)
      router.push('/login')
      throw error
    }
  }

  return (
    <AuthContext.Provider value={{ isAuthenticated, token, userId, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 