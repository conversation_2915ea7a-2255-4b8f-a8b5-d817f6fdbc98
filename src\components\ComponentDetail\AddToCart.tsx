'use client'

import { useState, useEffect, useRef } from 'react'
import { ComponentDetail, FullData } from '@/types/component'
import { addToCart, CartItem } from '@/services/api'
import { useAuth } from '@/contexts/AuthContext'

// 使用内联SVG图标替代lucide-react
const ShoppingCartIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M6 19a1 1 0 100 2 1 1 0 000-2zm10 0a1 1 0 100 2 1 1 0 000-2z" />
  </svg>
)

const PlusIcon = () => (
  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v14m7-7H5" />
  </svg>
)

const MinusIcon = () => (
  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
  </svg>
)

interface AddToCartProps {
  component: ComponentDetail
  parsedData: FullData
}

interface PriceItem {
  quantity: number
  price: number
  created_at?: string
}

export default function AddToCart({ component, parsedData }: AddToCartProps) {
  const { userId, isAuthenticated } = useAuth()
  const [quantity, setQuantity] = useState(1)
  const [inputValue, setInputValue] = useState('1')
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | null; text: string }>({
    type: null,
    text: ''
  })
  const validationTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 获取产品信息
  const productId = component.id
  const productName = (component.metadata as any)?.model || component.metadata.name || 'Unknown Product'
  const description = (component.metadata as any)?.description || component.metadata.desc || ''
  const manufacturer = (component.metadata as any)?.brand?.name_en || parsedData.manufacture || 'Unknown Manufacturer'
  
  // 获取价格和库存信息
  const prices = (component.metadata as any)?.prices || []
  const stock = (component.metadata as any)?.stock || 0

  // 检查是否有有效的价格数据
  const hasValidPrices = prices && prices.length > 0 && prices.some((p: PriceItem) => p.price > 0)

  // 检查是否有有效的库存数据
  const hasValidStock = stock > 0

  // 产品是否可以加入购物车（需要有价格和库存）
  const canAddToCart = hasValidPrices && hasValidStock

  // 获取最小订购数量（阶梯价格的最小数量）
  const minQuantity = hasValidPrices ? prices[0].quantity : 1

  // 确保初始数量不低于最小订购数量
  useEffect(() => {
    if (quantity < minQuantity) {
      setQuantity(minQuantity)
      setInputValue(minQuantity.toString())
    }
  }, [minQuantity, quantity])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (validationTimeoutRef.current) {
        clearTimeout(validationTimeoutRef.current)
      }
    }
  }, [])



  // 格式化价格显示
  const formatPrice = (price: number): string => {
    return price.toFixed(4)
  }

  // 根据数量获取对应的单价
  const getCurrentPrice = (): number => {
    if (!prices || prices.length === 0) return 0

    // 找到适用的价格档位（数量大于等于该档位的最小数量）
    let applicablePrice = prices[0]
    for (const price of prices) {
      if (quantity >= price.quantity) {
        applicablePrice = price
      } else {
        break
      }
    }

    return applicablePrice.price
  }

  // 获取当前适用的价格阶梯索引
  const getCurrentPriceIndex = (): number => {
    if (!prices || prices.length === 0) return -1

    let applicableIndex = 0
    for (let i = 0; i < prices.length; i++) {
      if (quantity >= prices[i].quantity) {
        applicableIndex = i
      } else {
        break
      }
    }

    return applicableIndex
  }

  // 处理数量变化
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity < minQuantity) {
      setMessage({ type: 'error', text: `Minimum order quantity is ${minQuantity}` })
      return
    }
    if (newQuantity > stock) {
      setMessage({ type: 'error', text: `Maximum available quantity is ${stock}` })
      return
    }
    setQuantity(newQuantity)
    setInputValue(newQuantity.toString())
    setMessage({ type: null, text: '' })
  }

  // 处理输入框值变化（延迟验证）
  const handleInputChange = (value: string) => {
    setInputValue(value)

    // 清除之前的定时器
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current)
    }

    // 设置新的定时器，3秒后验证
    validationTimeoutRef.current = setTimeout(() => {
      validateAndSetQuantity(value)
    }, 3000)
  }

  // 处理输入框失去焦点
  const handleInputBlur = (value: string) => {
    // 清除定时器
    if (validationTimeoutRef.current) {
      clearTimeout(validationTimeoutRef.current)
    }
    // 立即验证
    validateAndSetQuantity(value)
  }

  // 验证并设置数量
  const validateAndSetQuantity = (value: string) => {
    const newQuantity = parseInt(value) || minQuantity

    if (newQuantity < minQuantity) {
      setMessage({ type: 'error', text: `Minimum order quantity is ${minQuantity}` })
      setQuantity(minQuantity)
      setInputValue(minQuantity.toString())
      return
    }

    if (newQuantity > stock) {
      setMessage({ type: 'error', text: `Maximum available quantity is ${stock}` })
      setQuantity(stock)
      setInputValue(stock.toString())
      return
    }

    setQuantity(newQuantity)
    setInputValue(newQuantity.toString())
    setMessage({ type: null, text: '' })
  }

  // 处理加入购物车
  const handleAddToCart = async () => {
    if (!isAuthenticated || !userId) {
      setMessage({ type: 'error', text: 'Please log in to add items to cart' })
      return
    }

    if (!canAddToCart) {
      setMessage({ type: 'error', text: 'This product cannot be added to cart due to missing price or stock information' })
      return
    }

    if (quantity > stock) {
      setMessage({ type: 'error', text: `Maximum available quantity is ${stock}` })
      return
    }

    setIsLoading(true)
    setMessage({ type: null, text: '' })

    try {
      const cartItem: Omit<CartItem, 'cart_id' | 'added_at' | 'updated_at'> = {
        user_id: userId,
        product_id: productId,
        model_number: productName,
        brand: manufacturer,
        description: description,
        unit_price: getCurrentPrice(),
        stock_quantity: stock,
        cart_quantity: quantity
      }

      const result = await addToCart(cartItem)
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Item added to cart successfully!' })
        // 可以在这里触发购物车数量更新的事件
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to add item to cart. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const currentPrice = getCurrentPrice()
  const totalPrice = currentPrice * quantity

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-red-50 to-red-100 px-6 py-4 border-b border-red-200">
        <h2 className="text-xl font-semibold text-red-900 flex items-center gap-2">
          <ShoppingCartIcon />
          Add to Cart
        </h2>
      </div>
      <div className="p-6">

      {/* 价格信息 */}
      {hasValidPrices ? (
        <div className="mb-4">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Price Breaks</h3>
          <div className="space-y-1">
            {prices.map((price: PriceItem, idx: number) => {
              const isCurrentTier = idx === getCurrentPriceIndex();
              return (
                <div
                  key={idx}
                  className={`flex justify-between items-center py-1.5 px-3 rounded-md ${
                    isCurrentTier ? 'bg-[#DE2910]/10 border border-[#DE2910]/20' : 'bg-gray-50'
                  }`}
                >
                  <span className="text-gray-600 text-sm">{price.quantity}+ :</span>
                  <span className={`font-semibold ${
                    isCurrentTier ? 'text-[#DE2910]' : 'text-gray-700'
                  }`}>
                    ${formatPrice(price.price)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-700 font-medium">Price information not available</p>
          <p className="text-xs text-yellow-600 mt-1">Please contact us for pricing details</p>
        </div>
      )}

      {/* 库存信息 */}
      <div className="mb-4">
        <p className="text-sm text-gray-600">
          Stock: <span className={`font-medium ${hasValidStock ? 'text-green-600' : 'text-red-600'}`}>
            {hasValidStock ? `${stock.toLocaleString()} pcs` : 'Out of Stock'}
          </span>
        </p>
        {!hasValidStock && (
          <p className="text-xs text-red-600 mt-1">Stock information not available or out of stock</p>
        )}
      </div>

      {/* 数量选择 */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
        <div className="flex items-center gap-3">
          <button
            type="button"
            onClick={() => handleQuantityChange(quantity - 1)}
            disabled={!canAddToCart || quantity <= minQuantity || isLoading}
            className="w-8 h-8 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <MinusIcon />
          </button>

          <input
            type="number"
            value={inputValue}
            onChange={(e) => handleInputChange(e.target.value)}
            onBlur={(e) => handleInputBlur(e.target.value)}
            min={minQuantity}
            max={stock}
            disabled={!canAddToCart || isLoading}
            className="w-20 px-3 py-2 border border-gray-300 rounded-md text-center focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50 disabled:bg-gray-100 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
            style={{ MozAppearance: 'textfield' }}
          />

          <button
            type="button"
            onClick={() => handleQuantityChange(quantity + 1)}
            disabled={!canAddToCart || quantity >= stock || isLoading}
            className="w-8 h-8 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <PlusIcon />
          </button>

          {minQuantity > 1 && !(message.text && message.text.includes('Minimum order quantity')) && (
            <span className="text-xs text-gray-500 ml-2">
              Min: {minQuantity}
            </span>
          )}

          {message.text && message.text.includes('Minimum order quantity') && (
            <span className="text-xs text-red-600 ml-2">
              {message.text}
            </span>
          )}
        </div>
      </div>

      {/* 价格计算 */}
      {hasValidPrices && currentPrice > 0 && (
        <div className="mb-4 p-3 bg-gray-50 rounded-md">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-600">Unit Price:</span>
            <span className="font-medium">${formatPrice(currentPrice)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">Total:</span>
            <span className="text-lg font-semibold text-[#DE2910]">${formatPrice(totalPrice)}</span>
          </div>
        </div>
      )}

      {/* 消息显示（排除最小数量错误） */}
      {message.text && !message.text.includes('Minimum order quantity') && (
        <div className={`p-3 rounded-md mb-4 text-sm ${
          message.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
        }`}>
          {message.text}
        </div>
      )}



      {/* 登录提示 */}
      {!isAuthenticated && (
        <div className="p-3 rounded-md mb-4 text-sm bg-yellow-100 text-yellow-700">
          Please <a href="/login" className="font-medium underline hover:text-yellow-800">log in</a> to add items to cart.
        </div>
      )}

      {/* 不可购买提示 */}
      {!canAddToCart && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-700 font-medium">Cannot add to cart</p>
          <p className="text-xs text-red-600 mt-1">
            {!hasValidPrices && !hasValidStock
              ? 'Price and stock information not available'
              : !hasValidPrices
                ? 'Price information not available'
                : 'Stock not available'
            }
          </p>
        </div>
      )}

      {/* 添加到购物车按钮 */}
      <button
        onClick={handleAddToCart}
        disabled={!isAuthenticated || !canAddToCart || isLoading || quantity > stock}
        className="w-full bg-[#DE2910] text-white py-3 px-4 rounded-md font-medium hover:bg-[#DE2910]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
      >
        {isLoading ? (
          <>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            Adding...
          </>
        ) : (
          <>
            <ShoppingCartIcon />
            {canAddToCart ? 'Add to Cart' : 'Unavailable'}
          </>
        )}
      </button>
      </div>
    </div>
  )
}
