import { NextRequest, NextResponse } from 'next/server'

const DB_API_BASE_URL = 'https://api.chinaelectron.com/api'

// 更新订单状态
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params
    const body = await request.json()
    const { status, payment_method } = body

    if (!orderId || !status) {
      return NextResponse.json(
        { error: 'Missing required fields: orderId, status' },
        { status: 400 }
      )
    }

    // 更新订单状态
    const updateData: any = {
      order_status: status
    }

    if (payment_method) {
      updateData.payment_method = payment_method
    }

    const response = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Failed to update order status:', errorText)
      return NextResponse.json(
        { error: 'Failed to update order status' },
        { status: response.status }
      )
    }

    const result = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'Order status updated successfully',
      data: result
    })

  } catch (error) {
    console.error('Error updating order status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// 获取订单状态
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ orderId: string }> }
) {
  try {
    const { orderId } = await params

    if (!orderId) {
      return NextResponse.json(
        { error: 'Missing orderId parameter' },
        { status: 400 }
      )
    }

    const response = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`)

    if (!response.ok) {
      return NextResponse.json(
        { error: 'Failed to fetch order status' },
        { status: response.status }
      )
    }

    const result = await response.json()
    const orders = result.data || []

    if (orders.length === 0) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      )
    }

    const order = orders[0]
    
    return NextResponse.json({
      order_id: order.order_id,
      order_status: order.order_status,
      payment_method: order.payment_method,
      updated_at: order.updated_at
    })

  } catch (error) {
    console.error('Error fetching order status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
