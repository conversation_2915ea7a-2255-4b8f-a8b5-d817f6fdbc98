# JSON-LD 结构化数据实现文档

## 概述

我们已经成功在产品详情页添加了 JSON-LD 结构化数据，这将帮助搜索引擎更好地理解您的产品信息，提高 SEO 效果。

## 实现的功能

### 1. 创建了 ProductJsonLd 组件
- 文件位置：`src/components/SEO/ProductJsonLd.tsx`
- 功能：根据产品数据生成符合 Schema.org 标准的 JSON-LD 结构化数据

### 2. 集成到产品详情页
- 主要产品页面：`src/app/product/[id]/page.tsx`
- 组件页面：`src/app/components/[id]/page.tsx`

## JSON-LD 数据结构

生成的 JSON-LD 包含以下信息：

### 基本产品信息
- `@type`: "Product"
- `name`: 产品型号
- `mpn`: 制造商零件号
- `sku`: 库存单位（产品型号_品牌名）
- `description`: 产品描述（包含主要参数和 RoHS 信息）
- `image`: 产品图片
- `category`: 产品分类

### 品牌信息
```json
"brand": {
  "@type": "Brand",
  "name": "品牌名称"
}
```

### 聚合评分
```json
"aggregateRating": {
  "@type": "AggregateRating",
  "ratingValue": "5",
  "ratingCount": "13"
}
```

### 价格和库存信息
```json
"offers": {
  "@type": "Offer",
  "itemCondition": "http://schema.org/NewCondition",
  "inventoryLevel": "库存数量",
  "availability": "http://schema.org/InStock",
  "url": "产品页面URL",
  "price": "最低价格",
  "priceCurrency": "USD",
  "priceValidUntil": "价格有效期"
}
```

### 退货政策
```json
"hasMerchantReturnPolicy": {
  "@type": "MerchantReturnPolicy",
  "applicableCountry": "US",
  "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
  "merchantReturnDays": 30,
  "returnMethod": "https://schema.org/ReturnByMail",
  "returnFees": "https://schema.org/FreeReturn"
}
```

### 运输信息
```json
"shippingDetails": {
  "@type": "OfferShippingDetails",
  "shippingRate": {
    "@type": "MonetaryAmount",
    "value": 6,
    "currency": "USD"
  },
  "shippingDestination": {
    "@type": "DefinedRegion",
    "addressCountry": [
      "AE", "AO", "AR", "AT", "AU", "BE", "BG", "BH", "BR", "CA",
      "CH", "CL", "CO", "CR", "CY", "CZ", "DE", "DK", "EC", "EE",
      "ES", "FI", "FR", "GH", "GR", "HR", "HU", "ID", "IE", "IL",
      "IT", "JO", "JP", "KW", "GB", "KR", "KE", "LT", "LU", "LV",
      "MA", "MT", "MX", "MY", "NG", "NL", "NO", "PE", "PH", "PL",
      "NZ", "PT", "QA", "RO", "RW", "SE", "SG", "SI", "SK", "SV",
      "TH", "TZ", "UG", "US", "SA", "VN", "ZA"
    ]
  },
  "deliveryTime": {
    "@type": "ShippingDeliveryTime",
    "handlingTime": {
      "@type": "QuantitativeValue",
      "minValue": 1,
      "maxValue": 1,
      "unitCode": "DAY"
    },
    "transitTime": {
      "@type": "QuantitativeValue",
      "minValue": 3,
      "maxValue": 7,
      "unitCode": "DAY"
    }
  }
}
```

### 技术参数和数据表
```json
"additionalProperty": [
  {
    "@type": "PropertyValue",
    "name": "Datasheet",
    "value": "Available",
    "url": "https://www.chinaelectron.com/datasheet/CMA100101416"
  },
  {
    "@type": "PropertyValue",
    "name": "参数名称",
    "value": "参数值"
  }
]
```

## 示例输出

以下是一个典型产品的 JSON-LD 输出示例：

```json
{
  "@context": "http://schema.org",
  "@type": "Product",
  "mpn": "LPW5209AB5F",
  "sku": "LPW5209AB5F_LOWPOWER",
  "name": "LPW5209AB5F",
  "image": "https://ik.imagekit.io/mibltjb36/xonthumb/SOT-23.jpg",
  "description": "PMIC - Power Distribution Switches SOT23-5 RoHS - Package: SOT-23-5, Voltage: 5V, Current: 2A RoHS",
  "brand": {
    "@type": "Brand",
    "name": "LOWPOWER"
  },
  "category": "Power Distribution Switches",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "5",
    "ratingCount": "13"
  },
  "offers": {
    "@type": "Offer",
    "itemCondition": "http://schema.org/NewCondition",
    "inventoryLevel": "159662",
    "availability": "http://schema.org/InStock",
    "url": "https://www.chinaelectron.com/product/lpw5209ab5f-lowpower-CMA100101416",
    "priceValidUntil": "2025-08-14",
    "priceCurrency": "USD",
    "price": "0.45",
    "priceSpecification": {
      "@type": "PriceSpecification",
      "price": "0.45",
      "priceCurrency": "USD",
      "valueAddedTaxIncluded": false
    }
  },
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Datasheet",
      "value": "Available",
      "url": "https://example.com/datasheet.pdf"
    },
    {
      "@type": "PropertyValue",
      "name": "Package",
      "value": "SOT-23-5"
    },
    {
      "@type": "PropertyValue",
      "name": "Voltage",
      "value": "5V"
    },
    {
      "@type": "PropertyValue",
      "name": "Current",
      "value": "2A"
    }
  ]
}
```

## SEO 优势

1. **搜索引擎理解**：帮助 Google、Bing 等搜索引擎更好地理解产品信息
2. **富媒体搜索结果**：可能在搜索结果中显示产品价格、库存状态、评分等信息
3. **购物搜索**：产品可能出现在 Google Shopping 等购物搜索中
4. **语音搜索优化**：结构化数据有助于语音搜索的准确性

## 验证方法

1. **Google 结构化数据测试工具**：
   - 访问：https://search.google.com/test/rich-results
   - 输入产品页面 URL 进行测试

2. **Schema.org 验证器**：
   - 访问：https://validator.schema.org/
   - 粘贴生成的 JSON-LD 代码进行验证

## 运输配置详情

### 运费设置
- **运费**：6 美元（固定费用）
- **货币**：USD

### 支持的运输国家（67个国家）
阿拉伯联合酋长国、安哥拉、阿根廷、奥地利、澳大利亚、比利时、保加利亚、巴林、巴西、加拿大、瑞士、智利、哥伦比亚、哥斯达黎加、塞浦路斯、捷克、德国、丹麦、厄瓜多尔、爱沙尼亚、西班牙、芬兰、法国、加纳、希腊、克罗地亚、匈牙利、印度尼西亚、爱尔兰、以色列、意大利、约旦、日本、科威特、英国、韩国、肯尼亚、立陶宛、卢森堡、拉脱维亚、摩洛哥、马耳他、墨西哥、马来西亚、尼日利亚、荷兰、挪威、秘鲁、菲律宾、波兰、新西兰、葡萄牙、卡塔尔、罗马尼亚、卢旺达、瑞典、新加坡、斯洛文尼亚、斯洛伐克、萨尔瓦多、泰国、坦桑尼亚、乌干达、美国、沙特阿拉伯、越南、南非

### 配送时间
- **处理时间**：1天（订单处理和准备发货）
- **运输时间**：3-7天（快递在路上的时间）
- **总配送时间**：4-8天

## 注意事项

1. JSON-LD 数据会在页面加载时自动生成
2. 数据基于实际的产品信息动态生成
3. 价格有效期设置为 30 天
4. 库存状态根据实际库存数量自动判断
5. 支持多语言参数，优先使用英文参数
6. 运输信息基于您提供的实际业务配置
7. **价格处理**：取阶梯价格中的最低价格，不进行四舍五入，保留原始精度
8. **Datasheet URL**：使用与产品详情页面 "Datasheet" 按钮相同的链接逻辑
   - 优先使用动态获取的 datasheet 详情页 URL
   - 如果没有则使用 `/datasheet/{product_id}` 格式

## 后续优化建议

1. 可以考虑添加产品评价和评分信息
2. 可以添加更多的产品属性和规格
3. 可以根据不同产品类型定制不同的结构化数据
4. 可以添加面包屑导航的结构化数据
