# BOM配单功能使用指南

## 功能概述

BOM（Bill of Materials）配单功能允许用户上传物料清单文件，通过智能匹配系统找到对应的电子元器件，并提供价格、库存等信息。

## 功能流程

### 1. 文件上传 (`/bom`)
- 支持CSV格式文件上传
- 文件大小限制：10MB
- 需要输入BOM项目名称
- 自动验证文件格式

### 2. 列映射配置 (`/bom/mapping/[bomId]`)
- 自动获取BOM文件信息（无需URL参数）
- 显示文件预览数据
- 配置列类型映射
- 必填字段验证
- 支持数量倍数设置
- 优先匹配策略选择

### 3. BOM详情展示 (`/bom/detail/[bomId]`)
- 显示匹配结果统计
- 展示所有BOM项目
- 支持产品详情查看
- 提供导出功能

### 4. BOM管理 (`/bom/manage`)
- 显示所有BOM项目列表
- 支持搜索和筛选
- 提供删除操作
- 状态管理

## 页面结构

```
/bom/                          # BOM上传页面
├── /manage/                   # BOM管理页面
├── /mapping/[bomId]/          # 列映射配置页面
└── /detail/[bomId]/           # BOM详情页面
```

## API接口

### 上传BOM文件
```
POST https://bom-processor.962692556.workers.dev/upload
Content-Type: multipart/form-data

参数:
- file: CSV文件
- bomName: BOM项目名称
- userId: 用户ID
```

### 解析BOM文件
```
POST https://bom-processor.962692556.workers.dev/parse-excel
Content-Type: application/json

参数:
- fileUrl: 文件URL
- startLine: 起始行号（默认2）
```

### 处理BOM数据
```
POST https://bom-processor.962692556.workers.dev/process-bom
Content-Type: application/json

参数:
- bomId: BOM ID
- userId: 用户ID
- startLine: 起始行号
- selection: 列映射数组
- priorityMatch: 优先匹配策略
- coefficientTotal: 数量倍数
```

### 获取BOM详情
```
GET https://bom-processor.962692556.workers.dev/bom-detail?bomId={bomId}
```

### 获取BOM列表
```
GET https://bom-processor.962692556.workers.dev/boms?userId={userId}&page={page}&limit={limit}
```

## 列类型映射

| 值 | 类型 | 说明 |
|---|---|---|
| 0 | Select Type | 不选择 |
| 1 | Quantity | 数量（必选） |
| 2 | CE Part Number | CE产品编号 |
| 3 | Manufacturer | 制造商 |
| 4 | Manufacturer Part Number | 制造商产品编号 |
| 5 | Description | 描述 |
| 6 | Package | 封装 |
| 7 | Customer Part Number | 客户产品编号 |
| 8 | Target Price(USD) | 目标价格 |
| 9 | Target lead Time(Business days) | 目标交期 |

## 验证规则

### 必填字段
- **Quantity（数量）**：必须选择
- **三选一**：CE Part Number、Manufacturer Part Number、Description中至少选择一个

### 文件格式要求
- 文件格式：仅支持CSV
- 文件大小：最大10MB
- 第一行：应包含列标题
- 编码：建议使用UTF-8

## 匹配状态

| 状态 | 说明 |
|---|---|
| Exact Match | 精确匹配 |
| Partial Match | 部分匹配 |
| No Match | 无匹配 |

## 库存状态

| 状态 | 说明 |
|---|---|
| In Stock | 有库存 |
| Out of Stock | 缺货 |
| Backordered | 预订中 |
| Discontinued | 停产 |

## 组件结构

### 核心组件
- `BOMBreadcrumb`: 面包屑导航
- `BOMUploadPage`: 文件上传页面
- `BOMMappingPage`: 列映射配置页面
- `BOMDetailPage`: BOM详情页面
- `BOMManagePage`: BOM管理页面

### 服务模块
- `bomApi.ts`: BOM API服务
- `bom.ts`: TypeScript类型定义

### 工具函数
- `validateCSVFile()`: CSV文件验证
- `validateColumnMapping()`: 列映射验证
- `formatFileSize()`: 文件大小格式化

## 导航集成

### 主导航
- 在Header组件中添加了BOM导航项
- 在Footer组件中添加了BOM链接

### 面包屑导航
- 自动根据当前路径生成面包屑
- 支持自定义面包屑项目

## 错误处理

### 文件上传错误
- 文件格式不支持
- 文件大小超限
- 网络连接错误

### API调用错误
- 认证失败
- 服务器错误
- 数据格式错误

### 用户输入错误
- 必填字段缺失
- 列映射配置错误
- 参数验证失败

## 性能优化

### 前端优化
- 组件懒加载
- 状态管理优化
- 错误边界处理

### API优化
- 请求缓存
- 错误重试机制
- 超时处理

## 测试

### 测试页面
- `/test-bom`: BOM功能测试页面
- 包含各种验证函数的测试
- API连接测试

### 测试用例
- 文件格式验证
- 列映射验证
- API接口测试

## 部署注意事项

1. 确保API端点可访问
2. 配置正确的CORS设置
3. 检查文件上传限制
4. 验证用户认证流程

## 后续改进

1. 添加更多文件格式支持（XLSX）
2. 实现批量操作功能
3. 添加BOM模板下载
4. 优化移动端体验
5. 添加数据导出功能
6. 实现BOM版本管理
