import Link from 'next/link'

export default function QuickNav() {
  const quickLinks = [
    {
      title: 'Local Parts',
      description: 'Optimized for local market, Highly Cost Effective',
      href: '/local-parts',
      bgColor: 'from-cyan-400 to-blue-500',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    },
    {
      title: '24 Hour Shipping',
      description: 'Speed Up Production, Increase Profit',
      href: '/shipping',
      bgColor: 'from-amber-400 to-orange-500',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                d="M13 10V3L4 14h7v7l9-11h-7z" />
        </svg>
      )
    },
    {
      title: 'Discount Section',
      description: 'Flash Sale, Intelligent Selection',
      href: '/discounts',
      bgColor: 'from-green-400 to-emerald-500',
      icon: (
        <svg className="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} 
                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      )
    }
  ]

  return (
    <div className="grid grid-cols-3 gap-6 max-w-7xl mx-auto px-4 -mt-20 relative z-10">
      {quickLinks.map((link, index) => (
        <Link
          key={index}
          href={link.href}
          className="group"
        >
          <div className={`p-8 rounded-2xl bg-gradient-to-br ${link.bgColor} 
                        hover:shadow-xl hover:shadow-blue-500/20 transition-all duration-300 
                        transform hover:-translate-y-1`}>
            <div className="text-white mb-6">
              {link.icon}
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">
              {link.title}
            </h3>
            <p className="text-white/90">
              {link.description}
            </p>
          </div>
        </Link>
      ))}
    </div>
  )
} 