import Link from 'next/link'
import { Suspense } from 'react'
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

// Define API category data structure
interface APICategory {
  code: string
  name_en: string
  name_cn: string
  name_ru: string
  children: APICategory[]
}

// Define page category data structure
interface Category {
  code: string
  name: string
  subcategories: {
    code: string
    name: string
    children: {
      code: string
      name: string
    }[]
  }[]
}

// Example data, will be replaced by API data
const exampleCategories: Category[] = [
  {
    code: 'EX001',
    name: 'Amplifiers/Comparators',
    subcategories: [
      { 
        code: 'EX001001',
        name: 'Video Amplifiers', 
        children: [
          { code: 'EX001001001', name: 'HD Video Amplifiers' },
          { code: 'EX001001002', name: 'Ultra HD Video Amplifiers' }
        ]
      },
      { 
        code: 'EX001002',
        name: 'Operational Amplifier', 
        children: [
          { code: 'EX001002001', name: 'General Purpose Op Amps' },
          { code: 'EX001002002', name: 'High Speed Op Amps' }
        ] 
      }
    ]
  },
  {
    code: 'EX002',
    name: 'Audio Products / Vibration Motors',
    subcategories: [
      { 
        code: 'EX002001',
        name: 'Microphones', 
        children: [
          { code: 'EX002001001', name: 'Condenser Microphones' },
          { code: 'EX002001002', name: 'Dynamic Microphones' }
        ] 
      },
      { 
        code: 'EX002002',
        name: 'Speakers', 
        children: [
          { code: 'EX002002001', name: 'Woofers' },
          { code: 'EX002002002', name: 'Tweeters' }
        ] 
      }
    ]
  }
]

// 服务端数据获取函数
async function fetchCategories(): Promise<{ categories: Category[], error: string | null }> {
  try {
    const response = await fetch('https://webapi.chinaelectron.com/categories/tree', {
      // 设置缓存策略，对于分类数据可以适当缓存以提高性能
      next: { revalidate: 3600 } // 1小时重新验证
    })
    
    if (!response.ok) {
      throw new Error('Failed to fetch categories')
    }
    
    const data: APICategory[] = await response.json()
    
    // Transform API data to page format with third level
    const transformedCategories = data.map((level1Category) => ({
      code: level1Category.code,
      name: level1Category.name_en,
      subcategories: level1Category.children.map((level2Category) => ({
        code: level2Category.code,
        name: level2Category.name_en,
        children: level2Category.children.map((level3Category) => ({
          code: level3Category.code,
          name: level3Category.name_en
        }))
      }))
    }))
    
    return { categories: transformedCategories, error: null }
  } catch (err) {
    console.error('Error fetching categories:', err)
    // Use example data if API request fails
    return { categories: exampleCategories, error: 'Failed to load categories' }
  }
}

// 为服务端组件生成元数据
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Electronic Component & Raw Material Search | China Electron',
    description: 'Find and source a wide range of electronic components and raw materials on China Electron. Access supplier information, pricing, and datasheets for your B2B needs.',
    keywords: 'component search, raw material search, China Electron, B2B electronics sourcing, find electronic parts, electronic component suppliers, IC chip sourcing',
    alternates: {
      canonical: getCanonicalUrl('components'),
    },
  }
}

// 服务端组件
export default async function ComponentsPage() {
  // 服务端数据获取
  const { categories, error } = await fetchCategories()
  
  return (
    <main className="min-h-screen bg-gray-50">
      {/* Hero Header Section */}
      <div className="relative bg-gradient-to-r from-[#1e40af] to-[#3b82f6] text-white py-12 mb-8 overflow-hidden">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0"
               style={{
                 backgroundImage: `radial-gradient(circle at 20% 20%, white 1px, transparent 1px),
                                  radial-gradient(circle at 80% 80%, white 1px, transparent 1px)`,
                 backgroundSize: '40px 40px',
                 animation: 'float 8s ease-in-out infinite'
               }}
          />
        </div>

        {/* Floating circuit elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/5 w-1 h-1 bg-white/30 rounded-full animate-bounce" style={{animationDelay: '0s', animationDuration: '4s'}}></div>
          <div className="absolute top-1/3 right-1/4 w-2 h-2 bg-white/20 rounded-full animate-bounce" style={{animationDelay: '1.5s', animationDuration: '5s'}}></div>
          <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-white/25 rounded-full animate-bounce" style={{animationDelay: '3s', animationDuration: '6s'}}></div>
          <div className="absolute top-1/2 right-1/5 w-1 h-1 bg-white/20 rounded-full animate-bounce" style={{animationDelay: '2s', animationDuration: '4.5s'}}></div>
        </div>

        <div className="relative max-w mx-auto px-4 sm:px-6 lg:px-14">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-4">
                <div className="bg-white/20 rounded-full p-3 mr-4 animate-pulse hover:animate-spin transition-all duration-300">
                  <svg className="w-8 h-8 text-white transform hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                  </svg>
                </div>
                <div className="animate-fadeIn">
                  <h1 className="text-4xl md:text-5xl font-bold mb-2 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    Components Index
                  </h1>
                  <p className="text-xl text-white/90 animate-slideInUp">
                    Explore our comprehensive electronic components catalog
                  </p>
                </div>
              </div>
            </div>
            <div className="hidden md:block animate-slideInRight">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div className="text-3xl font-bold animate-pulse">{categories.length}</div>
                <div className="text-sm text-white/80">Categories</div>
                <div className="mt-2 w-full bg-white/20 rounded-full h-1">
                  <div className="bg-white h-1 rounded-full animate-pulse" style={{width: '85%'}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w mx-auto px-14 pb-6">
        {/* Main content area - full width */}
        <div className="bg-white rounded-lg shadow">

          {/* Categories list */}
          <div className="divide-y divide-gray-200">
            {error ? (
              <div className="p-6 text-center text-red-500">{error}</div>
            ) : (
              <Suspense fallback={<div className="p-6 text-center">Loading category data...</div>}>
                {categories.map((category) => (
                  <div key={category.code} className="p-6">
                    <h2 className="text-lg font-medium text-gray-900 mb-4">
                      {category.name}
                    </h2>
                    <div className="space-y-6">
                      {category.subcategories.map((level2) => (
                        <div key={level2.code} className="ml-4">
                          <h3 className="text-md font-medium text-gray-800 mb-2">{level2.name}</h3>
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-8 gap-y-2 ml-4">
                            {level2.children.map((level3) => (
                              <Link
                                key={level3.code}
                                href={`/search?category=${level3.code}`}
                                className="group flex items-center py-1"
                              >
                                <span className="text-sm text-blue-600 group-hover:text-blue-800 transition-colors line-clamp-1">
                                  {level3.name}
                                </span>
                              </Link>
                            ))}
                            {level2.children.length === 0 && (
                              <span className="text-sm text-gray-400 py-1">No subcategories</span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </Suspense>
            )}
          </div>
        </div>
      </div>
    </main>
  )
} 