'use client'

import Image from 'next/image'
import Link from 'next/link'

// Top navigation items
const navItems = [
  { name: 'Research Institute', icon: '📰', hot: false, new: false },
  { name: 'Tech Reviews', icon: '🔧', hot: true, new: false },
  { name: 'Board Applications', icon: '🔌', hot: false, new: false },
  { name: 'Design Resources', icon: '📚', hot: false, new: false },
  { name: 'Datasheets', icon: '📊', hot: false, new: false },
//   { name: 'Industry Map', icon: '🗺️', hot: false },
//   { name: 'Featured Topics', icon: '⭐', new: true },
//   { name: 'Supply & Demand', icon: '🤝', hot: false }
]

// Featured news
const featuredNews = {
  id: 'news1',
  title: 'Humanoid Robot Industry Chain in China Stock Market',
  image: '/images/robot.jpg',
  link: '/blog/robot-industry'
}

// Hot news
const hotNews = [
  {
    id: 'news2',
    title: '88.3% Growth: What Happened to Naxin Micro?',
    image: '/images/growth.jpg',
    readCount: 3240,
    commentCount: 158,
    publishTime: '3h ago'
  },
  {
    id: 'news3',
    title: 'Vintage Car Charger Teardown: Chinese Chips Everywhere',
    image: '/images/charger.jpg',
    readCount: 2156,
    commentCount: 89,
    publishTime: '5h ago'
  },
  {
    id: 'news4',
    title: 'Component Trading Weekly Report - AI Focus in AISC/MCU/MPU 2025',
    image: '/images/market.jpg',
    readCount: 1893,
    commentCount: 67,
    publishTime: '6h ago'
  },
  {
    id: 'news5',
    title: 'Xiaomi Group vs Tesla: Worth a Trillion Dollar Valuation?',
    image: '/images/company.jpg',
    readCount: 2789,
    commentCount: 234,
    publishTime: '8h ago'
  }
]

// Technical articles
const techArticles = [
  {
    id: 'tech1',
    title: 'Drive Circuit Design (Part 2) - Input Side Analysis of Drivers',
    author: 'Infineon',
    readCount: 154,
    tags: ['Circuit Design', 'Drivers'],
    publishTime: '3h ago',
    excerpt: 'Drive circuit design is a challenging aspect of power semiconductor applications. Beyond controlling the driving process and protecting devices, the complete chain includes several important functions. This series explains how to properly understand and apply these functions through Infineon\'s solution guide...'
  },
  {
    id: 'tech2',
    title: 'Latest RF Chip Application Ecosystem Compensation Summary',
    author: 'RF Expert',
    readCount: 95,
    tags: ['RF Engineer', 'Chips'],
    publishTime: '3h ago',
    excerpt: 'The autumn recruitment for 2025 graduates has concluded, while spring recruitment is still ongoing. Some cutting-edge companies have slightly reduced their compensation packages compared to last autumn...'
  }
]

// 24-hour trending
const hotTopics = [
  {
    id: 'hot1',
    title: 'Spring Recruitment - Hangzhou Chip Companies Guide',
    author: 'Chip Career',
    type: 'Featured',
    publishTime: '3h ago'
  },
  {
    id: 'hot2',
    title: 'Core Integration Chairman & CEO Speech: Strategic AI Focus, Three Growth Poles',
    author: 'Editor',
    publishTime: '3h ago'
  },
  {
    id: 'hot3',
    title: 'Infineon Reaches 200mm SiC Milestone: First Batch Delivery Begins',
    author: 'Editor',
    publishTime: '3h ago'
  }
]

export default function BlogPage() {
  return (
    <main className="pt-[112px]">
      <div className="min-h-screen bg-gray-50">
        {/* Top navigation */}
        <nav className="bg-white border-b border-gray-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center justify-between h-[48px]">
              <div className="flex items-center space-x-6 overflow-x-auto no-scrollbar">
                {navItems.map(item => (
                  <Link
                    key={item.name}
                    href="#"
                    className="flex items-center space-x-1 whitespace-nowrap text-gray-600 
                             hover:text-[#DE2910] transition-colors shrink-0"
                  >
                    <span className="text-base">{item.icon}</span>
                    <span className="text-sm">{item.name}</span>
                    {item.hot && (
                      <span className="px-1.5 py-0.5 text-xs bg-red-50 text-red-500 rounded shrink-0">HOT</span>
                    )}
                    {item.new && (
                      <span className="px-1.5 py-0.5 text-xs bg-blue-50 text-blue-500 rounded shrink-0">NEW</span>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </nav>

        {/* Main content area */}
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Left content area */}
            <div className="lg:col-span-3 space-y-6">
              {/* Featured news */}
              <div className="relative h-[360px] rounded-lg overflow-hidden">
                <Link href={featuredNews.link}>
                  <Image
                    src={featuredNews.image}
                    alt={featuredNews.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <h1 className="text-2xl font-bold text-white">
                      {featuredNews.title}
                    </h1>
                  </div>
                </Link>
              </div>

              {/* Hot news grid */}
              <div className="grid grid-cols-2 gap-4">
                {hotNews.map(news => (
                  <Link
                    key={news.id}
                    href={`/blog/${news.id}`}
                    className="group bg-white rounded-lg overflow-hidden border border-gray-200 
                             hover:shadow-md transition-shadow"
                  >
                    <div className="aspect-video relative">
                      <Image
                        src={news.image}
                        alt={news.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-3">
                      <h3 className="text-sm font-medium text-gray-900 mb-2 line-clamp-2 
                                   group-hover:text-[#DE2910] transition-colors">
                        {news.title}
                      </h3>
                      <div className="flex items-center text-xs text-gray-500 space-x-3">
                        <span>{news.publishTime}</span>
                        <span>阅读 {news.readCount}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>

              {/* Technical articles list */}
              <div className="bg-white rounded-lg border border-gray-200">
                {techArticles.map((article, index) => (
                  <article 
                    key={article.id}
                    className={`p-4 ${
                      index !== techArticles.length - 1 ? 'border-b border-gray-200' : ''
                    }`}
                  >
                    <Link href={`/blog/${article.id}`}>
                      <h3 className="text-lg font-medium text-gray-900 mb-2 
                                   hover:text-[#DE2910] transition-colors">
                        {article.title}
                      </h3>
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                        {article.excerpt}
                      </p>
                      <div className="flex items-center text-sm text-gray-500 space-x-4">
                        <span>{article.author}</span>
                        <span>{article.publishTime}</span>
                        <span>阅读 {article.readCount}</span>
                        <div className="flex items-center space-x-2">
                          {article.tags.map(tag => (
                            <span
                              key={tag}
                              className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </Link>
                  </article>
                ))}
              </div>
            </div>

            {/* Right sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-[56px] space-y-6">
                {/* 24-hour trending */}
                <div className="bg-white rounded-lg border border-gray-200 p-4">
                  <h3 className="text-base font-bold text-gray-900 mb-4">Trending 24h</h3>
                  <div className="space-y-3">
                    {hotTopics.map((topic, index) => (
                      <div key={topic.id} className="flex space-x-3">
                        <span className={`text-base font-bold ${
                          index < 3 ? 'text-[#DE2910]' : 'text-gray-400'
                        }`}>
                          {(index + 1).toString().padStart(2, '0')}
                        </span>
                        <div className="flex-1">
                          <Link href={`/blog/${topic.id}`}>
                            <h4 className="text-sm text-gray-900 hover:text-[#DE2910] 
                                         transition-colors mb-1 line-clamp-2">
                              {topic.title}
                            </h4>
                          </Link>
                          <div className="flex items-center text-xs text-gray-500 space-x-2">
                            {topic.type && (
                              <>
                                <span className="text-[#DE2910]">{topic.type}</span>
                                <span>·</span>
                              </>
                            )}
                            <span>{topic.author}</span>
                            <span>·</span>
                            <span>{topic.publishTime}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
} 