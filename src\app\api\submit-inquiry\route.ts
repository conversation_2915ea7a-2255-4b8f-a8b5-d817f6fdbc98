import { NextRequest, NextResponse } from 'next/server';

/**
 * 处理询价请求的API端点
 * 提交RFQ请求到外部API
 */
export async function POST(request: NextRequest) {
  try {
    // 验证授权
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized request' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];
    
    // 解析请求正文
    const requestData = await request.json();
    
    // 验证必要字段 - 同时接受user_id或userId
    if (!requestData.user_id && !requestData.userId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // 查看请求数据以便调试
    // console.log('Request data received:', JSON.stringify(requestData, null, 2));

    // 构建发送到外部API的数据 - 处理字段名称映射，确保使用正确的API字段名
    const inquiryData = {
      user_id: requestData.user_id || requestData.userId,
      type: requestData.type || 'rfq',
      status: requestData.status || 'pending',
      contact_name: requestData.contact_name || requestData.contactName,
      business_email: requestData.business_email || requestData.businessEmail,
      company_name: requestData.company_name || requestData.companyName,
      country: requestData.country,
      quantity: requestData.quantity,
      model: requestData.model,
      target_price: requestData.target_price || 0,
      delivery_time: requestData.delivery_time || '',
      brand_id: requestData.brand_id,
      brand_name: requestData.brand_name
    };

    // 记录将要发送到外部API的数据，以便调试
    // console.log('Data to be sent to external API:', JSON.stringify(inquiryData, null, 2));

    // 调用外部API
    const response = await fetch('https://requests-quota.chinaelectron.com/api/requests', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
      },
      body: JSON.stringify(inquiryData)
    });

    // 获取外部API响应
    const data = await response.json();
    
    // 记录外部API的响应，以便调试
    // console.log('External API response:', JSON.stringify(data, null, 2));
    
    // 如果外部API响应成功
    if (response.ok && data.id) {
      return NextResponse.json({
        success: true,
        message: 'Inquiry submitted successfully',
        id: data.id
      });
    } else {
      // 返回外部API错误
      return NextResponse.json(
        { 
          success: false, 
          message: data.message || 'Failed to submit inquiry',
          error: data.error
        },
        { status: response.status }
      );
    }
  } catch (error) {
    // 处理任何异常
    console.error('Error in submit-inquiry API:', error);
    return NextResponse.json(
      { success: false, message: 'An internal server error occurred' },
      { status: 500 }
    );
  }
} 