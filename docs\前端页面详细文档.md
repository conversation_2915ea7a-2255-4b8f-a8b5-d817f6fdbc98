# 前端页面详细文档

## 页面架构概览

ChinaElectron平台采用Next.js 14 App Router架构，包含以下主要页面类型：
- **公共页面**：首页、搜索、产品详情、分类浏览等
- **用户页面**：登录、注册、用户中心、询价管理等
- **内容页面**：博客、专题、关于我们等
- **功能页面**：产品索引、分销商、制造商等

## 1. 首页 (/)

### 页面文件
- **路径**: `src/app/page.tsx`
- **类型**: Server Component (SSR)
- **布局**: 使用默认布局 `src/app/layout.tsx`

### 页面结构
```
Header (导航栏)
├── Logo + 搜索框 + 用户菜单
├── 主导航菜单
└── 语言切换

Main Content (主要内容)
├── RecommendedSearches (推荐搜索)
├── AdCarousel + SideAds + DailyHot (广告轮播 + 侧边广告 + 每日热门)
├── CategoryShowcase (分类展示)
├── PreferredManufacture (优选制造商)
├── PopularParts (热门产品)
├── LatestBlogs (最新博客)
├── Newsletter (邮件订阅)
└── QuickNav (快速导航)

Footer (页脚)
├── 公司信息
├── 产品分类
├── 服务支持
└── 联系方式
```

### 核心组件和功能

#### 1.1 推荐搜索 (RecommendedSearches)
- **组件**: `src/components/Home/RecommendedSearches.tsx`
- **功能**: 显示热门搜索关键词
- **API**: `https://webapi.chinaelectron.com/recommend`
- **交互**: 
  - 点击关键词 → 跳转到搜索页面
  - 支持响应式布局

#### 1.2 广告轮播 (AdCarousel)
- **组件**: `src/components/Home/AdCarousel.tsx`
- **功能**: 首页主要广告位轮播
- **API**: `https://webapi.chinaelectron.com/ads`
- **交互**:
  - 自动轮播，3秒切换
  - 支持手动点击切换
  - 点击广告 → 跳转到目标页面

#### 1.3 侧边广告 (SideAds)
- **组件**: `src/components/Home/SideAds.tsx`
- **功能**: 垂直广告位展示
- **交互**: 点击广告 → 新窗口打开目标链接

#### 1.4 每日热门 (DailyHot)
- **组件**: `src/components/Home/DailyHot.tsx`
- **功能**: 显示热门产品列表
- **API**: `https://get-hot.chinaelectron.com/latest?type=hot`
- **交互**:
  - 点击产品 → 跳转到产品详情页
  - 支持实时更新

#### 1.5 分类展示 (CategoryShowcase)
- **组件**: `src/components/Home/CategoryShowcase.tsx`
- **功能**: 展示主要产品分类
- **API**: `https://webapi.chinaelectron.com/categories/popular`
- **交互**: 点击分类 → 跳转到分类搜索页面

#### 1.6 优选制造商 (PreferredManufacture)
- **组件**: `src/components/Home/PreferredManufacture.tsx`
- **功能**: 展示推荐品牌制造商
- **API**: `https://webapi.chinaelectron.com/brands/active`
- **交互**: 点击品牌 → 跳转到品牌页面

### 数据获取策略
- **SSR**: 服务端预渲染，提升SEO和首屏加载速度
- **缓存**: 使用Next.js revalidate机制，1小时缓存
- **错误处理**: 提供默认数据，确保页面正常显示

## 2. 搜索页面 (/search)

### 页面文件
- **路径**: `src/app/search/page.tsx`
- **类型**: Server Component with Client Components
- **参数**: `q` (搜索词), `category` (分类), `brand` (品牌), `page` (页码)

### 页面结构
```
SearchHeader (搜索头部)
├── 搜索框
├── 筛选器
└── 排序选项

SearchResults (搜索结果)
├── 结果统计
├── 产品列表/表格视图
├── 分页组件
└── 相关推荐
```

### 核心功能

#### 2.1 搜索逻辑
- **API**: `https://webapi.chinaelectron.com/products`
- **支持搜索类型**:
  - 关键词搜索: `?search=keyword`
  - 分类搜索: `?category=category_id`
  - 品牌搜索: `?brand=brand_id`
- **分页**: 每页30个结果

#### 2.2 搜索筛选器 (SearchFilters)
- **组件**: `src/components/Search/SearchFilters.tsx`
- **功能**: 
  - 品牌筛选
  - 分类筛选
  - 价格范围
  - 库存状态
- **交互**: 选择筛选条件 → 更新URL参数 → 重新搜索

#### 2.3 产品列表 (ProductList)
- **组件**: `src/components/Search/ProductList.tsx`
- **显示模式**: 
  - 列表视图: 详细信息展示
  - 表格视图: 紧凑信息展示
- **交互**: 
  - 点击产品 → 跳转到产品详情页
  - 点击询价 → 打开询价表单

#### 2.4 智能识别 (SmartIdentification)
- **组件**: `src/components/Search/SmartIdentification.tsx`
- **功能**: AI识别产品型号和规格
- **交互**: 输入模糊信息 → AI分析 → 推荐精确产品

### 搜索优化
- **SEO友好**: URL包含搜索参数，支持搜索引擎索引
- **性能优化**: 服务端渲染 + 客户端交互
- **用户体验**: 实时搜索建议，历史搜索记录

## 3. 产品详情页 (/product/[id])

### 页面文件
- **路径**: `src/app/product/[id]/page.tsx`
- **类型**: Server Component with Client Wrapper
- **参数**: `id` (产品ID)

### 页面结构
```
ProductHeader (产品头部)
├── 面包屑导航
├── 产品标题
└── 基本信息

ProductContent (产品内容)
├── ComponentOverview (产品概览)
├── Specifications (规格参数)
├── PriceAndStock (价格库存)
├── Distributors (分销商)
├── DocumentsAndFiles (文档文件)
└── AIAssistant (AI助手)
```

### 核心组件

#### 3.1 产品概览 (ComponentOverview)
- **组件**: `src/components/ComponentDetail/ComponentOverview.tsx`
- **功能**: 
  - 产品图片展示
  - 基本参数信息
  - 制造商信息
  - 产品描述
- **交互**: 
  - 图片放大查看
  - 点击制造商 → 跳转到制造商页面

#### 3.2 规格参数 (Specifications)
- **组件**: `src/components/ComponentDetail/Specifications.tsx`
- **功能**: 
  - 详细技术参数
  - 多语言支持
  - 参数对比
- **交互**: 
  - 参数筛选和搜索
  - 参数复制功能

#### 3.3 价格库存 (PriceAndStock)
- **组件**: `src/components/ComponentDetail/PriceAndStock.tsx`
- **功能**: 
  - 阶梯价格展示
  - 实时库存信息
  - 多供应商比价
- **交互**: 
  - 数量选择 → 价格计算
  - 点击询价 → 打开询价表单

#### 3.4 分销商信息 (Distributors)
- **组件**: `src/components/ComponentDetail/Distributors.tsx`
- **功能**: 
  - 授权分销商列表
  - 分销商评级
  - 联系方式
- **交互**: 
  - 点击分销商 → 跳转到分销商页面
  - 直接联系分销商

#### 3.5 文档文件 (DocumentsAndFiles)
- **组件**: `src/components/ComponentDetail/DocumentsAndFiles.tsx`
- **功能**: 
  - 数据手册下载
  - CAD模型下载
  - 应用笔记
- **交互**: 
  - 点击下载 → 文件下载
  - PDF在线预览

#### 3.6 AI助手 (AIAssistant)
- **组件**: `src/components/ComponentDetail/AIAssistant.tsx`
- **功能**: 
  - 产品问答
  - 应用建议
  - 替代方案推荐
- **交互**: 
  - 输入问题 → AI回答
  - 点击推荐 → 查看相关产品

### 数据获取
- **API**: `https://webapi.chinaelectron.com/products/{id}`
- **认证**: 需要Bearer Token
- **缓存**: 1小时服务端缓存
- **错误处理**: 404页面，产品不存在提示

## 4. 产品索引页 (/product_index/[id])

### 页面文件
- **路径**: `src/app/product_index/[id]/page.tsx`
- **类型**: Server Component
- **参数**: `id` (首字母)

### 页面结构
```
IndexHeader (索引头部)
├── 页面标题
└── 字母导航

IndexContent (索引内容)
├── FirstTextNavigation (字母导航)
├── ProductGrid (产品网格)
└── Pagination (分页)
```

### 核心功能

#### 4.1 字母导航 (FirstTextNavigation)
- **功能**: A-Z, 0-9 字母数字导航
- **API**: `https://webapi.chinaelectron.com/product-index/first-text`
- **交互**: 点击字母 → 跳转到对应索引页

#### 4.2 产品网格展示
- **API**: `https://webapi.chinaelectron.com/product-index/by-first`
- **显示**: 产品型号平铺展示
- **交互**: 点击型号 → 新窗口打开产品页面

### 性能优化
- **静态生成**: 预生成常用字母页面
- **缓存策略**: 24小时缓存
- **分页加载**: 每页30个产品

## 5. 用户登录页 (/login)

### 页面文件
- **路径**: `src/app/login/page.tsx`
- **类型**: Client Component
- **布局**: 独立布局 `src/app/login/layout.tsx`

### 页面结构
```
LoginContainer (登录容器)
├── LoginHeader (登录头部)
├── LoginForm (登录表单)
├── SocialLogin (社交登录)
└── RegisterLink (注册链接)
```

### 核心功能

#### 5.1 登录表单
- **字段**: 用户名/邮箱, 密码
- **验证**: 前端表单验证
- **安全**: Cloudflare Turnstile验证码
- **API**: `https://auth.chinaelectron.com/login`

#### 5.2 交互流程
1. 用户输入凭据
2. 前端验证表单
3. 提交Turnstile验证
4. 发送登录请求
5. 成功 → 保存Token → 跳转首页
6. 失败 → 显示错误信息

#### 5.3 状态管理
- **Loading状态**: 提交时显示加载动画
- **错误处理**: 显示具体错误信息
- **成功跳转**: 登录成功后跳转到来源页面

### 安全机制
- **CSRF防护**: Turnstile验证码
- **密码安全**: 前端不存储密码
- **Token管理**: JWT存储在localStorage

## 6. 用户注册页 (/register)

### 页面文件
- **路径**: `src/app/register/page.tsx`
- **类型**: Client Component
- **布局**: 独立布局 `src/app/register/layout.tsx`

### 注册流程
1. 填写基本信息 (邮箱、密码、公司信息)
2. 邮箱验证码验证
3. Turnstile人机验证
4. 提交注册请求
5. 邮箱激活
6. 注册完成

### 表单字段
- **个人信息**: 姓名、邮箱、电话
- **公司信息**: 公司名称、职位、行业
- **账户信息**: 密码、确认密码
- **验证信息**: 邮箱验证码、人机验证

## 7. 用户中心 (/account)

### 页面结构
- **主页**: `src/app/account/page.tsx`
- **布局**: `src/app/account/layout.tsx`
- **客户端包装**: `src/app/account/ClientLayout.tsx`

### 子页面
- **个人设置**: `/account/settings`
- **询价管理**: `/account/rfq`
- **订单历史**: `/account/orders`
- **地址管理**: `/account/addresses`

### 功能模块
- **个人信息管理**: 修改基本信息
- **询价历史**: 查看和管理询价记录
- **订单跟踪**: 订单状态和物流信息
- **收藏夹**: 收藏的产品和供应商

## 8. 博客页面 (/blog)

### 页面文件
- **列表页**: `src/app/blog/page.tsx`
- **详情页**: `src/app/blog/[id]/page.tsx`
- **布局**: `src/app/blog/layout.tsx`

### 博客功能
- **文章列表**: 分页展示博客文章
- **分类筛选**: 按技术分类筛选
- **搜索功能**: 文章标题和内容搜索
- **文章详情**: Markdown渲染，代码高亮
- **相关推荐**: 相关技术文章推荐

### API接口
- **文章列表**: `https://blog-manage.chinaelectron.com/api/blog`
- **文章详情**: `https://blog-manage.chinaelectron.com/api/blog/{id}`
- **作者信息**: 集成作者信息展示

## 9. 专题页面 (/insights)

### 页面文件
- **列表页**: `src/app/insights/page.tsx`
- **详情页**: `src/app/insights/[id]/page.tsx`
- **布局**: `src/app/insights/layout.tsx`

### 专题功能
- **专题集合**: 技术深度专题展示
- **特色专题**: 首页推荐专题
- **专题详情**: 包含多篇相关文章
- **专题分类**: 按技术领域分类

### API接口
- **专题列表**: `https://blog-manage.chinaelectron.com/api/insights`
- **专题详情**: `https://blog-manage.chinaelectron.com/api/insights/{id}`

## 10. 分销商页面 (/distributors)

### 页面文件
- **列表页**: `src/app/distributors/page.tsx`
- **详情页**: `src/app/distributors/[id]/page.tsx`

### 分销商功能
- **分销商列表**: 展示所有授权分销商
- **分销商详情**: 公司信息、产品线、联系方式
- **产品搜索**: 按分销商筛选产品
- **评级系统**: 基于服务质量的评级

## 11. 制造商页面 (/manufacturers)

### 页面文件
- **主页**: `src/app/manufacturers/page.tsx`
- **详情页**: `src/app/manufacturers/[id]/page.tsx`
- **中国制造商**: `src/app/manufacturers/chinese/page.tsx`
- **国际制造商**: `src/app/manufacturers/international/page.tsx`

### 制造商功能
- **品牌展示**: Logo、简介、官网链接
- **产品线**: 该品牌的所有产品分类
- **技术支持**: 技术文档、应用笔记
- **新闻动态**: 品牌最新资讯

## 12. 询价页面 (/rfq)

### 页面文件
- **路径**: `src/app/rfq/page.tsx`
- **类型**: Client Component
- **布局**: `src/app/rfq/layout.tsx`

### 询价功能
- **快速询价**: 单个产品询价
- **BOM询价**: 批量产品询价
- **询价历史**: 历史询价记录
- **报价比较**: 多供应商报价对比

### 询价流程
1. 选择询价类型 (单个/批量)
2. 填写产品信息 (型号、数量、要求)
3. 填写联系信息
4. 提交询价请求
5. 等待供应商报价
6. 比较和选择报价

## 13. 每日热门页面 (/dailyhot)

### 页面文件
- **路径**: `src/app/dailyhot/page.tsx`
- **布局**: `src/app/dailyhot/layout.tsx`

### 热门功能
- **热门产品**: 基于搜索和浏览统计
- **趋势分析**: 产品热度趋势图
- **分类热门**: 按产品分类的热门排行
- **实时更新**: 热门数据实时更新

### API接口
- **热门数据**: `https://get-hot.chinaelectron.com/latest?type=hot`
- **产品验证**: `https://webapi.chinaelectron.com/products/models/check`

## 14. 关于我们页面 (/about-us)

### 页面文件
- **路径**: `src/app/about-us/page.tsx`
- **类型**: Static Page

### 页面内容
- **公司简介**: 企业历史、使命愿景
- **团队介绍**: 核心团队成员
- **服务优势**: 平台核心竞争力
- **联系方式**: 多种联系渠道

## 15. 法律页面

### 隐私政策 (/privacy-policy)
- **文件**: `src/app/privacy-policy/page.tsx`
- **内容**: 数据收集、使用、保护政策

### 服务条款 (/terms-of-service)
- **文件**: `src/app/terms-of-service/page.tsx`
- **内容**: 平台使用条款和用户协议

## 页面交互设计规范

### 1. 导航交互
- **面包屑导航**: 显示当前位置，支持快速返回
- **主导航**: 鼠标悬停显示子菜单
- **移动端导航**: 汉堡菜单，侧滑展开

### 2. 搜索交互
- **搜索建议**: 输入时显示实时建议
- **搜索历史**: 记录用户搜索历史
- **高级搜索**: 支持多条件组合搜索

### 3. 产品交互
- **产品卡片**: 悬停显示快速操作
- **图片查看**: 支持放大、缩放、切换
- **参数对比**: 支持多产品参数对比

### 4. 表单交互
- **实时验证**: 输入时实时验证格式
- **错误提示**: 明确的错误信息提示
- **自动保存**: 重要表单支持自动保存

### 5. 加载状态
- **骨架屏**: 内容加载时显示骨架屏
- **进度指示**: 长时间操作显示进度
- **错误重试**: 加载失败提供重试选项

## 页面性能优化策略

### 1. 服务端渲染 (SSR)
- 首页、搜索页、产品详情页使用SSR
- 提升SEO效果和首屏加载速度
- 缓存策略优化数据获取

### 2. 客户端优化
- 代码分割和懒加载
- 图片优化和CDN加速
- 关键资源预加载

### 3. 缓存策略
- 静态资源长期缓存
- API数据适度缓存
- 浏览器缓存优化

### 4. 移动端适配
- 响应式设计
- 触摸友好的交互
- 移动端性能优化

## 页面SEO优化

### 1. 元数据优化
- 每个页面独立的title和description
- 结构化数据标记
- Open Graph和Twitter Card

### 2. URL结构
- 语义化URL设计
- 规范化URL处理
- 多语言URL支持

### 3. 内容优化
- 关键词密度控制
- 内链建设
- 图片alt属性优化

### 4. 技术SEO
- 网站地图生成
- robots.txt配置
- 页面加载速度优化
