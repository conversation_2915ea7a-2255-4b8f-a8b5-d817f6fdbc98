'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { uploadBOMFile, validateCSVFile, formatFileSize } from '@/services/bomApi'
import { Upload, FileText, AlertCircle, CheckCircle, List } from 'lucide-react'

export default function BOMUploadPage() {
  const router = useRouter()
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const [file, setFile] = useState<File | null>(null)
  const [bomName, setBomName] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState('')
  const [dragActive, setDragActive] = useState(false)

  // 等待认证状态加载完成
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Checking authentication...</p>
        </div>
      </div>
    )
  }

  // 检查用户是否已登录
  if (!isAuthenticated) {
    router.push('/login')
    return null
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0])
    }
  }

  const handleFileSelect = (selectedFile: File) => {
    setError('')
    
    if (!validateCSVFile(selectedFile)) {
      setError('Please select a valid CSV file')
      return
    }

    // 检查文件大小（限制为10MB）
    if (selectedFile.size > 10 * 1024 * 1024) {
      setError('File size must be less than 10MB')
      return
    }

    setFile(selectedFile)
    
    // 如果没有设置BOM名称，使用文件名（去掉扩展名）
    if (!bomName) {
      const nameWithoutExt = selectedFile.name.replace(/\.[^/.]+$/, '')
      setBomName(nameWithoutExt)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0])
    }
  }

  const handleUpload = async () => {
    if (!file || !bomName.trim() || !userId) {
      setError('Please select a file and enter a BOM name')
      return
    }

    setIsUploading(true)
    setError('')

    try {
      const response = await uploadBOMFile(file, bomName.trim(), userId)
      
      if (response.success) {
        // 上传成功，跳转到列映射页面
        router.push(`/bom/mapping/${response.bom_id}`)
      } else {
        setError('Upload failed. Please try again.')
      }
    } catch (err) {
      console.error('Upload error:', err)
      setError(err instanceof Error ? err.message : 'Upload failed. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  const removeFile = () => {
    setFile(null)
    setError('')
  }

  return (
    <>
      <div className="min-h-screen bg-gray-50">
        {/* Hero Header Section */}
        <div className="relative bg-gradient-to-r from-[#7c3aed] to-[#8b5cf6] text-white py-12 mb-8 overflow-hidden">
          {/* Animated background pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute inset-0"
                 style={{
                   backgroundImage: `repeating-linear-gradient(45deg, white 0px, white 2px, transparent 2px, transparent 20px),
                                    repeating-linear-gradient(-45deg, white 0px, white 2px, transparent 2px, transparent 20px)`,
                   backgroundSize: '28px 28px',
                   animation: 'float 12s ease-in-out infinite'
                 }}
            />
          </div>

          {/* Floating document elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-1/4 left-1/5 w-3 h-4 bg-white/25 rounded-sm animate-bounce" style={{animationDelay: '0s', animationDuration: '4s'}}></div>
            <div className="absolute top-1/3 right-1/4 w-2 h-3 bg-white/30 rounded-sm animate-bounce" style={{animationDelay: '1.5s', animationDuration: '5s'}}></div>
            <div className="absolute bottom-1/3 left-1/3 w-2.5 h-3.5 bg-white/20 rounded-sm animate-bounce" style={{animationDelay: '2.5s', animationDuration: '6s'}}></div>
            <div className="absolute top-1/2 right-1/5 w-1.5 h-2 bg-white/35 rounded-sm animate-bounce" style={{animationDelay: '1s', animationDuration: '4.5s'}}></div>
          </div>

          <div className="relative max-w mx-auto px-4 sm:px-6 lg:px-14">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center mb-4">
                  <div className="bg-white/20 rounded-full p-3 mr-4 animate-pulse hover:animate-spin transition-all duration-300">
                    <svg className="w-8 h-8 text-white transform hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div className="animate-fadeIn">
                    <h1 className="text-4xl md:text-5xl font-bold mb-2 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                      BOM Management
                    </h1>
                    <p className="text-xl text-white/90 animate-slideInUp">
                      Upload and manage your Bill of Materials efficiently
                    </p>
                  </div>
                </div>
              </div>
              <div className="hidden md:block animate-slideInRight">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                  <div className="text-3xl font-bold animate-pulse">CSV</div>
                  <div className="text-sm text-white/80">File Format</div>
                  <div className="mt-2 text-xs text-white/70">Max 10MB</div>
                  <div className="mt-2 w-full bg-white/20 rounded-full h-1">
                    <div className="bg-white h-1 rounded-full animate-pulse" style={{width: '75%'}}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w mx-auto px-14 py-8">
          {/* 页面操作按钮 */}
          <div className="flex justify-end mb-8">
            <button
              onClick={() => router.push('/bom/manage')}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center"
            >
              <List className="h-4 w-4 mr-2" />
              Manage BOMs
            </button>
          </div>

        {/* 上传步骤指示器 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-blue-600 text-white rounded-full text-sm font-medium">
              1
            </div>
            <span className="ml-2 text-sm font-medium text-blue-600">Upload BOM File</span>
          </div>
          <div className="w-16 h-0.5 bg-gray-300 mx-4"></div>
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full text-sm font-medium">
              2
            </div>
            <span className="ml-2 text-sm font-medium text-gray-500">Column Mapping</span>
          </div>
          <div className="w-16 h-0.5 bg-gray-300 mx-4"></div>
          <div className="flex items-center">
            <div className="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-500 rounded-full text-sm font-medium">
              3
            </div>
            <span className="ml-2 text-sm font-medium text-gray-500">BOM Details</span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {/* BOM名称输入 */}
          <div className="mb-6">
            <label htmlFor="bomName" className="block text-sm font-medium text-gray-700 mb-2">
              BOM Project Name *
            </label>
            <input
              type="text"
              id="bomName"
              value={bomName}
              onChange={(e) => setBomName(e.target.value)}
              placeholder="Enter your BOM project name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              disabled={isUploading}
            />
          </div>

          {/* 文件上传区域 */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Upload CSV File *
            </label>
            
            {!file ? (
              <div
                className={`relative border-2 border-dashed rounded-lg p-6 text-center hover:border-blue-400 transition-colors ${
                  dragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <div className="text-sm text-gray-600 mb-2">
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <span className="text-blue-600 hover:text-blue-500 font-medium">
                      Click to upload
                    </span>
                    <span> or drag and drop</span>
                  </label>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    accept=".csv"
                    className="sr-only"
                    onChange={handleFileInputChange}
                    disabled={isUploading}
                  />
                </div>
                <p className="text-xs text-gray-500">CSV files up to 10MB</p>
              </div>
            ) : (
              <div className="border border-gray-300 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileText className="h-8 w-8 text-blue-500 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">{file.name}</p>
                      <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                  </div>
                  <button
                    onClick={removeFile}
                    className="text-red-600 hover:text-red-800 text-sm font-medium"
                    disabled={isUploading}
                  >
                    Remove
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* 错误信息 */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" />
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* 上传按钮 */}
          <div className="flex justify-end">
            <button
              onClick={handleUpload}
              disabled={!file || !bomName.trim() || isUploading}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {isUploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload & Continue
                </>
              )}
            </button>
          </div>
        </div>

        {/* 帮助信息 */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-800 mb-2">File Format Requirements:</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• File format: CSV only</li>
            <li>• Maximum file size: 10MB</li>
            <li>• First row should contain column headers</li>
            <li>• Required columns: Quantity and at least one of (CE Part Number, Manufacturer Part Number, Description)</li>
          </ul>
        </div>
      </div>
      </div>
    </>
  )
}
