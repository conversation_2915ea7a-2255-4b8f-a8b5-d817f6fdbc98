'use client'

import { ComponentDetail, FullData } from '@/types/component'
import Image from 'next/image'
import Link from 'next/link'
import { getProductImageUrl } from '@/utils/imageUtils'
import { useState, useEffect } from 'react'

interface ComponentOverviewProps {
  component: ComponentDetail
  parsedData: FullData
}

interface PriceItem {
  quantity: number
  price: number
}

interface CategoryNameInfo {
  name_cn: string
  name_en: string
  name_ru: string
}

export default function ComponentOverview({ component, parsedData }: ComponentOverviewProps) {
  // 状态管理
  const [isModelsModalOpen, setIsModelsModalOpen] = useState(false);

  // 尝试从不同的数据结构中获取组件信息
  const productName =
    (component.metadata as any)?.model ||
    parsedData?.parameter?.languages?.english?.name ||
    component.metadata.name ||
    'Unknown Product';

  const description =
    (component.metadata as any)?.description ||
    parsedData?.parameter?.languages?.english?.desc ||
    component.metadata.desc ||
    '';

  const manufacturer =
    (component.metadata as any)?.brand?.name_en ||
    parsedData.manufacture ||
    component.metadata.manufacture ||
    'Unknown Manufacturer';

  const model = parsedData.model || component.metadata.model || 'Unknown Model';

  const location = parsedData.location || '';

  // 获取数据表链接
  const datasheetUrl = parsedData.datasheet?.[0] || component.metadata.datasheet || '';

  // 获取分类路径和分类名称
  const categoryPath = (parsedData as any)?.category_path || [];
  const categoryNames = (parsedData as any)?.category_names || {};

  // 获取图片列表
  const imageList = parsedData.img || [];



  // 控制模态框打开时禁用页面滚动
  useEffect(() => {
    if (isModelsModalOpen) {
      // 禁用页面滚动
      document.body.style.overflow = 'hidden';
    } else {
      // 恢复页面滚动
      document.body.style.overflow = 'unset';
    }

    // 清理函数：组件卸载时恢复滚动
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isModelsModalOpen]);




  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8 hover:shadow-lg transition-shadow duration-300">
      <div className="flex gap-8">
        {/* 组件图片和数据表 */}
        <div className="w-52 flex flex-col gap-4">
          <div className="h-52 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg flex items-center justify-center border border-gray-200 overflow-hidden shadow-inner">
            <Image 
              src={getProductImageUrl(imageList)}
              alt={productName}
              width={192}
              height={192}
              className="max-w-full max-h-full object-contain"
              unoptimized
            />
          </div>
          
          {/* 数据表链接 */}
          {datasheetUrl && (
            <button
              onClick={() => {
                const datasheetSection = document.getElementById('datasheet-section');
                if (datasheetSection) {
                  datasheetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                  });
                }
              }}
              className="flex items-center justify-center gap-2 py-3 px-4 bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 rounded-md hover:from-blue-100 hover:to-blue-200 transition-all duration-300 shadow-sm hover:shadow-md font-medium w-full"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                      d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
              <span className="font-medium">Datasheet</span>
            </button>
          )}

          {/* View Models 按钮 */}
          <button
            onClick={() => setIsModelsModalOpen(true)}
            className="flex items-center justify-center gap-2 py-3 px-4 bg-gradient-to-r from-green-50 to-green-100 text-green-700 rounded-md hover:from-green-100 hover:to-green-200 transition-all duration-300 shadow-sm hover:shadow-md font-medium"
          >
            <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <span className="font-medium">View Models</span>
          </button>
        </div>

        {/* 组件基本信息 */}
        <div className="flex-1">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {productName}
          </h1>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Manufacturer</p>
              <p className="font-medium text-gray-900">
                <Link 
                  href={`/manufacturers/${(component.metadata as any)?.brand?.id || ''}`}
                  className="hover:text-[#DE2910] transition-colors"
                >
                  {manufacturer}
                </Link>
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Model</p>
              <p className="font-medium text-gray-900">{model}</p>
            </div>
            {/* 分类信息 - 使用静态数据 */}
            <div className="col-span-2">
              <p className="text-sm text-gray-500">Category</p>
              {categoryPath.length > 0 && Object.keys(categoryNames).length > 0 ? (
                <div className="flex flex-wrap items-center">
                  {categoryPath.map((categoryCode: string, index: number) => (
                    <div key={index} className="inline-flex items-center">
                      {index > 0 && <span className="text-gray-400 mx-1">›</span>}
                      {index === categoryPath.length - 1 ? (
                        <Link 
                          href={`/search?category=${categoryCode}`}
                          className="font-medium text-gray-900 hover:text-[#DE2910] transition-colors"
                        >
                          {categoryNames[categoryCode]?.name_en || categoryCode}
                        </Link>
                      ) : (
                        <span className="font-medium text-gray-900">
                          {categoryNames[categoryCode]?.name_en || categoryCode}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">Category information unavailable</p>
              )}
            </div>
            {/* 描述移到这里 - 在分类下方 */}
            <div className="col-span-2 mt-2">
              <p className="text-gray-600">
                {description}
              </p>
            </div>
            {location && (
              <div>
                <p className="text-sm text-gray-500">Location</p>
                <p className="font-medium text-gray-900">{location}</p>
              </div>
            )}
          </div>
        </div>

        {/* 价格和库存信息 */}
        {/* <div className="w-64 border-l border-gray-100 pl-6">
          <div className="mb-4">
            <h2 className="text-xl font-semibold text-gray-900 mb-3">Reference Pricing</h2>
            {prices && prices.length > 0 ? (
              <div className="space-y-2">
                {prices.map((price: PriceItem, idx: number) => (
                  <div key={idx} className="flex justify-between items-center py-1.5 px-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600 text-sm">{price.quantity}+ :</span>
                    <span className="text-[#DE2910] font-semibold">${formatPrice(convertToUSD(price.price))}</span>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 italic">No price information available</p>
            )}
          </div>
          
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Reference Stock</h2>
            <div className="py-2 px-4 bg-green-50 rounded-lg flex items-center justify-between">
              <span className="text-gray-700">Available:</span>
              <span className="text-green-600 font-semibold">{stock}</span>
            </div>
          </div>
        </div> */}
      </div>

      {/* View Models Modal */}
      {isModelsModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setIsModelsModalOpen(false)}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-[920px] w-full mx-4 max-h-[80vh] flex flex-col"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-2 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                View Models - {productName}
              </h2>
              <button
                onClick={() => setIsModelsModalOpen(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Modal Content */}
            <div className="flex-1 p-4">
              <iframe
                src={`https://getsvg.chinaelectron.com/?product_id=${encodeURIComponent(component.metadata.id)}`}
                className="w-full h-[500px] border border-gray-200 rounded-lg"
                title="Component Models"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}