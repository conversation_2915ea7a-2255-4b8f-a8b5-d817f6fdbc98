#!/usr/bin/env node

/**
 * 修复country-state.json中的中文乱码问题
 * 
 * 问题：中文字符显示为乱码，如 "é¿å¯æ±" 应该是 "阿富汗"
 * 原因：UTF-8编码问题，需要重新解码
 * 
 * 使用方法：
 * node scripts/fix-chinese-encoding.js
 */

const fs = require('fs');
const path = require('path');

// 文件路径
const INPUT_FILE = path.join(__dirname, 'country-state.json');
const OUTPUT_FILE = path.join(__dirname, 'country-state-fixed.json');
const TS_OUTPUT_FILE = path.join(__dirname, '../src/data/countries-states-complete.ts');

// 中文国家名称映射表（部分常用国家）
const COUNTRY_NAME_MAP = {
  'AF': '阿富汗',
  'AX': '奥兰群岛',
  'AL': '阿尔巴尼亚',
  'DZ': '阿尔及利亚',
  'AS': '美属萨摩亚群岛',
  'AD': '安道尔',
  'AO': '安哥拉',
  'AI': '安圭拉岛',
  'AQ': '南极洲',
  'AG': '安提瓜和巴布达',
  'AR': '阿根廷',
  'AM': '亚美尼亚',
  'AW': '阿鲁巴岛',
  'AU': '澳大利亚',
  'AT': '奥地利',
  'AZ': '阿塞拜疆',
  'BS': '巴哈马',
  'BH': '巴林',
  'BD': '孟加拉国',
  'BB': '巴巴多斯',
  'BY': '白俄罗斯',
  'BE': '比利时',
  'BZ': '伯利兹',
  'BJ': '贝宁',
  'BM': '百慕大',
  'BT': '不丹',
  'BO': '玻利维亚',
  'BA': '波斯尼亚-黑塞哥维那联邦共和国',
  'BW': '博茨瓦纳',
  'BV': '布维岛',
  'BR': '巴西',
  'IO': '英属印度洋领区',
  'BN': '文莱',
  'BG': '保加利亚',
  'BF': '布基纳法索',
  'BI': '布隆迪',
  'KH': '柬埔寨',
  'CM': '喀麦隆',
  'CA': '加拿大',
  'CV': '佛得角群岛',
  'KY': '开曼群岛',
  'CF': '中非共和国',
  'TD': '乍得',
  'CL': '智利',
  'CN': '中国',
  'CX': '圣诞岛',
  'CC': '科科斯群岛',
  'CO': '哥伦比亚',
  'KM': '科摩罗',
  'CG': '刚果',
  'CD': '刚果民主共和国',
  'CK': '库克群岛',
  'CR': '哥斯达黎加',
  'CI': '科特迪瓦',
  'HR': '克罗地亚',
  'CU': '古巴',
  'CW': '库拉索',
  'CY': '塞浦路斯',
  'CZ': '捷克共和国',
  'DK': '丹麦',
  'DJ': '吉布提',
  'DM': '多米尼加',
  'DO': '多米尼加共和国',
  'EC': '厄瓜多尔',
  'EG': '埃及',
  'SV': '萨尔瓦多',
  'GQ': '赤道几内亚',
  'ER': '厄立特里亚',
  'EE': '爱沙尼亚',
  'ET': '埃塞俄比亚',
  'FK': '福克兰群岛',
  'FO': '法罗群岛',
  'FJ': '斐济',
  'FI': '芬兰',
  'FR': '法国',
  'GF': '法属圭亚那',
  'PF': '塔希提岛',
  'TF': '法属南部领土',
  'GA': '加蓬',
  'GM': '冈比亚',
  'GE': '格鲁吉亚',
  'DE': '德国',
  'GH': '加纳',
  'GI': '直布罗陀',
  'GR': '希腊',
  'GL': '格陵兰',
  'GD': '格林纳达',
  'GP': '瓜德罗普',
  'GU': '关岛',
  'GT': '危地马拉',
  'GG': '根西岛',
  'GN': '几内亚',
  'GW': '几内亚比绍',
  'GY': '圭亚那',
  'HT': '海地',
  'HM': '赫德岛和麦当劳岛',
  'VA': '梵蒂冈',
  'HN': '洪都拉斯',
  'HK': '中国香港',
  'HU': '匈牙利',
  'IS': '冰岛',
  'IN': '印度',
  'ID': '印度尼西亚',
  'IR': '伊朗',
  'IQ': '伊拉克',
  'IE': '爱尔兰',
  'IL': '以色列',
  'IT': '意大利',
  'JM': '牙买加',
  'JP': '日本',
  'JE': '泽西岛',
  'JO': '约旦',
  'KZ': '哈萨克斯坦',
  'KE': '肯尼亚',
  'KI': '基里巴斯共和国',
  'KP': '朝鲜',
  'KR': '韩国',
  'KW': '科威特',
  'KG': '吉尔吉斯斯坦',
  'LA': '老挝',
  'LV': '拉脱维亚',
  'LB': '黎巴嫩',
  'LS': '莱索托',
  'LR': '利比里亚',
  'LY': '利比亚',
  'LI': '列支敦士登',
  'LT': '立陶宛',
  'LU': '卢森堡',
  'MO': '中国澳门',
  'MK': '马其顿',
  'MG': '马达加斯加',
  'MW': '马拉维',
  'MY': '马来西亚',
  'MV': '马尔代夫',
  'ML': '马里',
  'MT': '马耳他',
  'MH': '马绍尔群岛',
  'MQ': '马提尼克岛',
  'MR': '毛里塔尼亚',
  'MU': '毛里求斯',
  'YT': '马约特',
  'MX': '墨西哥',
  'FM': '密克罗尼西亚',
  'MD': '摩尔多瓦',
  'MC': '摩纳哥',
  'MN': '蒙古',
  'ME': '黑山共和国',
  'MS': '蒙特塞拉特岛',
  'MA': '摩洛哥',
  'MZ': '莫桑比克',
  'MM': '缅甸',
  'NA': '纳米比亚',
  'NR': '瑙鲁共和国',
  'NP': '尼泊尔',
  'NL': '荷兰',
  'NC': '新喀里多尼亚',
  'NZ': '新西兰',
  'NI': '尼加拉瓜',
  'NE': '尼日尔',
  'NG': '尼日利亚',
  'NU': '纽埃岛',
  'NF': '诺福克岛',
  'MP': '塞班岛',
  'NO': '挪威',
  'OM': '阿曼',
  'PK': '巴基斯坦',
  'PW': '帕劳',
  'PA': '巴拿马',
  'PG': '巴布亚新几内亚',
  'PY': '巴拉圭',
  'PE': '秘鲁',
  'PH': '菲律宾',
  'PN': '皮特凯恩群岛',
  'PL': '波兰',
  'PT': '葡萄牙',
  'PR': '波多黎各',
  'QA': '卡塔尔',
  'RE': '留尼汪岛',
  'RO': '罗马尼亚',
  'RU': '俄罗斯',
  'RW': '卢旺达',
  'BL': '圣巴泰勒米岛',
  'SH': '圣赫勒拿岛',
  'KN': '圣基茨',
  'LC': '圣卢西亚',
  'MF': '法属圣马丁',
  'PM': '圣皮埃尔和密克隆群岛',
  'VC': '圣文森特和格林纳丁斯岛',
  'WS': '西萨摩亚',
  'SM': '圣马力诺',
  'ST': '圣多美和普林西比',
  'SA': '沙特阿拉伯',
  'SN': '塞内加尔',
  'RS': '塞尔维亚共和国',
  'SC': '塞舌尔',
  'SL': '塞拉利昂',
  'SG': '新加坡',
  'SX': '荷属圣马丁',
  'SK': '斯洛伐克共和国',
  'SI': '斯洛文尼亚',
  'SB': '所罗门群岛',
  'SO': '索马里',
  'ZA': '南非',
  'GS': '南乔治亚岛和南桑威奇群岛',
  'SS': '南苏丹共和国',
  'ES': '西班牙',
  'LK': '斯里兰卡',
  'SD': '苏丹',
  'SR': '苏里南',
  'SZ': '斯威士兰',
  'SE': '瑞典',
  'CH': '瑞士',
  'SY': '叙利亚',
  'TW': '中国台湾',
  'TJ': '塔吉克斯坦',
  'TZ': '坦桑尼亚',
  'TH': '泰国',
  'TL': '东帝汶',
  'TG': '多哥',
  'TK': '托克劳',
  'TO': '汤加',
  'TT': '特立尼达和多巴哥',
  'TN': '突尼斯',
  'TR': '土耳其',
  'TM': '土库曼斯坦',
  'TC': '特克斯和凯科斯群岛',
  'TV': '图瓦卢',
  'UG': '乌干达',
  'UA': '乌克兰',
  'AE': '阿拉伯联合酋长国',
  'GB': '英国',
  'US': '美国',
  'UM': '美国本土外小岛屿',
  'UY': '乌拉圭',
  'UZ': '乌兹别克斯坦',
  'VU': '瓦努阿图',
  'VE': '委内瑞拉',
  'VN': '越南',
  'VG': '英属维尔京群岛',
  'VI': '美属维尔京群岛',
  'WF': '瓦利斯群岛和富图纳群岛',
  'EH': '西撒哈拉',
  'YE': '也门阿拉伯共和国',
  'ZM': '赞比亚',
  'ZW': '津巴布韦'
};

/**
 * 修复各种编码问题（中文乱码 + 欧洲特殊字符）
 */
function fixEncoding(text) {
  if (!text || typeof text !== 'string') return text;

  try {
    // 1. 先尝试修复中文UTF-8乱码
    const buffer = Buffer.from(text, 'latin1');
    const decoded = buffer.toString('utf8');

    // 检查是否包含中文字符
    if (/[\u4e00-\u9fff]/.test(decoded)) {
      return decoded;
    }

    // 2. 修复欧洲特殊字符编码问题
    let fixed = text;

    // 直接替换最常见的编码错误
    // 注意：只修复真正的乱码，不要修复正确的字符
    if (text.includes('Ã') && !text.includes('Å')) {
      fixed = fixed.replace(/Ã¥/g, 'å');
      fixed = fixed.replace(/Ã¤/g, 'ä');
      fixed = fixed.replace(/Ã¶/g, 'ö');
      fixed = fixed.replace(/Ã¸/g, 'ø');
      fixed = fixed.replace(/Ã…/g, 'Å');
      fixed = fixed.replace(/Ã„/g, 'Ä');
      fixed = fixed.replace(/Ã–/g, 'Ö');
      fixed = fixed.replace(/Ã˜/g, 'Ø');
    }
    fixed = fixed.replace(/Ã¡/g, 'á');
    fixed = fixed.replace(/Ã /g, 'à');
    fixed = fixed.replace(/Ã¢/g, 'â');
    fixed = fixed.replace(/Ã£/g, 'ã');
    fixed = fixed.replace(/Ã©/g, 'é');
    fixed = fixed.replace(/Ã¨/g, 'è');
    fixed = fixed.replace(/Ãª/g, 'ê');
    fixed = fixed.replace(/Ã«/g, 'ë');
    fixed = fixed.replace(/Ã­/g, 'í');
    fixed = fixed.replace(/Ã¬/g, 'ì');
    fixed = fixed.replace(/Ã®/g, 'î');
    fixed = fixed.replace(/Ã¯/g, 'ï');
    fixed = fixed.replace(/Ã³/g, 'ó');
    fixed = fixed.replace(/Ã²/g, 'ò');
    fixed = fixed.replace(/Ã´/g, 'ô');
    fixed = fixed.replace(/Ãµ/g, 'õ');
    fixed = fixed.replace(/Ãº/g, 'ú');
    fixed = fixed.replace(/Ã¹/g, 'ù');
    fixed = fixed.replace(/Ã»/g, 'û');
    fixed = fixed.replace(/Ã¼/g, 'ü');
    fixed = fixed.replace(/Ã½/g, 'ý');
    fixed = fixed.replace(/Ã¿/g, 'ÿ');
    fixed = fixed.replace(/Ã§/g, 'ç');
    fixed = fixed.replace(/Ã±/g, 'ñ');
    fixed = fixed.replace(/Ã†/g, 'Æ');
    fixed = fixed.replace(/Ã¦/g, 'æ');
    fixed = fixed.replace(/Ã°/g, 'ð');
    fixed = fixed.replace(/Ãž/g, 'Þ');
    fixed = fixed.replace(/Ã¾/g, 'þ');
    fixed = fixed.replace(/ÃŸ/g, 'ß');
    fixed = fixed.replace(/Ã€/g, 'À');
    fixed = fixed.replace(/Ã‚/g, 'Â');
    fixed = fixed.replace(/Ãƒ/g, 'Ã');
    fixed = fixed.replace(/Ã‰/g, 'É');
    fixed = fixed.replace(/Ãˆ/g, 'È');
    fixed = fixed.replace(/ÃŠ/g, 'Ê');
    fixed = fixed.replace(/Ã‹/g, 'Ë');
    fixed = fixed.replace(/ÃŒ/g, 'Ì');
    fixed = fixed.replace(/ÃŽ/g, 'Î');
    fixed = fixed.replace(/Ã'/g, 'Ò');
    fixed = fixed.replace(/Ã"/g, 'Ô');
    fixed = fixed.replace(/Ã•/g, 'Õ');
    fixed = fixed.replace(/Ãš/g, 'Ú');
    fixed = fixed.replace(/Ã™/g, 'Ù');
    fixed = fixed.replace(/Ã›/g, 'Û');
    fixed = fixed.replace(/Ãœ/g, 'Ü');
    fixed = fixed.replace(/Ã‡/g, 'Ç');

    // 3. 检查是否有改进
    if (fixed !== text) {
      return fixed;
    }

    return text;
  } catch (error) {
    return text;
  }
}

/**
 * 处理国家数据
 */
function processCountryData(countries) {
  console.log('🔧 开始处理国家数据...');

  let fixedCountryCount = 0;
  let fixedProvinceCount = 0;
  let totalCountryCount = 0;
  let totalProvinceCount = 0;

  const processedCountries = countries.map(country => {
    totalCountryCount++;

    const processed = { ...country };

    // 修复国家英文名称
    if (processed.countryNameEn) {
      let fixed = fixEncoding(processed.countryNameEn);

      // 特殊修复：Ãland Islands -> Åland Islands
      if (fixed === 'Ãland Islands') {
        fixed = 'Åland Islands';
      }

      if (fixed !== processed.countryNameEn) {
        processed.countryNameEn = fixed;
        fixedCountryCount++;
      }
    }

    // 修复国家中文名称
    if (processed.countryNameCn) {
      const countryCode = processed.countryCode;

      // 优先使用映射表中的名称
      if (COUNTRY_NAME_MAP[countryCode]) {
        processed.countryNameCn = COUNTRY_NAME_MAP[countryCode];
        fixedCountryCount++;
      } else {
        // 尝试修复编码
        const fixed = fixEncoding(processed.countryNameCn);
        if (fixed !== processed.countryNameCn) {
          processed.countryNameCn = fixed;
          fixedCountryCount++;
        }
      }
    }

    // 处理省份数据
    if (processed.provinceList && Array.isArray(processed.provinceList)) {
      processed.provinceList = processed.provinceList.map(province => {
        totalProvinceCount++;
        const processedProvince = { ...province };

        // 修复省份英文名称
        if (processedProvince.provinceNameEn) {
          const fixed = fixEncoding(processedProvince.provinceNameEn);
          if (fixed !== processedProvince.provinceNameEn) {
            processedProvince.provinceNameEn = fixed;
            fixedProvinceCount++;
          }
        }

        // 修复省份中文名称
        if (processedProvince.provinceNameCn) {
          const fixed = fixEncoding(processedProvince.provinceNameCn);
          if (fixed !== processedProvince.provinceNameCn) {
            processedProvince.provinceNameCn = fixed;
            fixedProvinceCount++;
          }
        }

        return processedProvince;
      });
    }

    return processed;
  });

  console.log(`✅ 处理完成:`);
  console.log(`   📍 国家: ${fixedCountryCount}/${totalCountryCount} 个已修复`);
  console.log(`   🏛️  省州: ${fixedProvinceCount}/${totalProvinceCount} 个已修复`);
  return processedCountries;
}

/**
 * 转换为我们的数据格式
 */
function convertToOurFormat(countries) {
  console.log('🔄 转换数据格式...');
  
  const converted = countries.map(country => {
    const result = {
      code: country.countryCode,
      name: country.countryNameEn,
      nameCn: country.countryNameCn || country.countryNameEn
    };
    
    // 添加省州数据
    if (country.provinceList && country.provinceList.length > 0) {
      result.states = country.provinceList.map(province => ({
        code: province.provinceCode,
        name: province.provinceNameEn,
        nameCn: province.provinceNameCn || province.provinceNameEn
      }));
    }
    
    return result;
  }).filter(country => country.code && country.name);
  
  console.log(`✅ 转换完成: ${converted.length} 个国家`);
  return converted;
}

/**
 * 生成TypeScript文件
 */
function generateTypeScriptFile(countries) {
  console.log('📝 生成TypeScript文件...');
  
  // 分离有省州数据和无省州数据的国家
  const countriesWithStates = countries.filter(c => c.states && c.states.length > 0);
  const countriesWithoutStates = countries.filter(c => !c.states || c.states.length === 0);
  
  const content = `// 完整的全球国家和省/州数据
// 自动生成时间: ${new Date().toISOString()}
// 数据来源: scripts/country-state.json (已修复中文乱码)
// 总计: ${countries.length} 个国家，其中 ${countriesWithStates.length} 个有省州数据

export interface Country {
  code: string;        // 国家代码
  name: string;        // 英文名称
  nameCn?: string;     // 中文名称
  states?: State[];    // 省州列表
}

export interface State {
  code: string;        // 省州代码
  name: string;        // 英文名称
  nameCn?: string;     // 中文名称
}

// 有预定义省/州的国家 (${countriesWithStates.length}个)
export const countriesWithStates: Country[] = ${JSON.stringify(countriesWithStates, null, 2)};

// 其他国家 (${countriesWithoutStates.length}个)
export const otherCountries: Country[] = ${JSON.stringify(countriesWithoutStates, null, 2)};

// 合并所有国家
export const allCountries: Country[] = [...countriesWithStates, ...otherCountries];

// 根据国家代码获取省/州列表
export const getStatesByCountry = (countryCode: string): State[] => {
  const country = countriesWithStates.find(c => c.code === countryCode);
  return country?.states || [];
};

// 检查国家是否有预定义的省/州
export const hasStates = (countryCode: string): boolean => {
  return countriesWithStates.some(c => c.code === countryCode);
};

// 根据国家代码获取国家信息
export const getCountryByCode = (countryCode: string): Country | undefined => {
  return allCountries.find(c => c.code === countryCode);
};

// 获取国家的中文名称
export const getCountryNameCn = (countryCode: string): string => {
  const country = getCountryByCode(countryCode);
  return country?.nameCn || country?.name || countryCode;
};

// 获取省州的中文名称
export const getStateNameCn = (countryCode: string, stateCode: string): string => {
  const states = getStatesByCountry(countryCode);
  const state = states.find(s => s.code === stateCode);
  return state?.nameCn || state?.name || stateCode;
};
`;
  
  return content;
}

/**
 * 主函数
 */
async function main() {
  console.log('🌍 修复中文乱码工具');
  console.log('===================');
  console.log(`📁 输入文件: ${INPUT_FILE}`);
  console.log(`📁 输出文件: ${OUTPUT_FILE}`);
  console.log(`📁 TS文件: ${TS_OUTPUT_FILE}`);
  console.log('');
  
  try {
    // 检查输入文件是否存在
    if (!fs.existsSync(INPUT_FILE)) {
      throw new Error(`输入文件不存在: ${INPUT_FILE}`);
    }
    
    // 读取原始数据
    console.log('📖 读取原始数据...');
    const rawData = fs.readFileSync(INPUT_FILE, 'utf8');
    const countries = JSON.parse(rawData);
    
    if (!Array.isArray(countries)) {
      throw new Error('数据格式错误：期望数组格式');
    }
    
    console.log(`✅ 成功读取 ${countries.length} 个国家的数据`);
    
    // 处理数据
    const processedCountries = processCountryData(countries);
    const convertedCountries = convertToOurFormat(processedCountries);
    
    // 保存修复后的JSON文件
    console.log('💾 保存修复后的JSON文件...');
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(processedCountries, null, 2), 'utf8');
    console.log(`✅ JSON文件已保存: ${OUTPUT_FILE}`);
    
    // 生成TypeScript文件
    const tsContent = generateTypeScriptFile(convertedCountries);
    
    // 确保目录存在
    const tsDir = path.dirname(TS_OUTPUT_FILE);
    if (!fs.existsSync(tsDir)) {
      fs.mkdirSync(tsDir, { recursive: true });
    }
    
    fs.writeFileSync(TS_OUTPUT_FILE, tsContent, 'utf8');
    console.log(`✅ TypeScript文件已保存: ${TS_OUTPUT_FILE}`);
    
    // 统计信息
    console.log('');
    console.log('📊 统计信息:');
    console.log(`📍 总国家数: ${convertedCountries.length}`);
    console.log(`🏛️  有省州数据: ${convertedCountries.filter(c => c.states && c.states.length > 0).length} 个国家`);
    console.log(`🌐 无省州数据: ${convertedCountries.filter(c => !c.states || c.states.length === 0).length} 个国家`);
    
    const totalStates = convertedCountries.reduce((sum, country) => {
      return sum + (country.states ? country.states.length : 0);
    }, 0);
    console.log(`🗺️  总省州数: ${totalStates}`);
    
    console.log('');
    console.log('🎉 修复完成！');
    console.log('');
    console.log('📋 使用方法:');
    console.log('1. 在AddressForm中导入: import { allCountries, getStatesByCountry } from "@/data/countries-states-complete"');
    console.log('2. 替换现有的countries数据');
    console.log('3. 现在支持中英文双语显示');
    
  } catch (error) {
    console.error('');
    console.error('❌ 处理失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
