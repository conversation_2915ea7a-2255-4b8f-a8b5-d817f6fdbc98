'use client'

import { useState, useEffect } from 'react'
import { ComponentDetail, FullData } from '@/types/component'

// 扩展的品牌接口，用于类型断言
interface BrandInfo {
  id: string;
  name_en: string;
  name_cn: string;
  name_ru: string;
  logo_url: string;
  website: string;
}

// 扩展的组件元数据接口，包含品牌信息
interface ExtendedMetadata {
  brand?: BrandInfo;
  [key: string]: any; // 允许其他已有属性
}

interface AssistInquiryProps {
  component: ComponentDetail
  parsedData: FullData
  userId: string | null
  token: string | null
}

export default function AssistInquiry({ component, parsedData, userId, token }: AssistInquiryProps) {
  const [formData, setFormData] = useState({
    contactName: '',
    businessEmail: '',
    companyName: '',
    country: '',
    quantity: ''
  });

  const [formErrors, setFormErrors] = useState({
    contactName: '',
    businessEmail: '',
    companyName: '',
    country: '',
    quantity: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{ success: boolean | null; message: string }>({
    success: null,
    message: ''
  });

  // 当 userId 或 token 变化时记录日志，帮助调试
  useEffect(() => {
    // console.log("AssistInquiry component - Auth state updated:");
    // console.log("userId:", userId);
    // console.log("token exists:", !!token);
  }, [userId, token]);

  const isValidEmail = (email: string) => {
    return /^[\w\.-]+@[\w\.-]+\.\w+$/.test(email);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // 清除字段的错误提示，当用户开始编辑该字段
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }));
    }
    
    // 实时验证数量字段，确保英文错误信息
    if (name === 'quantity') {
      const numValue = Number(value);
      if (value && (isNaN(numValue) || numValue <= 0)) {
        setFormErrors(prev => ({ ...prev, quantity: 'Quantity must be greater than 0' }));
      } else {
        setFormErrors(prev => ({ ...prev, quantity: '' }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = { ...formErrors };
    let isValid = true;

    // 检查所有必填字段
    if (!formData.contactName.trim()) {
      newErrors.contactName = 'Contact name is required';
      isValid = false;
    }

    if (!formData.businessEmail.trim()) {
      newErrors.businessEmail = 'Business email is required';
      isValid = false;
    } else if (!isValidEmail(formData.businessEmail)) {
      newErrors.businessEmail = 'Please enter a valid business email address';
      isValid = false;
    }

    if (!formData.companyName.trim()) {
      newErrors.companyName = 'Company name is required';
      isValid = false;
    }

    if (!formData.country.trim()) {
      newErrors.country = 'Country is required';
      isValid = false;
    }

    if (!formData.quantity.trim()) {
      newErrors.quantity = 'Quantity is required';
      isValid = false;
    } else {
      const numQuantity = Number(formData.quantity);
      if (isNaN(numQuantity) || numQuantity <= 0) {
        newErrors.quantity = 'Quantity must be greater than 0';
        isValid = false;
      }
    }

    setFormErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitStatus({ success: null, message: '' });

    // 记录当前认证状态
    // console.log("Form submission - Current auth state:");
    // console.log("userId:", userId);
    // console.log("token exists:", !!token);

    if (!userId || !token) {
      console.error("Missing userId or token - cannot submit form");
      setSubmitStatus({ success: false, message: 'Please log in to submit an inquiry.' });
      return;
    }

    // 使用自定义验证而不是依赖 HTML5 内置验证
    if (!validateForm()) {
      // 如果验证失败，不继续提交
      return;
    }

    setIsSubmitting(true);

    // 使用类型断言安全地访问 brand 信息
    const extendedMetadata = component.metadata as unknown as ExtendedMetadata;
    const brandId = extendedMetadata.brand?.id || '';
    const brandName = extendedMetadata.brand?.name_en || component.metadata.manufacture || '';
    
    // 记录品牌信息，确保数据存在
    /* console.log("Brand information:", {
      brandId,
      brandName,
      brandObject: extendedMetadata.brand,
      manufacture: component.metadata.manufacture
    }); */

    const numQuantity = Number(formData.quantity);

    // 构建将发送到 API 的数据
    const inquiryData = {
      contactName: formData.contactName,
      businessEmail: formData.businessEmail,
      companyName: formData.companyName,
      country: formData.country,
      quantity: numQuantity,
      model: component.metadata.model,
      userId, // 直接使用组件接收到的 userId
      brand_id: brandId, // 添加品牌ID
      brand_name: brandName // 添加品牌名称
    };

    // console.log("Sending inquiry data to API:", inquiryData);

    try {
      const response = await fetch('/api/submit-inquiry', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(inquiryData),
      });

      // console.log("API response status:", response.status);
      const result = await response.json();
      // console.log("API response data:", result);

      if (response.ok && result.success) {
        setSubmitStatus({ success: true, message: result.message || 'Inquiry submitted successfully!' });
        setFormData({
          contactName: '',
          businessEmail: '',
          companyName: '',
          country: '',
          quantity: ''
        });
        // 清空错误信息
        setFormErrors({
          contactName: '',
          businessEmail: '',
          companyName: '',
          country: '',
          quantity: ''
        });
      } else {
        console.error("API error response:", result);
        setSubmitStatus({ 
          success: false, 
          message: result.message || 'Failed to submit inquiry. Please try again.' 
        });
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus({ 
        success: false, 
        message: 'An unexpected error occurred. Please check your connection and try again.' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-red-50 to-red-100 px-6 py-4 border-b border-red-200">
        <h2 className="text-xl font-semibold text-red-900 flex items-center gap-2">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Assist inquiry
        </h2>
      </div>
      <div className="p-6">
      
      {submitStatus.message && (
        <div 
          className={`p-3 rounded-md mb-4 text-sm ${submitStatus.success === true ? 'bg-green-100 text-green-700' : submitStatus.success === false ? 'bg-red-100 text-red-700' : 'hidden'}`}
          role="alert"
        >
          {submitStatus.message}
        </div>
      )}

      {!userId && (
        <div className="p-3 rounded-md mb-4 text-sm bg-yellow-100 text-yellow-700" role="alert">
          Please <a href="/login" className="font-medium underline hover:text-yellow-800">log in</a> to submit an inquiry.
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="contactName" className="block text-sm text-gray-600 mb-1">Contact Name</label>
          <input
            type="text"
            id="contactName"
            name="contactName"
            value={formData.contactName}
            onChange={handleChange}
            className={`w-full px-3 py-2 border ${formErrors.contactName ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50`}
            disabled={!userId || isSubmitting}
          />
          {formErrors.contactName && (
            <p className="mt-1 text-sm text-red-500">{formErrors.contactName}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="businessEmail" className="block text-sm text-gray-600 mb-1">Business Email</label>
          <input
            type="email"
            id="businessEmail"
            name="businessEmail"
            value={formData.businessEmail}
            onChange={handleChange}
            className={`w-full px-3 py-2 border ${formErrors.businessEmail ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50`}
            disabled={!userId || isSubmitting}
          />
          {formErrors.businessEmail && (
            <p className="mt-1 text-sm text-red-500">{formErrors.businessEmail}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="companyName" className="block text-sm text-gray-600 mb-1">Company Name</label>
          <input
            type="text"
            id="companyName"
            name="companyName"
            value={formData.companyName}
            onChange={handleChange}
            className={`w-full px-3 py-2 border ${formErrors.companyName ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50`}
            disabled={!userId || isSubmitting}
          />
          {formErrors.companyName && (
            <p className="mt-1 text-sm text-red-500">{formErrors.companyName}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="country" className="block text-sm text-gray-600 mb-1">Country</label>
          <input
            type="text"
            id="country"
            name="country"
            value={formData.country}
            onChange={handleChange}
            className={`w-full px-3 py-2 border ${formErrors.country ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50`}
            disabled={!userId || isSubmitting}
          />
          {formErrors.country && (
            <p className="mt-1 text-sm text-red-500">{formErrors.country}</p>
          )}
        </div>
        
        <div>
          <label htmlFor="quantity" className="block text-sm text-gray-600 mb-1">Quantity</label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            value={formData.quantity}
            onChange={handleChange}
            className={`w-full px-3 py-2 border ${formErrors.quantity ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50`}
            disabled={!userId || isSubmitting}
          />
          {formErrors.quantity && (
            <p className="mt-1 text-sm text-red-500">{formErrors.quantity}</p>
          )}
        </div>
        
        <button
          type="submit"
          className="w-full bg-[#DE2910] text-white font-medium py-2 rounded-md hover:bg-[#DE2910]/90 transition-colors mt-2 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!userId || isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : 'Quick RFQ'}
        </button>
      </form>
      </div>
    </div>
  )
} 