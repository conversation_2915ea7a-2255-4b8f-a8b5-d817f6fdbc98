'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
// 使用内联SVG图标替代lucide-react以减少编译负担
const ChevronRightIcon = () => (
  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 18l6-6-6-6" />
  </svg>
)

const HomeIcon = () => (
  <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
)

interface BreadcrumbItem {
  label: string
  href?: string
}

interface BOMBreadcrumbProps {
  items?: BreadcrumbItem[]
}

export default function BOMBreadcrumb({ items = [] }: BOMBreadcrumbProps) {
  const pathname = usePathname()
  
  // 根据路径自动生成面包屑
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', href: '/' }
    ]

    if (pathSegments.includes('bom')) {
      if (pathname === '/bom') {
        breadcrumbs.push({ label: 'BOM Upload' })
      } else if (pathname.includes('/bom/manage')) {
        breadcrumbs.push(
          { label: 'BOM', href: '/bom' },
          { label: 'Manage' }
        )
      } else if (pathname.includes('/bom/mapping/')) {
        const bomId = pathSegments[pathSegments.indexOf('mapping') + 1]
        breadcrumbs.push(
          { label: 'BOM', href: '/bom' },
          { label: `Mapping - BOM ${bomId}` }
        )
      } else if (pathname.includes('/bom/detail/')) {
        const bomId = pathSegments[pathSegments.indexOf('detail') + 1]
        breadcrumbs.push(
          { label: 'BOM', href: '/bom' },
          { label: 'Manage', href: '/bom/manage' },
          { label: `BOM ${bomId}` }
        )
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = items.length > 0 ? items : generateBreadcrumbs()

  if (breadcrumbs.length <= 1) {
    return null
  }

  return (
    <nav className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center py-3">
          <ol className="flex items-center space-x-2 text-sm">
            {breadcrumbs.map((item, index) => (
              <li key={index} className="flex items-center">
                {index > 0 && (
                  <div className="text-gray-400 mx-2"><ChevronRightIcon /></div>
                )}
                {item.href ? (
                  <Link
                    href={item.href}
                    className="text-blue-600 hover:text-blue-800 transition-colors flex items-center"
                  >
                    {index === 0 && <div className="mr-1"><HomeIcon /></div>}
                    {item.label}
                  </Link>
                ) : (
                  <span className="text-gray-500 flex items-center">
                    {index === 0 && <div className="mr-1"><HomeIcon /></div>}
                    {item.label}
                  </span>
                )}
              </li>
            ))}
          </ol>
        </div>
      </div>
    </nav>
  )
}
