#!/usr/bin/env python3
"""
创建支付记录表的脚本
根据API_USAGE_GUIDE.md中的定义
"""

import requests
import json

# 数据库API配置
DB_API_BASE_URL = "https://api.chinaelectron.com/api"

def execute_sql(sql, params=None):
    """执行SQL语句"""
    try:
        response = requests.post(f'{DB_API_BASE_URL}/query/', 
                               headers={'Content-Type': 'application/json'},
                               json={'sql': sql, 'params': params or []})
        
        if not response.ok:
            print(f"SQL执行失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
        result = response.json()
        print(f"SQL执行成功: {sql[:50]}...")
        return result
        
    except Exception as e:
        print(f"执行SQL时出错: {e}")
        return None

def create_payment_table():
    """创建支付记录表"""
    print("正在创建支付记录表...")
    
    create_payment_table_sql = """
    CREATE TABLE IF NOT EXISTS payment (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        payment_method TEXT NOT NULL,
        transaction_number TEXT NOT NULL,
        amount DECIMAL(10, 2) NOT NULL,
        payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
        status INTEGER DEFAULT 0,
        remarks TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    result = execute_sql(create_payment_table_sql)
    if result:
        print("✅ 支付记录表创建成功")
        return True
    else:
        print("❌ 支付记录表创建失败")
        return False

def check_table_structure():
    """检查表结构"""
    print("\n正在检查表结构...")
    
    # 检查payment表结构
    check_sql = "PRAGMA table_info(payment);"
    result = execute_sql(check_sql)
    
    if result and result.get('data'):
        print("✅ payment表结构:")
        for column in result['data']:
            if len(column) >= 6:
                print(f"  - {column[1]} ({column[2]}) {'NOT NULL' if column[3] else 'NULL'} {'PK' if column[5] else ''}")
            else:
                print(f"  - {column}")
    else:
        print("❌ 无法获取payment表结构")

def test_payment_operations():
    """测试支付记录操作"""
    print("\n正在测试支付记录操作...")
    
    # 测试插入支付记录
    test_payment_data = {
        "order_id": "TEST_ORDER_001",
        "user_id": "test_user",
        "payment_method": "PayPal",
        "transaction_number": "TEST_TXN_001",
        "amount": 99.99,
        "payment_date": "2025-06-30 12:00:00",
        "status": 1,
        "remarks": "Test payment record"
    }
    
    try:
        response = requests.post(f'{DB_API_BASE_URL}/table/payment',
                               headers={'Content-Type': 'application/json'},
                               json=test_payment_data)
        
        if response.ok:
            print("✅ 测试支付记录插入成功")
            result = response.json()
            print(f"插入结果: {result}")
            
            # 测试查询支付记录
            query_response = requests.get(f'{DB_API_BASE_URL}/table/payment?where_transaction_number=TEST_TXN_001')
            if query_response.ok:
                query_result = query_response.json()
                print("✅ 测试支付记录查询成功")
                print(f"查询结果: {query_result}")
                
                # 清理测试数据
                delete_response = requests.delete(f'{DB_API_BASE_URL}/table/payment?where_transaction_number=TEST_TXN_001')
                if delete_response.ok:
                    print("✅ 测试数据清理成功")
                else:
                    print("⚠️ 测试数据清理失败，请手动删除")
            else:
                print("❌ 测试支付记录查询失败")
        else:
            print(f"❌ 测试支付记录插入失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"测试支付记录操作时出错: {e}")

def main():
    """主函数"""
    print("🏦 支付记录表创建和测试工具")
    print("=" * 40)
    
    # 1. 创建支付记录表
    if not create_payment_table():
        print("支付记录表创建失败，退出")
        return
    
    # 2. 检查表结构
    check_table_structure()
    
    # 3. 测试支付记录操作
    test_payment_operations()
    
    print("\n🎉 支付记录表设置完成！")
    print("\n支付状态说明:")
    print("  0 - 待处理 (Pending)")
    print("  1 - 成功 (Success)")
    print("  2 - 失败 (Failed)")
    print("  3 - 退款 (Refunded)")

if __name__ == "__main__":
    main()
