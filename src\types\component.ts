export interface FullData {
  name: string
  desc: string
  model: string
  location: string
  manufacture: string
  parameter: {
    languages: {
      english: {
        name: string
        desc: string
        parameters: {
          main_parameters: Record<string, any>
          secondary_parameters: Record<string, any>
          common_parameters: Record<string, any>
        }
        features: string[]
        applications: string[]
      }
      chinese: any
      russian: any
    }
  }
  img: string[]
  datasheet: string[]
  cad: string[]
  update_time: string
  category_path?: string[]
  category_id?: string
  category_names?: Record<string, {
    name_cn: string
    name_en: string
    name_ru: string
  }>
}

export interface ComponentDetail {
  id: string
  metadata: {
    cad: string
    datasheet: string
    desc: string
    full_data: string | FullData
    id: string
    img: string
    manufacture: string
    model: string
    name: string
    category_path?: string[]
    category_names?: Record<string, {
      name_cn: string
      name_en: string
      name_ru: string
    }>
  }
  parsedFullData?: FullData
  score?: number
  values?: any[]
}

// 添加新的接口定义
export interface APIResponse {
  success: boolean
  query_id: string
  matches: ComponentDetail[]
}