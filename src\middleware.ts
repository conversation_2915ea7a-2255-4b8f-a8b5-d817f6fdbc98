import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // 创建响应
  const response = NextResponse.next();
  
  // 将当前路径添加到请求头中，供服务端组件使用
  response.headers.set('x-pathname', request.nextUrl.pathname);
  
  return response;
}

export const config = {
  // 匹配所有路径，除了API路由、静态文件等
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
