# 国家省州数据获取指南

## 📋 概述

本文档介绍如何获取和维护全球国家省州数据，为地址表单提供准确的地理信息。

## 🌍 推荐数据源

### 1. 免费开源数据库 (推荐)

#### **Countries States Cities Database**
- **GitHub**: https://github.com/hiiamrohit/Countries-States-Cities-database
- **优势**: 
  - ✅ 完全免费
  - ✅ 数据最全面 (250+国家, 4900+省州)
  - ✅ 多种格式 (JSON, SQL, CSV, XML)
  - ✅ 定期更新
  - ✅ 开源可信赖
- **使用**: `node scripts/update-countries-data.js github`

#### **World Cities Database**
- **GitHub**: https://github.com/lutangar/cities.json
- **优势**: 轻量级，包含主要国家数据
- **适用**: 基础需求

### 2. 免费API服务

#### **REST Countries API**
- **网址**: https://restcountries.com/
- **优势**: 
  - ✅ 完全免费
  - ✅ 无需注册
  - ✅ 稳定可靠
- **限制**: 
  - ❌ 不包含省州数据
  - ❌ 仅有基础国家信息
- **使用**: `node scripts/update-countries-data.js restcountries`

#### **GeoNames API**
- **网址**: http://www.geonames.org/
- **优势**: 
  - ✅ 数据非常全面
  - ✅ 权威性高
  - ✅ 免费额度充足
- **限制**: 
  - ⚠️ 需要注册账号
  - ⚠️ 有请求限制 (免费: 1000次/小时)
- **成本**: 免费版本足够大多数使用场景

### 3. 商业API服务

#### **Google Places API**
- **优势**: 
  - ✅ 数据最准确最全面
  - ✅ 实时更新
  - ✅ 支持多语言
- **限制**: 
  - 💰 按请求收费
  - 💰 成本较高
- **适用**: 大型商业项目

#### **MapBox API**
- **优势**: 
  - ✅ 数据质量高
  - ✅ 性能优秀
- **限制**: 
  - 💰 按请求收费
  - 💰 有免费额度

## 🛠️ 使用我们的更新工具

### 安装和使用

```bash
# 1. 从GitHub获取最全面的数据 (推荐)
node scripts/update-countries-data.js github

# 2. 从REST Countries获取基础国家数据
node scripts/update-countries-data.js restcountries

# 3. 查看帮助信息
node scripts/update-countries-data.js --help
```

### 工具特性

- 🔄 **自动备份**: 更新前自动备份现有数据
- 📊 **数据统计**: 显示获取的国家和省州数量
- 🛡️ **错误处理**: 完善的错误处理和重试机制
- 📝 **TypeScript**: 自动生成类型安全的TypeScript文件
- ⚡ **快速更新**: 一键更新所有数据

### 输出文件

更新后的数据保存在: `src/data/countries-states.ts`

```typescript
// 自动生成的文件结构
export interface Country {
  code: string;        // ISO 3166-1 alpha-2 国家代码
  name: string;        // 国家名称
  states?: State[];    // 省州列表 (可选)
}

export interface State {
  code: string;        // 省州代码
  name: string;        // 省州名称
}
```

## 📊 当前数据覆盖

### 有详细省州数据的国家 (10个)
- 🇺🇸 **美国**: 50个州 + 华盛顿特区
- 🇨🇦 **加拿大**: 13个省和地区
- 🇦🇺 **澳大利亚**: 8个州和地区
- 🇨🇳 **中国**: 34个省市自治区 (含港澳)
- 🇩🇪 **德国**: 16个联邦州
- 🇮🇳 **印度**: 29个邦 + 德里
- 🇧🇷 **巴西**: 27个州
- 🇲🇽 **墨西哥**: 32个州
- 🇷🇺 **俄罗斯**: 85个联邦主体
- 更多国家可通过更新工具获取...

### 其他国家 (50+个)
支持文本输入模式的国家，包括欧洲、亚洲、非洲等主要贸易伙伴。

## 🔄 数据更新策略

### 更新频率建议
- **生产环境**: 每季度更新一次
- **开发环境**: 根据需要更新
- **紧急情况**: 当发现数据错误时立即更新

### 自动化更新

可以设置GitHub Actions自动更新:

```yaml
# .github/workflows/update-countries-data.yml
name: Update Countries Data
on:
  schedule:
    - cron: '0 0 1 */3 *'  # 每季度第一天
  workflow_dispatch:        # 手动触发

jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: node scripts/update-countries-data.js github
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v4
        with:
          title: 'chore: update countries and states data'
```

## 🎯 最佳实践

### 1. 数据验证
```typescript
// 验证数据完整性
const validateCountryData = (countries: Country[]) => {
  return countries.every(country => 
    country.code && 
    country.name && 
    country.code.length === 2
  );
};
```

### 2. 缓存策略
```typescript
// 客户端缓存
const CACHE_KEY = 'countries-states-data';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24小时

const getCachedData = () => {
  const cached = localStorage.getItem(CACHE_KEY);
  if (cached) {
    const { data, timestamp } = JSON.parse(cached);
    if (Date.now() - timestamp < CACHE_DURATION) {
      return data;
    }
  }
  return null;
};
```

### 3. 错误处理
```typescript
// 优雅降级
const getStatesWithFallback = (countryCode: string) => {
  try {
    return getStatesByCountry(countryCode);
  } catch (error) {
    console.warn('Failed to get states, falling back to text input');
    return [];
  }
};
```

## 🚀 高级功能

### 多语言支持
```typescript
// 支持多语言的国家名称
interface MultiLanguageCountry extends Country {
  name_zh?: string;  // 中文名称
  name_es?: string;  // 西班牙语名称
  name_fr?: string;  // 法语名称
}
```

### 地理坐标
```typescript
// 包含地理坐标的扩展数据
interface GeoCountry extends Country {
  latitude?: number;
  longitude?: number;
  bounds?: {
    north: number;
    south: number;
    east: number;
    west: number;
  };
}
```

## 📞 技术支持

如果您在使用过程中遇到问题:

1. **检查网络连接**: 确保能访问数据源API
2. **查看错误日志**: 运行脚本时注意错误信息
3. **验证数据格式**: 确保API返回的数据格式正确
4. **联系维护者**: 如果是数据源问题，可以联系对应的维护团队

## 📈 未来规划

- [ ] 支持更多数据源
- [ ] 添加城市数据支持
- [ ] 实现增量更新
- [ ] 添加数据质量检查
- [ ] 支持自定义数据源
- [ ] 添加多语言支持
