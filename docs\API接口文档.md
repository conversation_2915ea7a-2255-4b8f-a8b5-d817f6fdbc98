# API接口文档

## 接口架构概览

ChinaElectron平台采用微服务架构，API服务分布在多个域名下：
- **数据库API**: `https://api.chinaelectron.com`
- **认证API**: `https://auth.chinaelectron.com`
- **产品API**: `https://webapi.chinaelectron.com`
- **博客API**: `https://blog-manage.chinaelectron.com`
- **询价API**: `https://requests-quota.chinaelectron.com`
- **热门数据API**: `https://get-hot.chinaelectron.com`
- **最新报价API**: `https://newly-quota.chinaelectron.com`

## 1. 数据库API (Database API)

### 基础信息
- **基础URL**: `https://api.chinaelectron.com`
- **数据库**: Cloudflare D1 (SQLite)
- **认证**: 无需认证
- **CORS**: 支持跨域请求

### 1.1 数据库信息接口

#### 获取数据库信息
```http
GET /api/info
```

**响应示例**:
```json
{
  "database": "little-field-db",
  "tables": {
    "products": {
      "columns": ["product_id", "model", "brand_id", "price_key", "stock_key"],
      "count": 150000
    },
    "brands": {
      "columns": ["id", "name_cn", "name_en", "website", "domestic"],
      "count": 2500
    }
  }
}
```

#### 获取API文档
```http
GET /api/docs
```

### 1.2 单表操作接口

#### 查询数据
```http
GET /api/table/{tableName}
```

**查询参数**:
- `where_{field}`: WHERE条件筛选
- `order_by`: 排序字段
- `order_dir`: 排序方向 (ASC/DESC)
- `limit`: 限制条数 (默认100，最大1000)
- `offset`: 偏移量 (默认0)

**示例**:
```bash
# 查询TI品牌的产品
GET /api/table/products?where_brand_id=TI&limit=50

# 查询活跃用户
GET /api/table/users?where_status=active&order_by=created_at&order_dir=DESC
```

**响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "product_id": "CMA100101416",
      "model": "LM358",
      "brand_id": "TI",
      "description": "双运算放大器"
    }
  ],
  "count": 1,
  "total": 1
}
```

#### 插入数据
```http
POST /api/table/{tableName}
Content-Type: application/json
```

**请求体** (单条插入):
```json
{
  "product_id": "CMA100101418",
  "model": "LM741",
  "brand_id": "TI",
  "description": "单运算放大器"
}
```

**请求体** (批量插入):
```json
[
  {
    "product_id": "CMA100101419",
    "model": "LM324",
    "brand_id": "TI"
  },
  {
    "product_id": "CMA100101420",
    "model": "LM339",
    "brand_id": "TI"
  }
]
```

#### 更新数据
```http
PUT /api/table/{tableName}
Content-Type: application/json
```

**请求体**:
```json
{
  "set": {
    "description": "更新的描述",
    "updated_at": "2025-06-28T10:00:00Z"
  },
  "where": {
    "product_id": "CMA100101416"
  }
}
```

#### 删除数据
```http
DELETE /api/table/{tableName}
Content-Type: application/json
```

**请求体**:
```json
{
  "where": {
    "product_id": "CMA100101416"
  }
}
```

### 1.3 批量操作接口

#### 批量插入
```http
POST /api/batch/insert/{tableName}
Content-Type: application/json
```

#### 批量更新
```http
POST /api/batch/update/{tableName}
Content-Type: application/json
```

#### 批量删除
```http
POST /api/batch/delete/{tableName}
Content-Type: application/json
```

### 1.4 多表联查接口

#### 复杂联查
```http
POST /api/join/
Content-Type: application/json
```

**请求体示例**:
```json
{
  "select": [
    "p.product_id",
    "p.model",
    "b.name_en as brand_name",
    "pr.price",
    "s.stocks"
  ],
  "from": "products p",
  "joins": [
    {
      "type": "INNER",
      "table": "brands b",
      "on": "p.brand_id = b.id"
    },
    {
      "type": "LEFT",
      "table": "price pr",
      "on": "p.product_id = pr.code"
    },
    {
      "type": "LEFT",
      "table": "stocks s",
      "on": "p.product_id = s.code"
    }
  ],
  "where": {
    "p.brand_id": "TI",
    "s.stocks": {"gt": 100}
  },
  "orderBy": ["pr.price ASC"],
  "limit": 50
}
```

### 1.5 自定义SQL查询接口

#### 执行自定义SQL
```http
POST /api/query/
Content-Type: application/json
```

**请求体**:
```json
{
  "sql": "SELECT COUNT(*) as total, brand_id FROM products WHERE updated_at > ? GROUP BY brand_id ORDER BY total DESC",
  "params": ["2025-06-01"]
}
```

## 2. 认证API (Authentication API)

### 基础信息
- **基础URL**: `https://auth.chinaelectron.com`
- **认证方式**: JWT Token
- **Token存储**: localStorage

### 2.1 用户登录

#### 登录接口
```http
POST /login
Content-Type: application/json
```

**请求体**:
```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "cf_turnstile_response": "turnstile_token_here"
}
```

**响应示例**:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user123",
    "username": "<EMAIL>",
    "name": "John Doe",
    "role": "user"
  },
  "expires_in": 86400
}
```

### 2.2 用户注册

#### 注册接口
```http
POST /register
Content-Type: application/json
```

**请求体**:
```json
{
  "username": "<EMAIL>",
  "password": "password123",
  "name": "New User",
  "company": "Example Corp",
  "phone": "+86 138 0000 0000",
  "cf_turnstile_response": "turnstile_token_here"
}
```

### 2.3 Token验证

#### 验证Token
```http
POST /verify
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "valid": true,
  "user": {
    "id": "user123",
    "username": "<EMAIL>",
    "role": "user"
  }
}
```

## 3. 产品API (Product API)

### 基础信息
- **基础URL**: `https://webapi.chinaelectron.com`
- **认证**: 部分接口需要Bearer Token
- **缓存**: 支持HTTP缓存头

### 3.1 产品搜索

#### 搜索产品
```http
GET /products?search={keyword}&page={page}&limit={limit}
```

**查询参数**:
- `search`: 搜索关键词
- `category`: 产品分类ID
- `brand`: 品牌ID
- `page`: 页码 (默认1)
- `limit`: 每页数量 (默认30)

**响应示例**:
```json
{
  "total": 1500,
  "page": 1,
  "limit": 30,
  "results": [
    {
      "product_id": "CMA100101416",
      "model": "LM358",
      "brand": {
        "name_en": "Texas Instruments",
        "name_cn": "德州仪器",
        "logo_url": "https://example.com/logo.png"
      },
      "category_path": ["Semiconductors", "Amplifiers", "Op Amps"],
      "description": "Dual Operational Amplifier",
      "image_list": ["https://example.com/image1.jpg"],
      "datasheet_url": "https://example.com/datasheet.pdf"
    }
  ]
}
```

### 3.2 产品详情

#### 获取产品详情
```http
GET /products/{productId}
Authorization: Bearer {token}
```

**响应示例**:
```json
{
  "product_id": "CMA100101416",
  "model": "LM358",
  "brand": {
    "id": "TI",
    "name_en": "Texas Instruments",
    "name_cn": "德州仪器",
    "logo_url": "https://example.com/logo.png",
    "website": "https://www.ti.com"
  },
  "prices": [
    {"quantity": 1, "price": 0.25},
    {"quantity": 100, "price": 0.20},
    {"quantity": 1000, "price": 0.15}
  ],
  "stock": 50000,
  "parameters": {
    "en": [
      {"param_name": "Supply Voltage", "param_value": "3V to 32V"},
      {"param_name": "Package", "param_value": "DIP-8, SOIC-8"}
    ],
    "cn": [
      {"param_name": "供电电压", "param_value": "3V至32V"},
      {"param_name": "封装", "param_value": "DIP-8, SOIC-8"}
    ]
  },
  "category_path": ["Semiconductors", "Amplifiers", "Op Amps"],
  "datasheet_url": "https://example.com/LM358.pdf",
  "image_list": [
    "https://example.com/LM358_1.jpg",
    "https://example.com/LM358_2.jpg"
  ],
  "description": "The LM358 consists of two independent, high gain, internally frequency compensated operational amplifiers."
}
```

### 3.3 产品分类

#### 获取分类列表
```http
GET /categories
```

#### 获取热门分类
```http
GET /categories/popular
```

### 3.4 品牌信息

#### 获取品牌列表
```http
GET /brands
```

#### 获取活跃品牌
```http
GET /brands/active
```

### 3.5 产品索引

#### 获取首字母列表
```http
GET /product-index/first-text
```

#### 按首字母查询产品
```http
GET /product-index/by-first?first_text={letter}&page={page}
```

## 4. 博客API (Blog API)

### 基础信息
- **基础URL**: `https://blog-manage.chinaelectron.com`
- **认证**: 无需认证 (读取)
- **内容格式**: Markdown

### 4.1 博客文章

#### 获取文章列表
```http
GET /api/blog?page={page}&limit={limit}&category={category}
```

**响应示例**:
```json
{
  "data": [
    {
      "id": 1,
      "title": "Understanding Op-Amp Circuits",
      "subtitle": "A comprehensive guide",
      "description": "Learn the basics of operational amplifier circuits",
      "cover_image": "https://example.com/cover.jpg",
      "content_markdown": "# Introduction\n\nOperational amplifiers...",
      "tags": "electronics,amplifiers,tutorial",
      "category": "tutorials",
      "created_at": "2025-06-28T10:00:00Z",
      "author": {
        "name": "John Engineer",
        "avatar": "https://example.com/avatar.jpg"
      }
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 10,
    "pages": 15
  }
}
```

#### 获取文章详情
```http
GET /api/blog/{id}
```

### 4.2 专题集合

#### 获取专题列表
```http
GET /api/insights?limit={limit}
```

#### 获取专题详情
```http
GET /api/insights/{id}
```

## 5. 询价API (RFQ API)

### 基础信息
- **基础URL**: `https://requests-quota.chinaelectron.com`
- **认证**: 需要Bearer Token
- **功能**: 询价请求管理

### 5.1 询价请求

#### 提交询价请求
```http
POST /api/rfq
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**:
```json
{
  "type": "single",
  "contact_name": "John Doe",
  "business_email": "<EMAIL>",
  "company_name": "Example Corp",
  "country": "China",
  "quantity": 1000,
  "model": "LM358",
  "brand_name": "Texas Instruments",
  "target_price": 0.20,
  "delivery_time": "2 weeks",
  "date_code": "2025+"
}
```

#### 获取询价列表
```http
GET /api/rfq?user_id={userId}&page={page}
Authorization: Bearer {token}
```

### 5.2 报价管理

#### 获取报价列表
```http
GET /api/quotes?request_id={requestId}
Authorization: Bearer {token}
```

## 6. 热门数据API (Hot Data API)

### 基础信息
- **基础URL**: `https://get-hot.chinaelectron.com`
- **认证**: 无需认证
- **更新频率**: 实时更新

### 6.1 热门产品

#### 获取最新热门产品
```http
GET /latest?type=hot&limit={limit}
```

**响应示例**:
```json
{
  "data": [
    {
      "model": "LM358",
      "brand": "Texas Instruments",
      "search_count": 1250,
      "trend": "up",
      "category": "Op Amps"
    }
  ],
  "updated_at": "2025-06-28T10:00:00Z"
}
```

## 7. 最新报价API (Latest Quote API)

### 基础信息
- **基础URL**: `https://newly-quota.chinaelectron.com`
- **认证**: 无需认证
- **数据来源**: 实时报价数据

### 7.1 最新报价

#### 获取最新报价
```http
GET /api/latest?domestic=chinese&limit={limit}
```

## 错误处理

### 标准错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETER",
    "message": "Invalid parameter value",
    "details": "The 'limit' parameter must be between 1 and 1000"
  },
  "timestamp": "2025-06-28T10:00:00Z"
}
```

### 常见错误码
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `429`: 请求频率限制
- `500`: 服务器内部错误

## 认证机制

### JWT Token格式
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token生命周期
- **有效期**: 24小时
- **刷新**: 自动刷新机制
- **存储**: localStorage (客户端)

## 8. Turnstile验证API

### 基础信息
- **基础URL**: 内部API `/api/verify-turnstile`
- **用途**: Cloudflare Turnstile人机验证
- **集成**: 登录、注册表单

### 8.1 验证Turnstile Token

#### 验证接口
```http
POST /api/verify-turnstile
Content-Type: application/json
```

**请求体**:
```json
{
  "token": "turnstile_response_token_here"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "challenge_ts": "2025-06-28T10:00:00Z",
    "hostname": "chinaelectron.com"
  }
}
```

## 9. 邮件订阅API

### 9.1 邮件订阅

#### 订阅接口
```http
POST /api/subscribe
Content-Type: application/json
```

**请求体**:
```json
{
  "email": "<EMAIL>",
  "name": "John Doe",
  "preferences": ["newsletter", "product_updates"]
}
```

## 10. 搜索建议API

### 10.1 搜索建议

#### 获取搜索建议
```http
GET /api/search/suggestions?q={query}&limit={limit}
```

**响应示例**:
```json
{
  "suggestions": [
    {
      "text": "LM358",
      "type": "product",
      "category": "Op Amps"
    },
    {
      "text": "Texas Instruments",
      "type": "brand"
    }
  ]
}
```

## 11. 推荐系统API

### 11.1 推荐关键词

#### 获取推荐搜索
```http
GET /api/recommend?date={date}&limit={limit}
```

**响应示例**:
```json
{
  "keywords": [
    {
      "keyword": "STM32",
      "popularity": 95,
      "category": "Microcontrollers"
    },
    {
      "keyword": "Arduino",
      "popularity": 88,
      "category": "Development Boards"
    }
  ]
}
```

## 12. 广告管理API

### 12.1 广告数据

#### 获取广告配置
```http
GET /api/ads/config?platform={platform}&page={page}
```

#### 获取广告详情
```http
GET /api/ads/details?location={location}&status=active
```

**响应示例**:
```json
{
  "ads": [
    {
      "id": 1,
      "title": "Summer Sale",
      "subtitle": "Up to 50% off",
      "img": "https://example.com/ad1.jpg",
      "target_url": "https://example.com/sale",
      "priority": 1,
      "effective_time": "2025-06-01T00:00:00Z"
    }
  ]
}
```

## API使用最佳实践

### 1. 请求频率限制
- **一般接口**: 100请求/分钟
- **搜索接口**: 50请求/分钟
- **认证接口**: 10请求/分钟

### 2. 错误重试策略
- **网络错误**: 指数退避重试
- **5xx错误**: 最多重试3次
- **4xx错误**: 不重试，检查请求参数

### 3. 数据分页
- **默认页大小**: 30条
- **最大页大小**: 100条
- **推荐页大小**: 20-50条

### 4. 性能优化
- **使用HTTP缓存**: 设置适当的缓存头
- **压缩请求**: 启用gzip压缩
- **批量操作**: 优先使用批量接口

### 5. 安全考虑
- **HTTPS**: 所有API必须使用HTTPS
- **Token安全**: 不在URL中传递敏感信息
- **输入验证**: 严格验证所有输入参数

## 缓存策略

### HTTP缓存头
- `Cache-Control`: 缓存控制
- `ETag`: 实体标签
- `Last-Modified`: 最后修改时间

### 缓存时间
- **静态数据**: 24小时
- **动态数据**: 1小时
- **实时数据**: 5分钟

## API版本管理

### 版本策略
- **当前版本**: v1
- **版本格式**: `/api/v1/endpoint`
- **向后兼容**: 保持至少2个版本

### 版本升级
- **弃用通知**: 提前3个月通知
- **迁移指南**: 提供详细迁移文档
- **并行支持**: 新旧版本并行运行

## 监控和日志

### API监控指标
- **响应时间**: 平均响应时间 < 200ms
- **成功率**: 99.9%以上
- **错误率**: 各类错误的分布统计

### 日志格式
```json
{
  "timestamp": "2025-06-28T10:00:00Z",
  "method": "GET",
  "url": "/api/products/CMA100101416",
  "status": 200,
  "response_time": 150,
  "user_id": "user123",
  "ip": "***********"
}
```
