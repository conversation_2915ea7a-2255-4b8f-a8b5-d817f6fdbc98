import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const metadata: Metadata = {
  title: 'Blog | China Electron - Electronic Component Industry News & Insights',
  description: 'Read the latest electronic component industry news, technical articles, market analysis, and insights on China Electron blog.',
  keywords: 'electronic component news, semiconductor industry, technical articles, market analysis, China Electron blog',
  alternates: {
    canonical: getCanonicalUrl('blog'),
  },
}

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
} 