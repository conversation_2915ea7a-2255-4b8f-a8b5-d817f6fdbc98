# 服务端面包屑使用指南

## 概述

为了提升SEO效果，我们将面包屑改为服务端渲染。这样搜索引擎爬虫可以直接看到面包屑内容，而不需要等待JavaScript执行。

## 使用方法

### 1. 导入必要的组件和函数

```typescript
import { generateBreadcrumbs } from '@/utils/breadcrumbs'
import ServerBreadcrumb from '@/components/Breadcrumb/ServerBreadcrumb'
```

### 2. 在页面组件中生成面包屑

```typescript
export default async function YourPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  
  // 生成面包屑
  const currentPath = `/your-path/${params.id}`;
  const breadcrumbs = await generateBreadcrumbs(currentPath);
  
  return (
    <div>
      {/* 服务端渲染的面包屑 */}
      <ServerBreadcrumb breadcrumbs={breadcrumbs} />
      
      {/* 页面其他内容 */}
      <div>Your page content...</div>
    </div>
  );
}
```

## 已实现的页面示例

### 1. 制造商详情页面 (`/manufacturers/[id]`)

```typescript
// 生成面包屑
const currentPath = `/manufacturers/${brandId}`;
const breadcrumbs = await generateBreadcrumbs(currentPath);

return (
  <div className="min-h-screen bg-gray-50">
    {/* 服务端渲染的面包屑 */}
    <ServerBreadcrumb breadcrumbs={breadcrumbs} />
    
    {/* 页面内容 */}
    <div className="bg-white border-b border-gray-200">
      {/* ... */}
    </div>
  </div>
);
```

### 2. 产品详情页面 (`/product/[id]`)

```typescript
// 生成面包屑
const currentPath = `/product/${urlPath}`;
const breadcrumbs = await generateBreadcrumbs(currentPath);

return (
  <div className="min-h-screen bg-white">
    {/* 服务端渲染的面包屑 */}
    <ServerBreadcrumb breadcrumbs={breadcrumbs} />
    
    {/* JSON-LD 结构化数据 */}
    <ProductJsonLd ... />
    
    {/* 页面内容 */}
    <div className="max-w mx-auto px-14 py-8">
      {/* ... */}
    </div>
  </div>
);
```

## 需要添加面包屑的其他页面

以下页面还需要添加服务端面包屑：

1. **搜索页面** (`/search`)
2. **分类页面** (`/category/[id]`)
3. **博客文章页面** (`/insights/[topic]/[article]`)
4. **数据表页面** (`/datasheet/[productId]`)
5. **BOM详情页面** (`/bom/detail/[bomId]`)
6. **用户账户页面** (`/account/*`)

## 性能优势

### SEO 优势
- 搜索引擎可以直接看到面包屑内容
- 提升页面的结构化数据质量
- 改善搜索结果中的面包屑显示

### 性能优势
- 减少客户端JavaScript执行
- 更快的首屏渲染
- 更好的Core Web Vitals 分数

### 缓存优势
- 利用 Cloudflare Workers 的边缘缓存
- 1小时的服务端缓存
- 减少重复的API调用

## 注意事项

1. **错误处理**: `generateBreadcrumbs` 函数包含了完整的错误处理和重试机制
2. **后备方案**: 如果 Workers API 失败，会自动使用简单的后备面包屑
3. **缓存**: 面包屑数据会被缓存1小时，提升性能
4. **类型安全**: 所有组件都有完整的TypeScript类型定义

## 迁移步骤

对于现有页面，按以下步骤迁移：

1. 添加导入语句
2. 在页面组件中调用 `generateBreadcrumbs`
3. 在JSX中添加 `<ServerBreadcrumb breadcrumbs={breadcrumbs} />`
4. 测试面包屑是否正确显示
5. 检查页面源代码确认面包屑已服务端渲染
