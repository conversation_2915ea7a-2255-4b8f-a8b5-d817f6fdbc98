'use client'

import { useState } from 'react'

interface FAQItem {
  question: string;
  answer: string;
}

interface FAQSection {
  title: string;
  questions: FAQItem[];
}

interface FAQAccordionProps {
  sections: FAQSection[];
}

export default function FAQAccordion({ sections }: FAQAccordionProps) {
  // 状态管理：跟踪每个问题的展开/折叠状态
  const [expandedItems, setExpandedItems] = useState<{[key: string]: boolean}>({});

  // 切换问题的展开/折叠状态
  const toggleItem = (sectionIndex: number, itemIndex: number) => {
    const key = `${sectionIndex}-${itemIndex}`;
    setExpandedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // 检查问题是否展开
  const isExpanded = (sectionIndex: number, itemIndex: number) => {
    const key = `${sectionIndex}-${itemIndex}`;
    return expandedItems[key] || false;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
        <h2 className="text-xl font-semibold text-blue-900 flex items-center gap-2">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Frequently Asked Questions (FAQ)
        </h2>
      </div>
      <div className="p-6">
        {sections.map((section, sectionIndex) => (
          <div key={sectionIndex} className="mb-8 last:mb-0">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 border-b pb-2">{section.title}</h3>
            <div className="space-y-3">
              {section.questions.map((item, itemIndex) => (
                <div 
                  key={itemIndex} 
                  className="border border-gray-200 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
                >
                  <button
                    onClick={() => toggleItem(sectionIndex, itemIndex)}
                    className="w-full text-left px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100 flex justify-between items-center"
                  >
                    <span className="font-medium text-gray-900">Q: {item.question}</span>
                    <svg 
                      className={`w-5 h-5 text-gray-500 transition-transform duration-200 ${isExpanded(sectionIndex, itemIndex) ? 'transform rotate-180' : ''}`} 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  {isExpanded(sectionIndex, itemIndex) && (
                    <div className="px-4 py-3 bg-white border-t border-gray-200">
                      <div className="text-gray-700">
                        {item.answer.split('\n').map((line, i) => (
                          <p key={i} className={i > 0 ? 'mt-2' : ''}>
                            {i === 0 && <span className="font-medium text-gray-900">A: </span>}
                            {line}
                          </p>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
