'use client'

import { useEffect, useRef, useState } from 'react'
import Script from 'next/script'

interface PayPalButtonProps {
  orderId: string
  amount: number
  currency?: string
  onSuccess?: (details: any) => void
  onError?: (error: any) => void
  onCancel?: () => void
  disabled?: boolean
}

declare global {
  interface Window {
    paypal?: any
  }
}

export default function PayPalButton({
  orderId,
  amount,
  currency = 'USD',
  onSuccess,
  onError,
  onCancel,
  disabled = false
}: PayPalButtonProps) {
  const paypalRef = useRef<HTMLDivElement>(null)
  const [isScriptLoaded, setIsScriptLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // PayPal客户端ID - 从环境变量获取，支持生产环境
  const PAYPAL_CLIENT_ID = process.env.NEXT_PUBLIC_PAYPAL_CLIENT_ID || 'Aaw539l1Q1uQs0ceDD3u_trL3a987pfyhVDBieEiZwOFJ9vioqbArb2Z0yKJuibdqDSGRPgqj3h3m3Mg'

  const createOrder = async () => {
    try {
      setIsLoading(true)
      setError(null)

      console.log('Creating PayPal order for:', { orderId, amount, currency })

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30秒超时

      const response = await fetch('/api/paypal/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          order_id: orderId,
          amount: amount,
          currency: currency
        }),
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      console.log('PayPal order creation response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        console.error('PayPal order creation failed:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to create PayPal order`)
      }

      const orderData = await response.json()
      console.log('PayPal order created successfully:', orderData)

      if (!orderData.id) {
        console.error('Invalid PayPal order response:', orderData)
        throw new Error('Invalid response: missing order ID')
      }

      return orderData.id

    } catch (error) {
      console.error('Error creating PayPal order:', error)
      let errorMessage = 'Failed to create order'

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Request timed out. Please try again.'
        } else {
          errorMessage = error.message
        }
      }

      setError(errorMessage)
      onError?.(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const onApprove = async (data: any, actions: any) => {
    try {
      setIsLoading(true)
      setError(null)

      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 45000) // 45秒超时（支付捕获可能需要更长时间）

      const response = await fetch(`/api/paypal/orders/${data.orderID}/capture`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to capture payment`)
      }

      const captureData = await response.json()

      // 检查支付状态
      const transaction = captureData?.purchase_units?.[0]?.payments?.captures?.[0]

      if (transaction && transaction.status === 'COMPLETED') {
        console.log('Payment completed successfully:', transaction)
        onSuccess?.(captureData)
      } else {
        console.error('Payment not completed:', captureData)
        throw new Error('Payment was not completed successfully')
      }

    } catch (error) {
      console.error('Error capturing payment:', error)
      let errorMessage = 'Payment capture failed'

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Payment processing timed out. Please check your order status.'
        } else {
          errorMessage = error.message
        }
      }

      setError(errorMessage)
      onError?.(error)
    } finally {
      setIsLoading(false)
    }
  }

  const initializePayPalButton = () => {
    if (!window.paypal || !paypalRef.current) return

    // 清空容器
    paypalRef.current.innerHTML = ''

    window.paypal.Buttons({
      createOrder: createOrder,
      onApprove: onApprove,
      onCancel: () => {
        console.log('Payment cancelled by user')
        onCancel?.()
      },
      onError: (err: any) => {
        console.error('PayPal button error:', err)
        setError('PayPal payment error occurred')
        onError?.(err)
      },
      style: {
        layout: 'vertical',
        color: 'gold',
        shape: 'rect',
        label: 'paypal',
        height: 45
      }
    }).render(paypalRef.current)
  }

  useEffect(() => {
    if (isScriptLoaded && !disabled) {
      initializePayPalButton()
    }
  }, [isScriptLoaded, disabled, orderId, amount])

  return (
    <div className="paypal-button-container">
      <Script
        src={`https://www.paypal.com/sdk/js?client-id=${PAYPAL_CLIENT_ID}&currency=${currency}&components=buttons&intent=capture&vault=false`}
        onLoad={() => setIsScriptLoaded(true)}
        onError={() => setError('Failed to load PayPal SDK')}
      />
      
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}
      
      {isLoading && (
        <div className="flex items-center justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#DE2910]"></div>
          <span className="ml-2 text-gray-600">Processing payment...</span>
        </div>
      )}
      
      <div 
        ref={paypalRef} 
        className={`${disabled ? 'opacity-50 pointer-events-none' : ''}`}
        style={{ minHeight: '45px' }}
      />
      
      {disabled && (
        <p className="text-sm text-gray-500 mt-2">
          Payment is not available for this order
        </p>
      )}
    </div>
  )
}
