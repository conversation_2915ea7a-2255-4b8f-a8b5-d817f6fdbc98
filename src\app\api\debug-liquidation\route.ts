import { NextRequest, NextResponse } from 'next/server'

/**
 * Debug API Route for liquidation items
 * This route helps debug what data is actually returned from the flashsale API
 */
export async function GET(request: NextRequest) {
  try {
    // Prepare request headers for external API
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'User-Agent': 'ChinaElectron-Debug/1.0',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://chinaelectron.com'
    }

    // Fetch data from external API
    const response = await fetch('https://get-hot.chinaelectron.com/latest?type=flashsale', {
      method: 'GET',
      headers,
      cache: 'no-store' // Don't cache to get fresh data
    })

    if (!response.ok) {
      return NextResponse.json(
        { 
          success: false, 
          error: `External API responded with status: ${response.status}`,
          status: response.status,
          statusText: response.statusText
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    
    // Return detailed debug information
    const debugInfo = {
      success: true,
      originalData: data,
      dataType: typeof data,
      hasSuccess: data?.success,
      hasData: data?.data,
      dataIsArray: Array.isArray(data?.data),
      totalItems: Array.isArray(data?.data) ? data.data.length : 0,
      sampleItems: Array.isArray(data?.data) ? data.data.slice(0, 3) : [],
      domesticValues: Array.isArray(data?.data) ? 
        [...new Set(data.data.map((item: any) => item.domestic))] : [],
      chineseItems: Array.isArray(data?.data) ? 
        data.data.filter((item: any) => item.domestic === "chinese").length : 0
    }

    return NextResponse.json(debugInfo, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      }
    })

  } catch (error: any) {
    console.error('Error in debug-liquidation API route:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error',
        stack: error.stack
      },
      { status: 500 }
    )
  }
}
