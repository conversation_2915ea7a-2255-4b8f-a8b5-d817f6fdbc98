# Cloudflare D1 数据库结构导出工具

这个工具包含两个 Python 脚本，用于导出 Cloudflare D1 数据库的表结构信息（不包含数据）。

## 文件说明

- `d1_schema_export.py` - 功能完整的导出工具，支持多种格式
- `simple_d1_export.py` - 简化版导出工具，快速使用
- `D1_EXPORT_README.md` - 使用说明文档

## 前置要求

1. **安装 Node.js 和 npm**
2. **安装 Wrangler CLI**:
   ```bash
   npm install -g wrangler
   ```
3. **登录 Cloudflare**:
   ```bash
   wrangler auth login
   ```
4. **Python 3.6+**

## 快速开始 - 使用简化版工具

### 基本用法

```bash
# 导出远程数据库结构
python simple_d1_export.py your-database-name

# 指定输出文件名
python simple_d1_export.py your-database-name my_schema.json

# 导出本地开发数据库
python simple_d1_export.py your-database-name --local
```

### 输出示例

```
正在导出数据库 'my-app-db' 的结构...
发现 3 个表: users, posts, comments
正在处理表: users
正在处理表: posts  
正在处理表: comments

✅ 导出完成!
📁 文件保存位置: my-app-db_schema_20241227_143022.json
📊 统计信息:
   - 总表数: 3
   - 总字段数: 15

📋 表结构摘要:
   users: 5 个字段, 1 个索引
     主键: id
   posts: 6 个字段, 2 个索引
     主键: id
   comments: 4 个字段, 1 个索引
     主键: id
```

## 完整版工具使用

### 基本用法

```bash
# 导出为 JSON 格式（默认）
python d1_schema_export.py your-database-name

# 导出为 CSV 格式
python d1_schema_export.py your-database-name --format csv

# 导出为 SQL 格式
python d1_schema_export.py your-database-name --format sql

# 导出所有格式
python d1_schema_export.py your-database-name --format all

# 显示数据库结构摘要
python d1_schema_export.py your-database-name --summary

# 使用本地数据库
python d1_schema_export.py your-database-name --local
```

### 高级用法

```bash
# 指定输出文件名
python d1_schema_export.py my-db --output my_schema.json

# 组合使用多个选项
python d1_schema_export.py my-db --format all --summary --output schema_backup
```

## 输出格式说明

### JSON 格式
包含完整的数据库结构信息：
```json
{
  "database_name": "my-database",
  "export_time": "2024-12-27T14:30:22",
  "tables": {
    "users": {
      "columns": [
        {
          "cid": 0,
          "name": "id",
          "type": "INTEGER",
          "notnull": 0,
          "dflt_value": null,
          "pk": 1
        }
      ],
      "indexes": []
    }
  },
  "create_statements": []
}
```

### CSV 格式
表格形式的字段信息：
```csv
表名,字段名,数据类型,是否非空,默认值,是否主键,字段序号
users,id,INTEGER,否,,是,0
users,email,TEXT,是,,否,1
```

### SQL 格式
完整的建表语句：
```sql
-- Cloudflare D1 数据库结构导出
-- 数据库名称: my-database
-- 导出时间: 2024-12-27 14:30:22

-- TABLE: users
CREATE TABLE users (
  id INTEGER PRIMARY KEY,
  email TEXT NOT NULL,
  created_at INTEGER
);
```

## 导出的信息包括

- ✅ 所有表名
- ✅ 每个表的字段信息（名称、类型、是否非空、默认值、是否主键）
- ✅ 索引信息
- ✅ 完整的建表语句
- ✅ 视图定义
- ❌ 实际数据（仅导出结构）

## 常见问题

### 1. 命令执行失败
确保：
- Wrangler CLI 已正确安装和登录
- 数据库名称正确
- 有访问数据库的权限

### 2. 找不到表
脚本会自动过滤系统表（以 `sqlite_` 和 `_cf_` 开头的表）

### 3. 本地 vs 远程数据库
- 默认导出远程数据库
- 使用 `--local` 参数导出本地开发数据库

## 示例用法场景

### 1. 备份数据库结构
```bash
python d1_schema_export.py production-db --format sql --output backup_schema.sql
```

### 2. 比较不同环境的数据库结构
```bash
python d1_schema_export.py prod-db --output prod_schema.json
python d1_schema_export.py dev-db --local --output dev_schema.json
# 然后比较两个 JSON 文件
```

### 3. 生成文档
```bash
python d1_schema_export.py my-db --summary
python d1_schema_export.py my-db --format csv --output database_docs.csv
```

### 4. 迁移准备
```bash
python d1_schema_export.py old-db --format all --output migration_backup
```

## 注意事项

1. 脚本需要网络连接来访问 Cloudflare D1
2. 导出时间取决于表的数量和复杂度
3. 大型数据库可能需要较长时间
4. 确保有足够的磁盘空间存储导出文件

## 故障排除

如果遇到问题，请检查：
1. Wrangler 版本是否最新：`wrangler --version`
2. 是否已登录：`wrangler auth whoami`
3. 数据库是否存在：`wrangler d1 list`
4. Python 版本：`python --version`
