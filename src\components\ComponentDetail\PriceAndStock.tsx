import { ComponentDetail, FullData } from '@/types/component'

interface PriceAndStockProps {
  component: ComponentDetail
  parsedData: FullData
}

export default function PriceAndStock({ component, parsedData }: PriceAndStockProps) {
  // 获取价格信息
  const prices = (parsedData as any).prices || [];
  const stock = (parsedData as any).stock || 'Unknown';
  
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Price & Stock</h2>
      
      <div className="space-y-4">
        {/* 价格信息 */}
        <div>
          <h3 className="text-base font-medium text-gray-800 mb-3">Price</h3>
          {prices.length > 0 ? (
            <div className="space-y-2">
              {prices.map((price: any, index: number) => (
                <div key={index} className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">{price.quantity}+ :</span>
                  <span className="font-medium text-[#DE2910]">${price.price.toFixed(4)}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 italic">Price information unavailable</div>
          )}
        </div>
        
        {/* 库存信息 */}
        <div>
          <h3 className="text-base font-medium text-gray-800 mb-3">Stock</h3>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Available:</span>
            <span className="font-medium text-gray-900">{stock}</span>
          </div>
        </div>
      </div>
    </div>
  )
} 