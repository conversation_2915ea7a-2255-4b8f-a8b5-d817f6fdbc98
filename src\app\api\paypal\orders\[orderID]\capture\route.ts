import { NextRequest, NextResponse } from 'next/server'

// PayPal配置
const PAYPAL_CLIENT_ID = process.env.PAYPAL_CLIENT_ID || 'Aaw539l1Q1uQs0ceDD3u_trL3a987pfyhVDBieEiZwOFJ9vioqbArb2Z0yKJuibdqDSGRPgqj3h3m3Mg'
const PAYPAL_CLIENT_SECRET = process.env.PAYPAL_CLIENT_SECRET || 'EISkpM6MAPZvkC5xLqd-8o_skXxPcXFRSt2LPixPYBq_N5jxCt5pv5Xi1sU8MXZRonlPRtA7fXhwYuoi'
const PAYPAL_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api-m.paypal.com' 
  : 'https://api-m.sandbox.paypal.com'

const DB_API_BASE_URL = 'https://api.chinaelectron.com/api'

// 获取PayPal访问令牌
async function getPayPalAccessToken(): Promise<string> {
  const auth = Buffer.from(`${PAYPAL_CLIENT_ID}:${PAYPAL_CLIENT_SECRET}`).toString('base64')
  
  const response = await fetch(`${PAYPAL_BASE_URL}/v1/oauth2/token`, {
    method: 'POST',
    headers: {
      'Authorization': `Basic ${auth}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: 'grant_type=client_credentials'
  })

  if (!response.ok) {
    throw new Error('Failed to get PayPal access token')
  }

  const data = await response.json()
  return data.access_token
}

// 创建支付记录
async function createPaymentRecord(orderId: string, userId: string, paymentDetails: any, retryCount = 0): Promise<boolean> {
  const maxRetries = 3
  const retryDelay = 2000 // 2秒延迟

  try {
    console.log(`Attempting to create payment record for ${orderId}, attempt ${retryCount + 1}/${maxRetries + 1}`)

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

    const paymentData = {
      order_id: orderId,
      user_id: userId,
      payment_method: 'PayPal',
      transaction_number: paymentDetails.id || 'UNKNOWN',
      amount: parseFloat(paymentDetails.amount?.value || '0'),
      payment_date: new Date().toISOString(),
      status: paymentDetails.status === 'COMPLETED' ? 1 : 0,
      remarks: `PayPal payment ${paymentDetails.status}. Transaction ID: ${paymentDetails.id}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const response = await fetch(`${DB_API_BASE_URL}/table/payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Failed to create payment record (HTTP ${response.status}):`, errorText)

      // 如果是服务器错误且还有重试次数，则重试
      if (response.status >= 500 && retryCount < maxRetries) {
        console.log(`Retrying in ${retryDelay}ms...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        return createPaymentRecord(orderId, userId, paymentDetails, retryCount + 1)
      }

      return false
    }

    const result = await response.json()
    console.log('Payment record created successfully:', result)
    return true

  } catch (error) {
    console.error(`Error creating payment record (attempt ${retryCount + 1}):`, error)

    if (error instanceof Error && error.name === 'AbortError') {
      console.error('Database request timed out')
    }

    // 如果还有重试次数，则重试
    if (retryCount < maxRetries) {
      console.log(`Retrying in ${retryDelay}ms...`)
      await new Promise(resolve => setTimeout(resolve, retryDelay))
      return createPaymentRecord(orderId, userId, paymentDetails, retryCount + 1)
    }

    return false
  }
}

// 更新订单状态为已支付（带重试机制）
async function updateOrderStatus(orderId: string, paymentDetails: any, retryCount = 0): Promise<boolean> {
  const maxRetries = 3
  const retryDelay = 2000 // 2秒延迟

  try {
    console.log(`Attempting to update order status for ${orderId}, attempt ${retryCount + 1}/${maxRetries + 1}`)

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 减少到15秒超时

    const response = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_status: 'PAID',
        payment_method: 'PayPal'
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`Failed to update order status (HTTP ${response.status}):`, errorText)

      // 如果是服务器错误且还有重试次数，则重试
      if (response.status >= 500 && retryCount < maxRetries) {
        console.log(`Retrying in ${retryDelay}ms...`)
        await new Promise(resolve => setTimeout(resolve, retryDelay))
        return updateOrderStatus(orderId, paymentDetails, retryCount + 1)
      }

      return false
    }

    const result = await response.json()
    console.log('Order status updated successfully:', result)
    return true

  } catch (error) {
    console.error(`Error updating order status (attempt ${retryCount + 1}):`, error)

    if (error instanceof Error && error.name === 'AbortError') {
      console.error('Database request timed out')
    }

    // 如果还有重试次数，则重试
    if (retryCount < maxRetries) {
      console.log(`Retrying in ${retryDelay}ms...`)
      await new Promise(resolve => setTimeout(resolve, retryDelay))
      return updateOrderStatus(orderId, paymentDetails, retryCount + 1)
    }

    return false
  }
}

// 异步处理支付记录和订单状态更新（不阻塞支付响应）
async function processPaymentAsync(orderId: string, userId: string, paymentDetails: any) {
  // 在后台异步执行，不阻塞主流程
  setImmediate(async () => {
    try {
      // 首先创建支付记录
      const paymentRecordSuccess = await createPaymentRecord(orderId, userId, paymentDetails)
      if (paymentRecordSuccess) {
        console.log(`Payment record created successfully for order ${orderId}`)
      } else {
        console.error(`Failed to create payment record for order ${orderId}`)
      }

      // 然后更新订单状态
      const orderStatusSuccess = await updateOrderStatus(orderId, paymentDetails)
      if (orderStatusSuccess) {
        console.log(`Order ${orderId} status updated successfully in background`)
      } else {
        console.error(`Failed to update order ${orderId} status after all retries`)
      }

      // 记录处理结果
      if (paymentRecordSuccess && orderStatusSuccess) {
        console.log(`Payment processing completed successfully for order ${orderId}`)
      } else {
        console.warn(`Payment processing partially failed for order ${orderId}. Payment record: ${paymentRecordSuccess}, Order status: ${orderStatusSuccess}`)
        // 这里可以添加其他通知机制，比如发送邮件或记录到日志系统
      }
    } catch (error) {
      console.error(`Background payment processing failed for ${orderId}:`, error)
    }
  })
}

// 捕获PayPal支付
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ orderID: string }> }
) {
  try {
    const { orderID } = await params

    if (!orderID) {
      return NextResponse.json(
        { error: 'Missing orderID parameter' },
        { status: 400 }
      )
    }

    // 获取访问令牌
    const accessToken = await getPayPalAccessToken()

    // 捕获PayPal订单
    const response = await fetch(`${PAYPAL_BASE_URL}/v2/checkout/orders/${orderID}/capture`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'PayPal-Request-Id': `capture-${orderID}-${Date.now()}`
      }
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('PayPal capture failed:', errorData)
      return NextResponse.json(
        { error: 'Failed to capture PayPal payment' },
        { status: response.status }
      )
    }

    const captureData = await response.json()
    
    // 检查支付状态
    const transaction = captureData?.purchase_units?.[0]?.payments?.captures?.[0]
    
    if (transaction && transaction.status === 'COMPLETED') {
      // 从reference_id获取我们的订单ID
      const ourOrderId = captureData?.purchase_units?.[0]?.reference_id

      if (ourOrderId) {
        // 获取用户ID（从订单中查询）
        try {
          const orderResponse = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${ourOrderId}`)
          if (orderResponse.ok) {
            const orderResult = await orderResponse.json()
            const orders = orderResult.data || []
            if (orders.length > 0) {
              const userId = orders[0].user_id
              // 异步处理支付记录和订单状态更新，不阻塞支付响应
              processPaymentAsync(ourOrderId, userId, transaction)
            } else {
              console.error(`Order ${ourOrderId} not found for payment processing`)
              // 如果找不到订单，仍然尝试更新状态（不创建支付记录）
              processPaymentAsync(ourOrderId, 'UNKNOWN', transaction)
            }
          } else {
            console.error(`Failed to fetch order ${ourOrderId} for payment processing`)
            // 如果查询失败，仍然尝试更新状态
            processPaymentAsync(ourOrderId, 'UNKNOWN', transaction)
          }
        } catch (error) {
          console.error(`Error fetching order ${ourOrderId} for payment processing:`, error)
          // 如果出错，仍然尝试更新状态
          processPaymentAsync(ourOrderId, 'UNKNOWN', transaction)
        }

        console.log(`Payment captured successfully for order ${ourOrderId}, payment processing initiated in background`)
      }
    }

    return NextResponse.json({
      id: captureData.id,
      status: captureData.status,
      purchase_units: captureData.purchase_units,
      payer: captureData.payer
    })

  } catch (error) {
    console.error('Error capturing PayPal payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
