import Image from 'next/image'
import Link from 'next/link'

interface AdItem {
  id: string
  title: string
  image_url: string
  link_url: string
  alt_text: string
}

interface SideAdsProps {
  ads: AdItem[]
  className?: string
  adLocations?: string[]
}

export default function SideAds({ ads = [], className = '', adLocations = [] }: SideAdsProps) {
  if (ads.length === 0) {
    return (
      <div className={`flex flex-col gap-4 ${className}`}>
        {[1, 2].map((index) => (
          <div
            key={index}
            className="group relative aspect-[1/0.54] w-full rounded-md overflow-hidden
                     bg-gradient-to-br from-blue-500 to-blue-600 shadow-sm
                     hover:shadow-md transition-all duration-300 ease-out"
            data-location={adLocations[index - 1] || ''}
          >
            {/* 内容 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center">
                <div className="text-white text-xl font-semibold mb-1">
                  Side Ad {index}
                </div>
                <div className="text-white/90 text-sm">
                  Advertisement Space
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      {ads.slice(0, 2).map((ad, index) => (
        <Link
          key={ad.id}
          href={ad.link_url}
          className="group block relative aspect-[1/0.54] w-full rounded-md overflow-hidden
                   bg-gradient-to-br from-blue-500 to-blue-600 shadow-sm
                   hover:shadow-md transition-all duration-300 ease-out"
          data-location={adLocations[index] || ''}
        >
          {/* 图片或默认内容 */}
          {ad.image_url ? (
            <>
              <Image
                src={ad.image_url}
                alt={ad.alt_text || ad.title}
                fill
                className="object-cover group-hover:scale-102 transition-transform duration-300"
              />
              {/* 悬停遮罩 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent
                            opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </>
          ) : (
            <>
              {/* 内容 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-white text-xl font-semibold mb-1">
                    Side Ad {index + 1}
                  </div>
                  <div className="text-white/90 text-sm">
                    Advertisement Space
                  </div>
                </div>
              </div>
            </>
          )}
        </Link>
      ))}
    </div>
  )
}