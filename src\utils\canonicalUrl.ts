/**
 * 生成规范化的 URL（canonical URL）
 * 
 * @param path - 相对路径（不包含域名）
 * @returns 完整的规范化 URL
 */
export const getCanonicalUrl = (path: string = ''): string => {
  // 基础域名
  const baseUrl = 'https://www.chinaelectron.com';
  
  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // 移除结尾的斜杠（除非是根路径）
  const cleanPath = normalizedPath === '/' 
    ? '/' 
    : normalizedPath.endsWith('/') 
      ? normalizedPath.slice(0, -1) 
      : normalizedPath;
  
  // 组合完整的 URL
  return `${baseUrl}${cleanPath}`;
};

/**
 * 为特定页面创建下一个 Metadata 对象，包含 canonical URL
 * 
 * @param path - 页面的相对路径
 * @param baseMetadata - 基础元数据对象
 * @returns 包含 canonical URL 的元数据对象
 */
export const createMetadataWithCanonical = (
  path: string,
  baseMetadata: Record<string, any> = {}
): Record<string, any> => {
  return {
    ...baseMetadata,
    alternates: {
      ...(baseMetadata.alternates || {}),
      canonical: getCanonicalUrl(path),
    }
  };
}; 