#!/usr/bin/env python3
"""
检查和创建合规声明表的脚本
根据API_USAGE_GUIDE.md中的定义
"""

import requests
import json

# 数据库API配置
DB_API_BASE_URL = "https://api.chinaelectron.com"

def check_table_exists():
    """检查表是否存在"""
    try:
        print("🔍 检查合规声明表是否存在...")
        
        response = requests.post(
            f"{DB_API_BASE_URL}/api/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": "SELECT name FROM sqlite_master WHERE type='table' AND name='compliance_statement'",
                "params": []
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('data') and len(result['data']) > 0:
                print("✅ 合规声明表已存在")
                return True
            else:
                print("❌ 合规声明表不存在")
                return False
        else:
            print(f"❌ 检查表失败: {response.status_code}")
            print(f"📄 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 检查表时发生错误: {str(e)}")
        return False

def create_compliance_table():
    """根据API_USAGE_GUIDE.md创建合规声明表"""
    
    # 根据API_USAGE_GUIDE.md的定义创建表
    create_table_sql = """
    CREATE TABLE compliance_statement (
        compliance_id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        order_id TEXT,
        compliance_confirmed BOOLEAN DEFAULT FALSE,
        resell_components BOOLEAN DEFAULT FALSE,
        ultimate_consignee TEXT,
        country TEXT,
        application_type TEXT,
        additional_information TEXT,
        is_default BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    try:
        print("🔧 创建合规声明表...")
        
        response = requests.post(
            f"{DB_API_BASE_URL}/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": create_table_sql,
                "params": []
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 合规声明表创建成功!")
            print(f"📊 响应: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ 创建表失败: {response.status_code}")
            print(f"📄 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 创建表时发生错误: {str(e)}")
        return False

def check_table_structure():
    """检查表结构"""
    try:
        print("🔍 检查表结构...")
        
        response = requests.post(
            f"{DB_API_BASE_URL}/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": "PRAGMA table_info(compliance_statement)",
                "params": []
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 表结构查询成功!")
            
            if result.get('data'):
                print("\n📋 表字段信息:")
                for column in result['data']:
                    cid, name, type_, notnull, default, pk = column
                    print(f"  - {name} ({type_}) {'NOT NULL' if notnull else 'NULL'} {'PK' if pk else ''} {f'DEFAULT {default}' if default else ''}")
                return True
            else:
                print("⚠️  表结构查询无数据")
                return False
                
        else:
            print(f"❌ 查询表结构失败: {response.status_code}")
            print(f"📄 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 查询表结构时发生错误: {str(e)}")
        return False

def test_insert():
    """测试插入数据"""
    try:
        print("🧪 测试插入合规声明数据...")
        
        test_data = {
            "user_id": "test_user_001",
            "compliance_confirmed": True,
            "resell_components": False,
            "ultimate_consignee": "Test Company Ltd",
            "country": "Germany",
            "application_type": "Industrial",
            "additional_information": "Test compliance statement for debugging",
            "is_default": False
        }
        
        insert_sql = """
        INSERT INTO compliance_statement (
            user_id, compliance_confirmed, resell_components, ultimate_consignee,
            country, application_type, additional_information, is_default, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """
        
        response = requests.post(
            f"{DB_API_BASE_URL}/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": insert_sql,
                "params": [
                    test_data["user_id"],
                    test_data["compliance_confirmed"],
                    test_data["resell_components"],
                    test_data["ultimate_consignee"],
                    test_data["country"],
                    test_data["application_type"],
                    test_data["additional_information"],
                    test_data["is_default"]
                ]
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 测试数据插入成功!")
            print(f"📊 完整响应: {json.dumps(result, indent=2)}")
            
            # 检查各种可能的ID字段
            insert_id = None
            if 'data' in result:
                if 'insertId' in result['data']:
                    insert_id = result['data']['insertId']
                elif 'compliance_id' in result['data']:
                    insert_id = result['data']['compliance_id']
            elif 'insertId' in result:
                insert_id = result['insertId']
            elif 'compliance_id' in result:
                insert_id = result['compliance_id']
                
            if insert_id:
                print(f"🆔 插入的记录ID: {insert_id}")
            else:
                print("⚠️  未找到插入的记录ID，但插入可能成功")
                
            return True
        else:
            print(f"❌ 插入数据失败: {response.status_code}")
            print(f"📄 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 插入数据时发生错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🌍 合规声明表检查和创建工具")
    print("=" * 50)
    
    # 1. 检查表是否存在
    table_exists = check_table_exists()
    
    # 2. 如果不存在，创建表
    if not table_exists:
        print("\n📝 表不存在，开始创建...")
        if not create_compliance_table():
            print("❌ 表创建失败，退出")
            return
    
    # 3. 检查表结构
    print("\n🔍 检查表结构...")
    if not check_table_structure():
        print("❌ 表结构检查失败")
        return
    
    # 4. 测试插入
    print("\n🧪 测试数据插入...")
    if test_insert():
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 测试失败")

if __name__ == "__main__":
    main()
