import Image from 'next/image'
import Link from 'next/link'
import { Suspense } from 'react'
import Pagination from '@/components/Pagination'
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

// 为服务端组件生成元数据
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Electronic Component Distributors Network | China Electron',
    description: 'Discover a global network of electronic component raw material distributors on China Electron. Find reliable partners for your supply chain.',
    keywords: 'distributor network, electronic component distributors, raw material distributors, B2B supply chain, China Electron, component sourcing',
    alternates: {
      canonical: getCanonicalUrl('distributors'),
    },
  }
}

// Define distributor data type
interface Distributor {
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  logo: string;
  address_cn: string;
  address_en: string;
  address_ru: string;
}

interface DistributorsResponse {
  total: number;
  page: number;
  limit: number;
  results: Distributor[];
}

// Function to get distributors data
async function getDistributors(page: number = 1, search?: string): Promise<DistributorsResponse> {
  const searchParams = new URLSearchParams();
  if (page) searchParams.append('page', page.toString());
  if (search) searchParams.append('search', search);
  
  const url = `https://webapi.chinaelectron.com/distributors?${searchParams.toString()}`;
  
  try {
    const response = await fetch(url, { next: { revalidate: 86400 } }); // Cache for 24 hours
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Failed to fetch distributors:', error);
    // Return empty data as fallback
    return { total: 0, page: 1, limit: 30, results: [] };
  }
}

// Build distributor image URL
function getDistributorImageUrl(imageName: string): string {
  // If it's a complete URL, return it directly
  if (imageName.startsWith('http')) {
    return imageName;
  }
  
  // Otherwise directly return the original API URL to frontend
  return `https://webapi.chinaelectron.com/distributors/image/${imageName}`;
}

// Determine region based on address
function getRegion(distributor: Distributor): string {
  if (distributor.address_cn) {
    return 'Asia';
  } else if (distributor.address_ru) {
    return 'Europe';
  } else {
    return 'Global';
  }
}

// Distributors list component
async function DistributorsList({ page = 1, search }: { page?: number, search?: string }) {
  const distributorsData = await getDistributors(page, search);
  const { results: distributors, total, limit } = distributorsData;
  
  // Calculate total pages
  const totalPages = Math.ceil(total / limit);
  
  return (
    <>
      {/* Distributors list */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-8">
        {distributors.length > 0 ? (
          distributors.map(distributor => (
            <Link
              key={distributor.distributor_id}
              href={`/distributors/${distributor.distributor_id}`}
              prefetch={false}
              className="group bg-white rounded-xl p-4 border border-gray-100
                      hover:border-[#DE2910]/20 hover:shadow-md
                      transition-all duration-300 flex flex-col items-center"
            >
              {/* Logo */}
              <div className="h-16 w-full mb-4 relative">
                <Image
                  src={getDistributorImageUrl(distributor.logo)}
                  alt={distributor.name_en || "Distributor logo"}
                  fill
                  className="object-contain"
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"
                />
              </div>
              
              {/* Name - Only content now */}
              <h3 className="font-medium text-gray-900 text-center
                          group-hover:text-[#DE2910] transition-colors">
                {distributor.name_en || distributor.name_cn}
              </h3>
            </Link>
          ))
        ) : (
          <div className="col-span-full text-center py-12 text-gray-500">
            No distributors found
          </div>
        )}
      </div>
      
      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination 
          currentPage={page} 
          totalPages={totalPages} 
          baseUrl="/distributors" 
          searchParam={search}
        />
      )}
    </>
  );
}

// Loading state component
function DistributorsListSkeleton() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
      {Array.from({ length: 10 }).map((_, index) => (
        <div 
          key={index}
          className="bg-white rounded-xl p-4 border border-gray-100 animate-pulse flex flex-col items-center"
        >
          <div className="h-16 w-full mb-4 bg-gray-200 rounded"></div>
          <div className="h-5 bg-gray-200 rounded w-3/4"></div>
        </div>
      ))}
    </div>
  );
}

// Main page component
export default async function DistributorsPage(props: {
  searchParams: Promise<{ page?: string, search?: string }>
}) {
  const searchParams = await props.searchParams;
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  const search = searchParams.search;
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">

      <div className="relative">
        <div className="max-w mx-auto px-14 py-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Distributors Directory</h1>
          
          {/* Search box */}
          <form className="mb-8">
            <div className="flex max-w-md">
              <input
                type="text"
                name="search"
                defaultValue={search}
                placeholder="Search distributors..."
                className="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
              />
              <button 
                type="submit"
                className="bg-[#DE2910] text-white px-4 py-2 rounded-r-md hover:bg-[#DE2910]/90 transition-colors"
              >
                Search
              </button>
            </div>
          </form>
          
          {/* Distributors list */}
          <Suspense fallback={<DistributorsListSkeleton />}>
            <DistributorsList page={page} search={search} />
          </Suspense>
        </div>
      </div>
    </div>
  );
} 