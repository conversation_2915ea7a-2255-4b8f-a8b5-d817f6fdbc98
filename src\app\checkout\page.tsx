'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import {
  getUserAddresses,
  addComplianceStatement,
  calculateShippingFee,
  createOrder,
  getLCSCExpressOptions,
  CartItem,
  AddressInfo,
  CreateOrderRequest,
  ExpressOption
} from '@/services/api'
import { allCountries } from '@/data/countries-states-complete'
// 使用内联SVG图标替代lucide-react
const MapPinIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const FileTextIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 2v6h6M16 13H8M16 17H8M10 9H8" />
  </svg>
)

const TruckIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17a2 2 0 11-4 0 2 2 0 014 0zM21 17a2 2 0 11-4 0 2 2 0 014 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M1 1h4l2.68 13.39a2 2 0 002 1.61h9.72a2 2 0 002-1.61L23 6H6" />
  </svg>
)

const CreditCardIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
  </svg>
)

const ArrowLeftIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 12H5m7-7l-7 7 7 7" />
  </svg>
)

const CheckIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
)
import Link from 'next/link'
import LoadingSpinner from '@/components/common/LoadingSpinner'

export default function CheckoutPage() {
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()
  
  // 状态管理
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [addresses, setAddresses] = useState<AddressInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [loadingShipping, setLoadingShipping] = useState(false)
  const [dataFetched, setDataFetched] = useState(false)

  // 表单状态
  const [selectedShippingAddress, setSelectedShippingAddress] = useState<number | null>(null)
  const [selectedBillingAddress, setSelectedBillingAddress] = useState<number | null>(null)
  const [selectedShippingMethod, setSelectedShippingMethod] = useState('')

  // 合规声明表单状态
  const [complianceForm, setComplianceForm] = useState({
    hasUnderstood: false,
    willResell: 'yes',
    ultimateConsignee: '',
    country: 'Germany',
    application: 'Industrial',
    additionalInfo: ''
  })

  // 合规声明选项数据 - 使用完整的国家列表
  const countries = allCountries.map(country => ({
    code: country.code,
    name: country.name,
    nameCn: country.nameCn
  })).sort((a, b) => a.name.localeCompare(b.name))

  const applications = [
    'Industrial', 'Consumer Electronics', 'Automotive', 'Medical',
    'Telecommunications', 'Aerospace', 'Military/Defense', 'Research & Development'
  ]

  // 运费和快递选项
  const [shippingInfo, setShippingInfo] = useState({
    shipping_fee: 0,
    handling_fee: 3,
    discount_amount: 0,
    estimated_weight: 0
  })
  const [expressOptions, setExpressOptions] = useState<ExpressOption[]>([])
  
  const [message, setMessage] = useState<{ type: 'success' | 'error' | null; text: string }>({
    type: null,
    text: ''
  })

  // 获取快递选项
  const fetchExpressOptions = async (shippingAddress?: AddressInfo) => {
    if (!cartItems.length || !shippingAddress) return

    try {
      setLoadingShipping(true)

      // 计算重量和订单金额（使用真实产品重量）
      const estimatedWeight = cartItems.reduce((total, item) => {
        const itemWeight = item.weight || 0.1; // 如果没有重量数据，使用默认0.1g
        return total + (item.cart_quantity * itemWeight);
      }, 0);
      const merchandiseTotal = cartItems.reduce((sum, item) => sum + (item.unit_price * item.cart_quantity), 0);

      // 准备API请求数据
      const countryCode = shippingAddress.country_region;
      const province = shippingAddress.state_province;
      const city = shippingAddress.city;
      const postCode = shippingAddress.postal_code;

      console.log('📦 Fetching express options with data:', {
        countryCode,
        province,
        city,
        postCode,
        weight: Math.max(estimatedWeight, 1),
        orderAmt: merchandiseTotal,
        currencyCode: 'USD',
        orderItemTotal: cartItems.length,
        vat: false,
        isFrom: true,
        isHasBuzzer: false,
        isIncludeSpecialCategory: false,
        weightDetails: cartItems.map(item => ({
          product_id: item.product_id,
          model: item.model_number,
          quantity: item.cart_quantity,
          weight: item.weight || 0.1,
          totalWeight: item.cart_quantity * (item.weight || 0.1)
        }))
      });

      // 获取快递选项（已包含重试机制）
      const options = await getLCSCExpressOptions(
        countryCode,
        Math.max(estimatedWeight, 1),
        merchandiseTotal,
        province,
        city,
        postCode,
        cartItems.length
      )

      setExpressOptions(options)

      // 如果还没有选择运输方式，选择第一个选项
      if (!selectedShippingMethod && options.length > 0) {
        setSelectedShippingMethod(options[0].expressCode)
        await updateShippingFee(options[0].expressCode, shippingAddress)
      }
    } catch (error) {
      console.error('Error fetching express options:', error)
      setMessage({ type: 'error', text: 'Unable to load shipping options. Please check your address and try again, or contact customer service.' })
    } finally {
      setLoadingShipping(false)
    }
  }

  // 更新运费信息
  const updateShippingFee = async (expressCode: string, shippingAddress?: AddressInfo) => {
    if (!cartItems.length || !shippingAddress) return

    try {
      const result = await calculateShippingFee(
        cartItems,
        expressCode,
        shippingAddress.country_region,
        shippingAddress.state_province,
        shippingAddress.city,
        shippingAddress.postal_code
      )

      setShippingInfo({
        shipping_fee: result.shipping_fee,
        handling_fee: result.handling_fee,
        discount_amount: result.discount_amount,
        estimated_weight: result.estimated_weight
      })
    } catch (error) {
      console.error('Error updating shipping fee:', error)
      setMessage({ type: 'error', text: 'Unable to calculate shipping cost. Please try again or contact customer service.' })
    }
  }

  // 获取数据
  const fetchData = async () => {
    if (!userId || dataFetched) {
      console.log('Skipping fetchData - userId:', userId, 'dataFetched:', dataFetched)
      return
    }

    console.log('=== CHECKOUT DATA FETCH START ===')
    console.log('dataFetched before:', dataFetched)

    // 立即设置dataFetched为true，防止重复调用
    setDataFetched(true)

    try {
      setLoading(true)

      // 只使用从购物车页面传递过来的商品数据
      console.log('Checking sessionStorage for checkoutItems...')
      console.log('All sessionStorage keys:', Object.keys(sessionStorage))
      const checkoutItemsStr = sessionStorage.getItem('checkoutItems')
      console.log('Raw sessionStorage data:', checkoutItemsStr)
      console.log('sessionStorage data type:', typeof checkoutItemsStr)
      console.log('sessionStorage data length:', checkoutItemsStr?.length || 0)

      if (!checkoutItemsStr) {
        console.log('❌ No checkout items found in sessionStorage')
        setMessage({ type: 'error', text: 'No items selected for checkout. Please go back to cart and select items.' })
        setLoading(false)
        return
      }

      // 使用传递过来的已筛选商品数据
      const cartData: CartItem[] = JSON.parse(checkoutItemsStr)
      console.log('✅ Using filtered checkout items from sessionStorage')
      console.log('Filtered items count:', cartData.length)

      // 不立即清除sessionStorage，保留数据以支持页面刷新
      // sessionStorage将在订单成功创建后清除

      // 清除任何之前的错误消息
      setMessage({ type: null, text: '' })

      let addressData: AddressInfo[] = []
      try {
        addressData = await getUserAddresses(userId)
        console.log('Successfully fetched user addresses:', addressData)
      } catch (addressError) {
        console.error('Failed to fetch user addresses:', addressError)
        // 继续执行，但地址列表为空，不设置错误消息，因为这不影响checkout流程
      }

      console.log('Final cartData being set:', cartData)
      console.log('Final cartData count:', cartData.length)
      setCartItems(cartData)
      setAddresses(addressData)

      // 设置默认选择
      const defaultShipping = addressData.find(addr => addr.address_type === 'shipping' && addr.is_default)
      const defaultBilling = addressData.find(addr => addr.address_type === 'billing' && addr.is_default)

      if (defaultShipping) {
        setSelectedShippingAddress(defaultShipping.address_id)
        // 获取快递选项
        if (cartData.length > 0) {
          await fetchExpressOptions(defaultShipping)
        }
      }
      if (defaultBilling) setSelectedBillingAddress(defaultBilling.address_id)

    } catch (error) {
      console.error('Error fetching checkout data:', error)
      setMessage({ type: 'error', text: 'Failed to load checkout data' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!authLoading && isAuthenticated && userId && !dataFetched) {
      fetchData()
    } else if (!authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [authLoading, isAuthenticated, userId, router, dataFetched])



  // 处理收货地址变更
  const handleShippingAddressChange = async (addressId: number) => {
    setSelectedShippingAddress(addressId)

    // 重置运费信息和物流方式选择
    setSelectedShippingMethod('')
    setShippingInfo({
      shipping_fee: 0,
      handling_fee: 3,
      discount_amount: 0,
      estimated_weight: 0
    })

    const address = addresses.find(addr => addr.address_id === addressId)
    if (address && cartItems.length > 0) {
      await fetchExpressOptions(address)
    }
  }

  // 处理运输方式变更
  const handleShippingMethodChange = async (expressCode: string) => {
    setSelectedShippingMethod(expressCode)
    const shippingAddress = addresses.find(addr => addr.address_id === selectedShippingAddress)
    if (shippingAddress) {
      await updateShippingFee(expressCode, shippingAddress)
    }
  }

  // 计算总价
  const calculateTotals = () => {
    const merchandiseTotal = cartItems.reduce((sum, item) => 
      sum + (item.unit_price * item.cart_quantity), 0
    )
    const totalQuantity = cartItems.reduce((sum, item) => sum + item.cart_quantity, 0)
    const orderTotal = merchandiseTotal + shippingInfo.shipping_fee + shippingInfo.handling_fee - shippingInfo.discount_amount
    
    return {
      merchandiseTotal,
      totalQuantity,
      orderTotal
    }
  }

  // 提交订单
  const handleSubmitOrder = async () => {
    if (!userId || !selectedShippingAddress || !selectedBillingAddress || !complianceForm.hasUnderstood || !complianceForm.ultimateConsignee.trim()) {
      setMessage({ type: 'error', text: 'Please complete all required fields including compliance statement' })
      return
    }

    if (cartItems.length === 0) {
      setMessage({ type: 'error', text: 'Your cart is empty' })
      return
    }

    // 安全检查：必须选择物流商
    if (!selectedShippingMethod) {
      setMessage({ type: 'error', text: 'Please select a shipping method before submitting your order' })
      return
    }

    // 安全检查：运费必须大于0
    if (shippingInfo.shipping_fee <= 0) {
      setMessage({ type: 'error', text: 'Invalid shipping cost. Please refresh the page and try again, or contact customer service' })
      return
    }

    try {
      setSubmitting(true)
      setMessage({ type: null, text: '' })

      // 先创建合规声明
      const complianceData = {
        user_id: userId,
        compliance_confirmed: complianceForm.hasUnderstood,
        resell_components: complianceForm.willResell === 'yes',
        ultimate_consignee: complianceForm.ultimateConsignee,
        country: complianceForm.country,
        application_type: complianceForm.application,
        additional_information: complianceForm.additionalInfo || undefined,
        is_default: false
      }

      // 创建合规声明并获取ID
      const complianceResult = await addComplianceStatement(complianceData)
      console.log('Compliance statement result:', complianceResult)

      if (!complianceResult.success) {
        throw new Error(`Failed to create compliance statement: ${complianceResult.message}`)
      }

      // 合规声明创建成功，即使没有返回ID也继续（某些API实现可能不返回ID）
      const complianceId = complianceResult.compliance_id || 0

      // 获取选中的地址信息
      const shippingAddress = addresses.find(addr => addr.address_id === selectedShippingAddress)
      const billingAddress = addresses.find(addr => addr.address_id === selectedBillingAddress)

      if (!shippingAddress || !billingAddress) {
        setMessage({ type: 'error', text: 'Selected addresses not found. Please refresh the page and try again.' })
        return
      }

      const orderRequest: CreateOrderRequest = {
        user_id: userId,
        shipping_address: shippingAddress,
        billing_address: billingAddress,
        compliance_statement_id: complianceId,
        shipping_method: selectedShippingMethod,
        cart_items: cartItems,
        shipping_fee: shippingInfo.shipping_fee,
        handling_fee: shippingInfo.handling_fee,
        discount_amount: shippingInfo.discount_amount
      }

      const result = await createOrder(orderRequest)

      if (result.success && result.order_id) {
        setMessage({ type: 'success', text: 'Order created successfully!' })
        // 订单创建成功后清除sessionStorage
        sessionStorage.removeItem('checkoutItems')
        console.log('Order created successfully, cleared sessionStorage checkoutItems')
        // 跳转到订单详情页
        setTimeout(() => {
          router.push(`/orders/${result.order_id}`)
        }, 1500)
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to create order' })
      }
    } catch (error) {
      console.error('Error creating order:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create order. Please try again.'
      setMessage({ type: 'error', text: errorMessage })
    } finally {
      setSubmitting(false)
    }
  }

  const { merchandiseTotal, totalQuantity, orderTotal } = calculateTotals()

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-600 mb-4">Your cart is empty</h2>
          <p className="text-gray-500 mb-8">Add items to your cart before checkout</p>
          <Link
            href="/cart"
            className="inline-flex items-center gap-2 bg-[#DE2910] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#DE2910]/90 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            Back to Cart
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center gap-4 mb-8">
          <Link
            href="/cart"
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            <ArrowLeftIcon className="w-5 h-5" />
            Back to Cart
          </Link>
          <h1 className="text-3xl font-bold text-gray-900">Checkout</h1>
        </div>

        {/* 消息显示 */}
        {message.text && (
          <div className={`p-4 rounded-lg mb-6 ${
            message.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
          }`}>
            {message.text}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧表单 */}
          <div className="lg:col-span-2 space-y-8">
            {/* 地址选择 */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-[#DE2910] text-white rounded-full flex items-center justify-center">
                  <CheckIcon className="w-4 h-4" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Address</h2>
              </div>

              {/* 收货地址 */}
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Shipping Address</h3>
                {addresses.filter(addr => addr.address_type === 'shipping').length > 0 ? (
                  <div className="space-y-3">
                    {addresses.filter(addr => addr.address_type === 'shipping').map((address) => (
                      <label key={address.address_id} className="flex items-start gap-3 p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-[#DE2910]/50">
                        <input
                          type="radio"
                          name="shippingAddress"
                          value={address.address_id}
                          checked={selectedShippingAddress === address.address_id}
                          onChange={() => handleShippingAddressChange(address.address_id)}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{address.first_name} {address.last_name}</div>
                          <div className="text-sm text-gray-600">
                            {address.company_name && <div>{address.company_name}</div>}
                            <div>{address.street_address}</div>
                            {address.apt_suite_building && <div>{address.apt_suite_building}</div>}
                            <div>{address.city}, {address.state_province} {address.postal_code}</div>
                            <div>{address.country_region}</div>
                            <div>{address.phone}</div>
                          </div>
                          {address.is_default && (
                            <span className="inline-block mt-1 px-2 py-1 bg-[#DE2910]/10 text-[#DE2910] text-xs rounded">Default</span>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <MapPinIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p>No shipping addresses found</p>
                    <Link href="/account/addresses" className="text-[#DE2910] hover:text-[#DE2910]/80 font-medium">
                      Add a shipping address
                    </Link>
                  </div>
                )}
              </div>

              {/* 账单地址 */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Billing Address</h3>
                {addresses.filter(addr => addr.address_type === 'billing').length > 0 ? (
                  <div className="space-y-3">
                    {addresses.filter(addr => addr.address_type === 'billing').map((address) => (
                      <label key={address.address_id} className="flex items-start gap-3 p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-[#DE2910]/50">
                        <input
                          type="radio"
                          name="billingAddress"
                          value={address.address_id}
                          checked={selectedBillingAddress === address.address_id}
                          onChange={() => setSelectedBillingAddress(address.address_id)}
                          className="mt-1"
                        />
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{address.first_name} {address.last_name}</div>
                          <div className="text-sm text-gray-600">
                            {address.company_name && <div>{address.company_name}</div>}
                            <div>{address.street_address}</div>
                            {address.apt_suite_building && <div>{address.apt_suite_building}</div>}
                            <div>{address.city}, {address.state_province} {address.postal_code}</div>
                            <div>{address.country_region}</div>
                            <div>{address.phone}</div>
                          </div>
                          {address.is_default && (
                            <span className="inline-block mt-1 px-2 py-1 bg-[#DE2910]/10 text-[#DE2910] text-xs rounded">Default</span>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <MapPinIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                    <p>No billing addresses found</p>
                    <Link href="/account/addresses" className="text-[#DE2910] hover:text-[#DE2910]/80 font-medium">
                      Add a billing address
                    </Link>
                  </div>
                )}
              </div>
            </div>

            {/* 合规声明 */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-[#DE2910] text-white rounded-full flex items-center justify-center">
                  <FileTextIcon className="w-4 h-4" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Compliance Statement</h2>
              </div>

              <div className="space-y-6">
                {/* 确认理解部分 */}
                <div>
                  <label className="flex items-start gap-3 cursor-pointer">
                    <div className="relative mt-1">
                      <input
                        type="checkbox"
                        checked={complianceForm.hasUnderstood}
                        onChange={(e) => setComplianceForm(prev => ({ ...prev, hasUnderstood: e.target.checked }))}
                        className="sr-only"
                      />
                      <div className={`w-5 h-5 border-2 rounded flex items-center justify-center transition-colors ${
                        complianceForm.hasUnderstood
                          ? 'bg-[#DE2910] border-[#DE2910] text-white'
                          : 'border-gray-300 bg-white'
                      }`}>
                        {complianceForm.hasUnderstood && <CheckIcon className="w-3 h-3" />}
                      </div>
                    </div>
                    <div className="flex-1">
                      <span className="text-gray-900">
                        I have understood and confirmed that I comply with the contents of the{' '}
                        <Link href="/compliance/customer-statement" target="_blank" className="text-[#DE2910] hover:text-[#DE2910]/80 underline">
                          "customer compliance statement"
                        </Link>
                        . Always keep the above selection and do not ask again.
                      </span>
                    </div>
                  </label>
                </div>

                {/* 转售问题 */}
                <div>
                  <h3 className="text-base font-medium text-gray-900 mb-3">
                    Will components purchased from China Electron be resold by you in their purchased form?
                  </h3>
                  <div className="flex gap-6">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <div className="relative">
                        <input
                          type="radio"
                          name="willResell"
                          value="yes"
                          checked={complianceForm.willResell === 'yes'}
                          onChange={(e) => setComplianceForm(prev => ({ ...prev, willResell: e.target.value }))}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 border-2 rounded-full flex items-center justify-center transition-colors ${
                          complianceForm.willResell === 'yes'
                            ? 'border-[#DE2910]'
                            : 'border-gray-300'
                        }`}>
                          {complianceForm.willResell === 'yes' && (
                            <div className="w-2 h-2 bg-[#DE2910] rounded-full"></div>
                          )}
                        </div>
                      </div>
                      <span className="text-gray-900">Yes</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <div className="relative">
                        <input
                          type="radio"
                          name="willResell"
                          value="no"
                          checked={complianceForm.willResell === 'no'}
                          onChange={(e) => setComplianceForm(prev => ({ ...prev, willResell: e.target.value }))}
                          className="sr-only"
                        />
                        <div className={`w-4 h-4 border-2 rounded-full flex items-center justify-center transition-colors ${
                          complianceForm.willResell === 'no'
                            ? 'border-[#DE2910]'
                            : 'border-gray-300'
                        }`}>
                          {complianceForm.willResell === 'no' && (
                            <div className="w-2 h-2 bg-[#DE2910] rounded-full"></div>
                          )}
                        </div>
                      </div>
                      <span className="text-gray-900">No</span>
                    </label>
                  </div>
                </div>

                {/* 详细信息说明 */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-blue-800 text-sm">
                    Please enter the Ultimate Consignee, the country where these products will be consumed or used in a
                    manufacturing process, the application that best describes how these products will be used, and finally any
                    additional information related to the application or Ultimate Consignee.
                  </p>
                </div>

                {/* 表单字段 */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Ultimate Consignee */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <span className="text-red-500">*</span> Ultimate Consignee
                    </label>
                    <input
                      type="text"
                      value={complianceForm.ultimateConsignee}
                      onChange={(e) => setComplianceForm(prev => ({ ...prev, ultimateConsignee: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                      placeholder="Enter ultimate consignee"
                      required
                    />
                  </div>

                  {/* Country */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <span className="text-red-500">*</span> Country
                    </label>
                    <select
                      value={complianceForm.country}
                      onChange={(e) => setComplianceForm(prev => ({ ...prev, country: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                      required
                    >
                      {countries.map((country) => (
                        <option key={country.code} value={country.name}>
                          {country.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Application */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <span className="text-red-500">*</span> What application best describe how the products will be used?
                  </label>
                  <select
                    value={complianceForm.application}
                    onChange={(e) => setComplianceForm(prev => ({ ...prev, application: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                    required
                  >
                    {applications.map((app) => (
                      <option key={app} value={app}>
                        {app}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Additional Information */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Please provide any additional information related to the application. (optional)
                  </label>
                  <textarea
                    value={complianceForm.additionalInfo}
                    onChange={(e) => setComplianceForm(prev => ({ ...prev, additionalInfo: e.target.value }))}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                    placeholder="Enter additional information..."
                  />
                </div>
              </div>
            </div>

            {/* 运输方式 */}
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 bg-[#DE2910] text-white rounded-full flex items-center justify-center">
                  <TruckIcon className="w-4 h-4" />
                </div>
                <h2 className="text-xl font-semibold text-gray-900">Shipping Method</h2>
              </div>

              {loadingShipping ? (
                <div className="flex items-center justify-center py-8">
                  <div className="w-6 h-6 border-2 border-[#DE2910] border-t-transparent rounded-full animate-spin"></div>
                  <span className="ml-2 text-gray-600">Loading shipping options...</span>
                </div>
              ) : expressOptions.length > 0 ? (
                <div className="space-y-3">
                  {expressOptions.map((option, index) => (
                    <label key={`${option.expressCode}-${index}`} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg cursor-pointer hover:border-[#DE2910]/50">
                      <div className="flex items-center gap-3">
                        <input
                          type="radio"
                          name="shippingMethod"
                          value={option.expressCode}
                          checked={selectedShippingMethod === option.expressCode}
                          onChange={() => handleShippingMethodChange(option.expressCode)}
                        />
                        <div>
                          <div className="font-medium text-gray-900">{option.expressNameEn}</div>
                          <div className="text-sm text-gray-600">
                            {option.days} Days • {option.forwarderType}
                            {option.shipmentType === 'customer' && (
                              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                                Customer Account
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-gray-900">
                          {option.shipmentType === 'customer' ? 'Free*' : `$${option.costOrigin.toFixed(2)}`}
                        </div>
                        {option.shipmentType === 'customer' && (
                          <div className="text-xs text-gray-500">*Use your account</div>
                        )}
                      </div>
                    </label>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <TruckIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p>Please select a shipping address to view shipping options</p>
                </div>
              )}

              <div className="mt-4 text-sm text-gray-600">
                <p>Estimated Weight: {shippingInfo.estimated_weight}g</p>
                {expressOptions.length > 0 && (
                  <p className="mt-1">
                    Available shipping options: {expressOptions.length}
                    ({expressOptions.filter(opt => opt.shipmentType === 'lc').length} paid, {expressOptions.filter(opt => opt.shipmentType === 'customer').length} customer account)
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* 右侧订单摘要 */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Order Total</h2>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Merchandise Total</span>
                  <span className="font-semibold">${merchandiseTotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping Cost</span>
                  <span className="font-semibold">${shippingInfo.shipping_fee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Handling Fee</span>
                  <span className="font-semibold">${shippingInfo.handling_fee.toFixed(2)}</span>
                </div>
                {shippingInfo.discount_amount > 0 && (
                  <div className="flex justify-between text-red-600">
                    <span>Shipping Discount</span>
                    <span>-${shippingInfo.discount_amount.toFixed(2)}</span>
                  </div>
                )}
                <hr className="border-gray-200" />
                <div className="flex justify-between text-lg font-semibold">
                  <span>Order Total</span>
                  <span className="text-[#DE2910]">${orderTotal.toFixed(2)}</span>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-3">Items ({totalQuantity})</h3>
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {cartItems.map((item) => (
                    <div key={item.cart_id} className="flex justify-between text-sm">
                      <span className="text-gray-600 truncate">{item.model_number} × {item.cart_quantity}</span>
                      <span className="font-medium">${(item.unit_price * item.cart_quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>

              <button
                onClick={handleSubmitOrder}
                disabled={submitting || !selectedShippingAddress || !selectedBillingAddress || !complianceForm.hasUnderstood || !complianceForm.ultimateConsignee.trim() || !selectedShippingMethod || shippingInfo.shipping_fee <= 0}
                className="w-full bg-[#DE2910] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#DE2910]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
              >
                {submitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Creating Order...
                  </>
                ) : (
                  <>
                    <CreditCardIcon className="w-4 h-4" />
                    Submit Order
                  </>
                )}
              </button>

              <div className="mt-6 text-center">
                <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                  <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                      <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z"/>
                    </svg>
                  </div>
                  All personal data is securely stored and transmitted using encryption
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
