'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function AccountRedirect() {
  const router = useRouter()
  
  useEffect(() => {
    // 使用短超时确保页面已完全加载
    const timer = setTimeout(() => {
      router.push('/account/rfq')
    }, 10)
    
    return () => clearTimeout(timer)
  }, [router])
  
  return (
    <div className="flex flex-col justify-center items-center h-64 w-full">
      <div className="animate-spin rounded-full h-12 w-12 border-4 border-[#DE2910]/20 border-t-[#DE2910] mb-4"></div>
      <p className="text-gray-600 font-medium">Loading RFQ list...</p>
    </div>
  )
} 