#!/usr/bin/env python3
"""
数据库迁移脚本：将 order_items 表中的 lcsc_id 字段改为 ce_id
适用于 Cloudflare D1 数据库
"""

import requests
import json

# 数据库 API 配置
DB_API_BASE_URL = 'https://api.chinaelectron.com/api'

def execute_sql(sql, params=None):
    """执行 SQL 语句"""
    try:
        response = requests.post(f'{DB_API_BASE_URL}/query/', 
                               headers={'Content-Type': 'application/json'},
                               json={'sql': sql, 'params': params or []})
        
        if not response.ok:
            print(f"SQL 执行失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
        result = response.json()
        print(f"SQL 执行成功: {sql[:50]}...")
        return result
        
    except Exception as e:
        print(f"执行 SQL 时出错: {e}")
        return None

def migrate_order_items_table():
    """迁移 order_items 表"""
    print("开始迁移 order_items 表...")
    
    # 1. 检查当前表结构
    print("\n1. 检查当前表结构...")
    result = execute_sql("SELECT * FROM order_items LIMIT 1")
    if result:
        print(f"当前记录数: {len(result.get('data', []))}")
    
    # 2. 创建新表
    print("\n2. 创建新的 order_items 表...")
    create_table_sql = """
    CREATE TABLE order_items_new (
      item_id INTEGER PRIMARY KEY,
      order_id TEXT,
      user_id TEXT NOT NULL,
      ce_id TEXT,
      mfr_number TEXT,
      manufacturer TEXT,
      description TEXT,
      quantity INTEGER,
      unit_price REAL,
      ext_price REAL
    )
    """
    execute_sql(create_table_sql)
    
    # 3. 复制数据
    print("\n3. 复制现有数据...")
    copy_data_sql = """
    INSERT INTO order_items_new (
      item_id, order_id, user_id, ce_id, mfr_number, 
      manufacturer, description, quantity, unit_price, ext_price
    )
    SELECT 
      item_id, order_id, user_id, lcsc_id, mfr_number, 
      manufacturer, description, quantity, unit_price, ext_price
    FROM order_items
    """
    execute_sql(copy_data_sql)
    
    # 4. 验证数据
    print("\n4. 验证复制的数据...")
    verify_sql = "SELECT COUNT(*) as count FROM order_items_new"
    result = execute_sql(verify_sql)
    if result and result.get('data'):
        count = result['data'][0]['count']
        print(f"新表中的记录数: {count}")
    
    # 5. 删除旧表
    print("\n5. 删除旧表...")
    execute_sql("DROP TABLE order_items")
    
    # 6. 重命名新表
    print("\n6. 重命名新表...")
    execute_sql("ALTER TABLE order_items_new RENAME TO order_items")
    
    # 7. 最终验证
    print("\n7. 最终验证...")
    final_check = execute_sql("SELECT * FROM order_items LIMIT 3")
    if final_check and final_check.get('data'):
        print("迁移完成！示例数据:")
        for row in final_check['data']:
            print(f"  - ce_id: {row.get('ce_id')}, mfr_number: {row.get('mfr_number')}")
    
    print("\n✅ 数据库迁移完成！")

if __name__ == "__main__":
    print("Cloudflare D1 数据库迁移工具")
    print("将 order_items 表中的 lcsc_id 字段改为 ce_id")
    print("=" * 50)
    
    # 确认执行
    confirm = input("确定要执行迁移吗？这将修改数据库结构。(y/N): ")
    if confirm.lower() != 'y':
        print("迁移已取消。")
        exit(0)
    
    migrate_order_items_table()
