{"name": "component", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 7988", "build": "next build", "start": "next start", "lint": "next lint", "update-countries": "node scripts/update-countries-data.js github", "update-countries:restcountries": "node scripts/update-countries-data.js restcountries", "update-countries:help": "node scripts/update-countries-data.js --help", "fix-chinese-encoding": "node scripts/fix-chinese-encoding.js", "convert-countries": "node scripts/convert-country-data.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.8.4", "lucide-react": "^0.468.0", "next": "15.1.6", "ogl": "^1.0.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-pdf": "^9.2.1", "rehype-raw": "^7.0.0", "rehype-stringify": "^10.0.1", "remark": "^15.0.1", "remark-gfm": "^4.0.1", "remark-html": "^16.0.1", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.2", "undici": "^6.21.2", "unified": "^11.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "engines": {"node": ">=18.18.0"}}