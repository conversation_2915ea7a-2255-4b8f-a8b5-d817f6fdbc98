'use client'

import { useRef, useEffect } from 'react'

interface TurnstileProps {
  sitekey: string
  onVerify: (token: string) => void
  onError?: () => void
  onExpire?: () => void
  onLoad?: () => void
  theme?: 'light' | 'dark' | 'auto'
  size?: 'normal' | 'compact'
  className?: string
}

// 声明全局 turnstile 对象类型
declare global {
  interface Window {
    turnstile: any
  }
}

export const Turnstile = ({
  sitekey,
  onVerify,
  onError,
  onExpire,
  onLoad,
  theme = 'auto',
  size = 'normal',
  className = ''
}: TurnstileProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const widgetIdRef = useRef<string | null>(null)

  useEffect(() => {
    // 加载 Turnstile 脚本
    const scriptId = 'cf-turnstile-script'
    if (!document.getElementById(scriptId)) {
      const script = document.createElement('script')
      script.id = scriptId
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js?render=explicit'
      script.async = true
      script.defer = true
      
      script.onload = () => {
        if (onLoad) onLoad()
        renderWidget()
      }
      
      document.head.appendChild(script)
    } else if (window.turnstile) {
      // 如果脚本已加载，直接渲染widget
      renderWidget()
    }

    return () => {
      // 清理组件时移除widget
      if (widgetIdRef.current && window.turnstile) {
        window.turnstile.remove(widgetIdRef.current)
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sitekey, theme, size])

  const renderWidget = () => {
    if (!containerRef.current || !window.turnstile) return

    // 如果已有widget，先移除
    if (widgetIdRef.current) {
      window.turnstile.remove(widgetIdRef.current)
      widgetIdRef.current = null
    }

    // 渲染新widget
    widgetIdRef.current = window.turnstile.render(containerRef.current, {
      sitekey: sitekey,
      callback: (token: string) => {
        if (onVerify) onVerify(token)
      },
      'error-callback': () => {
        if (onError) onError()
      },
      'expired-callback': () => {
        if (onExpire) onExpire()
      },
      theme: theme,
      size: size
    })
  }

  return <div ref={containerRef} className={className} data-testid="cf-turnstile" />
}

export default Turnstile 