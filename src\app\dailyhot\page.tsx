import { Suspense } from 'react'
import ProductTable from '@/components/Search/ProductTable'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const generateMetadata = (): Metadata => {
  return {
    title: 'Daily Hot Products | Trending Electronic Components | China Electron',
    description: 'Discover today\'s most popular and trending electronic components and raw materials. View daily updated hot products on China Electron.',
    keywords: 'daily hot products, trending electronic components, popular components, hot IC chips, China Electron daily trends',
    alternates: {
      canonical: getCanonicalUrl('dailyhot'),
    },
  };
};

// 完整的产品数据接口定义
interface FullProduct {
  product_id: string;
  model: string;
  url?: string; // 添加url字段
  brand_id: string;
  brand: {
    name_cn: string;
    name_en: string;
    name_ru: string;
    logo_url?: string;
    website?: string;
  };
  price_key: string;
  stock_key: string;
  datasheet_url: string;
  image_list: string[];
  parameters_key: string;
  parameters?: {
    english?: {
      param_name: string;
      param_value: string;
    }[];
    chinese?: {
      param_name: string;
      param_value: string;
    }[];
    russian?: {
      param_name: string;
      param_value: string;
    }[];
  };
  description: string;
  updated_at: string;
  prices: { quantity: number; price: number }[];
  stock: number;
  category_path: string[];
  category_id: string;
  category_names: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
}

interface HotProduct {
  product_id: string
  model: string
  type: string
  brand_id: string
  brand_cn: string | null
  brand_en: string | null
  brand_ru: string | null
  domestic: string | null
  created_at: string
  full?: FullProduct
}

interface HotProductsResponse {
  success: boolean
  type: string
  count: number
  latest_date: string
  data: HotProduct[]
}

interface ProductCheckResponse {
  [key: string]: {
    exists: boolean
    product_id: string | null
    brand_id: string | null
    brand_name_cn: string | null
    brand_name_en: string | null
    brand_name_ru: string | null
    domestic: string | null
    full: FullProduct | null
  }
}

// 获取热门产品列表
async function getHotProducts() {
  try {
    const hotResponse = await fetch('https://get-hot.chinaelectron.com/latest?type=hot', {
      cache: 'no-store' // 使用 no-store 替代 revalidate
    })
    
    if (!hotResponse.ok) {
      throw new Error('Failed to fetch hot products')
    }

    const hotData: HotProductsResponse = await hotResponse.json()
    
    if (!hotData.success || !hotData.data || !Array.isArray(hotData.data)) {
      throw new Error('Invalid hot products data format')
    }

    // 检查是否有full字段，如有则直接使用
    const productsWithFullData = hotData.data
      .filter(item => item.full)
      .map(item => {
        // 转换为ProductList期望的格式，保留full字段的所有内容
        const product = item.full as FullProduct;
        // 确保完整的category_path和category_names可用
        return product;
      });

    // 如果有足够的带full字段的数据，直接返回
    if (productsWithFullData.length >= 10) {
      return {
        success: true,
        total: productsWithFullData.length,
        results: productsWithFullData
      };
    }

    // 如果带full字段的数据不足，走原来的流程获取更多详细信息
    // 提取所有 product_ids
    const productIds = hotData.data.map((item: HotProduct) => item.product_id).filter(Boolean)

    // 由于API限制，我们需要将productIds分成每组100个
    const chunks: string[][] = []
    for (let i = 0; i < productIds.length; i += 100) {
      chunks.push(productIds.slice(i, i + 100))
    }

    // 获取每个chunk的详细产品信息
    const productsDetails: FullProduct[] = []
    for (const chunk of chunks) {
      const detailsResponse = await fetch('https://webapi.chinaelectron.com/products/models/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          product_ids: chunk
        }),
        next: { revalidate: 86400 } // 缓存24小时
      })

      if (!detailsResponse.ok) {
        throw new Error('Failed to fetch product details')
      }

      const detailsData: ProductCheckResponse = await detailsResponse.json()
      
      // 处理每个产品的详细信息
      Object.values(detailsData).forEach((product) => {
        if (product.exists && product.full) {
          productsDetails.push(product.full)
        }
      })
    }

    // 合并两种数据源
    const allProducts = [...productsWithFullData, ...productsDetails];
    
    // 去重，以product_id为键
    const uniqueProducts = allProducts.reduce((acc: FullProduct[], current) => {
      const x = acc.find(item => item.product_id === current.product_id);
      if (!x) {
        return acc.concat([current]);
      } else {
        return acc;
      }
    }, []);

    return {
      success: true,
      total: uniqueProducts.length,
      results: uniqueProducts
    }
  } catch (error) {
    console.error('Error fetching hot products:', error)
    return {
      success: false,
      total: 0,
      results: []
    }
  }
}

// 服务端分页导航包装组件
import { DailyHotPagination } from '@/components/Search/DailyHotPagination'

export default async function DailyHotPage({ searchParams }: { searchParams: Promise<{ page?: string }> }) {
  const resolvedSearchParams = await searchParams;
  const currentPage = parseInt(resolvedSearchParams.page || '1', 10);
  const productsData = await getHotProducts();
  
  // 分页处理
  const itemsPerPage = 20;
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedProducts = productsData.results.slice(startIndex, endIndex);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Header Section */}
      <div className="relative bg-gradient-to-r from-[#ef233c] to-[#ff4757] text-white py-12 mb-8 overflow-hidden">
        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0"
               style={{
                 backgroundImage: `radial-gradient(circle at 25% 25%, white 2px, transparent 2px),
                                  radial-gradient(circle at 75% 75%, white 2px, transparent 2px)`,
                 backgroundSize: '60px 60px',
                 animation: 'float 6s ease-in-out infinite'
               }}
          />
        </div>

        {/* Floating particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white/20 rounded-full animate-bounce" style={{animationDelay: '0s', animationDuration: '3s'}}></div>
          <div className="absolute top-1/3 right-1/4 w-1 h-1 bg-white/30 rounded-full animate-bounce" style={{animationDelay: '1s', animationDuration: '4s'}}></div>
          <div className="absolute bottom-1/3 left-1/3 w-1.5 h-1.5 bg-white/25 rounded-full animate-bounce" style={{animationDelay: '2s', animationDuration: '5s'}}></div>
          <div className="absolute top-1/2 right-1/3 w-1 h-1 bg-white/20 rounded-full animate-bounce" style={{animationDelay: '0.5s', animationDuration: '3.5s'}}></div>
        </div>

        <div className="relative max-w mx-auto px-4 sm:px-6 lg:px-14">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center mb-4">
                <div className="bg-white/20 rounded-full p-3 mr-4 animate-pulse hover:animate-spin transition-all duration-300">
                  <svg className="w-8 h-8 text-white transform hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 18.657A8 8 0 016.343 7.343S7 9 9 10c0-2 .5-5 2.986-7C14 5 16.09 5.777 17.656 7.343A7.975 7.975 0 0120 13a7.975 7.975 0 01-2.343 5.657z" />
                  </svg>
                </div>
                <div className="animate-fadeIn">
                  <h1 className="text-4xl md:text-5xl font-bold mb-2 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                    Daily Hot Products
                  </h1>
                  <p className="text-xl text-white/90 animate-slideInUp">
                    Discover today's trending electronic components
                  </p>
                </div>
              </div>
            </div>
            <div className="hidden md:block animate-slideInRight">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 text-center hover:bg-white/20 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                <div className="text-3xl font-bold animate-pulse">{productsData.total}</div>
                <div className="text-sm text-white/80">Hot Products</div>
                <div className="mt-2 w-full bg-white/20 rounded-full h-1">
                  <div className="bg-white h-1 rounded-full animate-pulse" style={{width: '75%'}}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w mx-auto px-4 sm:px-6 lg:px-14 pb-6">

          {/* Products table with client-side pagination wrapper */}
          <Suspense fallback={
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          }>
            <DailyHotPagination 
              products={paginatedProducts} 
              totalResults={productsData.total}
              currentPage={currentPage}
            />
          </Suspense>
        </div>
    </div>
  )
} 