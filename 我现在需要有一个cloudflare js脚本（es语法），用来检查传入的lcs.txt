我现在需要有一个cloudflare js脚本（es语法），用来检查传入的lcsc编码对应的我自己的编码是否在products202503中存在该产品，如果不存在该产品，就调用以下流程获取并增加该产品和相关信息：
1 获取传入的LCSC编码
2 从component_mapping表中查询对应的CE编码，如果查询得到对应的CE编码，则进行以下“补充产品信息”流程，如果不存在对应的编码，则证明数据库中绝对没有该产品，则进行以下“新增产品信息”流程：

2.1 “补充产品信息”流程
2.1.1 拿着获取到的CE编码去products202503表中查询，如果不存在该CE编码，调用以下API，productCode就是LCSC编码，cookie是固定的，不要改：
curl --location 'https://wmsc.lcsc.com/ftps/wm/product/detail?productCode=C14663' \
--header 'Cookie: wmsc_cart_key=90CD1C5752AB78615BC188255B7EFC82CE0346844A0CD5349910DE1EF2DF18691D5BA85C3DFF4502'

返回结果示例：
{
    "code": 200,
    "msg": null,
    "result": {
        "productId": 15331,
        "productCode": "C14663",
        "productModel": "CC0603KRX7R9BB104",
        "productNameEn": null,
        "parentCatalogId": 312,
        "parentCatalogName": "Capacitors",
        "catalogId": 313,
        "catalogName": "Multilayer Ceramic Capacitors MLCC - SMD/SMT",
        "brandId": 396,
        "brandNameEn": "YAGEO",
        "encapStandard": "0603",
        "split": 100,
        "productUnit": "Piece",
        "minPacketUnit": "Reel",
        "minBuyNumber": 100,
        "maxBuyNumber": -1,
        "minPacketNumber": 4000,
        "isEnvironment": true,
        "allHotLevel": null,
        "isHot": true,
        "isPreSale": false,
        "isReel": true,
        "reelPrice": 3.00,
        "stockNumber": 60691400,
        "stockSz": 44289000,
        "stockJs": 16402300,
        "wmStockHk": 100,
        "domesticStockVO": {
            "total": 60691300,
            "shipImmediately": 44289000,
            "ship3Days": 16402300
        },
        "overseasStockVO": {
            "total": 60691400,
            "shipImmediately": 44289100,
            "ship3Days": 16402300
        },
        "productPriceList": [
            {
                "ladder": 100,
                "productPrice": "0.0024",
                "usdPrice": 0.0024,
                "cnyProductPriceList": null,
                "discountRate": "1",
                "currencyPrice": 0.0024,
                "currencySymbol": "$",
                "isForeignDiscount": null,
                "ladderLevel": null
            },
            {
                "ladder": 1000,
                "productPrice": "0.0018",
                "usdPrice": 0.0018,
                "cnyProductPriceList": null,
                "discountRate": "1",
                "currencyPrice": 0.0018,
                "currencySymbol": "$",
                "isForeignDiscount": null,
                "ladderLevel": null
            },
            {
                "ladder": 4000,
                "productPrice": "0.0015",
                "usdPrice": 0.0015,
                "cnyProductPriceList": null,
                "discountRate": "1",
                "currencyPrice": 0.0015,
                "currencySymbol": "$",
                "isForeignDiscount": null,
                "ladderLevel": null
            },
            {
                "ladder": 8000,
                "productPrice": "0.0013",
                "usdPrice": 0.0013,
                "cnyProductPriceList": null,
                "discountRate": "1",
                "currencyPrice": 0.0013,
                "currencySymbol": "$",
                "isForeignDiscount": null,
                "ladderLevel": null
            },
            {
                "ladder": 48000,
                "productPrice": "0.0012",
                "usdPrice": 0.0012,
                "cnyProductPriceList": null,
                "discountRate": "1",
                "currencyPrice": 0.0012,
                "currencySymbol": "$",
                "isForeignDiscount": null,
                "ladderLevel": null
            }
        ],
        "productImages": [
            "https://assets.lcsc.com/images/lcsc/900x900/20221228_YAGEO-CC0603KRX7R9BB104_C14663_front.jpg",
            "https://assets.lcsc.com/images/lcsc/900x900/20221228_YAGEO-CC0603KRX7R9BB104_C14663_back.jpg",
            "https://assets.lcsc.com/images/lcsc/900x900/20221228_YAGEO-CC0603KRX7R9BB104_C14663_blank.jpg"
        ],
        "pdfUrl": "https://datasheet.lcsc.com/lcsc/2506131735_YAGEO-CC0603KRX7R9BB104_C14663.pdf",
        "productDescEn": null,
        "productIntroEn": "50V 100nF X7R ±10% 0603 Multilayer Ceramic Capacitors MLCC - SMD/SMT ROHS",
        "paramVOList": [
            {
                "paramCode": "param_10953_n",
                "paramName": "额定电压",
                "paramNameEn": "Voltage Rating",
                "paramValue": "50V",
                "paramValueEn": "50V",
                "paramValueEnForSearch": 50.0,
                "isMain": true,
                "sortNumber": 1,
                "sort": 3
            },
            {
                "paramCode": "param_10951_n",
                "paramName": "容值",
                "paramNameEn": "Capacitance",
                "paramValue": "100nF",
                "paramValueEn": "100nF",
                "paramValueEnForSearch": 100000.0,
                "isMain": true,
                "sortNumber": 1,
                "sort": 1
            },
            {
                "paramCode": "param_10954",
                "paramName": "温度系数",
                "paramNameEn": "Temperature Coefficient",
                "paramValue": "X7R",
                "paramValueEn": "X7R",
                "paramValueEnForSearch": -1.0,
                "isMain": true,
                "sortNumber": 1,
                "sort": 5
            },
            {
                "paramCode": "param_10952_s",
                "paramName": "精度",
                "paramNameEn": "Tolerance",
                "paramValue": "±10%",
                "paramValueEn": "±10%",
                "paramValueEnForSearch": null,
                "isMain": true,
                "sortNumber": 1,
                "sort": 2
            }
        ],
        "productArrange": "Tape & Reel (TR)",
        "productWeight": 0.000039068,
        "foreignWeight": 0.000037000,
        "isForeignOnsale": true,
        "isNotOverstock": false,
        "productCycle": "normal",
        "isHasBattery": false,
        "pdfLinkUrl": null,
        "productLadderPrice": null,
        "ladderDiscountRate": null,
        "eccn": "EAR99",
        "currencyType": "USD",
        "currencySymbol": "$",
        "title": "YAGEO CC0603KRX7R9BB104",
        "weight": 0.037,
        "hasThirdPartyStock": true,
        "flashSaleProductPO": null,
        "htsMap": {
            "BR": "85322410",
            "TARIC": "8532240000",
            "IN": "85322400",
            "CN": "8532241000",
            "MX": "8532.24.01",
            "CA": "8532240090",
            "US": "8532240020"
        },
        "isAsianBrand": true
    }
}

2.1.2 数据对应以下数据表字段处理：
产品
- **products202503** - 主产品表   
  - `product_id` (TEXT, PK) - 产品ID：如果component_mapping表中有，则用component_mapping表中的CE编码，如果没有，则创建一个编码，编码规则为CMA+9位数字递增，需要在现有的最大编码基础上+1
  - `model` (TEXT) - 产品型号：对应返回结果的productModel
  - `brand_id` (TEXT) - 品牌ID：拿着brandId到表brand_mapping中查找对应的ce_brand_id，如果没有，则创建一个brand，编码规则为CEB+6位数字递增，需要在现有的最大编码基础上+1
  - `price_key` (TEXT) - 价格键：等于product_id
  - `stock_key` (TEXT) - 库存键：等于product_id
  - `datasheet_url` (TEXT) - 数据表URL：对应返回结果的pdfUrl
  - `image_list` (TEXT) - 图片列表：暂时用“[]”表示空
  - `parameters_key` (TEXT) - 参数键：等于product_id
  - `description` (TEXT) - 产品描述：对应返回结果的productIntroEn
  - `updated_at` (TEXT) - 更新时间
  - `rohs` (INTEGER) - RoHS认证：对应返回结果的isRoHS
  - `summarizer` (TEXT) - 摘要

品牌：只在brand_mapping表中没有的话才需要创建
- **brands** - 品牌表
  - `id` (TEXT, PK) - 品牌ID：编码规则为CEB+6位数字递增，需要在现有的最大编码基础上+1
  - `name_cn` (TEXT) - 中文名称：暂时为空
  - `name_en` (TEXT) - 英文名称：对应返回结果的brandNameEn
  - `name_ru` (TEXT) - 俄文名称：暂时为空
  - `introduction_cn` (TEXT) - 中文介绍：暂时为空
  - `introduction_en` (TEXT) - 英文介绍：暂时为空
  - `introduction_ru` (TEXT) - 俄文介绍：暂时为空
  - `website` (TEXT) - 官网：暂时为空
  - `logo_url` (TEXT) - Logo URL：暂时为空
  - `domestic` (TEXT) - 是否国产：对应返回结果的isAsianBrand，为true则为国产，写入数据库的值为chinese，false则为非国产，写入数据库的值为international
  - `area` (TEXT) - 地区：暂时为空
品类
- **product_category_map** - 产品分类映射表
  - `product_code` (TEXT, PK) - 产品代码：等于product_id
  - `category_code` (TEXT) - 分类代码：拿着catalogId到category_mapping表中查找对应的ce_category_id，如果没有则为CMC0016015001
  - `created_at` (TEXT) - 创建时间
价格
- **price** - 价格表，一个阶梯价格一行数据，多个阶梯价格多行数据
  - `id` (INTEGER, PK) - 价格ID：自增
  - `code` (TEXT) - 产品代码 (对应product_id)：等于product_id
  - `quantity` (INTEGER) - 数量：productPriceList的ladder
  - `price` (REAL) - 价格：productPriceList的usdPrice
  - `created_at` (TEXT) - 创建时间
库存
- **stocks** - 库存表
  - `code` (TEXT, PK) - 产品代码 (对应product_id)：等于product_id
  - `stocks` (INTEGER) - 库存数量：对应返回结果的stockNumber
  - `created_at` (TEXT) - 创建时间
参数
- **parameters** - 参数表，一个参数一行数据，多个参数多行数据
  - `id` (INTEGER, PK) - 参数ID：自增
  - `code` (TEXT) - 产品代码：等于product_id
  - `languages` (TEXT) - 语言：如果参数名称的key为paramNameEn，则为english，如果参数名称的key为paramName，则为chinese
  - `param_name` (TEXT) - 参数名称：paramVOList的paramName或paramNameEn
  - `param_value` (TEXT) - 参数值：paramVOList的paramValue或paramValueEn
  - `created_at` (TEXT) - 创建时间

产品编码映射表，只在component_mapping中找不到对应的ce_code时才需要创建
- **component_mapping** - 产品编码映射表
  - `id` (INTEGER, PK) - 映射ID：自增
  - `lcsc_code` (TEXT, NOT NULL) - LCSC编码：返回结果的productCode
  - `ce_code` (TEXT, NOT NULL) - CE编码：等于products202503的product_id
  - `created_at` (DATETIME) - 创建时间
  - `updated_at` (DATETIME) - 更新时间