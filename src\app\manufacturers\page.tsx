import { Suspense } from 'react'
import ManufacturersLayout from './ManufacturersLayout'
import { fetchAllBrands } from './fetchBrands'
import ErrorDisplay from './ErrorDisplay'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import type { Metadata } from 'next'

// 定义searchParams类型
type SearchParams = {
  page?: string;
  limit?: string;
}

// Generate metadata for the server component
export async function generateMetadata(): Promise<Metadata> {
  return {
    title: 'Electronic Component Manufacturers Directory | China Electron',
    description: 'Browse our directory of leading electronic component raw material manufacturers on China Electron. Connect with trusted suppliers for your B2B sourcing needs.',
    keywords: 'manufacturer directory, electronic component manufacturers, raw material suppliers, B2B sourcing, China Electron, IC chip manufacturers',
    alternates: {
      canonical: getCanonicalUrl('manufacturers'),
    }
  }
}

// Default page - All manufacturers
export default async function ManufacturersPage({
  searchParams
}: {
  searchParams?: Promise<SearchParams>
}) {
  // 从URL查询参数获取页码，默认为第1页
  const resolvedSearchParams = await searchParams || {};
  const page = resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1;
  // 每页显示数量，默认为20
  const limit = resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit) : 20;
  
  // 获取分页数据
  const { brands, total, currentPage, totalPages, error } = await fetchAllBrands(page, limit);
  
  /* console.log('ManufacturersPage data:', { 
    brandsLength: brands.length, 
    total, 
    currentPage, 
    totalPages, 
    hasError: !!error 
  }); */
  
  if (error) {
    return <ErrorDisplay error={error} />;
  }
  
  return (
    <ManufacturersLayout 
      brands={brands} 
      filterType="all" 
      total={total}
      currentPage={currentPage}
      totalPages={totalPages}
    />
  );
} 