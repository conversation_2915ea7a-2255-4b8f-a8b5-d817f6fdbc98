import { NextRequest, NextResponse } from 'next/server'
import { config } from '@/lib/config'

/**
 * 验证 Turnstile 令牌的 API 路由
 * @param request 请求对象
 * @returns 验证结果响应
 */
export async function POST(request: NextRequest) {
  try {
    // 从请求中获取 token
    const requestData = await request.json()
    const { token } = requestData

    if (!token) {
      return NextResponse.json(
        { success: false, message: 'Token is required' },
        { status: 400 }
      )
    }

    // 验证 token
    const formData = new FormData()
    formData.append('secret', config.turnstile.secretKey)
    formData.append('response', token)
    // 可选地添加客户端 IP 地址
    const clientIP = request.headers.get('cf-connecting-ip') || request.headers.get('x-forwarded-for') || '127.0.0.1'
    formData.append('remoteip', clientIP)

    // 发送请求到 Cloudflare
    const verifyResponse = await fetch(
      'https://challenges.cloudflare.com/turnstile/v0/siteverify',
      {
        method: 'POST',
        body: formData,
      }
    )

    const verifyData = await verifyResponse.json()

    // 返回验证结果
    if (verifyData.success) {
      return NextResponse.json(
        { success: true, data: verifyData },
        { status: 200 }
      )
    } else {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Verification failed', 
          errors: verifyData['error-codes'] 
        },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Turnstile verification error:', error)
    return NextResponse.json(
      { 
        success: false, 
        message: 'Internal server error',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
} 