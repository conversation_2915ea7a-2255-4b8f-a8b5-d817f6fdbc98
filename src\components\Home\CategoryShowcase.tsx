import Image from 'next/image'
import Link from 'next/link'

interface CategoryShowcaseProps {
  items: Array<{
    id: string;
    name: string;
    image: string;
    description: string;
    count: number;
  }>;
  type: 'category' | 'distributor' | 'manufacturer';
  className?: string;
}

export default function CategoryShowcase({ items, type, className = '' }: CategoryShowcaseProps) {
  return (
    <div className={className}>
      {items.map((item) => (
        <div 
          key={item.id}
          className="group relative bg-gray-50 hover:bg-white rounded-lg transition-all duration-300"
        >
          <div className="aspect-w-1 aspect-h-1 bg-white rounded-lg overflow-hidden">
            <img
              src={item.image}
              alt={item.name}
              className="object-contain w-full h-full p-2 group-hover:scale-110 transition-transform duration-300"
            />
          </div>
          
          <div className="p-3 text-center">
            <h3 className="text-sm font-medium text-gray-900 truncate">
              {item.name}
            </h3>
            {type !== 'category' && (
              <p className="text-xs text-gray-500 mt-1 truncate">
                {item.description}
              </p>
            )}
            {/* <div className="mt-2">
              <span className="text-xs text-gray-500">
                {item.count.toLocaleString()} {type === 'category' ? 'items' : 'products'}
              </span>
            </div> */}
          </div>
        </div>
      ))}
    </div>
  );
} 