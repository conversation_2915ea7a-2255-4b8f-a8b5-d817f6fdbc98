import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const metadata: Metadata = {
  title: 'Sign In | China Electron',
  description: 'Sign in to your China Electron account to access electronic component sourcing, quotes, and supplier connections.',
  keywords: 'login, sign in, China Electron account, electronic component platform login',
  alternates: {
    canonical: getCanonicalUrl('login'),
  },
}

export default function LoginLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
} 