/**
 * Spotlight Article Detail Page
 * 
 * Displays detailed view of a spotlight article by ID
 * This page is server-side rendered for better SEO performance
 */

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { fetchBlogArticleById, fetchArticleContent, fetchBlogArticles } from '@/services/api'
import type { BlogArticle } from '@/services/api'
import { Metadata, ResolvingMetadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

// 安全的alt文本处理，确保只含ASCII字符
function safeAltText(text: string): string {
  // 移除非ASCII字符，确保客户端和服务器端渲染一致
  return text.replace(/[^\x00-\x7F]/g, '') || 'Image';
}

// 从URL中提取文章ID
function extractArticleIdFromUrl(urlPath: string): number {
  try {
    // 分割路径并获取最后一部分作为ID
    const parts = urlPath.split('-');
    
    // 如果最后一部分是数字，则使用它作为ID
    if (parts.length > 0) {
      const lastPart = parts[parts.length - 1];
      if (/^\d+$/.test(lastPart)) {
        return parseInt(lastPart, 10);
      }
    }
    
    // 如果不是新格式，尝试直接解析整个路径作为ID
    const directId = parseInt(urlPath, 10);
    if (!isNaN(directId)) {
      return directId;
    }
    
    // 如果无法解析，返回默认值或抛出错误
    throw new Error(`无法从URL路径提取文章ID: ${urlPath}`);
  } catch (error) {
    console.error('解析文章ID时出错:', error);
    // 返回一个不存在的ID，这样页面会显示404
    return -1;
  }
}

// 将标题转换为URL友好的格式
function slugifyTitle(title: string): string {
  // 将所有符号和空格替换为连字符，并将连续的连字符替换为单个连字符
  return title
    .replace(/[^\w\s]/g, '-') // 将所有非字母数字字符替换为连字符
    .replace(/\s+/g, '-')     // 将空格替换为连字符
    .replace(/-+/g, '-')      // 将连续的连字符替换为单个连字符
    .replace(/^-|-$/g, '')    // 移除开头和结尾的连字符
    .toLowerCase();           // 转为小写
}

// 转换文章数据为UI格式
function transformArticleData(article: BlogArticle) {
  return {
    id: article.id,
    title: article.title,
    excerpt: article.description,
    image: article.cover_image,
    content: '', // 内容需要单独获取
    author: {
      name: article.author?.name || 'Unknown Author',
      avatar: article.author?.avatar || '',
      bio: article.author?.bio || ''
    },
    date: new Date(article.created_at).toISOString().split('T')[0],
    tags: article.tags ? article.tags.split(',') : [],
    category: article.category,
    contentUrl: article.content_markdown
  }
}

// 获取文章详情
async function getArticleById(id: number) {
  try {
    const article = await fetchBlogArticleById(id)
    if (!article) return null
    
    // 转换数据
    const transformedArticle = transformArticleData(article)
    
    // 获取Markdown内容
    if (transformedArticle.contentUrl) {
      // 不需要try/catch，因为fetchArticleContent内部已经处理了错误
      const content = await fetchArticleContent(transformedArticle.contentUrl)
      transformedArticle.content = content
    } else {
      // 如果没有内容URL，使用简单的占位内容
      transformedArticle.content = '<p>No content available for this article.</p>'
    }
    
    return transformedArticle
  } catch (error) {
    // console.log(`Unable to fetch article with ID ${id}, using fallback content`)
    
    // 创建一个后备文章以确保页面能够渲染
    return {
      id: id,
      title: 'Article Temporarily Unavailable',
      excerpt: 'We\'re having trouble loading this article. Please try again later.',
      image: '',
      content: `
        <div class="p-4 border border-yellow-200 bg-yellow-50 rounded-md mb-4">
          <p class="text-yellow-700">
            <strong>Note:</strong> We encountered an error while trying to load this article.
            This is placeholder content for demonstration purposes.
          </p>
        </div>
        <p>We apologize for the inconvenience. The article you're looking for is temporarily unavailable.</p>
        <p>Please try again later or explore other articles in our collection.</p>
      `,
      author: {
        name: 'Spotlight Team',
        avatar: '',
        bio: 'Content creators and curators.'
      },
      date: new Date().toISOString().split('T')[0],
      tags: ['Unavailable'],
      category: 'Uncategorized',
      contentUrl: '' // 添加缺少的contentUrl属性
    }
  }
}

// 获取相关文章
async function getRelatedArticles(article: ReturnType<typeof transformArticleData>) {
  try {
    // 根据相同分类获取相关文章
    const response = await fetchBlogArticles(1, 3, article.category)
    
    // 排除当前文章
    const related = response.data
      .filter(a => a.id !== article.id)
      .map(transformArticleData)
      .slice(0, 3)
    
    // 如果相关文章不足3篇，用最新文章补充
    if (related.length < 3) {
      try {
        const latestResponse = await fetchBlogArticles(1, 3 - related.length)
        const latestArticles = latestResponse.data
          .filter(a => a.id !== article.id && !related.some(r => r.id === a.id))
          .map(transformArticleData)
        
        return [...related, ...latestArticles]
      } catch {
        // 如果获取最新文章失败，只返回已有的相关文章
        return related
      }
    }
    
    return related
  } catch {
    // 返回空数组而不是抛出错误，确保页面能够渲染
    return []
  }
}

// 生成页面元数据
export async function generateMetadata(
  props: { params: Promise<{ id: string }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  // 获取异步参数
  const params = await props.params;
  
  // 获取文章
  const urlPath = params.id;
  const articleId = extractArticleIdFromUrl(urlPath);
  const article = await getArticleById(articleId)
  
  if (!article) {
    return {
      title: 'Article Not Found'
    }
  }
  
  // 获取关键词
  const keywords = article.tags && article.tags.length > 0 
    ? `${article.tags.join(', ')}, electronics news, industry updates, China Electron`
    : 'electronics news, industry updates, China Electron';
  
  return {
    title: `${article.title} | China Electron`,
    description: `${article.excerpt}. Get the full story on China Electron.`,
    keywords: keywords,
    openGraph: {
      title: `${article.title} | China Electron`,
      description: article.excerpt,
      images: article.image ? [article.image] : [],
      type: 'article',
      authors: [article.author.name],
      publishedTime: article.date
    },
    alternates: {
      canonical: getCanonicalUrl(`spotlight/${params.id}`),
    },
  }
}

export default async function SpotlightArticlePage(props: { params: Promise<{ id: string }> }) {
  // 获取异步参数
  const params = await props.params;
  
  // 解析ID参数
  const urlPath = params.id;
  const articleId = extractArticleIdFromUrl(urlPath);
  
  // 获取文章
  const article = await getArticleById(articleId)
  
  // 如果文章不存在，显示404页面
  if (!article) {
    notFound()
  }
  
  // 判断是否是后备内容（错误情况下的内容）
  const isPlaceholder = article.title === 'Article Temporarily Unavailable'
  
  // 获取相关文章
  const relatedArticles = await getRelatedArticles(article)

  return (
    <div className="min-h-screen bg-white">
      {/* 错误通知栏 - 仅在使用占位符内容时显示 */}
      {isPlaceholder && (
        <div className="bg-yellow-50 border-b border-yellow-100 py-3">
          <div className="container mx-auto px-14">
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <p className="text-sm text-yellow-700">
                We're experiencing some issues loading this article. Showing placeholder content instead.
              </p>
            </div>
          </div>
        </div>
      )}
      
      {/* Article header */}
      <div className="relative h-[400px] overflow-hidden bg-gray-800">
        {/* Header background image */}
        <div className="absolute inset-0">
          <Image 
            src={article.image || `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1920' height='600' viewBox='0 0 1920 600'%3E%3Crect width='1920' height='600' fill='%23333333'/%3E%3C/svg%3E`}
            alt={safeAltText(article.title)}
            width={1920}
            height={600}
            className="w-full h-full object-cover"
            unoptimized
            priority
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/30"></div>
        </div>
        
        {/* Article title and meta */}
        <div className="absolute inset-0 flex flex-col justify-end px-14 pb-12">
          <div className="max-w-5xl mx-auto text-center w-full">
            {/* Category */}
            <div className="inline-block bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded mb-4">
              {article.category}
            </div>
            
            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 mx-auto">
              {article.title}
            </h1>
            
            {/* Author and date */}
            <div className="flex items-center justify-center">
              {/* Author avatar */}
              <div className="w-12 h-12 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                {article.author.avatar ? (
                  <Image 
                    src={article.author.avatar}
                    alt={safeAltText(article.author.name) || "Author"}
                    width={48}
                    height={48}
                    className="w-full h-full object-cover"
                    unoptimized
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-sm text-gray-500">
                    {article.author.name.charAt(0)}
                  </div>
                )}
              </div>
              <div className="ml-3">
                <div className="text-white font-medium">{article.author.name}</div>
                <div className="text-gray-300 text-sm">{article.date}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mx-auto px-14 py-12">
        <div className="max-w-5xl mx-auto">
          
          {/* Article tags */}
          <div className="mb-8 flex flex-wrap gap-2">
            {article.tags.map((tag, index) => (
              <span key={index} className="inline-block bg-gray-100 text-gray-800 px-3 py-1.5 rounded-full text-sm">
                {tag}
              </span>
            ))}
          </div>
          
          {/* Article content */}
          <div className="prose prose-lg max-w-none mb-12" dangerouslySetInnerHTML={{ __html: article.content }}></div>
          
          {/* Author bio */}
          <div className="border-t border-b border-gray-200 py-6 my-12">
            <div className="flex items-start gap-4">
              {/* Author avatar */}
              <div className="w-16 h-16 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                {article.author.avatar ? (
                  <Image 
                    src={article.author.avatar}
                    alt={safeAltText(article.author.name) || "Author"}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                    unoptimized
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-lg text-gray-500">
                    {article.author.name.charAt(0)}
                  </div>
                )}
              </div>
              <div>
                <div className="text-xl font-bold mb-2">{article.author.name}</div>
                <p className="text-gray-600">{article.author.bio}</p>
              </div>
            </div>
          </div>
          
          {/* Related articles */}
          {relatedArticles.length > 0 && (
            <div className="my-12">
              <h2 className="text-2xl font-bold mb-6">Related Articles</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {relatedArticles.map((relatedArticle) => (
                  <Link 
                    key={relatedArticle.id} 
                    href={`/spotlight/${encodeURIComponent(slugifyTitle(relatedArticle.title))}-${relatedArticle.id}`}
                    className="block group"
                  >
                    <div className="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden h-full">
                      {/* Article image */}
                      <div className="h-40 relative bg-gray-200">
                        <Image 
                          src={relatedArticle.image || `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='600' height='400' viewBox='0 0 600 400'%3E%3Crect width='600' height='400' fill='%23333333'/%3E%3C/svg%3E`}
                          alt={safeAltText(relatedArticle.title)}
                          width={600}
                          height={400}
                          className="w-full h-full object-cover"
                          unoptimized
                        />
                      </div>
                      
                      {/* Article details */}
                      <div className="p-4">
                        <h3 className="text-lg font-bold mb-2 text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
                          {relatedArticle.title}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {relatedArticle.excerpt}
                        </p>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 