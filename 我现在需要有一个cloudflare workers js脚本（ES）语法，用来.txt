我现在需要有一个cloudflare workers js脚本（ES）语法，用来处理BOM配单业务，所需API和流程如下：
1、需要有个API给用户上传Excel表，该API需要调用以下API来上传文件到R2存储桶：

### 2. 上传文件

上传一个新的BOM文件。

**请求**:
```
POST /[可选路径]
```

**CURL示例**:
```bash
curl -X POST "https://bom-files.962692556.workers.dev/" \
  -H "Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" \
  -H "Content-Disposition: attachment; filename=\"my_bom_file.xlsx\"" \
  --data-binary @/path/to/local/bom_file.xlsx
```

**响应示例**:
```json
{
  "success": true,
  "filePath": "bom_1686123456789_a1b2c3d4.xlsx",
  "url": "https://bom-files.962692556.workers.dev/bom_1686123456789_a1b2c3d4.xlsx"
}
```

2、然后创建BOM项目，将数据存入bom_main表中
- **bom_main** - BOM主记录表
  - `bom_id` (INTEGER, PK) - BOM ID (自增主键)
  - `bom_name` (TEXT, NOT NULL) - BOM名称
  - `status` (INTEGER) - 状态 (默认0)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `file_url` (TEXT) - 文件URL
  - `item_count` (INTEGER) - 项目总数 (默认0)
  - `success_count` (INTEGER) - 成功匹配数 (默认0)
  - `exact_match_count` (INTEGER) - 精确匹配数 (默认0)
  - `partial_match_count` (INTEGER) - 部分匹配数 (默认0)
  - `no_match_count` (INTEGER) - 无匹配数 (默认0)
  - `est_total_price` (REAL) - 预估总价 (默认0)
  - `each_price` (REAL) - 单价 (默认0)
  - `priority_matching` (INTEGER) - 优先匹配 (默认0)
  - `quantity_multiplier` (INTEGER) - 数量倍数 (默认1)
  - `create_at` (DATETIME) - 创建时间 (默认当前时间)
  - `update_at` (DATETIME) - 更新时间 (默认当前时间)




3、需要有个API用来读取上传的Excel的列数据（返回前10行）来给用户确认每一列代表什么意思，只需要返回数据就行，其他的是另外的API。


4、需要有个API用来调用以下API将文件和传入的form-data上传到LCSC服务器，获取匹配结果：
startLine、selection、priorityMatch、coefficientTotal这几个参数是给到用户在前端选择后传入的，不要写死，file是之前上传的，需要根据bom_id来获取对应的file_url，然后下载后上传上去


# LCSC BOM API 集成文档

## API概述

此API用于与LCSC（立创商城）的BOM（物料清单）匹配服务集成，提供文件上传、BOM匹配和获取详细结果的功能。

## 基本信息

- **基础URL**: `https://bom.962692556.workers.dev`
- **认证方式**: LCSC Cookie认证

## API端点

### BOM文件处理

处理BOM文件并获取匹配结果。

**请求**:
```
POST /
```

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | 是 | BOM文件（Excel或CSV格式） |
| startLine | Number | 否 | 开始解析的行号（默认：2） |
| selection | JSON Array | 否 | 列映射配置（默认：`[[0,1],[1,4],[2,0],[3,5],[4,0],[5,0],[6,0]]`） |
| priorityMatch | String | 否 | 优先匹配策略（默认：`in_stock`） |
| coefficientTotal | Number | 否 | 数量系数（默认：1） |
| cookie | String | 否 | LCSC网站的Cookie（如未提供则使用默认值） |

**selection参数说明**:
二维数组，每个子数组表示一个映射：`[LCSC字段索引, 用户文件列索引]`
- 0: 物料编号/型号
- 1: 数量
- 2: 参考价格
- 3: 品牌
- 4: 封装
- 5: 描述
- 6: 备注

**CURL示例**:
```bash
curl -X POST "https://bom.962692556.workers.dev/" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/bom.xlsx" \
  -F "startLine=2" \
  -F "selection=[[0,1],[1,4],[2,0],[3,5],[4,0],[5,0],[6,0]]" \
  -F "priorityMatch=in_stock" \
  -F "coefficientTotal=1" \
  -F "cookie=wmsc_cart_key=5BFC271AC91EE8707B9C39A08F0A22EAB9089FEE2B0EF750F86CD5F08EE942A905C4310741CC19D3"
```

**响应示例**:
```json
{
    "success": true,
    "data": {
        "uploadUuid": "D749A94FB80FE887EDB83810F16923A1",
        "matchUuid": "01F81CF56DABCED7CE54CF20771DB678",
        "bomDetails": {
            "bomId": 161729,
            "uuid": "01F81CF56DABCED7CE54CF20771DB678",
            "customerCode": null,
            "bomName": "LCSC_BomTemplate.csv",
            "coefficientTotal": 1,
            "currencyCode": "USD",
            "currencySymbol": "$",
            "currencyRateUsd": 1,
            "totalAmount": null,
            "singleAmount": null,
            "isBom": null,
            "projectIndexVO": [
                {
                    "bomId": 161729,
                    "coefficientTotal": 1,
                    "bomProductId": 5537360,
                    "bomProductUuid": "CA90B463E97BF39DC1009527AD34502F",
                    "productId": 44466188,
                    "productCode": "C42433400"
                    "productDetail": {
                        "productId": 44466188,
                        "productCode": "C42433400",
                        "productWeight": 0.00005,
                        "foreignWeight": null,
                        "weight": 0.05,
                        "dollarLadderPrice": null,
                        "isForeignOnsale": true,
                        "minBuyNumber": 50,
                        "maxBuyNumber": -1
                        "productPriceList": [
                            {
                                "ladder": 50,
                                "productPrice": "0.0078",
                                "usdPrice": 0.0078,
                                "cnyProductPriceList": [
                                    {
                                        "ladder": 50,
                                        "productPrice": "0.0513",
                                        "usdPrice": 0.0513,
                                        "cnyProductPriceList": null,
                                        "discountRate": "1",
                                        "currencyPrice": null,
                                        "currencySymbol": null,
                                        "isForeignDiscount": null,
                                        "ladderLevel": null
                                    }
                                ],
                                "discountRate": "1",
                                "currencyPrice": 0.0078,
                                "currencySymbol": "$",
                                "isForeignDiscount": null,
                                "ladderLevel": 1
                            }
                        ],
                        "stockJs": 0,
                        "stockSz": 7150,
                        "smtAloneNumberSz": null,
                        "smtAloneNumberJs": null,
                        "wmStockHk": 0,
                        "domesticStockVO": {
                            "total": 7150,
                            "shipImmediately": 7150,
                            "ship3Days": 0
                        },
                        "overseasStockVO": {
                            "total": 7150,
                            "shipImmediately": 7150,
                            "ship3Days": 0
                        },
                        "stockNumber": 7150,
                        "split": 50,
                        "productImageUrl": "https://assets.lcsc.com/images/lcsc/96x96/20250115_JUXING-P7_C42433400_front.jpg"
                        "productImages": [
                            "https://assets.lcsc.com/images/lcsc/96x96/20250115_JUXING-P7_C42433400_front.jpg"
                        ],
                        "paramVOList": [
                            {
                                "paramCode": "param_10920_n",
                                "paramName": "非重复峰值浪涌电流 (Ifsm)",
                                "paramNameEn": "Non-Repetitive Peak Forward Surge Current",
                                "paramValue": "9A",
                                "paramValueEn": "9A",
                                "paramValueEnForSearch": 9,
                                "isMain": true,
                                "sortNumber": 1,
                                "sort": 5
                            }
                        ],
                        "isReel": false,
                        "reelPrice": 0,
                        "productModelHighlight": null,
                        "productCodeHighlight": null,
                        "encapStandardHighlight": null,
                        "productArrangeHighlight": null
                    },
                    "priceVO": {
                        "split": 50,
                        "weight": 0.05,
                        "discountRate": 1,
                        "price": 0.0078,
                        "totalPrice": 0.39,
                        "realPrice": 0.0078,
                        "realTotalPrice": 0.39,
                        "discountOffPrice": 0
                    },
                    "matchValueMap": {
                        "Mfr.#": "R3000FGP-TP",
                        "Description": "Micro Commercial Components (MCC) Rectifiers 0.2A 3000V 2100V 3000V 200mA 20A / R3000FGP-TP"
                    },
                    "matchScore": "Partial Matches",
                    "matchValue": "{\"Mfr.#\":\"R3000FGP-TP\",\"Description\":\"Micro Commercial Components (MCC) Rectifiers 0.2A 3000V 2100V 3000V 200mA 20A / R3000FGP-TP\"}",
                    "projectIndex": 1,
                    "targetPrice": null,
                    "targetDay": null,

                },
                {
                    "bomId": 161729,
                    "coefficientTotal": 1,
                    "bomProductId": 5537371,
                    "bomProductUuid": "E8DD0FCBAD55B9B0DF3DEB87BD2ECBF0",
                    "productId": 544697,
                    "productCode": "C526482",
                    "realPrice": 0.0634,
                    "realTotalPrice": 1.27,
                    "productDetail": {
                        "productId": 544697,
                        "productCode": "C526482",
                        "productWeight": 0.000041,
                        "foreignWeight": 0,
                        "weight": 0.041,
                        "dollarLadderPrice": null
                        "productPriceList": [
                            {
                                "ladder": 5,
                                "productPrice": "0.0634",
                                "usdPrice": 0.0634,
                                "cnyProductPriceList": [
                                    {
                                        "ladder": 5,
                                        "productPrice": "0.4122",
                                        "usdPrice": 0.4122,
                                        "cnyProductPriceList": null,
                                        "discountRate": "1",
                                        "currencyPrice": null,
                                        "currencySymbol": null,
                                        "isForeignDiscount": null,
                                        "ladderLevel": null
                                    }
                                ],
                                "discountRate": "1",
                                "currencyPrice": 0.0634,
                                "currencySymbol": "$",
                                "isForeignDiscount": null,
                                "ladderLevel": 1
                            }
                        ],
                        "stockJs": 0,
                        "stockSz": 325,
                        "smtAloneNumberSz": null,
                        "smtAloneNumberJs": null,
                        "wmStockHk": 0,
                        "domesticStockVO": {
                            "total": 325,
                            "shipImmediately": 325,
                            "ship3Days": 0
                        },
                        "overseasStockVO": {
                            "total": 325,
                            "shipImmediately": 325,
                            "ship3Days": 0
                        },
                        "stockNumber": 325,
                        "split": 5,
                        "productImageUrl": "https://assets.lcsc.com/images/lcsc/96x96/20230203_Diodes-Incorporated-BSS138-13-F_C526482_front.jpg"
                        "productImages": [
                            "https://assets.lcsc.com/images/lcsc/96x96/20230203_Diodes-Incorporated-BSS138-13-F_C526482_front.jpg"
                        ],
                        "paramVOList": [
                            {
                                "paramCode": "param_11380_n",
                                "paramName": "漏源电压(Vdss)",
                                "paramNameEn": "Drain to Source Voltage",
                                "paramValue": "50V",
                                "paramValueEn": "50V",
                                "paramValueEnForSearch": 50,
                                "isMain": true,
                                "sortNumber": 1,
                                "sort": 2
                            }
                        ],
                        "isReel": true,
                        "reelPrice": 3,
                    },
                    "priceVO": {
                        "split": 5,
                        "weight": 0.041,
                        "discountRate": 1,
                        "price": 0.0634,
                    },
                    "matchValueMap": {
                        "Mfr.#": "BSS138-13-F",
                        "Description": "Diodes Incorporated MOSFET BSS Family / BSS138-13-F"
                    },
                    "matchScore": "Exact Matches",
                    "matchValue": "{\"Mfr.#\":\"BSS138-13-F\",\"Description\":\"Diodes Incorporated MOSFET BSS Family / BSS138-13-F\"}",
                    "projectIndex": 12,
                    "targetPrice": null,
                    "targetDay": null,
                }
            ],
            "createTime": "2025-07-02 16:49:43",
            "waitMatchNumber": 0,
            "priorityMatch": "in_stock",
            "quoteOrderCodeList": null,
            "allowOutStock": null
        }
    },
    "message": "BOM processing completed successfully"
}
```

5、将获取到的结果的所有productId提取出来，调用以下API，传入lcscCode，去获取库里的product_id
curl --location 'https://check-product.962692556.workers.dev?lcscCode=C514413'

返回结果示例：
{
    "product_id": "CMA100138743",
    "status": "updated",
    "lcsc_code": "C514413",
    "details": {
        "new_product": true,
        "new_brand": true,
        "new_mapping": false,
        "updated_tables": [
            "brands",
            "brand_mapping",
            "products202503",
            "product_category_map",
            "price",
            "stocks",
            "parameters"
        ],
        "brand_info": {
            "brand_id": "CEB003326",
            "is_new": true,
            "brand_name": "Analog Devices",
            "domestic": "international"
        },
        "category_info": {
            "category_code": "CMC0016015001"
        },
        "price_count": 6,
        "parameter_count": 24,
        "stock_info": {
            "stock_quantity": 3484
        }
    }
}

6、将获取到的所有product_id存入一个数组，然后调用以下API，传入product_id数组，获取所有product数据：

# 批量查询产品库是否有该型号
```
curl --location 'https://webapi.962692556.workers.dev/products/models/check' \
--header 'Content-Type: application/json' \
--data '{
  "models": ["STM32F103C8T6", "ESP32-WROOM-32"],
  "product_ids": ["P12345", "P67890"]
}'

```

```json
响应示例：
{
    "ATMEGA644PA-AU": {
        "exists": false,
        "product_id": null,
        "brand_id": null,
        "brand_name_cn": null,
        "brand_name_en": null,
        "brand_name_ru": null,
        "domestic": null,
        "full":null
    },
    "CD4148WP": {
        "exists": true,
        "product_id": "CMA100590276",
        "brand_id": "CEB003110",
        "full": {
            "product_id": "CMA100590276",
            "model": "CD4148WP",
            "brand_id": "CEB003110",
            "brand": {
                "name_cn": "LIZ(丽智电子)",
                "name_en": "Liz Electronic",
                "name_ru": "Лиз Электроник",
                "logo_url": "https://brandimg.962692556.workers.dev/1516867087889.jpg",
                "website": "http://www.lizgroup.com/"
            },
            "price_key": "CMA100590276",
            "stock_key": "CMA100590276",
            "datasheet_url": "https://datasheet.962692556.workers.dev/CMA100520894_CD4148WP_规格书_wj217781.PDF",
            "image_list": [],
            "parameters_key": "CMA100590276",
            "description": "",
            "updated_at": "2025-03-19 02:00:58",
            "prices": [
                {
                    "quantity": 50,
                    "price": 0.08
                },
                {
                    "quantity": 500,
                    "price": 0.07
                },
                {
                    "quantity": 1500,
                    "price": 0.06
                },
                {
                    "quantity": 5000,
                    "price": 0.05
                },
                {
                    "quantity": 25000,
                    "price": 0.04
                }
            ],
            "stock": 0,
            "category_path": [
                "CMC0039",
                "CMC0039011",
                "CMC0039011003"
            ],
            "category_id": "CMC0039011003",
            "category_names": {
                "CMC0039": {
                    "name_cn": "电子元件",
                    "name_en": "electronic_components",
                    "name_ru": "electronic_components"
                },
                "CMC0039011": {
                    "name_cn": "数字器件",
                    "name_en": "digital_components",
                    "name_ru": "digital_components"
                },
                "CMC0039011003": {
                    "name_cn": "数字隔离器",
                    "name_en": "Digital Isolator",
                    "name_ru": "数字隔离器"
                }
            }
                
        }
    }
}

7、将获取到的所有product数据最终存入bom_main和bom_items表中

#### 8. BOM管理表 (2张)
- **bom_main** - BOM主记录表
  - `bom_id` (INTEGER, PK) - BOM ID (自增主键)
  - `bom_name` (TEXT, NOT NULL) - BOM名称
  - `status` (INTEGER) - 状态 (默认0)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `file_url` (TEXT) - 文件URL
  - `item_count` (INTEGER) - 项目总数 (默认0)
  - `success_count` (INTEGER) - 成功匹配数 (默认0)
  - `exact_match_count` (INTEGER) - 精确匹配数 (默认0)
  - `partial_match_count` (INTEGER) - 部分匹配数 (默认0)
  - `no_match_count` (INTEGER) - 无匹配数 (默认0)
  - `est_total_price` (REAL) - 预估总价 (默认0)
  - `each_price` (REAL) - 单价 (默认0)
  - `priority_matching` (INTEGER) - 优先匹配 (默认0)
  - `quantity_multiplier` (INTEGER) - 数量倍数 (默认1)
  - `create_at` (DATETIME) - 创建时间 (默认当前时间)
  - `update_at` (DATETIME) - 更新时间 (默认当前时间)

- **bom_items** - BOM明细表
  - `bom_items_id` (INTEGER, PK) - BOM明细ID (自增主键)
  - `user_id` (TEXT, NOT NULL) - 用户ID
  - `bom_id` (INTEGER, NOT NULL) - BOM ID (外键关联bom_main表)
  - `product_code` (TEXT) - 产品代码
  - `customer_part_number` (TEXT) - 客户零件号
  - `match_status` (INTEGER) - 匹配状态 (默认0)
  - `requested_qty` (INTEGER) - 请求数量 (默认0)
  - `order_qty` (INTEGER) - 订单数量 (默认0)
  - `stock_availability` (TEXT) - 库存可用性
  - `target_price` (REAL) - 目标价格
  - `lead_time` (TEXT) - 交货时间
  - `unit_price` (REAL) - 单价 (默认0)
  - `ext_price` (REAL) - 扩展价格 (默认0)
  - `offer_availability` (TEXT) - 报价可用性
  - `packaging_choice` (TEXT) - 包装选择
  - `notes` (TEXT) - 备注
  - `create_at` (DATETIME) - 创建时间 (默认当前时间)
  - `update_at` (DATETIME) - 更新时间 (默认当前时间)


