{"database_name": "little-field-db", "export_time": "2025-06-27T12:41:50.598415", "table_count": 29, "tables": {"distributor_products": {"columns": [{"cid": 0, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "distributor_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 2}, {"cid": 2, "name": "model", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "brand_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "price_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "stock_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "datasheet_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "image_keys", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "parameters", "type": "JSON", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "vector_data", "type": "JSON", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "updated_at", "type": "TIMESTAMP", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "idx_distributor_products_distributor_id", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "sqlite_autoindex_distributor_products_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 12}, "users": {"columns": [{"cid": 0, "name": "user_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "email", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "password_hash", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "role", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "created_at", "type": "TIMESTAMP", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [{"seq": 0, "name": "idx_users_email", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "sqlite_autoindex_users_2", "unique": 1, "origin": "u", "partial": 0}, {"seq": 2, "name": "sqlite_autoindex_users_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 5}, "orders": {"columns": [{"cid": 0, "name": "order_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "user_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "status", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "total_amount", "type": "DECIMAL(10,2)", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "created_at", "type": "TIMESTAMP", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [{"seq": 0, "name": "idx_orders_user_id", "unique": 0, "origin": "c", "partial": 0}, {"seq": 1, "name": "sqlite_autoindex_orders_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 5}, "products": {"columns": [{"cid": 0, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "model", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "brand_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "price_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "stock_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "datasheet_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "image_keys", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "parameters", "type": "JSON", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "updated_at", "type": "TIMESTAMP", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_products_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 10}, "brands": {"columns": [{"cid": 0, "name": "id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "introduction_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "name_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "name_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "website", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "name_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "introduction_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "logo_url", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "introduction_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "domestic", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "area", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_brands_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 11}, "products202503": {"columns": [{"cid": 0, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "model", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "brand_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "price_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "stock_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "datasheet_url", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "image_list", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "parameters_key", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "updated_at", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "rohs", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "summarizer", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_products202503_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 12}, "price": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "quantity", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "price", "type": "REAL", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "created_at", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_price_1", "unique": 1, "origin": "u", "partial": 0}], "column_count": 5}, "stocks": {"columns": [{"cid": 0, "name": "code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "stocks", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "created_at", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_stocks_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 3}, "parameters": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "languages", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "param_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "param_value", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "created_at", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_parameters_1", "unique": 1, "origin": "u", "partial": 0}], "column_count": 6}, "categories": {"columns": [{"cid": 0, "name": "code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "parent_code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "level", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "name_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "name_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "name_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "created_at", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_categories_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 7}, "product_category_map": {"columns": [{"cid": 0, "name": "product_code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "category_code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "created_at", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_product_category_map_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 3}, "distributors": {"columns": [{"cid": 0, "name": "distributor_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "name_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "name_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "name_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "category_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "description_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "description_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "description_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "logo", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "img_list", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "contact", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "certification", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "address_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "address_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "address_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 15, "name": "website", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 16, "name": "established_date", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 17, "name": "membership_years", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_distributors_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 18}, "recommend": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "keyword", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "date", "type": "DATE", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "created_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [], "column_count": 4}, "discount_products": {"columns": [{"cid": 0, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "model", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "type", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "brand_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "brand_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "brand_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "brand_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "domestic", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "created_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_discount_products_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 9}, "distributors-portal": {"columns": [{"cid": 0, "name": "distributor_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "type", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "data", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_distributors-portal_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 3}, "popular-categories": {"columns": [{"cid": 0, "name": "code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "parent_code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "level", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "name_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "name_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "name_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "created_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_popular-categories_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 7}, "newly_quota": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "model", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "brand_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "brand_name_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "brand_name_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "brand_name_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "domestic", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "distributor_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "name_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "name_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "name_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "logo", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "address_cn", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "address_en", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 15, "name": "address_ru", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 16, "name": "create_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [], "column_count": 17}, "requests": {"columns": [{"cid": 0, "name": "id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "bom_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "user_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "type", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "status", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "contact_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "business_email", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "company_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "country", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "quantity", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "model", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "brand_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "brand_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "target_price", "type": "REAL", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "delivery_time", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 15, "name": "date_code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 16, "name": "distribution_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 17, "name": "distribution_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 18, "name": "create_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_requests_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 19}, "quote": {"columns": [{"cid": 0, "name": "id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "request_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "bom_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "user_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "type", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "status", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "model", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "prices", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "shipping", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "quantity", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "brand_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "brand_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "target_price", "type": "REAL", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "delivery_time", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "date_code", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 15, "name": "distribution_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 16, "name": "distribution_name", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 17, "name": "create_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_quote_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 18}, "ads": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "platform", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "page", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "page_location", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "tags", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "status", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "priority", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "img", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "img_alt", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "title", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "subtitle", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 12, "name": "target_url", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 13, "name": "effective_time", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "create_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [], "column_count": 15}, "author": {"columns": [{"cid": 0, "name": "author_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "name", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "avatar", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "bio", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_author_1", "unique": 1, "origin": "pk", "partial": 0}], "column_count": 5}, "insights": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "type", "type": "INTEGER", "notnull": 0, "dflt_value": "0", "pk": 0}, {"cid": 2, "name": "status", "type": "INTEGER", "notnull": 0, "dflt_value": "0", "pk": 0}, {"cid": 3, "name": "title", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "subtitle", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "cover_image", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "tags", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "created_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 9, "name": "updated_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 10, "name": "author_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}], "indexes": [], "column_count": 11}, "insights_blog_map": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "insights_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "blog_id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [], "column_count": 3}, "blog": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "type", "type": "INTEGER", "notnull": 0, "dflt_value": "0", "pk": 0}, {"cid": 2, "name": "status", "type": "INTEGER", "notnull": 0, "dflt_value": "0", "pk": 0}, {"cid": 3, "name": "title", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "subtitle", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "cover_image", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "content_markdown", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "tags", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 9, "name": "category", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 10, "name": "location", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 11, "name": "created_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 12, "name": "updated_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 13, "name": "author_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 14, "name": "view_count", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [], "column_count": 15}, "subscribe": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "email", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "create_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [], "column_count": 3}, "tdkh": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "platform", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "url", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "title", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "description", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 5, "name": "keywords", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 6, "name": "h1", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 7, "name": "note", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 8, "name": "create_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}, {"cid": 9, "name": "update_at", "type": "DATETIME", "notnull": 0, "dflt_value": "CURRENT_TIMESTAMP", "pk": 0}], "indexes": [], "column_count": 10}, "product_index": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "first_text", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "product_id", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "model", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}, {"cid": 4, "name": "url", "type": "TEXT", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [], "column_count": 5}, "brand_distributor_mapping": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "brand_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "distributor_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "accountfor", "type": "REAL", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_brand_distributor_mapping_1", "unique": 1, "origin": "u", "partial": 0}], "column_count": 4}, "category_distributor_mapping": {"columns": [{"cid": 0, "name": "id", "type": "INTEGER", "notnull": 0, "dflt_value": null, "pk": 1}, {"cid": 1, "name": "category_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 2, "name": "distributor_id", "type": "TEXT", "notnull": 1, "dflt_value": null, "pk": 0}, {"cid": 3, "name": "accountfor", "type": "REAL", "notnull": 0, "dflt_value": null, "pk": 0}], "indexes": [{"seq": 0, "name": "sqlite_autoindex_category_distributor_mapping_1", "unique": 1, "origin": "u", "partial": 0}], "column_count": 4}}, "create_statements": [{"type": "index", "name": "idx_distributor_products_distributor_id", "sql": "CREATE INDEX idx_distributor_products_distributor_id ON distributor_products(distributor_id)"}, {"type": "index", "name": "idx_orders_user_id", "sql": "CREATE INDEX idx_orders_user_id ON orders(user_id)"}, {"type": "index", "name": "idx_users_email", "sql": "CREATE INDEX idx_users_email ON users(email)"}, {"type": "table", "name": "ads", "sql": "CREATE TABLE ads (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    platform TEXT,\n    page TEXT,\n    page_location TEXT,\n    tags TEXT,\n    status TEXT,\n    priority INTEGER,\n    img TEXT,\n    img_alt TEXT,\n    title TEXT,\n    subtitle TEXT,\n    description TEXT,\n    target_url TEXT,\n    effective_time TEXT,\n    create_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "author", "sql": "CREATE TABLE author (\n    author_id TEXT PRIMARY KEY,\n    name TEXT NOT NULL,\n    avatar TEXT,\n    description TEXT,\n    bio TEXT\n)"}, {"type": "table", "name": "blog", "sql": "CREATE TABLE blog (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    type INTEGER DEFAULT 0,\n    status INTEGER DEFAULT 0,\n    title TEXT NOT NULL,\n    subtitle TEXT,\n    description TEXT,\n    cover_image TEXT,\n    content_markdown TEXT,\n    tags TEXT,\n    category TEXT,\n    location TEXT,\n    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n    author_id TEXT NOT NULL\n, view_count INTEGER)"}, {"type": "table", "name": "brand_distributor_mapping", "sql": "CREATE TABLE brand_distributor_mapping (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    brand_id TEXT NOT NULL,\n    distributor_id TEXT NOT NULL,\n    accountfor REAL,\n    UNIQUE(brand_id, distributor_id)\n)"}, {"type": "table", "name": "brands", "sql": "CREATE TABLE brands (id TEXT PRIMARY KEY, introduction_en TEXT, name_cn TEXT, name_en TEXT, website TEXT, name_ru TEXT, introduction_ru TEXT, logo_url TEXT, introduction_cn TEXT, domestic TEXT, area TEXT)"}, {"type": "table", "name": "categories", "sql": "CREATE TABLE categories (\n        code TEXT PRIMARY KEY,\n        parent_code TEXT,\n        level INTEGER,\n        name_cn TEXT,\n        name_en TEXT,\n        name_ru TEXT,\n        created_at TEXT\n    )"}, {"type": "table", "name": "category_distributor_mapping", "sql": "CREATE TABLE category_distributor_mapping (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    category_id TEXT NOT NULL,\n    distributor_id TEXT NOT NULL,\n    accountfor REAL,\n    UNIQUE(category_id, distributor_id)\n)"}, {"type": "table", "name": "discount_products", "sql": "CREATE TABLE discount_products (\n    product_id TEXT PRIMARY KEY,\n    model TEXT,\n    type TEXT,\n    brand_id TEXT,\n    brand_cn TEXT,\n    brand_en TEXT,\n    brand_ru TEXT,\n    domestic  TEXT,\n    created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "distributor_products", "sql": "CREATE TABLE distributor_products (\n    product_id TEXT,\n    distributor_id TEXT,\n    model TEXT NOT NULL,\n    brand_id TEXT NOT NULL,\n    price_key TEXT,\n    stock_key TEXT,\n    datasheet_key TEXT,\n    image_keys TEXT,\n    parameters JSON,\n    vector_data JSON,\n    description TEXT,\n    updated_at TIMESTAMP,\n    PRIMARY KEY (product_id, distributor_id),\n    FOREIGN KEY (distributor_id) REFERENCES distributors(distributor_id)\n)"}, {"type": "table", "name": "distributors", "sql": "CREATE TABLE distributors (\n    distributor_id TEXT PRIMARY KEY,\n    name_cn TEXT,\n    name_en TEXT,\n    name_ru TEXT,\n    category_id TEXT,\n    description_cn TEXT,\n    description_en TEXT,\n    description_ru TEXT,\n    logo TEXT,\n    img_list TEXT,\n    contact TEXT,\n    certification TEXT,\n    address_cn TEXT,\n    address_en TEXT,\n    address_ru TEXT,\n    website TEXT\n, established_date TEXT, membership_years INTEGER)"}, {"type": "table", "name": "distributors-portal", "sql": "CREATE TABLE `distributors-portal` (\n    distributor_id TEXT PRIMARY KEY,\n    type TEXT,\n    data TEXT\n)"}, {"type": "table", "name": "insights", "sql": "CREATE TABLE insights (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    type INTEGER DEFAULT 0,\n    status INTEGER DEFAULT 0,\n    title TEXT NOT NULL,\n    subtitle TEXT,\n    description TEXT,\n    cover_image TEXT,\n    tags TEXT,\n    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n    author_id TEXT NOT NULL\n)"}, {"type": "table", "name": "insights_blog_map", "sql": "CREATE TABLE insights_blog_map (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    insights_id INTEGER,\n    blog_id INTEGER\n)"}, {"type": "table", "name": "newly_quota", "sql": "CREATE TABLE newly_quota (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    product_id TEXT,\n    model TEXT NOT NULL,\n    brand_id TEXT,\n    brand_name_cn TEXT,\n    brand_name_en TEXT,\n    brand_name_ru TEXT,\n    domestic TEXT,\n    distributor_id TEXT,\n    name_cn TEXT,\n    name_en TEXT,\n    name_ru TEXT,\n    logo TEXT,\n    address_cn TEXT,\n    address_en TEXT,\n    address_ru TEXT,\n    create_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "orders", "sql": "CREATE TABLE orders (\n    order_id TEXT PRIMARY KEY,\n    user_id TEXT NOT NULL,\n    status TEXT NOT NULL,\n    total_amount DECIMAL(10,2) NOT NULL,\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n    FOREIGN KEY (user_id) REFERENCES users(user_id)\n)"}, {"type": "table", "name": "parameters", "sql": "CREATE TABLE parameters (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        code TEXT,\n        languages TEXT,\n        param_name TEXT,\n        param_value TEXT,\n        created_at TEXT,\n        UNIQUE(code, languages, param_name)\n    )"}, {"type": "table", "name": "popular-categories", "sql": "CREATE TABLE \"popular-categories\" (\n    code TEXT PRIMARY KEY,\n    parent_code TEXT,\n    level INTEGER,\n    name_cn TEXT,\n    name_en TEXT,\n    name_ru TEXT,\n    created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "price", "sql": "CREATE TABLE price (\n        id INTEGER PRIMARY KEY AUTOINCREMENT,\n        code TEXT,\n        quantity INTEGER,\n        price REAL,\n        created_at TEXT,\n        UNIQUE(code, quantity)\n    )"}, {"type": "table", "name": "product_category_map", "sql": "CREATE TABLE product_category_map (\n        product_code TEXT PRIMARY KEY,\n        category_code TEXT,\n        created_at TEXT\n    )"}, {"type": "table", "name": "product_index", "sql": "CREATE TABLE product_index (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    first_text TEXT,\n    product_id TEXT,\n    model TEXT,\n    url TEXT\n)"}, {"type": "table", "name": "products", "sql": "CREATE TABLE products (\n            product_id TEXT PRIMARY KEY,\n            model TEXT NOT NULL,\n            brand_id TEXT NOT NULL,\n            price_key TEXT,\n            stock_key TEXT,\n            datasheet_key TEXT,\n            image_keys TEXT,\n            parameters JSON,\n            description TEXT,\n            updated_at TIMESTAMP,\n            FOREIGN KEY (brand_id) REFERENCES brands(brand_id)\n        )"}, {"type": "table", "name": "products202503", "sql": "CREATE TABLE products202503 (\n        product_id TEXT PRIMARY KEY,\n        model TEXT,\n        brand_id TEXT,\n        price_key TEXT,\n        stock_key TEXT,\n        datasheet_url TEXT,\n        image_list TEXT,\n        parameters_key TEXT,\n        description TEXT,\n        updated_at TEXT\n    , rohs INTEGER, summarizer TEXT)"}, {"type": "table", "name": "quote", "sql": "CREATE TABLE quote (\n    id TEXT PRIMARY KEY,\n    request_id TEXT,\n    bom_id TEXT,\n    user_id TEXT,\n    type TEXT,\n    status TEXT,\n    model TEXT,\n    prices TEXT,\n    shipping TEXT,\n    quantity INTEGER,\n    brand_id TEXT,\n    brand_name TEXT,\n    target_price REAL,\n    delivery_time TEXT,\n    date_code TEXT,\n    distribution_id TEXT,\n    distribution_name TEXT,\n    create_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "recommend", "sql": "CREATE TABLE recommend (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    keyword TEXT NOT NULL,\n    date DATE NOT NULL,\n    created_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "requests", "sql": "CREATE TABLE requests (\n    id TEXT PRIMARY KEY,\n    bom_id TEXT,\n    user_id TEXT,\n    type TEXT,\n    status TEXT,\n    contact_name TEXT,\n    business_email TEXT,\n    company_name TEXT,\n    country TEXT,\n    quantity INTEGER,\n    model TEXT,\n    brand_id TEXT,\n    brand_name TEXT,\n    target_price REAL,\n    delivery_time TEXT,\n    date_code TEXT,\n    distribution_id TEXT,\n    distribution_name TEXT,\n    create_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "stocks", "sql": "CREATE TABLE stocks (\n        code TEXT PRIMARY KEY,\n        stocks INTEGER,\n        created_at TEXT\n    )"}, {"type": "table", "name": "subscribe", "sql": "CREATE TABLE subscribe (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    email TEXT,\n    create_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "tdkh", "sql": "CREATE TABLE tdkh (\n    id INTEGER PRIMARY KEY AUTOINCREMENT,\n    platform TEXT NOT NULL,\n    url TEXT NOT NULL,\n    title TEXT,\n    description TEXT,\n    keywords TEXT,\n    h1 TEXT,\n    note TEXT,\n    create_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n    update_at DATETIME DEFAULT CURRENT_TIMESTAMP\n)"}, {"type": "table", "name": "users", "sql": "CREATE TABLE users (\n    user_id TEXT PRIMARY KEY,\n    email TEXT UNIQUE NOT NULL,\n    password_hash TEXT NOT NULL,\n    role TEXT NOT NULL,\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n)"}]}