'use client'

import { useState, FormEvent, Suspense } from 'react'
import Link from 'next/link'
import { useRouter, useSearchParams } from 'next/navigation'
// 使用内联SVG图标替代lucide-react以减少编译负担
const ShieldIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
  </svg>
)

const ArrowRightIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
  </svg>
)

const MailIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
  </svg>
)

const RefreshCwIcon = () => (
  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
)
// 假设您的 AuthContext 提供了 verifyCode 和 loginWithToken 方法
// import { useAuth } from '@/contexts/AuthContext'

function VerifyCodeComponent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const email = searchParams.get('email')

  const [code, setCode] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  // const { verifyCode, loginWithToken } = useAuth() // 如果 AuthContext 准备好了，可以取消注释

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault()
    setError('')
    setMessage('')

    if (!email) {
      setError('Email not found. Please go back to registration.')
      return
    }
    if (!code || code.length !== 6) {
      setError('Please enter a valid 6-digit verification code.')
      return
    }

    setLoading(true)

    try {
      // 模拟 API 调用 - 替换为实际的 AuthContext verifyCode 调用
      // const token = await verifyCode(email, code)
      // START: API 调用模拟
      const response = await fetch('https://auth.chinaelectron.com/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin
        },
        body: JSON.stringify({ email, code }),
      });
      const data = await response.json();
      // END: API 调用模拟

      if (data.success && data.data && data.data.token) {
        setMessage(data.message || 'Account verified successfully! Redirecting to login...')
        // 假设您有一个方法可以直接使用token登录，或者您在此处存储token并重定向
        // await loginWithToken(data.data.token) 
        // 简单起见，我们先存储到localStorage并跳转到登录页提示用户登录
        if (typeof window !== "undefined") {
            localStorage.setItem('authToken', data.data.token)
        }
        setTimeout(() => {
          router.push('/login')
        }, 3000);
      } else {
        setError(data.message || data.error || 'Verification failed. Please check the code and try again.')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred during verification.')
    } finally {
      setLoading(false)
    }
  }

  const handleResendCode = async () => {
    if (!email) {
        setError('Email not found. Cannot resend code.');
        return;
    }
    setLoading(true);
    setError('');
    setMessage('');
    try {
        // 这个API端点在文档中没有明确指出"重新发送验证码"的接口
        // 这里我们假设注册接口可以被再次调用以重新发送验证码
        const response = await fetch('https://auth.chinaelectron.com/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Origin': window.location.origin
            },
            body: JSON.stringify({ email, password: 'dummyPasswordForResend' }), // 密码字段可能是必需的，但不会被使用
        });
        const data = await response.json();
        if (data.success) {
            setMessage(data.message || 'A new verification code has been sent to your email.');
        } else {
            setError(data.message || 'Failed to resend code. Please try again later.');
        }
    } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred while resending the code.');
    }
    setLoading(false);
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8 min-h-[calc(100vh-120px)]">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-40">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f1f5f9' fill-opacity='0.4'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="relative w-full max-w-md">
        {/* Main Card */}
        <div className="modern-card backdrop-blur-xl bg-white/95 border border-white/20 shadow-2xl p-6">
          {/* Header */}
          <div className="text-center mb-6">
            <div className="flex items-center justify-center mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-[#DE2910] to-[#FF6B45] rounded-xl flex items-center justify-center shadow-lg mr-3">
                <div className="text-white"><ShieldIcon /></div>
              </div>
              <h2 className="text-2xl font-bold gradient-text-primary">
                Verify Account
              </h2>
            </div>
            <div className="flex items-center justify-center mb-2">
              <div className="text-gray-400 mr-2"><MailIcon /></div>
              <p className="text-sm text-gray-600">
                Code sent to <span className="font-medium text-gray-800">{email || 'your email'}</span>
              </p>
            </div>
            <p className="text-xs text-gray-500">
              Enter the 6-digit code to complete registration
            </p>
          </div>
          {/* Form */}
          <form className="space-y-4" onSubmit={handleSubmit}>
            {/* Verification Code Input */}
            <div className="space-y-1">
              <label htmlFor="code" className="block text-sm font-semibold text-gray-700">
                Verification Code
              </label>
              <div className="relative">
                <input
                  id="code"
                  name="code"
                  type="text"
                  maxLength={6}
                  required
                  value={code}
                  onChange={(e) => setCode(e.target.value.replace(/[^0-9]/g, ''))}
                  className="w-full px-3 py-3 rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm focus:border-[#DE2910] focus:ring-4 focus:ring-[#DE2910]/20 transition-all duration-300 ease-out placeholder-gray-400 text-sm text-center tracking-[0.3em] font-mono text-lg"
                  placeholder="000000"
                />
              </div>
              <p className="text-xs text-gray-500 text-center">Enter the 6-digit code from your email</p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-2">
                    <p className="text-xs text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Success Message */}
            {message && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-4 w-4 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-2">
                    <p className="text-xs text-green-800">{message}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-gradient-to-r from-[#DE2910] to-[#FF6B45] text-white font-semibold py-3 rounded-xl hover:from-[#FF3B20] hover:to-[#FF8055] hover:scale-105 hover:-translate-y-0.5 transition-all duration-300 ease-out shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-2"
            >
              <span>{loading ? 'Verifying...' : 'Verify Account'}</span>
              {!loading && <ArrowRightIcon />}
            </button>
          </form>

          {/* Resend Code */}
          <div className="mt-4 text-center">
            <button
                onClick={handleResendCode}
                disabled={loading || !email}
                className="text-sm text-[#DE2910] hover:text-[#FF6B45] disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 mx-auto"
            >
                <div className="w-3 h-3"><RefreshCwIcon /></div>
                <span>Didn't receive the code? Resend</span>
            </button>
          </div>

          {/* Divider */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="px-3 bg-white text-gray-500 font-medium">Need help?</span>
              </div>
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-4 text-center">
            <div className="bg-blue-50 rounded-lg p-3 mb-4">
              <p className="text-xs text-blue-800 mb-2">
                <strong>Code expires in 10 minutes</strong>
              </p>
              <p className="text-xs text-blue-700">
                Check your spam folder if you don't see the email. Each new code invalidates previous ones.
              </p>
            </div>

            <div className="flex items-center justify-center space-x-4 text-xs">
              <Link href="/login" className="text-[#DE2910] hover:text-[#FF6B45] font-medium">
                Back to Login
              </Link>
              <span className="text-gray-300">|</span>
              <Link href="/register" className="text-[#DE2910] hover:text-[#FF6B45] font-medium">
                Back to Registration
              </Link>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-4 text-center">
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
            <span className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
              </svg>
              SSL Secured
            </span>
            <span className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Privacy Protected
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}

// Next.js Pages Router or App Router with Suspense for useSearchParams
export default function VerifyCodePage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyCodeComponent />
    </Suspense>
  );
} 