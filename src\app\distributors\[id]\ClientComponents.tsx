'use client'

import { useState, useEffect } from 'react'

// Social media links component
export function SocialMediaLinks({ socialMedia }: { 
  socialMedia: { type: string; link: string }[] 
}) {
  const [isHovered, setIsHovered] = useState<string | null>(null)

  return (
    <div className="flex gap-4">
      {socialMedia.map(social => (
        <a
          key={social.type}
          href={social.link}
          target="_blank"
          rel="noopener noreferrer"
          className={`px-4 py-2 bg-gray-50 rounded-lg text-gray-600 transition-colors ${
            isHovered === social.type ? 'bg-gray-100' : ''
          }`}
          onMouseEnter={() => setIsHovered(social.type)}
          onMouseLeave={() => setIsHovered(null)}
        >
          {social.type}
        </a>
      ))}
    </div>
  )
}

// Brand website link component
export function BrandLink({ website }: { website: string }) {
  // 处理点击事件，阻止冒泡
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <a 
      href={website} 
      target="_blank" 
      rel="noopener noreferrer"
      className="text-sm text-blue-600 hover:underline"
      onClick={handleClick}
    >
      Website
    </a>
  );
}

// Email display component with login check
export function ContactEmailDisplay({ email }: { email: string }) {
  const [userId, setUserId] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [showEmail, setShowEmail] = useState(false);
  const [copied, setCopied] = useState(false);
  const [maskedEmail, setMaskedEmail] = useState('');
  const [actualEmail, setActualEmail] = useState('');

  // 页面加载时检查用户登录状态
  useEffect(() => {
    // 从localStorage获取用户认证信息
    const storedUserId = localStorage.getItem('userId');
    const storedToken = localStorage.getItem('token');
    
    setUserId(storedUserId);
    setToken(storedToken);
    
    // 处理邮箱格式
    if (email.startsWith('MASK:')) {
      // 解析掩码格式: MASK:首字母:用户名长度:域名:扩展名[:base64邮箱]
      const parts = email.split(':');
      if (parts.length >= 5) {
        const firstChar = parts[1];
        const usernameLength = parseInt(parts[2], 10);
        const domain = parts[3];
        const extension = parts[4];
        
        // 生成掩码邮箱
        setMaskedEmail(generateMaskedEmail(firstChar, usernameLength, domain, extension));
        
        // 如果有base64编码的邮箱部分（第6个部分）
        if (parts.length >= 6 && parts[5]) {
          try {
            // 解码base64字符串获取实际邮箱
            const decodedEmail = atob(parts[5]);
            setActualEmail(decodedEmail);
          } catch (e) {
            console.error('Failed to decode email:', e);
          }
        }
      } else {
        setMaskedEmail('*****@****.***');
      }
    } else if (email === 'SECURED_EMAIL_REQUIRES_LOGIN') {
      // 默认掩码
      setMaskedEmail('*****@****.***');
    } else {
      // 正常邮箱情况
      setActualEmail(email);
      setMaskedEmail(maskEmail(email));
    }
  }, [email]); // 当email属性变化时重新计算

  // 生成掩码邮箱，基于提供的信息
  const generateMaskedEmail = (
    firstChar: string, 
    usernameLength: number, 
    domain: string, 
    extension: string
  ): string => {
    const maskedUsername = firstChar + '*'.repeat(Math.max(0, usernameLength - 1));
    const domainParts = domain.split('.');
    const domainName = domainParts[0];
    const maskedDomain = '*'.repeat(domainName.length) + '.' + extension;
    return `${maskedUsername}@${maskedDomain}`;
  };

  // 复制文本到剪贴板
  const copyToClipboard = async (text: string) => {
    try {
      // 检查 navigator.clipboard API 是否可用
      if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text);
        setCopied(true);
      } else {
        // 备用方法：使用文本区域元素复制
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';  // 避免滚动到底部
        textArea.style.opacity = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
          // 执行复制命令
          const successful = document.execCommand('copy');
          setCopied(successful);
        } catch (err) {
          console.error('备用复制方法失败:', err);
        }
        
        // 清理DOM
        document.body.removeChild(textArea);
      }
      
      // 3秒后重置复制状态
      setTimeout(() => {
        setCopied(false);
      }, 3000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  // 将邮箱地址部分遮蔽处理
  const maskEmail = (email: string): string => {
    const [username, domain] = email.split('@');
    if (!username || !domain) return '******@*****'; // 格式错误时的默认遮蔽

    const maskedUsername = username.length > 3 
      ? `${username.substring(0, 2)}${'*'.repeat(username.length - 2)}` 
      : `${username[0]}${'*'.repeat(username.length > 1 ? username.length - 1 : 1)}`;
    
    const [domainName, extension] = domain.split('.');
    const maskedDomain = `${'*'.repeat(domainName.length)}.${extension}`;
    
    return `${maskedUsername}@${maskedDomain}`;
  };

  const toggleEmailVisibility = () => {
    setShowEmail(!showEmail);
  };

  // 检查用户是否已验证（使用客户端状态）
  const isAuthenticated = userId && token;
  // 确认是否有可以显示的实际邮箱
  const hasActualEmail = isAuthenticated && actualEmail;

  return (
    <div>
      {isAuthenticated ? (
        // 已登录用户显示操作界面
        <div className="flex items-center">
          <span className="mr-3">
            {showEmail && hasActualEmail
              ? actualEmail // 显示完整邮箱
              : maskedEmail // 显示掩码邮箱
            }
          </span>
          {hasActualEmail && (
            <div className="flex space-x-2">
              <button
                onClick={toggleEmailVisibility}
                className="flex items-center justify-center text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#DE2910]/30"
                title={showEmail ? "Hide email" : "Show email"}
              >
                {showEmail ? (
                  // 显示"Hide"图标
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    strokeWidth={1.5} 
                    stroke="currentColor" 
                    className="w-4 h-4"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" 
                    />
                  </svg>
                ) : (
                  // 显示"Show"图标
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    strokeWidth={1.5} 
                    stroke="currentColor" 
                    className="w-4 h-4"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 ************.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" 
                    />
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" 
                    />
                  </svg>
                )}
              </button>
              
              {showEmail && (
                <button
                  onClick={() => copyToClipboard(actualEmail)}
                  className={`flex items-center justify-center text-xs ${copied ? 'bg-green-100 text-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'} px-2 py-1 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-[#DE2910]/30`}
                  title="Copy email"
                >
                  {copied ? (
                    // 复制成功图标
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      strokeWidth={1.5} 
                      stroke="currentColor" 
                      className="w-4 h-4"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        d="M4.5 12.75l6 6 9-13.5" 
                      />
                    </svg>
                  ) : (
                    // 复制图标
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      fill="none" 
                      viewBox="0 0 24 24" 
                      strokeWidth={1.5} 
                      stroke="currentColor" 
                      className="w-4 h-4"
                    >
                      <path 
                        strokeLinecap="round" 
                        strokeLinejoin="round" 
                        d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 01-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 011.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 00-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125v3.5m7.5 10.375H9.375a1.125 1.125 0 01-1.125-1.125v-9.25m12 6.625v-1.875a3.375 3.375 0 00-3.375-3.375h-1.5a1.125 1.125 0 01-1.125-1.125v-1.5a3.375 3.375 0 00-3.375-3.375H9.75" 
                      />
                    </svg>
                  )}
                </button>
              )}
            </div>
          )}
          {!hasActualEmail && (
            <p className="text-xs text-amber-500 ml-2">
              Please refresh or login again to view this email
            </p>
          )}
        </div>
      ) : (
        // 未登录用户显示加密邮箱和登录提示
        <div>
          <div className="flex items-center">
            <span className="mr-2">{maskedEmail}</span>
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24" 
              strokeWidth={1.5} 
              stroke="currentColor" 
              className="w-4 h-4 text-[#DE2910]"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" 
              />
            </svg>
          </div>
          <p className="text-sm text-[#DE2910] mt-1 flex items-center">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              viewBox="0 0 20 20" 
              fill="currentColor" 
              className="w-3 h-3 mr-1"
            >
              <path 
                fillRule="evenodd" 
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" 
                clipRule="evenodd" 
              />
            </svg>
            <a href="/login" className="hover:underline">Login</a> to view complete contact information
          </p>
        </div>
      )}
    </div>
  );
}

// Add other client-side interactive components here as needed 