'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import Link from 'next/link'

// 国家列表
const countries = [
  "Afghanistan", "Albania", "Algeria", "Andorra", "Angola", "Antigua and Barbuda", "Argentina", "Armenia", "Australia", "Austria", 
  "Azerbaijan", "Bahamas", "Bahrain", "Bangladesh", "Barbados", "Belarus", "Belgium", "Belize", "Benin", "Bhutan", 
  "Bolivia", "Bosnia and Herzegovina", "Botswana", "Brazil", "Brunei", "Bulgaria", "Burkina Faso", "Burundi", "Cabo Verde", "Cambodia", 
  "Cameroon", "Canada", "Central African Republic", "Chad", "Chile", "China", "Colombia", "Comoros", "Congo", "Costa Rica", 
  "Croatia", "Cuba", "Cyprus", "Czech Republic", "Denmark", "Djibouti", "Dominica", "Dominican Republic", "East Timor", "Ecuador", 
  "Egypt", "El Salvador", "Equatorial Guinea", "Eritrea", "Estonia", "<PERSON>s<PERSON>ni", "Ethiopia", "Fiji", "Finland", "France", 
  "Gabon", "Gambia", "Georgia", "Germany", "Ghana", "Greece", "Grenada", "Guatemala", "Guinea", "Guinea-Bissau", 
  "Guyana", "Haiti", "Honduras", "Hungary", "Iceland", "India", "Indonesia", "Iran", "Iraq", "Ireland", 
  "Israel", "Italy", "Jamaica", "Japan", "Jordan", "Kazakhstan", "Kenya", "Kiribati", "Korea, North", "Korea, South", 
  "Kosovo", "Kuwait", "Kyrgyzstan", "Laos", "Latvia", "Lebanon", "Lesotho", "Liberia", "Libya", "Liechtenstein", 
  "Lithuania", "Luxembourg", "Madagascar", "Malawi", "Malaysia", "Maldives", "Mali", "Malta", "Marshall Islands", "Mauritania", 
  "Mauritius", "Mexico", "Micronesia", "Moldova", "Monaco", "Mongolia", "Montenegro", "Morocco", "Mozambique", "Myanmar", 
  "Namibia", "Nauru", "Nepal", "Netherlands", "New Zealand", "Nicaragua", "Niger", "Nigeria", "North Macedonia", "Norway", 
  "Oman", "Pakistan", "Palau", "Palestine", "Panama", "Papua New Guinea", "Paraguay", "Peru", "Philippines", "Poland", 
  "Portugal", "Qatar", "Romania", "Russia", "Rwanda", "Saint Kitts and Nevis", "Saint Lucia", "Saint Vincent and the Grenadines", "Samoa", "San Marino", 
  "Sao Tome and Principe", "Saudi Arabia", "Senegal", "Serbia", "Seychelles", "Sierra Leone", "Singapore", "Slovakia", "Slovenia", "Solomon Islands", 
  "Somalia", "South Africa", "South Sudan", "Spain", "Sri Lanka", "Sudan", "Suriname", "Sweden", "Switzerland", "Syria", 
  "Taiwan", "Tajikistan", "Tanzania", "Thailand", "Togo", "Tonga", "Trinidad and Tobago", "Tunisia", "Turkey", "Turkmenistan", 
  "Tuvalu", "Uganda", "Ukraine", "United Arab Emirates", "United Kingdom", "United States", "Uruguay", "Uzbekistan", "Vanuatu", "Vatican City", 
  "Venezuela", "Vietnam", "Yemen", "Zambia", "Zimbabwe"
];

// 表单数据类型
interface FormData {
  contact_name: string
  business_email: string
  company_name: string
  country: string
  quantity: number
  model: string
  brand_name: string
  target_price: number
  delivery_time: string
}

// 表单错误类型
interface FormErrors {
  contact_name: string
  business_email: string
  company_name: string
  country: string
  quantity: string
  model: string
  brand_name: string
  target_price: string
  delivery_time: string
}

export default function RFQPage() {
  const router = useRouter()
  const { isAuthenticated, token, userId, isLoading } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  
  // 国家选择相关状态
  const [countrySearchTerm, setCountrySearchTerm] = useState('')
  const [filteredCountries, setFilteredCountries] = useState(countries)
  const [isCountryDropdownOpen, setIsCountryDropdownOpen] = useState(false)
  const countryDropdownRef = useRef<HTMLDivElement>(null)
  
  // 表单状态
  const [formData, setFormData] = useState<FormData>({
    contact_name: '',
    business_email: '',
    company_name: '',
    country: '',
    quantity: 1,
    model: '',
    brand_name: '',
    target_price: 0,
    delivery_time: ''
  })

  // 表单错误
  const [formErrors, setFormErrors] = useState<FormErrors>({
    contact_name: '',
    business_email: '',
    company_name: '',
    country: '',
    quantity: '',
    model: '',
    brand_name: '',
    target_price: '',
    delivery_time: ''
  })

  // 验证邮箱格式
  const isValidEmail = (email: string) => {
    return /^[\w\.-]+@[\w\.-]+\.\w+$/.test(email)
  }

  // 处理表单输入变化
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    
    // 更新表单数据
    setFormData(prev => ({ 
      ...prev, 
      [name]: (name === 'quantity' || name === 'target_price') 
        ? parseFloat(value) || 0 
        : value // delivery_time始终作为字符串处理
    }))
    
    // 清除当前字段的错误信息
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
    
    // 实时验证数量字段
    if (name === 'quantity') {
      const numValue = Number(value)
      if (value && (isNaN(numValue) || numValue <= 0)) {
        setFormErrors(prev => ({ ...prev, quantity: 'Quantity must be greater than 0' }))
      }
    }
    
    // 实时验证交期天数字段 - 保持为字符串，但验证内容是否为有效数字
    if (name === 'delivery_time') {
      const numValue = Number(value)
      if (value && (isNaN(numValue) || numValue < 0)) {
        setFormErrors(prev => ({ ...prev, delivery_time: 'Delivery time must be a positive number' }))
      }
    }
  }

  // 验证表单
  const validateForm = () => {
    const newErrors = { ...formErrors }
    let isValid = true

    // 验证联系人
    if (!formData.contact_name.trim()) {
      newErrors.contact_name = 'Contact name is required'
      isValid = false
    }

    // 验证邮箱
    if (!formData.business_email.trim()) {
      newErrors.business_email = 'Business email is required'
      isValid = false
    } else if (!isValidEmail(formData.business_email)) {
      newErrors.business_email = 'Please enter a valid business email address'
      isValid = false
    }

    // 验证公司名
    if (!formData.company_name.trim()) {
      newErrors.company_name = 'Company name is required'
      isValid = false
    }

    // 验证国家
    if (!formData.country) {
      newErrors.country = 'Country is required'
      isValid = false
    }

    // 验证型号
    if (!formData.model.trim()) {
      newErrors.model = 'Model/Part number is required'
      isValid = false
    }

    // 验证品牌名称
    if (!formData.brand_name.trim()) {
      newErrors.brand_name = 'Brand name is required'
      isValid = false
    }

    // 验证数量
    if (!formData.quantity) {
      newErrors.quantity = 'Quantity is required'
      isValid = false
    } else if (formData.quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0'
      isValid = false
    }
    
    // 验证交货期 - 作为字符串处理，但验证其数值
    if (formData.delivery_time) {
      const numDeliveryTime = Number(formData.delivery_time)
      if (isNaN(numDeliveryTime) || numDeliveryTime < 0) {
        newErrors.delivery_time = 'Delivery time must be a positive number'
        isValid = false
      }
    }

    setFormErrors(newErrors)
    return isValid
  }

  // 当搜索词变化时，过滤国家列表
  useEffect(() => {
    if (countrySearchTerm) {
      const filtered = countries.filter(country => 
        country.toLowerCase().includes(countrySearchTerm.toLowerCase())
      )
      setFilteredCountries(filtered)
    } else {
      setFilteredCountries(countries)
    }
  }, [countrySearchTerm])
  
  // 点击外部关闭下拉框
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target as Node)) {
        setIsCountryDropdownOpen(false)
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])
  
  // 选择国家
  const selectCountry = (country: string) => {
    setFormData(prev => ({ ...prev, country }))
    setCountrySearchTerm('')
    setIsCountryDropdownOpen(false)
    
    // 清除错误
    if (formErrors.country) {
      setFormErrors(prev => ({ ...prev, country: '' }))
    }
  }

  // 提交询价请求
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 检查登录状态
    if (!isAuthenticated || !token || !userId) {
      // console.log('User not authenticated, redirecting to login page')
      router.push('/login?redirect=rfq')
      return
    }
    
    // 验证表单
    // console.log('Validating form')
    if (!validateForm()) {
      // console.log('Form validation failed')
      return
    }
    
    setIsSubmitting(true)
    
    try {
      // 记录当前认证状态，帮助调试
      // console.log("Form submission - Current auth state:")
      // console.log("userId:", userId)
      // console.log("token exists:", !!token)
      // console.log("Form data:", formData)
      
      // 构建API请求数据
      const inquiryData = {
        user_id: userId,
        type: 'rfq',
        status: 'pending',
        contact_name: formData.contact_name,
        business_email: formData.business_email,
        company_name: formData.company_name,
        country: formData.country,
        quantity: Number(formData.quantity),
        model: formData.model,
        brand_name: formData.brand_name,
        target_price: Number(formData.target_price) || 0,
        delivery_time: formData.delivery_time
      }
      
      // console.log("Sending inquiry data to API:", inquiryData)
      
      // 使用内部API端点，而不是直接暴露外部API
      const response = await fetch('/api/submit-inquiry', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(inquiryData)
      })
      
      // console.log("API response status:", response.status)
      const data = await response.json()
      // console.log("API response data:", data)
      
      if (response.ok && data.success) {
        // console.log('Form submission successful')
        setSubmitSuccess(true)
        // 重置表单
        setFormData({
          contact_name: '',
          business_email: '',
          company_name: '',
          country: '',
          quantity: 1,
          model: '',
          brand_name: '',
          target_price: 0,
          delivery_time: ''
        })
        // 清空错误信息
        setFormErrors({
          contact_name: '',
          business_email: '',
          company_name: '',
          country: '',
          quantity: '',
          model: '',
          brand_name: '',
          target_price: '',
          delivery_time: ''
        })
      } else {
        // 处理API返回的错误
        console.error("API error response:", data)
        const errorMessage = data.message || 'Failed to submit request. Please try again.'
        setFormErrors(prev => ({ ...prev, business_email: errorMessage }))
      }
    } catch (error) {
      console.error('Error submitting RFQ:', error)
      setFormErrors(prev => ({
        ...prev,
        business_email: 'An error occurred while submitting your request. Please try again later.'
      }))
    } finally {
      setIsSubmitting(false)
    }
  }

  // 如果还在加载认证状态，显示加载中
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {/* 页面标题 */}
          <div className="bg-gradient-to-r from-[#2563eb] to-[#3b82f6] py-6 px-8">
            <h1 className="text-2xl font-bold text-white">Request For Quotation (RFQ)</h1>
            <p className="text-blue-100 mt-2">
              Submit your inquiry and our team will get back to you as soon as possible.
            </p>
          </div>
          
          {/* 未登录提示 */}
          {!isAuthenticated && !isLoading && (
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 m-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-yellow-700">
                    You need to <Link href="/login?redirect=rfq" className="font-medium underline hover:text-yellow-600">log in</Link> or <Link href="/register" className="font-medium underline hover:text-yellow-600">create an account</Link> to submit a request.
                  </p>
                </div>
              </div>
            </div>
          )}
          
          {/* 成功提交消息 */}
          {submitSuccess && (
            <div className="bg-green-50 border-l-4 border-green-500 p-4 m-6">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-700">
                    Your request has been submitted successfully! Our team will review it and get back to you soon.
                  </p>
                  <button 
                    onClick={() => setSubmitSuccess(false)}
                    className="mt-2 text-sm font-medium text-green-700 hover:text-green-600"
                  >
                    Submit another request
                  </button>
                </div>
              </div>
            </div>
          )}
          
          {/* 表单区域 */}
          {!submitSuccess && (
            <form onSubmit={handleSubmit} className="py-6 px-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 联系人姓名 */}
                <div>
                  <label htmlFor="contact_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Name *
                  </label>
                  <input
                    type="text"
                    id="contact_name"
                    name="contact_name"
                    value={formData.contact_name}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.contact_name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                  {formErrors.contact_name && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.contact_name}</p>
                  )}
                </div>
                
                {/* 商务邮箱 */}
                <div>
                  <label htmlFor="business_email" className="block text-sm font-medium text-gray-700 mb-1">
                    Business Email *
                  </label>
                  <input
                    type="email"
                    id="business_email"
                    name="business_email"
                    value={formData.business_email}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.business_email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                  {formErrors.business_email && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.business_email}</p>
                  )}
                </div>
                
                {/* 公司名称 */}
                <div>
                  <label htmlFor="company_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Company Name *
                  </label>
                  <input
                    type="text"
                    id="company_name"
                    name="company_name"
                    value={formData.company_name}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.company_name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                  {formErrors.company_name && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.company_name}</p>
                  )}
                </div>
                
                {/* 国家/地区 */}
                <div>
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                    Country/Region *
                  </label>
                  <div className="relative" ref={countryDropdownRef}>
                    <div 
                      className={`flex items-center w-full px-3 py-2 border ${formErrors.country ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus-within:outline-none focus-within:ring-blue-500 focus-within:border-blue-500 ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : 'cursor-pointer'}`}
                      onClick={() => !isAuthenticated ? null : setIsCountryDropdownOpen(!isCountryDropdownOpen)}
                    >
                      <input
                        type="text"
                        id="country-search"
                        placeholder="Search country..."
                        value={countrySearchTerm}
                        onChange={(e) => {
                          setCountrySearchTerm(e.target.value)
                          if (!isCountryDropdownOpen) setIsCountryDropdownOpen(true)
                        }}
                        onClick={(e) => e.stopPropagation()}
                        disabled={!isAuthenticated}
                        className="w-full focus:outline-none bg-transparent disabled:cursor-not-allowed"
                      />
                      <input type="hidden" name="country" id="country" value={formData.country} />
                      <svg 
                        className={`w-5 h-5 text-gray-400 ${isCountryDropdownOpen ? 'transform rotate-180' : ''}`}
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24" 
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                      </svg>
                    </div>
                    
                    {isCountryDropdownOpen && isAuthenticated && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {filteredCountries.length > 0 ? (
                          filteredCountries.map((country) => (
                            <div
                              key={country}
                              className={`px-4 py-2 cursor-pointer hover:bg-blue-50 ${formData.country === country ? 'bg-blue-100' : ''}`}
                              onClick={() => selectCountry(country)}
                            >
                              {country}
                            </div>
                          ))
                        ) : (
                          <div className="px-4 py-2 text-gray-500">No countries found</div>
                        )}
                      </div>
                    )}
                    
                    {formData.country && (
                      <div className="mt-2 text-sm text-blue-600">Selected: {formData.country}</div>
                    )}
                  </div>
                  {formErrors.country && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.country}</p>
                  )}
                </div>
                
                {/* 型号 */}
                <div>
                  <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
                    Model/Part Number *
                  </label>
                  <input
                    type="text"
                    id="model"
                    name="model"
                    value={formData.model}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.model ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                    placeholder="e.g. STM32F103C8T6"
                  />
                  {formErrors.model && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.model}</p>
                  )}
                </div>
                
                {/* 品牌名称 */}
                <div>
                  <label htmlFor="brand_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Brand Name *
                  </label>
                  <input
                    type="text"
                    id="brand_name"
                    name="brand_name"
                    value={formData.brand_name}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.brand_name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                  {formErrors.brand_name && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.brand_name}</p>
                  )}
                </div>
                
                {/* 数量 */}
                <div>
                  <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                    Quantity *
                  </label>
                  <input
                    type="number"
                    id="quantity"
                    name="quantity"
                    min="1"
                    value={formData.quantity}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.quantity ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  />
                  {formErrors.quantity && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.quantity}</p>
                  )}
                </div>
                
                {/* 目标价格 */}
                <div>
                  <label htmlFor="target_price" className="block text-sm font-medium text-gray-700 mb-1">
                    Target Price (USD)
                  </label>
                  <input
                    type="number"
                    id="target_price"
                    name="target_price"
                    min="0"
                    step="0.01"
                    value={formData.target_price}
                    onChange={handleInputChange}
                    disabled={!isAuthenticated}
                    className={`block w-full px-3 py-2 border ${formErrors.target_price ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                    placeholder="0.00"
                  />
                  {formErrors.target_price && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.target_price}</p>
                  )}
                </div>
                
                {/* 交期 - 将日期选择器改为天数输入框 */}
                <div>
                  <label htmlFor="delivery_time" className="block text-sm font-medium text-gray-700 mb-1">
                    Target Lead Time
                  </label>
                  <div className="relative flex items-center">
                    <input
                      type="text"
                      inputMode="numeric"
                      pattern="[0-9]*"
                      id="delivery_time"
                      name="delivery_time"
                      value={formData.delivery_time}
                      onChange={handleInputChange}
                      disabled={!isAuthenticated}
                      className={`block w-full px-3 py-2 border ${formErrors.delivery_time ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${!isAuthenticated ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                    />
                    <span className="absolute right-3 text-gray-500">days</span>
                  </div>
                  {formErrors.delivery_time && (
                    <p className="mt-1 text-sm text-red-500">{formErrors.delivery_time}</p>
                  )}
                </div>
              </div>
              
              {/* 提交按钮 */}
              <div className="mt-8 flex justify-end">
                <Link
                  href="/"
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mr-4"
                >
                  Cancel
                </Link>
                {isAuthenticated ? (
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? 'Submitting...' : 'Submit Request'}
                  </button>
                ) : (
                  <Link 
                    href="/login?redirect=rfq"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Log In to Submit
                  </Link>
                )}
              </div>
            </form>
          )}
          
          {/* 说明信息 */}
          <div className="bg-gray-50 px-8 py-6 border-t border-gray-200 text-sm text-gray-500">
            <h3 className="text-base font-medium text-gray-700 mb-3">About Request For Quotation</h3>
            <p className="mb-2">
              The Request For Quotation (RFQ) process allows you to request pricing and availability information for electronic components directly from manufacturers and distributors. Our platform connects you with reputable suppliers who can provide the parts you need.
            </p>
            <p className="mb-2">
              For best results, please provide as much detail as possible in your request. Include specific model numbers, quantity requirements, and any technical specifications that are important for your application. 
            </p>
            <p className="mb-2">
              Once submitted, your RFQ will be reviewed by our team and forwarded to relevant suppliers. You can expect to receive responses within 1-2 business days for standard components, though specialized or hard-to-find parts may take longer.
            </p>
            <p>
              All information provided is kept confidential and used only for the purpose of facilitating your component sourcing needs. For bulk orders or specialized requirements, our team may contact you directly to discuss your needs in more detail.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}