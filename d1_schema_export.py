#!/usr/bin/env python3
"""
Cloudflare D1 数据库结构导出工具
用于导出 D1 数据库中所有表的字段和结构信息
"""

import subprocess
import json
import sys
import argparse
from typing import Dict, List, Any
import csv
from datetime import datetime

class D1SchemaExporter:
    def __init__(self, database_name: str, remote: bool = True):
        self.database_name = database_name
        self.remote = remote
        self.base_command = ["npx", "wrangler", "d1", "execute", database_name]
        if remote:
            self.base_command.append("--remote")
    
    def execute_sql(self, sql: str) -> Dict[str, Any]:
        """执行 SQL 命令并返回结果"""
        try:
            command = self.base_command + ["--command", sql, "--json"]
            # 在 Windows 上使用 shell=True 来确保能找到 npx
            result = subprocess.run(command, capture_output=True, text=True, check=True, shell=True)
            data = json.loads(result.stdout)
            # wrangler 返回的是数组，取第一个元素
            if isinstance(data, list) and len(data) > 0:
                return data[0]
            return data
        except subprocess.CalledProcessError as e:
            print(f"执行 SQL 命令失败: {sql}")
            print(f"错误信息: {e.stderr}")
            print(f"执行的命令: {' '.join(command)}")
            return {"results": []}
        except json.JSONDecodeError as e:
            print(f"解析 JSON 结果失败: {e}")
            print(f"原始输出: {result.stdout}")
            return {"results": []}
    
    def get_all_tables(self) -> List[str]:
        """获取所有用户表名（排除系统表）"""
        sql = "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_cf_%'"
        result = self.execute_sql(sql)
        return [row["name"] for row in result.get("results", [])]
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表的字段信息"""
        sql = f"PRAGMA table_info('{table_name}')"
        result = self.execute_sql(sql)
        return result.get("results", [])
    
    def get_table_indexes(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表的索引信息"""
        sql = f"PRAGMA index_list('{table_name}')"
        result = self.execute_sql(sql)
        return result.get("results", [])
    
    def get_create_statements(self) -> List[Dict[str, Any]]:
        """获取所有建表语句"""
        sql = "SELECT type, name, sql FROM sqlite_master WHERE sql IS NOT NULL AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_cf_%' ORDER BY type, name"
        result = self.execute_sql(sql)
        return result.get("results", [])
    
    def export_to_json(self, output_file: str = None):
        """导出为 JSON 格式"""
        if not output_file:
            output_file = f"{self.database_name}_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        schema_data = {
            "database_name": self.database_name,
            "export_time": datetime.now().isoformat(),
            "tables": {},
            "create_statements": self.get_create_statements()
        }
        
        tables = self.get_all_tables()
        print(f"发现 {len(tables)} 个表")
        
        for table_name in tables:
            print(f"正在处理表: {table_name}")
            table_info = self.get_table_info(table_name)
            table_indexes = self.get_table_indexes(table_name)
            
            schema_data["tables"][table_name] = {
                "columns": table_info,
                "indexes": table_indexes
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(schema_data, f, indent=2, ensure_ascii=False)
        
        print(f"结构信息已导出到: {output_file}")
        return output_file
    
    def export_to_csv(self, output_file: str = None):
        """导出为 CSV 格式"""
        if not output_file:
            output_file = f"{self.database_name}_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        tables = self.get_all_tables()
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['表名', '字段名', '数据类型', '是否非空', '默认值', '是否主键', '字段序号'])
            
            for table_name in tables:
                print(f"正在处理表: {table_name}")
                table_info = self.get_table_info(table_name)
                
                for column in table_info:
                    writer.writerow([
                        table_name,
                        column.get('name', ''),
                        column.get('type', ''),
                        '是' if column.get('notnull') == 1 else '否',
                        column.get('dflt_value', ''),
                        '是' if column.get('pk') == 1 else '否',
                        column.get('cid', '')
                    ])
        
        print(f"结构信息已导出到: {output_file}")
        return output_file
    
    def export_to_sql(self, output_file: str = None):
        """导出为 SQL 格式"""
        if not output_file:
            output_file = f"{self.database_name}_schema_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
        
        create_statements = self.get_create_statements()
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"-- Cloudflare D1 数据库结构导出\n")
            f.write(f"-- 数据库名称: {self.database_name}\n")
            f.write(f"-- 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for stmt in create_statements:
                if stmt.get('sql'):
                    f.write(f"-- {stmt.get('type', '').upper()}: {stmt.get('name', '')}\n")
                    f.write(f"{stmt['sql']};\n\n")
        
        print(f"SQL 结构已导出到: {output_file}")
        return output_file
    
    def print_summary(self):
        """打印数据库结构摘要"""
        tables = self.get_all_tables()
        print(f"\n=== {self.database_name} 数据库结构摘要 ===")
        print(f"总表数: {len(tables)}")
        
        for table_name in tables:
            table_info = self.get_table_info(table_name)
            indexes = self.get_table_indexes(table_name)
            print(f"\n表名: {table_name}")
            print(f"  字段数: {len(table_info)}")
            print(f"  索引数: {len(indexes)}")
            
            if table_info:
                print("  字段列表:")
                for col in table_info:
                    pk_mark = " [PK]" if col.get('pk') == 1 else ""
                    null_mark = " NOT NULL" if col.get('notnull') == 1 else ""
                    print(f"    - {col.get('name', '')} ({col.get('type', '')}){pk_mark}{null_mark}")

def main():
    parser = argparse.ArgumentParser(description='导出 Cloudflare D1 数据库结构')
    parser.add_argument('database_name', help='D1 数据库名称')
    parser.add_argument('--local', action='store_true', help='使用本地数据库（默认使用远程）')
    parser.add_argument('--format', choices=['json', 'csv', 'sql', 'all'], default='json', 
                       help='导出格式 (默认: json)')
    parser.add_argument('--output', help='输出文件名（可选）')
    parser.add_argument('--summary', action='store_true', help='显示数据库结构摘要')
    
    args = parser.parse_args()
    
    try:
        exporter = D1SchemaExporter(args.database_name, remote=not args.local)
        
        if args.summary:
            exporter.print_summary()
        
        if args.format == 'json':
            exporter.export_to_json(args.output)
        elif args.format == 'csv':
            exporter.export_to_csv(args.output)
        elif args.format == 'sql':
            exporter.export_to_sql(args.output)
        elif args.format == 'all':
            base_name = args.output.rsplit('.', 1)[0] if args.output else None
            exporter.export_to_json(f"{base_name}.json" if base_name else None)
            exporter.export_to_csv(f"{base_name}.csv" if base_name else None)
            exporter.export_to_sql(f"{base_name}.sql" if base_name else None)
        
    except KeyboardInterrupt:
        print("\n操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
