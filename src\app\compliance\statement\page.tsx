'use client'

import Link from 'next/link'

// 使用内联SVG图标
const FileTextIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 2v6h6M16 13H8M16 17H8M10 9H8" />
  </svg>
)

const ArrowLeftIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 12H5M12 19l-7-7 7-7" />
  </svg>
)

export default function ComplianceStatementPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link
            href="/compliance"
            className="inline-flex items-center gap-2 text-[#DE2910] hover:text-[#DE2910]/80 transition-colors"
          >
            <ArrowLeftIcon className="w-4 h-4" />
            Back to Compliance Form
          </Link>
        </div>

        {/* 页面标题 */}
        <div className="flex items-center gap-3 mb-8">
          <FileTextIcon className="w-8 h-8 text-[#DE2910]" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Customer Compliance Statement</h1>
            <p className="text-gray-600 mt-1">Last modified: January 29, 2024</p>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
          <div className="prose prose-gray max-w-none">
            <p className="text-lg text-gray-700 mb-8">
              In order to minimize potential business risks and damages of all parties to the transaction arising from violations of export control and sanctions, I/We and all our affiliated companies (hereinafter collectively referred to as the "I") confirm and agree that the products subject to the export restrictions of the country of origin I have purchased or will purchase from <strong>China Electron Technology (HK) Limited</strong> or its subsidiaries or affiliated companies (collectively referred to as the "China Electron") shall comply with the followings:
            </p>

            <div className="space-y-6">
              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">1. Export Compliance Laws</h3>
                <p className="text-gray-700">
                  I/we understand that China Electron's products are subject to export restrictions to certain destinations, end-uses and end-users beyond China's jurisdiction. I agree to comply with all applicable export compliance laws and regulations applicable to the United Nations (U.N.), the United States (U.S.) or the European Union (EU) and any other applicable countries/region.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">2. Transfer Restrictions</h3>
                <p className="text-gray-700">
                  Unless approved or licensed for export by the appropriate government agency, I will not transfer (including direct or indirect use, export, re-export or sale, etc) any of China Electron's Products directly or indirectly to any embargoed or sanctioned country/region, jurisdiction, person, enterprise, organization or entity. I confirm that all of my Customers are not included in (1) the U.S. Treasury's Special Designated National List ("SDN"), or more than 50% of the shares are owned by individuals or entities on the SDN; nor (2) the U.S. Department of Commerce's Denied Parties List.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">3. SDN Compliance</h3>
                <p className="text-gray-700">
                  I warrant and covenant that: (a) I am not owned or controlled by, or acting on behalf or for the benefit of, any entity of SDN, and (b) no entity subject to SDN will be involved in any transaction with China Electron.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">4. Prohibited End-Uses</h3>
                <p className="text-gray-700">
                  I warrant and covenant that: any of China Electron's Products will not be used for chemical, biological or nuclear weapons, rocket systems (including ballistic missile systems, military space launchers and sounding rockets) or UAVs capable of launching chemical, biological or nuclear weapons, development of weapons of mass destruction, the products will not be used for internal repression or human rights violations, or any other end-use prohibited by applicable law.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">5. Advanced Computing Restrictions</h3>
                <p className="text-gray-700">
                  The products obtained by me from China Electron that are not labeled as EAR99 will not be used to manufacture advanced computing integrated circuits, computer commodity that contains such integrated circuits, and semiconductor manufacturing items as stipulated in Sec. 740.2, Sec. 740.10, Sec. 742.6, Sec. 744.23 and supplement no.1 to part 774.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">6. Prohibited Countries</h3>
                <p className="text-gray-700">
                  I/We will not export or re-export China Electron's products to Belarus, Cuba, Iran, North Korea, Russia, Russian-occupied Ukraine and Syria.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">7. Documentation Requirements</h3>
                <p className="text-gray-700">
                  In order to comply with export compliance laws and regulations, when China Electron requires me to provide import/export information and supporting documents relating to these products, I agree to provide all of China Electron's requests.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">8. Change Notification</h3>
                <p className="text-gray-700">
                  In the event of any change in the above statement or my/our failure to continue to comply with the above commitments, I/we will immediately notify China Electron in writing.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">9. Order Acknowledgment</h3>
                <p className="text-gray-700">
                  I/We understand and confirm that: the Act of continuing to submit the order at China Electron represents that I understand and agree to comply with the contents of the Statement; the order may be cancelled based on the above mentioned matters.
                </p>
              </div>

              <div className="border-l-4 border-[#DE2910] pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">10. Statement Validity</h3>
                <p className="text-gray-700">
                  This Compliance Statement is valid for all completed orders or upcoming orders and until further notice or renewal.
                </p>
              </div>
            </div>

            <div className="mt-12 p-6 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Important Notice</h3>
              <p className="text-gray-700">
                By proceeding with any purchase from China Electron, you acknowledge that you have read, understood, and agree to comply with all terms and conditions outlined in this Customer Compliance Statement. Failure to comply with these requirements may result in order cancellation and potential legal consequences.
              </p>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
            <Link
              href="/compliance"
              className="inline-flex items-center gap-2 text-[#DE2910] hover:text-[#DE2910]/80 transition-colors"
            >
              <ArrowLeftIcon className="w-4 h-4" />
              Back to Compliance Form
            </Link>
            
            <div className="flex gap-4">
              <button
                onClick={() => window.print()}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Print Statement
              </button>
              <Link
                href="/compliance"
                className="px-6 py-2 bg-[#DE2910] text-white rounded-lg hover:bg-[#DE2910]/90 transition-colors"
              >
                I Understand & Agree
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
