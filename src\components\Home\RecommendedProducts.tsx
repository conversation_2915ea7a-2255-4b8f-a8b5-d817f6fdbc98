import Image from 'next/image'

export default function RecommendedProducts() {
  const products = [
    {
      id: 1,
      name: 'BG95M3LA-64-SGNS',
      manufacturer: 'Quectel',
      image: 'https://dummyimage.com/400x300/0057b8/ffffff.png&text=BG95M3LA',
      description: 'Multi-mode LPWA module'
    },
    {
      id: 2,
      name: 'STM32F103C8T6',
      manufacturer: 'STMicroelectronics',
      image: 'https://dummyimage.com/400x300/0057b8/ffffff.png&text=STM32',
      description: 'ARM Cortex-M3 MCU'
    },
    {
      id: 3,
      name: 'ESP32-WROOM-32',
      manufacturer: 'Espressif',
      image: 'https://dummyimage.com/400x300/0057b8/ffffff.png&text=ESP32',
      description: 'WiFi & Bluetooth Module'
    },
    {
      id: 4,
      name: 'MAX232CPE',
      manufacturer: 'Maxim',
      image: 'https://dummyimage.com/400x300/0057b8/ffffff.png&text=MAX232',
      description: 'RS-232 Interface IC'
    }
  ]

  return (
    <section className="py-8">
      <div className="max-w-7xl mx-auto px-4">
        <h2 className="text-2xl font-bold mb-6">Recommended for You</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {products.map((product) => (
            <div key={product.id} className="border rounded-lg p-4 hover:shadow-lg transition-shadow">
              <div className="relative h-48 mb-4">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-contain"
                />
              </div>
              <h3 className="font-medium">{product.name}</h3>
              <p className="text-sm text-gray-600">{product.manufacturer}</p>
              <p className="text-sm text-gray-500 mt-2">{product.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
} 