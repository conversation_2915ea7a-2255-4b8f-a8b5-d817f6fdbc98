/**
 * Spotlight Layout
 * 
 * This layout is applied to all pages under the /spotlight route
 * It provides consistent structure and styling for all spotlight pages
 */

import { ReactNode } from 'react'
import Link from 'next/link'
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

interface SpotlightLayoutProps {
  children: ReactNode
}

export const metadata: Metadata = {
  title: 'Latest Electronic Industry News & Spotlights | China Electron',
  description: 'Stay updated with the latest news, announcements, and spotlights in the electronic components and raw materials industry on China Electron.',
  keywords: 'industry news, electronic spotlights, component updates, raw material news, China Electron, tech news',
  openGraph: {
    title: 'Latest Electronic Industry News & Spotlights | China Electron',
    description: 'Stay updated with the latest news, announcements, and spotlights in the electronic components and raw materials industry.',
    images: ['/images/spotlight-og.jpg'],
    type: 'website',
  },
  alternates: {
    canonical: getCanonicalUrl('spotlight'),
  },
}

export default function SpotlightLayout({ children }: SpotlightLayoutProps) {
  return (
    <main className="min-h-screen flex flex-col">
      {/* Navigation breadcrumb can be added here if needed */}
      
      {/* Content area */}
      <div className="flex-grow">
        {children}
      </div>
    </main>
  )
} 