'use client';

import React from 'react';
// 使用内联SVG图标替代@heroicons/react以减少编译负担
const ShieldCheckIcon = ({ className }: { className?: string }) => (
  <svg className={className || "w-8 h-8"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285z" />
  </svg>
)

const TruckIcon = ({ className }: { className?: string }) => (
  <svg className={className || "w-8 h-8"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.25 18.75a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 01-1.125-1.125V14.25m15.75 4.5a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m3 0h1.125A1.125 1.125 0 0021 17.625v-2.25m-8.25-6.75h7.5m-7.5 3h7.5m-8.25-3h.375a1.125 1.125 0 011.125 1.125v4.5c0 .621-.504 1.125-1.125 1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
  </svg>
)

const ChartBarIcon = ({ className }: { className?: string }) => (
  <svg className={className || "w-8 h-8"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z" />
  </svg>
)

const GlobeAltIcon = ({ className }: { className?: string }) => (
  <svg className={className || "w-8 h-8"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3s-4.5 4.03-4.5 9 2.015 9 4.5 9zm0 0c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3s4.5 4.03 4.5 9-2.015 9-4.5 9zm-9-9a9 9 0 1118 0 9 9 0 01-18 0z" />
  </svg>
)

const ArrowPathIcon = ({ className }: { className?: string }) => (
  <svg className={className || "w-8 h-8"} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
  </svg>
)

interface TrustItem {
  id: string;
  icon: React.ReactNode;
  title: string;
  description: string;
}

const TrustLogos: React.FC = () => {
  const trustItems: TrustItem[] = [
    {
      id: 'authentic-products',
      icon: <ShieldCheckIcon className="w-12 h-12 text-red-500" />,
      title: '100% Authentic Products',
      description: 'Only parts from trusted brands'
    },
    {
      id: 'fast-delivery',
      icon: <TruckIcon className="w-12 h-12 text-red-500" />,
      title: 'Fast Delivery',
      description: 'Free Shipping over $499'
    },
    {
      id: 'stock-availability',
      icon: <ChartBarIcon className="w-12 h-12 text-red-500" />,
      title: '600,000+ In Stock',
      description: 'High-Quality Asian Brands'
    },
    {
      id: 'authorized-brands',
      icon: <GlobeAltIcon className="w-12 h-12 text-red-500" />,
      title: '670+ Authorised Brands',
      description: 'Dedicated Support'
    },
    {
      id: 'return-guarantee',
      icon: <ArrowPathIcon className="w-12 h-12 text-red-500" />,
      title: '30-Day Return Guarantee',
      description: '30 days open purchase'
    }
  ];

  return (
    <section className="relative w-screen ml-[calc(-50vw+50%)] py-12 bg-white">

      <div className="max-w-[1900px] mx-auto px-4 lg:px-14 relative">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 lg:gap-8">
          {trustItems.map((item, index) => (
            <div
              key={item.id}
              className="group flex flex-col items-center text-center p-6 rounded-lg bg-white border border-gray-100 shadow-sm hover:shadow-lg hover:border-gray-200 transition-all duration-300 hover:-translate-y-1"
              style={{
                animationDelay: `${index * 100}ms`
              }}
            >
              {/* 图标容器 */}
              <div className="mb-4 p-3 rounded-lg bg-red-50 group-hover:bg-red-100 transition-colors duration-300">
                {item.icon}
              </div>
              
              {/* 标题 */}
              <h3 className="text-gray-900 font-semibold text-base lg:text-lg mb-2 leading-tight">
                {item.title}
              </h3>
              
              {/* 描述 */}
              <p className="text-gray-600 text-sm leading-relaxed">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TrustLogos;
