'use client';

import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { generateBreadcrumbs, type BreadcrumbItem } from '@/utils/breadcrumbs';
import Link from 'next/link';

export default function ServerBreadcrumbWrapper() {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function fetchBreadcrumbs() {
      try {
        setIsLoading(true);
        const result = await generateBreadcrumbs(pathname);
        setBreadcrumbs(result);
      } catch (error) {
        console.error('Error fetching breadcrumbs:', error);
        setBreadcrumbs([]);
      } finally {
        setIsLoading(false);
      }
    }

    fetchBreadcrumbs();
  }, [pathname]);

  // 不在首页显示面包屑
  if (pathname === '/') {
    return null;
  }

  // 加载状态
  if (isLoading) {
    return (
      <nav className="" aria-label="Breadcrumb">
        <div className="px-4 lg:px-14 py-3">
          <div className="animate-pulse h-4 w-48 bg-gray-200 rounded"></div>
        </div>
      </nav>
    );
  }

  // 如果只有首页，不显示面包屑
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <div className="w-full">
      <div className="max-w-[1900px] mx-auto">
        <nav className="" aria-label="Breadcrumb">
          <div className="px-4 lg:px-14 py-3">
            <ol className="flex items-center space-x-2 text-sm">
              {breadcrumbs.map((item, index) => (
                <li key={`breadcrumb-${index}-${item.path}`} className="flex items-center">
                  {index > 0 && (
                    <span className="mx-2 text-gray-400">/</span>
                  )}
                  {index === breadcrumbs.length - 1 ? (
                    <span className="text-gray-500">{item.label}</span>
                  ) : (
                    <Link
                      href={item.path}
                      className="text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      {item.label}
                    </Link>
                  )}
                </li>
              ))}
            </ol>
          </div>
        </nav>
      </div>
    </div>
  );
}
