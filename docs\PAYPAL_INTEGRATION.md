# PayPal支付集成文档

## 概述

本项目已集成PayPal支付功能，支持在订单详情页面进行在线支付。集成包括：

- PayPal沙盒环境配置
- 订单创建和支付捕获API
- 前端支付按钮组件
- 订单状态自动更新

## 功能特性

### 1. 支付流程
1. 用户在订单详情页面看到待支付订单
2. 点击PayPal支付按钮
3. 跳转到PayPal支付页面完成支付
4. 支付成功后自动返回订单页面
5. 订单状态自动更新为"已支付"

### 2. 支持的支付方式
- PayPal账户支付
- 信用卡/借记卡支付（通过PayPal处理）

### 3. 安全特性
- 使用PayPal官方SDK
- 沙盒环境测试
- 服务器端支付验证
- 订单状态同步更新

## 技术实现

### 数据库表结构

#### payment表 - 支付记录表
```sql
CREATE TABLE IF NOT EXISTS payment (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    payment_method TEXT NOT NULL,
    transaction_number TEXT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    payment_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status INTEGER DEFAULT 0,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

支付状态说明：
- `0` - 待处理 (Pending)
- `1` - 成功 (Success)
- `2` - 失败 (Failed)
- `3` - 退款 (Refunded)

### API端点

#### 1. 创建PayPal订单
```
POST /api/paypal/orders
```

请求体：
```json
{
  "order_id": "ORD20250627001",
  "amount": 299.99,
  "currency": "USD"
}
```

响应：
```json
{
  "id": "paypal_order_id",
  "status": "CREATED",
  "links": [...]
}
```

#### 2. 捕获PayPal支付
```
POST /api/paypal/orders/{orderID}/capture
```

响应：
```json
{
  "id": "capture_id",
  "status": "COMPLETED",
  "purchase_units": [...],
  "payer": {...}
}
```

**注意**: 支付捕获成功后，系统会自动：
1. 创建支付记录到 `payment` 表
2. 更新订单状态为 `PAID`

#### 3. 检查/更新支付状态
```
POST /api/orders/{orderId}/payment-status
```

请求体：
```json
{
  "paypal_transaction_id": "9XF7978138827292X",
  "force_update": false
}
```

响应：
```json
{
  "success": true,
  "order_status": "PAID",
  "payment_method": "PayPal",
  "updated": true,
  "payment_record_created": true
}
```

#### 4. 更新订单状态（旧接口）
```
PUT /api/orders/{orderId}/status
```

请求体：
```json
{
  "status": "PAID",
  "payment_method": "PayPal"
}
```

### 前端组件

#### PayPalButton组件
位置：`src/components/PayPalButton.tsx`

属性：
- `orderId`: 订单ID
- `amount`: 支付金额
- `currency`: 货币类型（默认USD）
- `onSuccess`: 支付成功回调
- `onError`: 支付错误回调
- `onCancel`: 支付取消回调
- `disabled`: 是否禁用

使用示例：
```tsx
<PayPalButton
  orderId={order.order_id}
  amount={order.order_total}
  currency="USD"
  onSuccess={handlePaymentSuccess}
  onError={handlePaymentError}
  onCancel={handlePaymentCancel}
/>
```

## 配置说明

### 环境变量
创建`.env.local`文件并配置以下变量：

```env
# PayPal沙盒配置
PAYPAL_CLIENT_ID=your_sandbox_client_id
PAYPAL_CLIENT_SECRET=your_sandbox_client_secret

# 应用基础URL
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# 环境设置
NODE_ENV=development
```

### PayPal沙盒账户
当前使用的测试账户信息（来自paypal.txt）：
- 商户账户：<EMAIL>
- 密码：O.9Msbd/
- Client ID：ARl9l5SwhfEJEFlrbqeqySOdEASq8IjwBNyaEWqYIBLJUQyMZkjxO4kV__23pqWz2VW-_76FYEI48Fe1
- Client Secret：EPr-ojaMW7uYkhN7jNHWRZOWeJDBRpYbLXHN95DF4_fPPtC1nh1prxxxCkkMw7NMQ0rVzNanaZYempHX

## 订单状态说明

### 支付前状态
- `PENDING_PAYMENT`: 待支付
- `pending`: 待处理（旧格式）

### 支付后状态
- `PAID`: 已支付
- `confirmed`: 已确认
- `processing`: 处理中
- `shipped`: 已发货
- `delivered`: 已送达

## 测试指南

### 1. 测试支付流程
1. 创建一个测试订单（状态为PENDING_PAYMENT）
2. 访问订单详情页面
3. 点击PayPal支付按钮
4. 使用PayPal沙盒测试账户完成支付
5. 验证订单状态是否更新为PAID

### 2. 测试账户
PayPal提供的测试买家账户：
- 邮箱：<EMAIL>
- 密码：通常为自动生成

### 3. 错误处理测试
- 测试支付取消场景
- 测试网络错误场景
- 测试无效订单ID场景

## 生产环境部署

### 1. 更新环境变量
将沙盒配置替换为生产环境配置：
```env
NODE_ENV=production
PAYPAL_CLIENT_ID=your_production_client_id
PAYPAL_CLIENT_SECRET=your_production_client_secret
NEXT_PUBLIC_BASE_URL=https://your-domain.com
```

### 2. PayPal应用审核
确保PayPal应用已通过审核并启用生产环境。

### 3. SSL证书
确保网站使用HTTPS，PayPal要求生产环境必须使用安全连接。

## 故障排除

### 常见问题

1. **支付按钮不显示**
   - 检查订单状态是否为待支付
   - 检查PayPal SDK是否正确加载
   - 检查控制台错误信息

2. **支付失败**
   - 检查PayPal配置是否正确
   - 检查网络连接
   - 查看服务器日志

3. **订单状态未更新**
   - 检查数据库API连接
   - 检查订单ID是否正确
   - 查看API响应日志
   - 使用页面上的"Refresh Status"按钮手动刷新

4. **Next.js 15 动态API参数错误**
   - 确保所有动态路由参数使用 `await params`
   - 参数类型应为 `Promise<{ paramName: string }>`

5. **数据库连接超时**
   - API请求已配置30-45秒超时
   - 支付成功但状态更新失败不影响用户体验
   - 可以手动刷新订单状态

### 调试技巧
- 使用浏览器开发者工具查看网络请求
- 检查服务器端日志
- 使用PayPal开发者工具调试
- 查看控制台中的详细错误信息

### 已修复的问题

1. **Next.js 15兼容性**
   - 修复了动态路由参数的async/await问题
   - 更新了所有API路由以支持新的参数格式

2. **网络超时处理**
   - 添加了请求超时控制（30-45秒）
   - 改进了错误处理和用户反馈
   - 支付成功后自动重试状态更新

3. **用户体验改进**
   - 添加了手动刷新状态按钮
   - 改进了错误消息显示
   - 支付成功后多次尝试状态更新

## 安全注意事项

1. **敏感信息保护**
   - 不要在前端暴露Client Secret
   - 使用环境变量存储配置
   - 定期更新API密钥

2. **支付验证**
   - 服务器端验证所有支付
   - 检查支付金额和订单信息
   - 防止重复支付

3. **错误处理**
   - 优雅处理支付错误
   - 记录所有支付相关日志
   - 提供用户友好的错误信息

## 测试工具

### 数据库连接测试
访问 `/test-db-connection` 页面来测试数据库连接状态：
- 测试基本连接
- 测试订单状态更新
- 查看详细的错误信息和响应时间

### PayPal支付测试
访问 `/test-paypal` 页面来测试PayPal支付功能：
- 使用测试订单进行支付
- 验证支付流程
- 测试错误处理

## 数据库连接问题解决方案

### 问题描述
数据库API (`https://api.chinaelectron.com/api`) 偶尔会出现连接超时问题，导致订单状态更新失败。

### 解决方案
1. **异步状态更新**: 支付捕获成功后，订单状态更新在后台异步进行，不阻塞支付响应
2. **重试机制**: 自动重试失败的状态更新（最多3次）
3. **超时控制**: 设置合理的超时时间（15秒）
4. **用户界面改进**:
   - 支付成功后显示状态更新进度
   - 提供手动刷新和强制更新按钮
   - 多次自动尝试状态检查

### API端点
- `POST /api/orders/{orderId}/payment-status` - 检查并更新支付状态
- `GET /api/test-db` - 测试数据库连接
- `POST /api/test-db` - 测试订单状态更新

### 支付记录API
```javascript
// 获取订单支付记录
const paymentRecords = await getOrderPaymentRecords('ORD20250627001')

// 获取用户支付记录
const userPayments = await getUserPaymentRecords('user_001', 20)

// 根据交易号查询支付记录
const payment = await getPaymentRecordByTransaction('9XF7978138827292X')

// 创建支付记录
const result = await createPaymentRecord({
  order_id: 'ORD20250627001',
  user_id: 'user_001',
  payment_method: 'PayPal',
  transaction_number: '9XF7978138827292X',
  amount: 299.99,
  payment_date: new Date().toISOString(),
  status: 1, // 成功
  remarks: 'PayPal payment completed'
})

// 更新支付记录状态
const updateResult = await updatePaymentRecordStatus(
  '9XF7978138827292X',
  1, // 成功状态
  'Payment confirmed'
)
```

## 扩展功能

### 未来可能的改进
1. 支持多种货币
2. 添加退款功能
3. 集成其他支付方式
4. 支付状态实时通知
5. 支付报表和分析
6. 数据库连接池和负载均衡
7. 支付状态同步队列系统
