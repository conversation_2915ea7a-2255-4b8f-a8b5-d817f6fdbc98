/**
 * Turnstile 验证服务
 * 用于验证 Cloudflare Turnstile 生成的令牌
 */

/**
 * 验证 Turnstile 令牌
 * @param token Turnstile Widget 生成的令牌
 * @param secretKey Turnstile 密钥
 * @returns 验证结果，包含是否成功和错误消息
 */
export async function verifyTurnstileToken(token: string, secretKey: string): Promise<{ success: boolean; message?: string }> {
  try {
    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        secret: secretKey,
        response: token,
      }),
    });

    const data = await response.json();

    if (data.success) {
      return { success: true };
    } else {
      // 验证失败，返回错误消息
      return {
        success: false,
        message: data['error-codes']?.join(', ') || 'Verification failed',
      };
    }
  } catch (error) {
    console.error('Turnstile verification error:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Verification service error',
    };
  }
} 