'use client';

import { useState, useRef, useEffect } from 'react';

type SubscriptionStatus = 'idle' | 'loading' | 'success' | 'error';

const EmailSubscriptionForm = () => {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<SubscriptionStatus>('idle');
  const [errorMessage, setErrorMessage] = useState('');
  const [lastSubmitTime, setLastSubmitTime] = useState<number | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  
  // 常量配置
  const DEBOUNCE_DELAY = 300; // 输入防抖延迟时间(ms)
  const THROTTLE_DELAY = 10000; // 提交节流延迟时间(ms)，防止频繁调用API
  
  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);
  
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newEmail = e.target.value;
    setEmail(newEmail);
    
    // 清除之前的错误消息
    if (status === 'error') {
      setStatus('idle');
      setErrorMessage('');
    }
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证邮箱格式
    if (!email.trim()) {
      setStatus('error');
      setErrorMessage('Please enter your email address');
      return;
    }
    
    if (!validateEmail(email)) {
      setStatus('error');
      setErrorMessage('Please enter a valid email address');
      return;
    }
    
    // 检查节流 - 防止频繁调用API
    const now = Date.now();
    if (lastSubmitTime && (now - lastSubmitTime < THROTTLE_DELAY)) {
      setStatus('error');
      setErrorMessage(`Please wait ${Math.ceil((THROTTLE_DELAY - (now - lastSubmitTime)) / 1000)} seconds before trying again`);
      return;
    }
    
    // 更新提交时间，开始加载状态
    setLastSubmitTime(now);
    setStatus('loading');
    
    try {
      // 使用API路由进行邮件订阅，而不是直接调用外部API
      // 这样可以隐藏实际的API端点和认证信息
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to subscribe');
      }
      
      // 成功订阅
      setStatus('success');
      setEmail(''); // 清空输入
    } catch (error) {
      // 处理错误
      setStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'An unexpected error occurred');
    }
  };
  
  return (
    <div className="max-w-2xl mx-auto">
      <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row justify-center items-center gap-4">
        <div className="w-full sm:w-3/4 relative">
          <input 
            type="email" 
            value={email}
            onChange={handleEmailChange}
            placeholder="Enter your email" 
            disabled={status === 'loading' || status === 'success'}
            className="w-full py-3 px-6 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm" 
          />
          {status === 'error' && (
            <p className="absolute text-left text-red-500 text-sm mt-1">{errorMessage}</p>
          )}
        </div>
        <button 
          type="submit"
          disabled={status === 'loading' || status === 'success'}
          className={`w-full sm:w-auto py-3 px-8 font-medium rounded-lg shadow-sm transition-colors ${
            status === 'loading' 
              ? 'bg-gray-400 text-white cursor-not-allowed' 
              : status === 'success'
                ? 'bg-green-600 text-white cursor-default'
                : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {status === 'loading' ? 'Subscribing...' : status === 'success' ? 'Subscribed ✓' : 'Subscribe'}
        </button>
      </form>
      
      {status === 'success' && (
        <p className="text-green-600 mt-4">Thank you for subscribing! We've sent a confirmation to your email.</p>
      )}
    </div>
  );
};

export default EmailSubscriptionForm; 