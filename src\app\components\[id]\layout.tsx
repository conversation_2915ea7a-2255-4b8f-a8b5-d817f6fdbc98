import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export async function generateMetadata(
  props: { params: Promise<{ id: string }> }
): Promise<Metadata> {
  // 获取异步参数
  const params = await props.params;
  
  return {
    title: `${params.id} | Electronic Component Details | China Electron`,
    description: `View detailed specifications, technical data, and supplier information for ${params.id} on China Electron.`,
    keywords: `${params.id}, electronic component details, component specifications, technical data, datasheet, China Electron`,
    alternates: {
      canonical: getCanonicalUrl(`components/${params.id}`),
    },
  }
}

export default function ComponentDetailLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
} 