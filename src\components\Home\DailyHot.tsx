/**
 * DailyHot Component
 * 
 * A client component that displays hot product items.
 * This component supports both server-side rendering and client-side data fetching:
 * 
 * 1. Server-Side Rendering: 
 *    - Accepts initialItems prop containing pre-fetched data from the server
 *    - Uses this data for initial render, improving SEO and reducing content shift
 * 
 * 2. Client-Side Features:
 *    - Static list displaying top 8 items
 *    - "More" link to view all items
 *    - Error handling with retry functionality
 *    - Loading, error, and empty states
 */

'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { buildProductUrl } from '@/utils/productUrl'

// Interface for hot item data
interface HotItem {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_en: string
  brand_cn: string
  full?: {
    url?: string // 添加url字段到full对象
    category_names?: {
      [key: string]: {
        name_cn: string
        name_en: string
        name_ru: string
      }
    }
    category_path?: string[]
    brand?: {
      name_en: string
      name_cn: string
    }
  }
}

// Component props interface
interface DailyHotProps {
  initialItems?: HotItem[] // Add initialItems for server-side rendering
}

export default function DailyHot({ initialItems = [] }: DailyHotProps) {
  // State for hot items data - initialize with server-provided data
  const [hotItems, setHotItems] = useState<HotItem[]>(initialItems)
  // State for tracking loading and error
  const [isLoading, setIsLoading] = useState<boolean>(initialItems.length === 0)
  const [error, setError] = useState<string | null>(null)

  /**
   * Function to fetch hot items data from API
   * Used for initial client-side fetch
   */
  const fetchHotItems = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // 使用内部 API 路由而不是直接调用外部 API
      const response = await fetch('/api/hot-items', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        cache: 'no-store'
      })

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (data && data.success && Array.isArray(data.data)) {
        // Only take the first 8 items
        setHotItems(data.data.slice(0, 8))
      } else {
        throw new Error('Invalid data format received from server')
      }
    } catch (error: any) {
      console.error('Error fetching hot items:', error)

      // 如果有初始数据，使用初始数据而不显示错误
      if (initialItems.length > 0) {
        setHotItems(initialItems.slice(0, 12))
        setError(null) // 清除错误状态
      } else {
        // 只有在没有任何数据时才显示错误
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          setError('Network error. Please check your connection.')
        } else if (!navigator.onLine) {
          setError('No internet connection. Please check your network.')
        } else {
          setError(error.message || 'Failed to load hot items')
        }
      }
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * Effect for initial data fetching
   * Only fetches when no initialItems are provided
   */
  useEffect(() => {
    // 添加延迟以确保组件完全挂载
    const timer = setTimeout(() => {
      // Only fetch data on client-side if no initialItems were provided
      if (initialItems.length === 0) {
        fetchHotItems()
      } else {
        // Ensure we only display the first 12 items from initialItems
        setHotItems(initialItems.slice(0, 12))
        setIsLoading(false) // Ensure loading is false when using initialItems
      }
    }, 100) // 100ms 延迟

    return () => clearTimeout(timer)
  }, []) // Remove initialItems from dependencies to prevent re-fetching

  return (
    <div className="relative aspect-[9/15.8] w-full bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">

      {/* Header with title and More link */}
      <div className="absolute top-0 left-0 right-0 flex items-center justify-between px-5 py-3 border-b border-gray-100 bg-white z-10">
        <h3 className="text-base font-bold text-gray-900">Daily Hot</h3>
        <Link
          href="/dailyhot"
          className="text-blue-600 text-sm hover:text-blue-700 flex items-center"
        >
          More <span className="ml-1">›</span>
        </Link>
      </div>
      
      {/* Content area with hidden scrollbar */}
      <div className="absolute top-[52px] left-0 right-0 bottom-0 px-4 py-2 overflow-y-auto [scrollbar-width:none] [-ms-overflow-style:none] [&::-webkit-scrollbar]:hidden">
        {/* Loading state */}
        {isLoading && (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        )}

        {/* Error state */}
        {error && !isLoading && (
          <div className="h-full flex items-center justify-center p-4 text-center">
            <p className="text-red-500">{error}</p>
          </div>
        )}

        {/* No data state */}
        {!error && !isLoading && hotItems.length === 0 && (
          <div className="h-full flex items-center justify-center p-4 text-center">
            <p className="text-gray-500">No hot items available</p>
          </div>
        )}
        
        {/* Data display with hover effects */}
        {!error && !isLoading && hotItems.length > 0 && (
          <div className="space-y-1.5">
            {hotItems.map((item, index) => (
              <Link
                href={item.url || item.full?.url || buildProductUrl(item)}
                key={`${item.product_id}-${index}`}
                className="block py-2 px-3 group hover:bg-gray-50 rounded-md transition-colors duration-200"
              >
                <div className="flex items-center gap-3">
                  <span
                    className={`flex-shrink-0 flex items-center justify-center w-6 h-6
                      ${index < 3 ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'}
                      text-xs font-medium rounded-lg`}
                  >
                    {index + 1}
                  </span>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 truncate leading-tight group-hover:text-red-600 transition-colors duration-200">
                      {item.model}
                    </h4>
                    <p className="text-xs text-gray-500 leading-tight group-hover:text-gray-700 transition-colors duration-200">
                      {item.full?.brand?.name_en || item.brand_en || item.brand_cn || 'Unknown brand'}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </div>
  )
} 