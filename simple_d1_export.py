#!/usr/bin/env python3
"""
简化版 Cloudflare D1 数据库结构导出工具
快速导出数据库表结构信息
"""

import subprocess
import json
import sys
from datetime import datetime

def run_wrangler_command(database_name, sql_command, remote=True):
    """执行 wrangler d1 命令"""
    cmd = ["npx", "wrangler", "d1", "execute", database_name]
    if remote:
        cmd.append("--remote")
    cmd.extend(["--command", sql_command, "--json"])

    try:
        # 在 Windows 上使用 shell=True 来确保能找到 npx
        result = subprocess.run(cmd, capture_output=True, text=True, check=True, shell=True)
        data = json.loads(result.stdout)
        # wrangler 返回的是数组，取第一个元素
        if isinstance(data, list) and len(data) > 0:
            return data[0]
        return data
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e.stderr}")
        print(f"执行的命令: {' '.join(cmd)}")
        return {"results": []}
    except json.JSONDecodeError as e:
        print(f"解析结果失败: {e}")
        print(f"原始输出: {result.stdout}")
        return {"results": []}

def export_d1_schema(database_name, output_file=None, remote=True):
    """导出 D1 数据库结构"""
    
    if not output_file:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"{database_name}_schema_{timestamp}.json"
    
    print(f"正在导出数据库 '{database_name}' 的结构...")
    
    # 获取所有表名
    tables_result = run_wrangler_command(
        database_name, 
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_cf_%'",
        remote
    )
    
    table_names = [row["name"] for row in tables_result.get("results", [])]
    print(f"发现 {len(table_names)} 个表: {', '.join(table_names)}")
    
    # 获取完整的建表语句
    create_statements_result = run_wrangler_command(
        database_name,
        "SELECT type, name, sql FROM sqlite_master WHERE sql IS NOT NULL AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_cf_%' ORDER BY type, name",
        remote
    )
    
    # 构建导出数据
    export_data = {
        "database_name": database_name,
        "export_time": datetime.now().isoformat(),
        "table_count": len(table_names),
        "tables": {},
        "create_statements": create_statements_result.get("results", [])
    }
    
    # 获取每个表的详细信息
    for table_name in table_names:
        print(f"正在处理表: {table_name}")
        
        # 获取表字段信息
        table_info_result = run_wrangler_command(
            database_name,
            f"PRAGMA table_info('{table_name}')",
            remote
        )
        
        # 获取表索引信息
        index_info_result = run_wrangler_command(
            database_name,
            f"PRAGMA index_list('{table_name}')",
            remote
        )
        
        export_data["tables"][table_name] = {
            "columns": table_info_result.get("results", []),
            "indexes": index_info_result.get("results", []),
            "column_count": len(table_info_result.get("results", []))
        }
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 导出完成!")
    print(f"📁 文件保存位置: {output_file}")
    print(f"📊 统计信息:")
    print(f"   - 总表数: {len(table_names)}")
    
    total_columns = sum(len(table_data["columns"]) for table_data in export_data["tables"].values())
    print(f"   - 总字段数: {total_columns}")
    
    # 显示表结构摘要
    print(f"\n📋 表结构摘要:")
    for table_name, table_data in export_data["tables"].items():
        columns = table_data["columns"]
        indexes = table_data["indexes"]
        print(f"   {table_name}: {len(columns)} 个字段, {len(indexes)} 个索引")
        
        # 显示主键字段
        pk_columns = [col["name"] for col in columns if col.get("pk") == 1]
        if pk_columns:
            print(f"     主键: {', '.join(pk_columns)}")

def main():
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python simple_d1_export.py <数据库名称> [输出文件名] [--local]")
        print("")
        print("示例:")
        print("  python simple_d1_export.py my-database")
        print("  python simple_d1_export.py my-database schema.json")
        print("  python simple_d1_export.py my-database --local")
        sys.exit(1)
    
    database_name = sys.argv[1]
    output_file = None
    remote = True
    
    # 解析参数
    for arg in sys.argv[2:]:
        if arg == "--local":
            remote = False
        elif not arg.startswith("--"):
            output_file = arg
    
    try:
        export_d1_schema(database_name, output_file, remote)
    except KeyboardInterrupt:
        print("\n❌ 操作已取消")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
