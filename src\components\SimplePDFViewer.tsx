'use client'

import { useState, useEffect } from 'react'

interface SimplePDFViewerProps {
  url: string
  productName?: string
  productId?: string
  className?: string
}

export default function SimplePDFViewer({ url, productName = 'Document', productId, className = '' }: SimplePDFViewerProps) {
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>('')
  const [pdfUrl, setPdfUrl] = useState<string>('')

  useEffect(() => {
    // 如果有productId，使用下载API；否则使用原始URL
    if (productId) {
      const apiUrl = `https://download-datasheet.chinaelectron.com/?product_id=${productId}`
      setPdfUrl(apiUrl)
      setLoading(false)
    } else if (url) {
      // 简单的URL验证
      try {
        new URL(url)
        setPdfUrl(url)
        setLoading(false)
      } catch {
        setError('Invalid PDF URL')
        setLoading(false)
      }
    } else {
      setError('No PDF URL or Product ID provided')
      setLoading(false)
    }
  }, [url, productId])

  // 下载PDF - 使用指定的下载API
  const downloadPDF = () => {
    if (productId) {
      // 使用产品ID的下载API
      const downloadUrl = `https://download-datasheet.chinaelectron.com/?product_id=${productId}`
      window.open(downloadUrl, '_blank')
    } else {
      // 备用方案：直接下载PDF文件
      const link = document.createElement('a')
      link.href = url
      link.download = `${productName}-datasheet.pdf`
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  if (error) {
    return (
      <div className={`w-full h-[calc(100vh-200px)] ${className}`}>
        <div className="flex flex-col items-center justify-center h-full bg-gray-50 p-4 rounded-lg border">
          <div className="text-red-500 mb-4 text-center">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-lg font-medium">Unable to load PDF</p>
            <p className="text-sm text-gray-600 mt-1">{error}</p>
          </div>
          <button
            onClick={downloadPDF}
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Download PDF
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`w-full ${className || 'h-[calc(100vh-200px)]'} flex flex-col`}>
      {/* 工具栏 */}
      <div className="bg-gray-100 border-b border-gray-200 p-2 sm:p-3 flex-shrink-0">
        <div className="flex items-center justify-between flex-wrap gap-2">
          <div className="text-sm text-gray-600 truncate flex-1">
            {productName} Datasheet
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={downloadPDF}
              className="bg-blue-500 text-white px-3 py-2 rounded hover:bg-blue-600 transition-colors text-sm flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span className="hidden sm:inline">Download</span>
            </button>
          </div>
        </div>
      </div>

      {/* PDF内容区域 */}
      <div className="flex-1 bg-gray-50 p-1 sm:p-2 min-h-0">
        {loading && (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600 text-sm sm:text-base">Loading PDF...</span>
          </div>
        )}

        {!loading && (
          <div className="w-full h-full border border-gray-300 rounded overflow-hidden bg-white">
            <object
              data={pdfUrl}
              type="application/pdf"
              className="w-full h-full"
              style={{ minHeight: '800px' }}
            >
              <div className="flex flex-col items-center justify-center h-full bg-gray-50 p-4">
                <div className="text-center mb-6">
                  <svg className="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                  <p className="text-gray-600 mb-2">Unable to display PDF in browser</p>
                  <p className="text-sm text-gray-500">Your browser doesn't support embedded PDFs</p>
                </div>
                
                <div className="flex justify-center">
                  <button
                    onClick={downloadPDF}
                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm flex items-center gap-2 justify-center"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download PDF
                  </button>
                </div>
              </div>
            </object>
          </div>
        )}
      </div>
    </div>
  )
}
