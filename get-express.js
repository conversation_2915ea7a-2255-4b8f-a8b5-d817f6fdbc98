/**
 * Cloudflare Worker - 获取快递运费信息
 * 调用 LCSC 快递API获取运费报价
 */

export default {
  async fetch(request, env, ctx) {
    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400',
        },
      });
    }

    try {
      const url = new URL(request.url);
      
      // 只处理 POST 请求到 /api/express 路径
      if (request.method !== 'POST' || url.pathname !== '/api/express') {
        return new Response(JSON.stringify({
          error: 'Method not allowed or invalid path',
          message: 'Please use POST method to /api/express'
        }), {
          status: 405,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        });
      }

      // 获取请求体数据
      const requestData = await request.json();
      
      // 验证必需的参数
      const requiredFields = ['countryCode', 'weight', 'orderAmt'];
      const missingFields = requiredFields.filter(field => !requestData[field]);
      
      if (missingFields.length > 0) {
        return new Response(JSON.stringify({
          error: 'Missing required fields',
          missingFields: missingFields,
          message: 'Please provide: countryCode, weight, orderAmt'
        }), {
          status: 400,
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
          },
        });
      }

      // 构建发送到 LCSC API 的数据，设置默认值
      const expressRequestData = {
        countryCode: requestData.countryCode,
        province: requestData.province || '',
        city: requestData.city || '',
        postCode: requestData.postCode || '',
        currencyCode: requestData.currencyCode || 'USD',
        weight: parseFloat(requestData.weight),
        orderAmt: parseFloat(requestData.orderAmt),
        orderItemTotal: parseInt(requestData.orderItemTotal) || 1,
        vat: requestData.vat || false,
        isFrom: requestData.isFrom !== undefined ? requestData.isFrom : true,
        isHasBuzzer: requestData.isHasBuzzer || false,
        isIncludeSpecialCategory: requestData.isIncludeSpecialCategory || false
      };

      // 调用 LCSC 快递API
      const lcscResponse = await fetch('https://wmsc.lcsc.com/wmsc/cart/product/getExpress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': 'wmsc_cart_key=90CD1C5752AB78615BC188255B7EFC82CE0346844A0CD5349910DE1EF2DF18691D5BA85C3DFF4502',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
          'Origin': 'https://www.lcsc.com',
          'Referer': 'https://www.lcsc.com/'
        },
        body: JSON.stringify(expressRequestData)
      });

      // 检查响应状态
      if (!lcscResponse.ok) {
        throw new Error(`LCSC API responded with status: ${lcscResponse.status}`);
      }

      // 获取响应数据
      const responseData = await lcscResponse.json();

      // 返回成功响应
      return new Response(JSON.stringify({
        success: true,
        data: responseData,
        requestData: expressRequestData,
        timestamp: new Date().toISOString()
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Cache-Control': 'no-cache'
        },
      });

    } catch (error) {
      console.error('Express API Error:', error);
      
      return new Response(JSON.stringify({
        success: false,
        error: 'Internal server error',
        message: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }
  },
};
