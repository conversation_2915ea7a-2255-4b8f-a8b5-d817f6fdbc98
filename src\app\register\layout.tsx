import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const metadata: Metadata = {
  title: 'Register Account | China Electron',
  description: 'Create a new account on China Electron to access comprehensive electronic component sourcing and connect with suppliers worldwide.',
  keywords: 'register, sign up, create account, China Electron registration, electronic component platform',
  alternates: {
    canonical: getCanonicalUrl('register'),
  },
}

export default function RegisterLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <>{children}</>
} 