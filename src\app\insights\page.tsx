/**
 * Insights Page
 * 
 * Displays collections of topic-focused articles
 * This page is server-side rendered for better SEO performance
 */

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'

// 定义专题集合的类型
interface TopicCollection {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle: string;
  description: string;
  cover_image: string;
  tags: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  featured?: boolean; // 客户端用于展示布局的属性
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface ApiResponse {
  data: TopicCollection[];
  pagination: PaginationData;
}

// 从API获取专题集合的函数
async function getTopicCollections(): Promise<TopicCollection[]> {
  try {
    // 调用API获取专题列表
    const response = await fetch('https://blog-manage.chinaelectron.com/api/insights?limit=20', {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch insights: ${response.status}`);
    }

    const data: ApiResponse = await response.json();
    
    // 处理API响应，为每个专题添加featured属性
    // 将前两个专题设置为featured展示在顶部
    return data.data.map((topic, index) => ({
      ...topic,
      featured: index < 2
    }));
  } catch (error) {
    console.error('Error fetching topic collections:', error);
    return []; // 发生错误时返回空数组
  }
}

export default async function InsightsPage() {
  // 获取专题集合数据
  const topicCollections = await getTopicCollections();
  
  // 分离特色专题和常规专题
  const featuredTopics = topicCollections.filter(topic => topic.featured);
  const regularTopics = topicCollections.filter(topic => !topic.featured);

  return (
    <div className="min-h-screen bg-white">
      {/* 顶部横幅 */}
      <div className="relative h-[300px] overflow-hidden bg-blue-900">
        {/* 背景图像 - 实际应用中使用真实图像 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-800 to-blue-600">
          {/* 动态图案叠加 */}
          <div className="absolute inset-0" style={{ 
            backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
        
        {/* Insights标题文本 */}
        <div className="absolute inset-0 flex flex-col justify-center px-14">
          <div className="max-w-4xl">
            <h1 className="text-4xl font-bold text-white mb-4">THE BLUEPRINT</h1>
            <p className="text-xl text-white/80">
              Engineering Insights for Smarter PCB & Component Decisions!
            </p>
          </div>
        </div>
      </div>

      <div className="mx-auto px-14 py-12">
        {/* 特色专题 - 大卡片布局 */}
        {featuredTopics.map((topic) => (
          <div key={topic.id} className="mb-16">
            <div className="bg-white overflow-hidden rounded-lg border border-gray-200 shadow-sm">
              <div className="flex flex-col lg:flex-row">
                {/* 左侧：图像 */}
                <div className="lg:w-2/5 h-[300px] lg:h-auto relative bg-gray-200">
                  {/* 图像 */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Image 
                      src={topic.cover_image || `https://dummyimage.com/800x600/04347b/ffffff.jpg&text=${encodeURIComponent(topic.title)}`}
                      alt={topic.title}
                      width={800}
                      height={600}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="absolute inset-0 flex flex-col justify-center items-center text-white">
                    <h2 className="text-3xl font-bold mb-2">{topic.title}</h2>
                    <p className="text-lg">Articles Collection</p>
                  </div>
                </div>
                
                {/* 右侧：文章列表 */}
                <div className="lg:w-3/5 p-8">
                  <div className="mb-6">
                    <Link href={`/insights/${topic.id}`} className="text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors">
                      {topic.title}
                    </Link>
                    <p className="text-gray-600 mt-2">{topic.description}</p>
                  </div>
                  
                  {/* 文章列表示例 */}
                  <div className="space-y-5">
                    <div className="mt-6 text-right">
                      <Link 
                        href={`/insights/${topic.id}`}
                        className="text-blue-600 hover:text-blue-800 font-medium"
                      >
                        View Articles &gt;
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* 常规专题 - 网格布局 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {regularTopics.map((topic) => (
            <div key={topic.id} className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
              {/* 主题图像 */}
              <div className="h-48 relative bg-gray-200">
                <Image 
                  src={topic.cover_image || `https://dummyimage.com/600x400/333/fff.jpg&text=${encodeURIComponent(topic.title)}`}
                  alt={topic.title}
                  width={600}
                  height={400}
                  className="w-full h-full object-cover"
                />
                
                {/* 叠加标题 */}
                <div className="absolute inset-0 flex items-center justify-center bg-black/40">
                  <div className="text-center text-white">
                    <h3 className="text-xl font-bold mb-1">{topic.title}</h3>
                    <p className="text-sm">Articles Collection</p>
                  </div>
                </div>
              </div>
              
              {/* 主题内容 */}
              <div className="p-6">
                <Link href={`/insights/${topic.id}`} className="text-xl font-bold text-gray-900 hover:text-blue-600 transition-colors">
                  {topic.title}
                </Link>
                <p className="text-gray-600 mt-2 mb-4 line-clamp-2">{topic.description}</p>
                
                {/* 查看更多链接 */}
                <div className="text-right">
                  <Link 
                    href={`/insights/${topic.id}`}
                    className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    View Articles &gt;
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
} 