// 产品基本信息组件 
import Image from 'next/image'
import Link from 'next/link'
import { useState, useEffect } from 'react'

interface ProductInfoProps {
  result: {
    id: string
    metadata: {
      name: string
      desc: string
      img: string
      manufacture: string
      model: string
      datasheet: string
      category?: string // 类别
      parameters?: string // 主要参数
    }
  }
}

export default function ProductInfo({ result }: ProductInfoProps) {
  const [imgSrc, setImgSrc] = useState<string>('')

  useEffect(() => {
    // 从 localStorage 获取 token
    const token = localStorage.getItem('token')
    if (token && result.metadata.img) {
      // 构建带有认证信息的图片 URL
      fetch(result.metadata.img, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'image/*',
          'Origin': window.location.origin
        }
      })
      .then(response => {
        if (!response.ok) throw new Error('Failed to load image')
        return response.blob()
      })
      .then(blob => {
        const url = URL.createObjectURL(blob)
        setImgSrc(url)
      })
      .catch(error => {
        console.error('Error loading image:', error)
        setImgSrc('/placeholder.png') // 设置一个默认图片
      })
    }
  }, [result.metadata.img])

  const getDatasheetFilename = (url: string) => {
    try {
      return url.split('/').pop() || ''
    } catch (error) {
      console.error('Error extracting filename:', error)
      return ''
    }
  }

  return (
    <div className="grid grid-cols-[auto,1fr,220px] gap-6 p-5">
      {/* Left section: Product image and datasheet */}
      <div className="space-y-2">
        {/* Product image */}
        <div className="relative w-32 h-32 border border-gray-100 rounded-xl bg-white 
                      group hover:border-[#DE2910]/20 transition-colors overflow-hidden">
          {imgSrc ? (
            <Image
              src={imgSrc}
              alt={result.metadata.name}
              fill
              sizes="128px"
              className="object-contain p-2.5 group-hover:scale-105 transition-transform duration-300"
              unoptimized
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-xl">
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          )}
        </div>

        {/* Datasheet link */}
        <Link 
          href={`/pdf/${encodeURIComponent(getDatasheetFilename(result.metadata.datasheet))}`}
          className="flex items-center gap-2 group w-32 p-2.5 
                   bg-white border border-gray-100 rounded-xl
                   hover:border-[#DE2910]/20 hover:shadow-lg hover:shadow-[#DE2910]/10 
                   transition-all duration-300"
          target="_blank"
        >
          <svg className="w-5 h-5 text-[#DE2910] group-hover:scale-110 transition-transform" 
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
              d="M15 2v5a2 2 0 002 2h5" />
          </svg>
          <span className="text-sm text-gray-600 group-hover:text-[#DE2910]">Datasheet</span>
        </Link>
      </div>

      {/* Middle section: Main product information */}
      <div>
        <div className="space-y-3">
          {/* Product name with link */}
          <Link href={`/components/${result.id}`}>
            <h2 className="text-lg font-semibold text-gray-900 hover:text-[#DE2910] transition-colors">
              {result.metadata.name}
            </h2>
          </Link>
          
          {/* Product description */}
          <p className="text-sm text-gray-600 leading-relaxed">{result.metadata.desc}</p>
        </div>

        {/* Main parameters */}
        <div className="mt-4">
          <div className="bg-gray-50 rounded-xl p-3 border border-gray-100">
            <p className="text-sm text-gray-600">{result.metadata.parameters || 'Main Parameters'}</p>
          </div>
        </div>
      </div>

      {/* Right section: Product details and actions */}
      <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
        {/* Model */}
        <div className="mb-3">
          <div className="text-xs text-gray-500 mb-1">Model</div>
          <div className="font-medium text-gray-900">{result.metadata.model}</div>
        </div>

        {/* Brand */}
        <div className="mb-4">
          <div className="text-xs text-gray-500 mb-1">Brand</div>
          <div className="font-medium text-gray-900">{result.metadata.manufacture}</div>
        </div>

        {/* Divider */}
        <div className="border-t border-gray-200 my-4"></div>

        {/* Action buttons */}
        <div className="flex flex-col gap-2">
          <button 
            className="w-full flex items-center justify-center gap-2 px-4 py-2 
                     border border-[#DE2910] text-[#DE2910] rounded-xl 
                     hover:bg-[#DE2910]/5 transition-colors"
            onClick={() => {/* AI Analysis handler */}}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span className="text-sm font-medium">Analyze</span>
          </button>
        </div>
      </div>
    </div>
  )
} 