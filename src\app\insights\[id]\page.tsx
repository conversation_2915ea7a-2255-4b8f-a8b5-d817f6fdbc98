/**
 * Insights Topic Detail Page
 * 
 * Displays the articles for a specific topic collection
 * This page is server-side rendered for better SEO performance
 */

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { Metadata, ResolvingMetadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

// 将标题转换为URL友好的格式
function slugifyTitle(title: string): string {
  // 将所有符号和空格替换为连字符，并将连续的连字符替换为单个连字符
  return title
    .replace(/[^\w\s]/g, '-') // 将所有非字母数字字符替换为连字符
    .replace(/\s+/g, '-')     // 将空格替换为连字符
    .replace(/-+/g, '-')      // 将连续的连字符替换为单个连字符
    .replace(/^-|-$/g, '')    // 移除开头和结尾的连字符
    .toLowerCase();           // 转为小写
}

// 定义专题类型
interface Topic {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle: string;
  description: string;
  cover_image: string;
  tags: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
  blogs?: any[];
}

// 定义文章类型
interface Article {
  id: number;
  title: string;
  description: string;
  cover_image: string;
  created_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
  tags?: string; // API返回的是标签字符串
}

// 定义文章响应类型
interface ArticlesResponse {
  articles: Article[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// 从API获取专题详情
async function getTopicById(id: string): Promise<Topic | null> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/insights/${id}`, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch insight: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching topic with ID ${id}:`, error);
    return null;
  }
}

// 从API获取专题下的文章列表
async function getArticlesByTopicId(topicId: string, page: number = 1): Promise<ArticlesResponse> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/insights/${topicId}/blogs?page=${page}`, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch articles: ${response.status}`);
    }

    const result = await response.json();
    
    // 根据API返回的数据结构提取文章和分页信息
    return {
      articles: result.data || [],
      pagination: result.pagination || { total: 0, page: 1, limit: 10, pages: 0 }
    };
  } catch (error) {
    console.error(`Error fetching articles for topic ${topicId}:`, error);
    return { articles: [], pagination: { total: 0, page: 1, limit: 10, pages: 0 } };
  }
}

// 添加根据ID获取文章的函数
async function getArticleById(id: number): Promise<any | null> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/blogs/${id}`, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch article: ${response.status}`);
    }

    const result = await response.json();
    // 返回data对象，如果不存在则返回null
    return result.data || null;
  } catch (error) {
    console.error(`Error fetching article with ID ${id}:`, error);
    return null;
  }
}

// 从URL中提取专题ID
function extractTopicIdFromUrl(urlPath: string): number {
  try {
    // 尝试从"专题名-ID"格式中提取
    const topicParts = urlPath.split('-');
    
    // 如果最后一部分是数字，则使用它作为ID
    if (topicParts.length > 0) {
      const lastPart = topicParts[topicParts.length - 1];
      if (/^\d+$/.test(lastPart)) {
        return parseInt(lastPart, 10);
      }
    }
    
    // 如果不是新格式，尝试直接解析整个路径作为ID
    const directId = parseInt(urlPath, 10);
    if (!isNaN(directId)) {
      return directId;
    }
    
    // 如果无法解析，返回默认值或抛出错误
    throw new Error(`无法从URL路径提取专题ID: ${urlPath}`);
  } catch (error) {
    console.error('解析专题ID时出错:', error);
    // 返回一个不存在的ID，这样页面会显示404
    return -1;
  }
}

// 生成页面元数据
export async function generateMetadata(
  props: { params: Promise<{ id: string }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  // 解析异步 params
  const params = await props.params;
  
  // 获取文章
  const topicId = extractTopicIdFromUrl(params.id);
  const topic = await getTopicById(topicId.toString());
  
  if (!topic) {
    return {
      title: 'Topic Not Found'
    }
  }
  
  return {
    title: `${topic.title} | China Electron`,
    description: topic.description || `Explore our ${topic.title} collection.`,
    alternates: {
      canonical: getCanonicalUrl(`insights/${params.id}`),
    },
  }
}

export default async function TopicDetailPage(props: { 
  params: Promise<{ id: string }>,
  searchParams?: Promise<{ page?: string }>
}) {
  // 解析异步 params 和 searchParams
  const params = await props.params;
  const searchParams = props.searchParams ? await props.searchParams : undefined;
  
  // 获取专题ID
  const topicId = extractTopicIdFromUrl(params.id);
  
  // 获取当前页码
  const currentPage = searchParams?.page ? parseInt(searchParams.page, 10) : 1;
  
  // 获取专题详情
  const topic = await getTopicById(topicId.toString());
  
  // 如果专题不存在，显示404页面
  if (!topic) {
    notFound();
  }
  
  // 获取专题文章
  const articlesResponse = await getArticlesByTopicId(topicId.toString(), currentPage);

  // 生成分页链接
  const generatePageLink = (page: number) => `/insights/${slugifyTitle(topic.title)}-${topic.id}?page=${page}`;

  return (
    <div className="min-h-screen bg-white">
      {/* 专题头部 */}
      <div className="relative h-[300px] overflow-hidden bg-blue-900">
        {/* 背景图像 */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-800 to-blue-600">
          {/* 动态图案叠加 */}
          <div className="absolute inset-0" style={{ 
            backgroundImage: `radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px)`,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
        
        {/* 专题标题文本 */}
        <div className="absolute inset-0 flex flex-col justify-center px-14">
          <div className="max-w-4xl">
            <h1 className="text-4xl font-bold text-white mb-4">{topic.title}</h1>
            <p className="text-xl text-white/80">
              {topic.description}
            </p>
          </div>
        </div>
      </div>

      <div className="mx-auto px-14 py-12">
        <div>
          {/* 文章内容 - 现在占据全宽 */}
          <div className="w-full">
            {/* 专题介绍 */}
            <div className="mb-10">
              <p className="text-gray-600">
                {topic.description}
              </p>
            </div>
            
            {/* 文章列表 */}
            <div className="space-y-8">
              {articlesResponse.articles.length > 0 ? (
                articlesResponse.articles.map((article) => (
                <div key={article.id} className="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                        {/* 作者头像（如果有的话） */}
                      <div className="w-8 h-8 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                        <div className="w-full h-full flex items-center justify-center text-xs text-gray-500">
                            {article.author && article.author.name ? article.author.name.charAt(0) : "A"}
                          </div>
                        </div>
                        <div className="ml-2">
                          <div className="text-gray-700 text-sm font-medium">
                            {article.author && article.author.name ? article.author.name : "Anonymous"}
                          </div>
                          <div className="text-gray-500 text-xs">{new Date(article.created_at).toLocaleDateString()}</div>
                      </div>
                    </div>
                    
                    <Link href={`/insights/${slugifyTitle(topic.title)}-${topic.id}/${slugifyTitle(article.title)}-${article.id}`} className="block group">
                      <h3 className="text-xl font-bold mb-3 text-gray-900 group-hover:text-blue-600 transition-colors">
                        {article.title}
                      </h3>
                    </Link>
                    
                    <p className="text-gray-600 mb-4">
                        {article.description}
                    </p>
                    
                      {article.tags && article.tags.trim() && (
                    <div className="flex flex-wrap gap-2">
                          {article.tags.split(',').slice(0, 3).map((tag, index) => (
                        <span key={index} className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                              {tag.trim()}
                        </span>
                      ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500">No articles found for this topic.</p>
                </div>
              )}
            </div>
            
            {/* 分页控件 */}
            {articlesResponse.pagination.pages > 1 && (
              <div className="mt-10 flex justify-center">
                <div className="inline-flex rounded-md shadow-sm">
                  {currentPage > 1 && (
                    <Link href={generatePageLink(currentPage - 1)} className="px-3 py-2 rounded-l-md border border-gray-300 bg-white text-gray-500 hover:bg-gray-50">
                      上一页
                    </Link>
                  )}
                  
                  {/* 页码按钮 */}
                  {Array.from({ length: articlesResponse.pagination.pages }, (_, i) => i + 1)
                    .filter(page => 
                      // 显示当前页、第一页、最后一页，以及当前页附近的页码
                      page === 1 || 
                      page === articlesResponse.pagination.pages || 
                      Math.abs(page - currentPage) <= 1
                    )
                    .map((page, index, array) => {
                      // 添加省略号
                      const showEllipsis = index > 0 && array[index - 1] !== page - 1;
                      
                      return (
                        <React.Fragment key={page}>
                          {showEllipsis && (
                            <span className="px-3 py-2 border-t border-b border-gray-300 bg-white text-gray-400">
                              ...
                            </span>
                          )}
                          <Link
                            href={generatePageLink(page)}
                            className={`px-3 py-2 border-t border-b border-gray-300 ${
                              currentPage === page 
                                ? 'bg-blue-600 text-white font-medium' 
                                : 'bg-white text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </Link>
                        </React.Fragment>
                      );
                    })
                  }
                  
                  {currentPage < articlesResponse.pagination.pages && (
                    <Link href={generatePageLink(currentPage + 1)} className="px-3 py-2 rounded-r-md border border-gray-300 bg-white text-gray-700 hover:bg-gray-50">
                      下一页
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 