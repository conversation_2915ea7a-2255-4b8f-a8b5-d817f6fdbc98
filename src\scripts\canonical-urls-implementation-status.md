# Canonical URL 实现状态

本文档记录了网站中 Canonical URL 的实现状态，帮助团队追踪哪些页面已经完成实现，哪些页面还需要添加。

## 已实现 Canonical URL 的页面

1. 根布局 (`src/app/layout.tsx`) - 设置了网站首页的基础 canonical URL
2. 首页 (`src/app/page.tsx`) - 针对首页的特定 canonical URL
3. 制造商列表页 (`src/app/manufacturers/page.tsx`)
4. 制造商详情页 (`src/app/manufacturers/[id]/page.tsx`)
5. 制造商子分类页 (`src/app/manufacturers/chinese/page.tsx`, `src/app/manufacturers/international/page.tsx`)
6. 产品详情页 (`src/app/product/[id]/page.tsx`)
7. 分销商列表页 (`src/app/distributors/page.tsx`)
8. 分销商详情页 (`src/app/distributors/[id]/page.tsx`)
9. 关于我们页面 (`src/app/about-us/page.tsx`)
10. 服务条款页面 (`src/app/terms-of-service/page.tsx`)
11. 隐私政策页面 (`src/app/privacy-policy/page.tsx`)
12. 搜索页面 (`src/app/search/page.tsx`)
13. 登录页面 (`src/app/login/layout.tsx`)
14. 注册页面 (`src/app/register/layout.tsx`)
15. 博客列表页 (`src/app/blog/layout.tsx`)
16. 行业洞察列表页 (`src/app/insights/layout.tsx`)
17. 行业洞察详情页 (`src/app/insights/[id]/page.tsx`)
18. 行业洞察文章详情页 (`src/app/insights/[id]/[articleId]/page.tsx`)
19. 热点列表页 (`src/app/spotlight/layout.tsx`)
20. 组件列表页 (`src/app/components/page.tsx`)
21. 组件详情页 (`src/app/components/[id]/layout.tsx`)
22. 每日热门页面 (`src/app/dailyhot/page.tsx`)
23. 询价请求 (RFQ) 页面 (`src/app/rfq/layout.tsx`)
24. 账户页面 (`src/app/account/layout.tsx`)

## 需要添加 Canonical URL 的页面

以下页面还需要添加 Canonical URL：

1. 热点详情页 (`src/app/spotlight/[id]/page.tsx`)
2. 验证代码页面 (`src/app/verify-code/page.tsx`)
3. 验证人类页面 (`src/app/verify-human/page.tsx`)
4. PDF 页面 (`src/app/pdf/[filename]/page.tsx`)
5. 账户子页面：
   - 账户设置页面 (`src/app/account/settings/page.tsx`)
   - 账户 RFQ 页面 (`src/app/account/rfq/page.tsx`)

## 如何添加 Canonical URL

对于服务器组件，使用以下模式：

```tsx
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const generateMetadata = (): Metadata => {
  return {
    // 其他元数据...
    alternates: {
      canonical: getCanonicalUrl('your-page-path'),
    },
  };
};
```

对于动态路由页面，使用以下模式：

```tsx
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  return {
    // 其他元数据...
    alternates: {
      canonical: getCanonicalUrl(`your-page-path/${params.id}`),
    },
  }
}
```

对于客户端组件，在相应的 layout.tsx 文件中添加：

```tsx
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

export const metadata: Metadata = {
  // 其他元数据...
  alternates: {
    canonical: getCanonicalUrl('your-page-path'),
  },
}
```

## 验证方法

实施后，可以通过以下方式验证 canonical URL 是否正确配置：

1. 使用浏览器开发者工具检查页面的 HTML 源代码，确认 `<link rel="canonical" href="...">` 标签是否存在
2. 使用 Google Search Console 检查页面的索引状态
3. 使用在线 SEO 工具验证 canonical 链接是否正确配置 