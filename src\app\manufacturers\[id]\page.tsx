import Link from 'next/link'
import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import { buildProductUrl } from '@/utils/productUrl'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import { generateBreadcrumbs } from '@/utils/breadcrumbs'
import ServerBreadcrumb from '@/components/Breadcrumb/ServerBreadcrumb'
import type { Metadata } from 'next'

// Brand detail interface
interface Brand {
  id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  website: string;
  logo_url: string;
  introduction_cn: string;
  introduction_en: string;
  introduction_ru: string;
}

// Product interface
interface Product {
  product_id: string;
  model: string;
  url?: string; // 添加url字段
  category_path?: string[];
  category_names?: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
  category_id?: string;
  brand?: {
    name_cn: string;
    name_en: string;
    name_ru: string;
  };
}



// 服务端数据获取 - 品牌详情
async function fetchBrandDetails(brandId: string): Promise<Brand | null> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/brands/${brandId}`, {
      next: { revalidate: 86400 } // 24小时重新验证
    })
    
    if (!response.ok) {
      if (response.status === 404) {
        return null
      }
      throw new Error(`API request failed: ${response.status}`)
    }
    
    return await response.json()
  } catch (err) {
    console.error('Failed to fetch brand details:', err)
    return null
  }
}

// 服务端数据获取 - 品牌产品
async function fetchBrandProducts(brandId: string): Promise<{products: Product[], error: string | null}> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/products?brand=${brandId}`, {
      next: { revalidate: 86400 } // 24小时重新验证
    })
    
    if (!response.ok) {
      throw new Error(`Failed to get product list: ${response.status}`)
    }
    
    const data = await response.json()
    return { products: data.results || [], error: null }
  } catch (err) {
    console.error('Failed to fetch brand products:', err)
    return { products: [], error: err instanceof Error ? err.message : 'Unknown error' }
  }
}



// 服务端组件 - 产品列表
function ProductList({ products, error }: { products: Product[], error: string | null }) {
  // 处理错误情况
  if (error) {
    return (
      <div className="bg-red-50 text-red-700 p-4 rounded-lg">
        <p>Error loading product list: {error}</p>
      </div>
    )
  }
  
  // 没有产品时显示的内容
  if (products.length === 0) {
    return (
      <div className="text-center py-12 bg-white rounded-lg border border-gray-100">
        <p className="text-gray-500">No products available</p>
      </div>
    )
  }
  
  // 正常显示产品列表
  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
      {products.map(product => (
        <Link
          key={product.product_id}
          href={product.url || buildProductUrl(product)}
          prefetch={false} // 禁用预加载
          className="bg-white border border-gray-100 rounded-lg p-4 hover:shadow-md hover:border-[#DE2910]/20 transition-all"
        >
          <span className="font-medium block text-center truncate">{product.model}</span>
        </Link>
      ))}
    </div>
  )
}



// 服务端组件 - 品牌信息显示
function BrandInfo({ brand }: { brand: Brand }) {
  // 获取适当的品牌名称和介绍
  const brandName = brand.name_en || brand.name_cn || brand.name_ru || 'Unknown brand'
  const brandIntro = brand.introduction_en || brand.introduction_cn || brand.introduction_ru || 'No brand introduction available'
  const isDefaultLogo = brand.logo_url === 'https://brandimg.chinaelectron.com/no_brand_logo.png' || !brand.logo_url
  
  return (
    <div className="flex flex-col md:flex-row gap-8 items-start">
      <div className="w-full md:w-1/3 relative">
        <div className="aspect-[2/1] relative rounded-xl overflow-hidden bg-white">
          {!isDefaultLogo ? (
            <img
              src={brand.logo_url}
              alt={brandName}
              className="w-full h-full object-contain p-4"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200 rounded">
              <span className="text-gray-600 font-medium">{brandName}</span>
            </div>
          )}
        </div>
      </div>
      <div className="flex-1">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">{brandName}</h1>
        <p className="text-gray-600 mb-6">{brandIntro}</p>
        {brand.website && (
          <div className="flex flex-wrap items-center gap-4">
            <a
              href={brand.website}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 border border-[#DE2910] text-[#DE2910] rounded-lg hover:bg-[#DE2910] hover:text-white transition-colors"
            >
              Visit Website
            </a>
          </div>
        )}
      </div>
    </div>
  )
}

// 为服务端组件生成元数据
export async function generateMetadata(props: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const params = await props.params;
  const brandId = params.id;
  const brand = await fetchBrandDetails(brandId);
  
  if (!brand) {
    return {
      title: 'Brand Not Found',
      description: 'The requested brand could not be found'
    };
  }
  
  const brandName = brand.name_en || brand.name_cn || brand.name_ru || 'Unknown brand';
  // 确定简称：使用第一个名称部分或全名如果没有空格
  let shortName = brandName;
  if (brandName.includes(' ')) {
    shortName = brandName.split(' ')[0];
  }
  
  return {
    title: `${brandName} (${shortName}) - Component Raw Materials | China Electron`,
    description: `Source electronic component raw materials from ${brandName} (${shortName}) via China Electron. Access product listings, datasheets, and connect directly.`,
    keywords: `${brandName}, ${shortName}, ${brandName} raw materials, ${shortName} components, component sourcing, China Electron, ${shortName} suppliers`,
    openGraph: {
      images: brand.logo_url ? [brand.logo_url] : [],
    },
    alternates: {
      canonical: getCanonicalUrl(`manufacturers/${brandId}`),
    },
  };
}

// 页面主组件 - 服务端渲染
export default async function ManufacturerDetailPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const brandId = params.id;

  try {
    // 服务端数据获取
    const brand = await fetchBrandDetails(brandId);

    // 品牌不存在，返回404
    if (!brand) {
      notFound();
    }

    // 生成面包屑
    const currentPath = `/manufacturers/${brandId}`;
    const breadcrumbs = await generateBreadcrumbs(currentPath);
    
    // 获取产品数据
    const { products, error: productsError } = await fetchBrandProducts(brandId);

    return (
      <div className="min-h-screen bg-gray-50">
        {/* 服务端渲染的面包屑 */}
        <ServerBreadcrumb breadcrumbs={breadcrumbs} />

        {/* Brand basic information */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 py-8">
            <BrandInfo brand={brand} />
          </div>
        </div>

        {/* 垂直布局的内容区域 */}
        <div className="max-w-7xl mx-auto px-4 py-8 space-y-8">
          {/* Products section */}
          <div className="bg-white rounded-xl shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Product Models</h2>
            
            <Suspense fallback={
              <div className="animate-pulse">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {[...Array(10)].map((_, index) => (
                    <div key={index} className="h-12 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
            }>
              <ProductList products={products} error={productsError} />
            </Suspense>
          </div>
          

        </div>

        {/* Back button */}
        <div className="max-w-7xl mx-auto px-4 py-8">
          <Link 
            href="/manufacturers"
            prefetch={false} // 禁用预加载
            className="inline-flex items-center text-[#DE2910] hover:underline"
          >
            &larr; Back to manufacturer list
          </Link>
        </div>
      </div>
    )
  } catch (error) {
    // 处理整体渲染过程中的任何未捕获错误
    console.error('Error rendering manufacturer page:', error);
    
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-xl shadow p-8 max-w-2xl w-full mx-4">
          <div className="text-center mb-6">
            <svg className="w-12 h-12 text-red-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <h2 className="text-2xl font-bold text-gray-900 mt-4">Page Error</h2>
          </div>
          <p className="text-gray-600 mb-6">We encountered an error while trying to load this manufacturer page. Please try again later.</p>
          <div className="flex justify-center">
            <Link 
              href="/manufacturers"
              prefetch={false}
              className="inline-flex items-center px-4 py-2 border border-[#DE2910] text-[#DE2910] rounded-lg hover:bg-[#DE2910] hover:text-white transition-colors"
            >
              Return to Manufacturer List
            </Link>
          </div>
        </div>
      </div>
    );
  }
} 