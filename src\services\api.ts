// 导入markdown解析库
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeRaw from 'rehype-raw';
import rehypeStringify from 'rehype-stringify';

// RFQ请求接口
export interface RFQRequest {
  id: string;
  user_id: string;
  type: string;
  status: string;
  contact_name: string;
  business_email: string;
  company_name: string;
  country: string;
  quantity: number;
  model: string;
  brand_name: string;
  target_price?: number;
  delivery_time?: string;
  date_code?: string;
  distribution_name?: string;
  create_at: string;
}

// 博客文章接口
export interface BlogArticle {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle?: string;
  description: string;
  cover_image: string;
  content_markdown: string;
  tags: string;
  category: string;
  location?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
  insights?: Array<{
    id: number;
    title: string;
    description: string;
  }>;
}

// 博客分页接口
export interface BlogPagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// 博客响应接口
export interface BlogResponse {
  data: BlogArticle[];
  pagination: BlogPagination;
}

// 获取token的辅助函数
const getToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('token');
  }
  return null;
};

// API基础URL
const API_BASE_URL = 'https://requests-quota.chinaelectron.com/api';
const BLOG_API_BASE_URL = 'https://blog-manage.chinaelectron.com/api';
const DB_API_BASE_URL = 'https://api.chinaelectron.com/api';

// 获取RFQ请求列表
export const fetchRFQRequests = async (userId: string | null): Promise<RFQRequest[]> => {
  if (!userId) {
    throw new Error('User ID is required');
  }

  const token = getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  try {
    const response = await fetch(`${API_BASE_URL}/requests?user_id=${userId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Origin': window.location.origin
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch RFQ requests');
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'Failed to fetch RFQ requests');
    }
    
    // 确保返回的是数组
    const results = data.results || [];
    // console.log('API raw results:', results);
    
    return results;
  } catch (error) {
    console.error('Error fetching RFQ requests:', error);
    throw error;
  }
};

// 创建新的RFQ请求
export const createRFQRequest = async (requestData: Partial<RFQRequest>): Promise<RFQRequest> => {
  const token = getToken();
  if (!token) {
    throw new Error('No authentication token found');
  }

  try {
    const response = await fetch(`${API_BASE_URL}/requests`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        'Origin': window.location.origin
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create RFQ request');
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'Failed to create RFQ request');
    }
    
    return data.result;
  } catch (error) {
    console.error('Error creating RFQ request:', error);
    throw error;
  }
};

// 将API状态转换为UI友好的状态
export const mapStatusToUI = (apiStatus: string): 'pending' | 'quoted' | 'closed' => {
  const statusMap: Record<string, 'pending' | 'quoted' | 'closed'> = {
    '待处理': 'pending',
    '已报价': 'quoted',
    '已关闭': 'closed',
    'pending': 'pending',
    'quoted': 'quoted',
    'closed': 'closed'
  };
  
  return statusMap[apiStatus] || 'pending';
};

// 通用错误处理
export const handleApiError = (error: unknown, fallbackMessage = '操作失败，请稍后再试'): string => {
  const errorMessage = error instanceof Error ? error.message : fallbackMessage;
  console.error(errorMessage);
  return errorMessage;
};

// 获取博客文章列表
export const fetchBlogArticles = async (
  page: number = 1, 
  limit: number = 10, 
  category?: string, 
  tag?: string, 
  author_id?: string
): Promise<BlogResponse> => {
  try {
    let url = `${BLOG_API_BASE_URL}/blogs?page=${page}&limit=${limit}`;
    
    if (category) url += `&category=${encodeURIComponent(category)}`;
    if (tag) url += `&tag=${encodeURIComponent(tag)}`;
    if (author_id) url += `&author_id=${encodeURIComponent(author_id)}`;
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Origin': typeof window !== 'undefined' ? window.location.origin : ''
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch blog articles');
    }

    const data: BlogResponse = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching blog articles:', error);
    throw error;
  }
};

// 获取单个博客文章
export const fetchBlogArticleById = async (id: number): Promise<BlogArticle> => {
  try {
    const response = await fetch(`${BLOG_API_BASE_URL}/blogs/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Origin': typeof window !== 'undefined' ? window.location.origin : ''
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch blog article with ID: ${id}`);
    }

    const article: BlogArticle = await response.json();
    return article;
  } catch (error) {
    console.error(`Error fetching blog article with ID ${id}:`, error);
    throw error;
  }
};

// 获取单个专题数据
export const fetchSpotlightById = async (id: number): Promise<BlogArticle> => {
  try {
    const response = await fetch(`${BLOG_API_BASE_URL}/insights/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Origin': typeof window !== 'undefined' ? window.location.origin : ''
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch spotlight with ID: ${id}`);
    }

    const spotlight: BlogArticle = await response.json();
    return spotlight;
  } catch (error) {
    console.error(`Error fetching spotlight with ID ${id}:`, error);
    throw error;
  }
};

// 获取文章Markdown内容
export const fetchArticleContent = async (contentUrl: string): Promise<string> => {
  try {
    // 检查contentUrl是否有效，如果无效直接返回占位内容
    if (!contentUrl || contentUrl.includes('example.com')) {
      // console.log('Invalid content URL or example URL detected:', contentUrl);
      return generateFallbackContent(contentUrl);
    }
    
    // 如果contentUrl是完整的URL，则直接使用；否则构建API URL
    const url = contentUrl.startsWith('http') 
      ? contentUrl 
      : `${BLOG_API_BASE_URL}${contentUrl.startsWith('/') ? '' : '/'}${contentUrl}`;
    
    // console.log('Fetching content from URL:', url);
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'text/plain',
          'Origin': typeof window !== 'undefined' ? window.location.origin : '',
          'Accept': 'text/plain, text/markdown, */*'
        },
        next: { revalidate: 3600 } // 缓存一小时
      });
  
      // 对于404错误，我们不显示错误，直接返回占位内容
      if (response.status === 404) {
        // console.log('Markdown file not found (404), using placeholder content');
        return generateFallbackContent(contentUrl);
      }
      
      // 处理其他错误
      if (!response.ok) {
        console.error(`Failed to fetch content, status: ${response.status}, statusText: ${response.statusText}`);
        return generateFallbackContent(contentUrl);
      }
  
      const markdownContent = await response.text();
      
      if (!markdownContent || markdownContent.trim() === '') {
        // 如果内容为空，返回占位内容
        return generateFallbackContent(contentUrl);
      }
      
      // 预处理图片链接，确保相对路径正确解析
      let processedMarkdown = markdownContent;
      
      // 处理图片链接 ![alt](./images/x.jpg) 或 ![alt](images/x.jpg) 格式
      processedMarkdown = processedMarkdown.replace(
        /!\[(.*?)\]\(\.(\/images\/.*?)\)/g, 
        `![$1](https://blog-manage.chinaelectron.com/api$2)`
      ).replace(
        /!\[(.*?)\]\((images\/.*?)\)/g, 
        `![$1](https://blog-manage.chinaelectron.com/api/images/$1)`
      ).replace(
        /!\[(.*?)\]\(\.\/(.*?)\)/g,
        `![$1](https://blog-manage.chinaelectron.com/api/$2)`
      );
      
      // 检测并移除重复的参考文献部分
      // 查找"Further Reading"或"References"等标题后的内容，如果在文章末尾有重复内容，则移除
      const referenceHeaders = [
        '## Further Reading & References:',
        '## Further Reading & References',
        '## References:',
        '## References',
        '## Further Reading:',
        '## Further Reading',
        '# Further Reading & References:',
        '# Further Reading & References',
        '# References:',
        '# References',
        '# Further Reading:',
        '# Further Reading'
      ];
      
      // 查找最后一个参考文献部分的位置
      let lastRefSectionPos = -1;
      for (const header of referenceHeaders) {
        const pos = processedMarkdown.lastIndexOf(header);
        if (pos > lastRefSectionPos) {
          lastRefSectionPos = pos;
        }
      }
      
      // 如果找到参考文献部分，检查是否有重复内容
      if (lastRefSectionPos !== -1) {
        // 获取参考文献部分的内容
        const refSection = processedMarkdown.substring(lastRefSectionPos);
        
        // 查找是否有重复的维基百科引用
        const wikiRegex = /Wikipedia contributors\. \(2023\)\. (.*?) In Wikipedia, The Free Encyclopedia\./g;
        const wikiMatches = [...refSection.matchAll(wikiRegex)];
        
        // 如果有多个相同的维基百科引用，保留第一个，移除其他
        if (wikiMatches.length > 1) {
          const uniqueWikiRefs = new Set();
          let cleanedMarkdown = processedMarkdown;
          
          for (const match of wikiMatches) {
            const fullMatch = match[0];
            const title = match[1];
            
            if (uniqueWikiRefs.has(title)) {
              // 找到这个引用在文本中的位置
              const matchPos = cleanedMarkdown.indexOf(fullMatch, lastRefSectionPos);
              if (matchPos !== -1) {
                // 找到这一行的结束位置（可能包含URL等）
                let lineEndPos = cleanedMarkdown.indexOf('\n', matchPos);
                // 如果找不到换行符，就使用字符串末尾
                if (lineEndPos === -1) lineEndPos = cleanedMarkdown.length;
                
                // 检查是否有URL部分
                const urlStartPos = cleanedMarkdown.indexOf('Retrieved from', matchPos);
                if (urlStartPos !== -1 && urlStartPos < lineEndPos) {
                  // 找到URL后的换行符
                  const urlEndPos = cleanedMarkdown.indexOf('\n', urlStartPos);
                  if (urlEndPos !== -1) {
                    lineEndPos = urlEndPos;
                  }
                }
                
                // 移除这一整行或整个引用块
                cleanedMarkdown = 
                  cleanedMarkdown.substring(0, matchPos) + 
                  cleanedMarkdown.substring(lineEndPos + 1);
              }
            } else {
              uniqueWikiRefs.add(title);
            }
          }
          
          processedMarkdown = cleanedMarkdown;
        }
        
        // 检查是否有重复的引用块（红框标记的整个块）
        const refBlockRegex = /(Wikipedia contributors\. \(2023\)\. .*? In Wikipedia, The Free Encyclopedia\. Retrieved from https:\/\/en\.wikipedia\.org\/wiki\/.*?)(\n|$)/g;
        const refBlockMatches = [...refSection.matchAll(refBlockRegex)];
        
        if (refBlockMatches.length > 0) {
          const uniqueRefBlocks = new Set();
          let cleanedMarkdown = processedMarkdown;
          
          for (const match of refBlockMatches) {
            const fullBlock = match[1];
            
            // 提取引用的主题（例如"Resistor"或"Georg Ohm"）
            const subjectMatch = fullBlock.match(/\/wiki\/([^\/\s]+)/);
            const subject = subjectMatch ? subjectMatch[1] : fullBlock;
            
            if (uniqueRefBlocks.has(subject)) {
              // 找到这个引用块在文本中的位置
              const matchPos = cleanedMarkdown.indexOf(fullBlock, lastRefSectionPos);
              if (matchPos !== -1) {
                // 找到引用块的结束位置
                let blockEndPos = cleanedMarkdown.indexOf('\n\n', matchPos);
                if (blockEndPos === -1) blockEndPos = cleanedMarkdown.length;
                
                // 移除整个引用块
                cleanedMarkdown = 
                  cleanedMarkdown.substring(0, matchPos) + 
                  cleanedMarkdown.substring(blockEndPos);
              }
            } else {
              uniqueRefBlocks.add(subject);
            }
          }
          
          processedMarkdown = cleanedMarkdown;
        }
        
        // 特别处理：移除文章结尾处的红框引用
        // 查找"I hope this deep dive has been helpful"之后的内容
        const conclusionPos = processedMarkdown.indexOf("I hope this deep dive has been helpful");
        if (conclusionPos !== -1) {
          // 查找结论后的维基百科引用
          const afterConclusion = processedMarkdown.substring(conclusionPos);
          const redBoxMatch = afterConclusion.match(/Wikipedia contributors\. \(2023\)\. (.*?) In Wikipedia, The Free Encyclopedia\. Retrieved from https:\/\/en\.wikipedia\.org\/wiki\/(.*?)( →)?(\n|$)/);
          
          if (redBoxMatch) {
            // 找到引用的位置
            const matchPos = processedMarkdown.indexOf(redBoxMatch[0], conclusionPos);
            if (matchPos !== -1) {
              // 截断文章，移除结尾的引用
              processedMarkdown = processedMarkdown.substring(0, matchPos).trim();
            }
          }
        }
      }
      
      // 使用unified处理流程，更好地支持表格和其他GitHub风格的Markdown元素
      const processedContent = await unified()
        .use(remarkParse) // 解析Markdown
        .use(remarkGfm) // 支持GitHub风格的Markdown（表格、任务列表等）
        .use(remarkRehype, { allowDangerousHtml: true }) // 转换为rehype（HTML）格式，允许原始HTML
        .use(rehypeRaw) // 处理Markdown中的原始HTML
        .use(rehypeStringify) // 将rehype（HTML）格式转换为字符串
        .process(processedMarkdown);
      
      const result = String(processedContent);
      
      // 添加全局样式
      const globalStyles = `
        <style>
          .markdown-content {
            line-height: 1.8;
            font-size: 1.1rem;
            color: #333;
          }
          .markdown-content h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-top: 2rem;
            margin-bottom: 1.5rem;
            color: #111;
            border-bottom: 1px solid #eaeaea;
            padding-bottom: 0.5rem;
          }
          .markdown-content h2 {
            font-size: 2rem;
            font-weight: 700;
            margin-top: 2rem;
            margin-bottom: 1rem;
            color: #222;
          }
          .markdown-content h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            color: #333;
          }
          .markdown-content h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
          }
          .markdown-content p {
            margin-bottom: 1.5rem;
            line-height: 1.8;
          }
          .markdown-content img {
            max-width: 100%;
            height: auto;
            margin: 2rem auto;
            display: block;
            border-radius: 4px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          }
          .markdown-content ul, .markdown-content ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
          }
          .markdown-content li {
            margin-bottom: 0.5rem;
          }
          .markdown-content blockquote {
            border-left: 4px solid #3b82f6;
            padding-left: 1rem;
            margin-left: 0;
            margin-right: 0;
            font-style: italic;
            color: #4b5563;
            margin-bottom: 1.5rem;
          }
          .markdown-content pre {
            background-color: #f3f4f6;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            margin-bottom: 1.5rem;
          }
          .markdown-content code {
            background-color: #f3f4f6;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
            font-size: 0.9rem;
          }
          .markdown-content hr {
            border: 0;
            height: 1px;
            background-color: #e5e7eb;
            margin: 2rem 0;
          }
          .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            font-size: 0.9rem;
          }
          .markdown-content th {
            background-color: #f9fafb;
            padding: 0.75rem 1rem;
            text-align: left;
            font-weight: 600;
            border: 1px solid #e5e7eb;
          }
          .markdown-content td {
            padding: 0.75rem 1rem;
            border: 1px solid #e5e7eb;
          }
          .markdown-content tr:nth-child(even) {
            background-color: #f9fafb;
          }
        </style>
      `;
      
      // 处理图片链接，确保正确加载
      let styledResult = result.replace(
        /<img src="\.\/images\/(.*?)"/g,
        '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
      ).replace(
        /<img src="images\/(.*?)"/g,
        '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
      ).replace(
        /<img src="\.\/(.*?)"/g,
        '<img src="https://blog-manage.chinaelectron.com/api/$1"'
      ).replace(
        /<img src="\.\.\/images\/(.*?)"/g,
        '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
      ).replace(
        /<img src="\.\.\/(.*?)"/g,
        '<img src="https://blog-manage.chinaelectron.com/api/$1"'
      );
      
      // 包装内容到带样式的div中
      styledResult = `${globalStyles}<div class="markdown-content">${styledResult}</div>`;
      
      return styledResult;
    } catch (fetchError) {
      // console.log('Error fetching from direct URL, using placeholder content');
      // 如果直接获取失败，返回占位内容
      return generateFallbackContent(contentUrl);
    }
  } catch (error) {
    // console.log('Error in fetchArticleContent, using placeholder content');
    // 返回友好的占位内容
    return generateFallbackContent(contentUrl);
  }
};

// 生成临时内容的辅助函数
function generateFallbackContent(contentUrl: string): string {
  return `
    <div class="bg-gray-50 border border-gray-200 rounded-md p-6 mb-8">
      <h2 class="text-xl font-semibold text-gray-700 mb-4">Article Content</h2>
      <p class="mb-4">This article is part of our spotlight collection highlighting industry trends and innovations.</p>
      <div class="mb-5">
        <h3 class="text-lg font-medium text-gray-700 mb-3">Topics covered in our articles:</h3>
        <ul class="list-disc pl-5 space-y-2">
          <li>Market analysis and industry perspectives</li>
          <li>Technology innovations and future trends</li>
          <li>Best practices and practical implementation guides</li>
          <li>Expert insights from industry leaders</li>
        </ul>
      </div>
      <p class="text-sm text-gray-500 italic">Browse our other spotlight articles for more insights.</p>
    </div>
  `;
}

// ==================== 购物车相关API ====================

// 购物车商品接口
export interface CartItem {
  cart_id: number;
  user_id: string;
  product_id: string;
  model_number: string;
  brand: string;
  description: string;
  unit_price: number;
  stock_quantity: number;
  cart_quantity: number;
  added_at: string;
  updated_at: string;
  available_stock?: number; // 来自联查的实时库存
  weight?: number; // 产品重量（单位：克）
}

// 添加商品到购物车
export const addToCart = async (cartItem: Omit<CartItem, 'cart_id' | 'added_at' | 'updated_at'>): Promise<{ success: boolean; message: string; cart_id?: number }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/cart`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(cartItem)
    });

    if (!response.ok) {
      throw new Error(`Failed to add item to cart: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      message: 'Item added to cart successfully',
      cart_id: result.insertId
    };
  } catch (error) {
    console.error('Error adding item to cart:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to add item to cart'
    };
  }
};

// 批量添加商品到购物车
export const addBatchToCart = async (cartItems: Omit<CartItem, 'cart_id' | 'added_at' | 'updated_at'>[]): Promise<{ success: boolean; message: string; inserted_count?: number }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/batch/insert/cart`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(cartItems)
    });

    if (!response.ok) {
      throw new Error(`Failed to add items to cart: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: true,
      message: `${result.inserted_count || cartItems.length} items added to cart successfully`,
      inserted_count: result.inserted_count
    };
  } catch (error) {
    console.error('Error adding items to cart:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to add items to cart'
    };
  }
};

// 获取产品详情（用于BOM项目添加到购物车）
export const getProductDetail = async (productId: string): Promise<any> => {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/products/${encodeURIComponent(productId)}`, {
      headers: {
        'Accept': 'application/json',
        'Origin': typeof window !== 'undefined' ? window.location.origin : 'https://chinaelectron.com'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch product details: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching product detail:', error);
    throw error;
  }
};

// 获取用户购物车列表（包含实时库存信息和重量）
export const getCartItems = async (userId: string): Promise<CartItem[]> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/join/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        select: [
          'c.cart_id',
          'c.user_id',
          'c.product_id',
          'c.model_number',
          'c.brand',
          'c.description',
          'c.unit_price',
          'c.stock_quantity',
          'c.cart_quantity',
          'c.added_at',
          'c.updated_at',
          's.stocks as available_stock',
          'p.weight'
        ],
        from: 'cart c',
        joins: [
          {
            type: 'LEFT',
            table: 'stocks s',
            on: 'c.product_id = s.code'
          },
          {
            type: 'LEFT',
            table: 'products202503 p',
            on: 'c.product_id = p.product_id'
          }
        ],
        where: {
          'c.user_id': userId
        },
        orderBy: 'c.added_at DESC'
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch cart items: ${response.status}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching cart items:', error);
    throw error;
  }
};

// 更新购物车商品数量
export const updateCartItemQuantity = async (cartId: number, quantity: number): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/cart?where_cart_id=${cartId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        cart_quantity: quantity,
        updated_at: new Date().toISOString()
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to update cart item: ${response.status}`);
    }

    return {
      success: true,
      message: 'Cart item updated successfully'
    };
  } catch (error) {
    console.error('Error updating cart item:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update cart item'
    };
  }
};

// 从购物车删除商品
export const removeFromCart = async (cartId: number): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/cart?where_cart_id=${cartId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to remove item from cart: ${response.status}`);
    }

    return {
      success: true,
      message: 'Item removed from cart successfully'
    };
  } catch (error) {
    console.error('Error removing item from cart:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to remove item from cart'
    };
  }
};

// 清空用户购物车
export const clearCart = async (userId: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/cart?where_user_id=${userId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to clear cart: ${response.status}`);
    }

    return {
      success: true,
      message: 'Cart cleared successfully'
    };
  } catch (error) {
    console.error('Error clearing cart:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to clear cart'
    };
  }
};

// 获取购物车商品数量统计
export const getCartItemCount = async (userId: string): Promise<number> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: 'SELECT SUM(cart_quantity) as total_count FROM cart WHERE user_id = ?',
        params: [userId]
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to get cart count: ${response.status}`);
    }

    const result = await response.json();
    return result.data?.[0]?.total_count || 0;
  } catch (error) {
    console.error('Error getting cart count:', error);
    return 0;
  }
};

// ==================== 订单相关API ====================

// 订单接口
export interface Order {
  order_id: string;
  user_id: string;
  order_date: string;
  order_total: number;
  order_status: string;
  payment_timeout: string;
  payment_method?: string;
  created_at: string;
  updated_at: string;
}

// 订单商品接口
export interface OrderItem {
  order_item_id: number;
  order_id: string;
  user_id: string;
  ce_id: string;
  mfr_number: string;
  manufacturer: string;
  description: string;
  quantity: number;
  unit_price: number;
  ext_price: number;
  created_at: string;
}

// 订单费用接口
export interface OrderFee {
  fee_id: number;
  order_id: string;
  fee_type: string;
  fee_amount: number;
  fee_description?: string;
  created_at: string;
}

// 支付记录接口
export interface PaymentRecord {
  id: number;
  order_id: string;
  user_id: string;
  payment_method: string;
  transaction_number: string;
  amount: number;
  payment_date: string;
  status: number; // 0=待处理, 1=成功, 2=失败, 3=退款
  remarks?: string;
  created_at: string;
  updated_at: string;
}

// 地址信息接口（根据实际数据库表结构）
export interface AddressInfo {
  address_id: number;
  user_id: string;
  address_type: 'shipping' | 'billing';
  is_default: boolean;
  first_name: string;
  last_name: string;
  phone: string;
  street_address: string;
  apt_suite_building?: string;
  country_region: string;
  postal_code: string;
  state_province: string;
  city: string;
  company_name?: string;
  created_at: string;
  updated_at: string;
}

// 合规声明接口
export interface ComplianceStatement {
  compliance_id: number;
  user_id: string;
  compliance_confirmed: boolean;
  resell_components: boolean;
  ultimate_consignee: string;
  country: string;
  application_type: string;
  additional_information?: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

// 创建订单请求接口
export interface CreateOrderRequest {
  user_id: string;
  shipping_address: AddressInfo;
  billing_address: AddressInfo;
  compliance_statement_id: number;
  shipping_method: string;
  cart_items: CartItem[];
  shipping_fee: number;
  handling_fee?: number;
  discount_amount?: number;
}

// 生成订单ID
const generateOrderId = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `ORD${year}${month}${day}${random}`;
};

// 创建订单
export const createOrder = async (orderRequest: CreateOrderRequest): Promise<{ success: boolean; message: string; order_id?: string }> => {
  try {
    console.log('Creating order with request:', orderRequest);
    const orderId = generateOrderId();
    const now = new Date().toISOString();

    // 计算订单总额
    const merchandiseTotal = orderRequest.cart_items.reduce((sum, item) => sum + (item.unit_price * item.cart_quantity), 0);
    const orderTotal = merchandiseTotal + orderRequest.shipping_fee + (orderRequest.handling_fee || 0) - (orderRequest.discount_amount || 0);

    console.log('Order calculations:', { orderId, merchandiseTotal, orderTotal });
    console.log('Creating order with data:', {
      order_id: orderId,
      user_id: orderRequest.user_id,
      order_date: now,
      order_total: orderTotal,
      order_status: 'PENDING_PAYMENT',
      payment_timeout: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      payment_method: null
    });

    // 1. 创建订单主记录 (匹配现有orders表结构)
    const orderResponse = await fetch(`${DB_API_BASE_URL}/table/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_id: orderId,
        user_id: orderRequest.user_id,
        order_date: now,
        order_total: orderTotal,
        order_status: 'PENDING_PAYMENT',
        payment_timeout: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24小时后超时
        payment_method: null
      })
    });

    if (!orderResponse.ok) {
      const errorText = await orderResponse.text();
      console.error('Create order response error:', errorText);
      throw new Error(`Failed to create order: ${orderResponse.status} - ${errorText}`);
    }

    // 2. 创建订单商品记录
    const orderItems = orderRequest.cart_items.map(item => ({
      order_id: orderId,
      user_id: orderRequest.user_id,
      ce_id: item.product_id,
      mfr_number: item.model_number,
      manufacturer: item.brand,
      description: item.description,
      quantity: item.cart_quantity,
      unit_price: item.unit_price,
      ext_price: item.unit_price * item.cart_quantity
    }));

    const orderItemsResponse = await fetch(`${DB_API_BASE_URL}/batch/insert/order_items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderItems)
    });

    if (!orderItemsResponse.ok) {
      const errorText = await orderItemsResponse.text();
      console.error('Create order items response error:', errorText);
      throw new Error(`Failed to create order items: ${orderItemsResponse.status} - ${errorText}`);
    }

    // 3. 创建订单费用记录 (匹配现有order_fees表结构)
    const orderFeeData = {
      order_id: orderId,
      user_id: orderRequest.user_id,
      merchandise_total: merchandiseTotal,
      freight: orderRequest.shipping_fee,
      handling_fee: orderRequest.handling_fee || 0,
      total: orderTotal
    };

    const orderFeesResponse = await fetch(`${DB_API_BASE_URL}/table/order_fees`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderFeeData)
    });

    if (!orderFeesResponse.ok) {
      const errorText = await orderFeesResponse.text();
      console.error('Create order fees response error:', errorText);
      throw new Error(`Failed to create order fees: ${orderFeesResponse.status} - ${errorText}`);
    }

    // 4. 创建收货地址记录到order_address表
    const shippingAddressData = {
      order_id: orderId,
      shipping_method: orderRequest.shipping_method,
      user_id: orderRequest.user_id,
      address_type: 'shipping',
      first_name: orderRequest.shipping_address.first_name,
      last_name: orderRequest.shipping_address.last_name,
      phone: orderRequest.shipping_address.phone,
      street_address: orderRequest.shipping_address.street_address,
      apt_suite_building: orderRequest.shipping_address.apt_suite_building,
      country_region: orderRequest.shipping_address.country_region,
      postal_code: orderRequest.shipping_address.postal_code,
      state_province: orderRequest.shipping_address.state_province,
      city: orderRequest.shipping_address.city,
      company_name: orderRequest.shipping_address.company_name,
      created_at: now,
      updated_at: now
    };

    const shippingAddressResponse = await fetch(`${DB_API_BASE_URL}/table/order_address`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(shippingAddressData)
    });

    if (!shippingAddressResponse.ok) {
      const errorText = await shippingAddressResponse.text();
      console.error('Create shipping address response error:', errorText);
      throw new Error(`Failed to create shipping address: ${shippingAddressResponse.status} - ${errorText}`);
    }

    // 5. 创建账单地址记录到order_address表
    const billingAddressData = {
      order_id: orderId,
      shipping_method: orderRequest.shipping_method,
      user_id: orderRequest.user_id,
      address_type: 'billing',
      first_name: orderRequest.billing_address.first_name,
      last_name: orderRequest.billing_address.last_name,
      phone: orderRequest.billing_address.phone,
      street_address: orderRequest.billing_address.street_address,
      apt_suite_building: orderRequest.billing_address.apt_suite_building,
      country_region: orderRequest.billing_address.country_region,
      postal_code: orderRequest.billing_address.postal_code,
      state_province: orderRequest.billing_address.state_province,
      city: orderRequest.billing_address.city,
      company_name: orderRequest.billing_address.company_name,
      created_at: now,
      updated_at: now
    };

    const billingAddressResponse = await fetch(`${DB_API_BASE_URL}/table/order_address`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(billingAddressData)
    });

    if (!billingAddressResponse.ok) {
      const errorText = await billingAddressResponse.text();
      console.error('Create billing address response error:', errorText);
      throw new Error(`Failed to create billing address: ${billingAddressResponse.status} - ${errorText}`);
    }

    // 6. 从购物车中移除已创建订单的商品
    // 只移除本次订单的商品，保留其他商品在购物车中
    for (const item of orderRequest.cart_items) {
      if (item.cart_id) {
        await removeFromCart(item.cart_id);
      }
    }

    return {
      success: true,
      message: 'Order created successfully',
      order_id: orderId
    };
  } catch (error) {
    console.error('Error creating order:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to create order'
    };
  }
};

// 获取订单详情（包含所有相关信息）
export const getOrderDetails = async (orderId: string): Promise<{
  order: Order;
  items: OrderItem[];
  fees: OrderFee[];
  shipping_info: any;
  billing_info: any;
  shipping_address: AddressInfo;
  billing_address: AddressInfo;
  compliance_statement: ComplianceStatement;
} | null> => {
  try {
    // 获取订单基本信息
    const orderResponse = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${orderId}`);
    if (!orderResponse.ok) {
      throw new Error(`Failed to fetch order: ${orderResponse.status}`);
    }
    const orderResult = await orderResponse.json();
    const order = orderResult.data?.[0];
    if (!order) return null;

    // 获取订单商品
    const itemsResponse = await fetch(`${DB_API_BASE_URL}/table/order_items?where_order_id=${orderId}`);
    if (!itemsResponse.ok) {
      throw new Error(`Failed to fetch order items: ${itemsResponse.status}`);
    }
    const itemsResult = await itemsResponse.json();
    const items = itemsResult.data || [];

    // 获取订单费用
    const feesResponse = await fetch(`${DB_API_BASE_URL}/table/order_fees?where_order_id=${orderId}`);
    if (!feesResponse.ok) {
      throw new Error(`Failed to fetch order fees: ${feesResponse.status}`);
    }
    const feesResult = await feesResponse.json();
    const fees = feesResult.data || [];

    // 获取订单的收货地址和账单地址（从order_address表）
    const orderAddressesResponse = await fetch(`${DB_API_BASE_URL}/table/order_address?where_order_id=${orderId}`);
    if (!orderAddressesResponse.ok) {
      throw new Error(`Failed to fetch order addresses: ${orderAddressesResponse.status}`);
    }
    const orderAddressesResult = await orderAddressesResponse.json();
    const orderAddresses = orderAddressesResult.data || [];

    // 查找收货地址和账单地址
    const shipping_address = orderAddresses.find((addr: any) => addr.address_type === 'shipping');
    const billing_address = orderAddresses.find((addr: any) => addr.address_type === 'billing');

    // 构造shipping_info和billing_info对象以保持向后兼容
    const shipping_info = shipping_address ? {
      shipping_method: shipping_address.shipping_method,
      order_id: orderId
    } : null;

    const billing_info = billing_address ? {
      order_id: orderId
    } : null;

    // 获取合规声明 - 查找与此订单相关的合规声明
    const complianceResponse = await fetch(`${DB_API_BASE_URL}/table/compliance_statement?where_user_id=${order.user_id}&order_by=created_at&order_dir=DESC&limit=1`);
    if (!complianceResponse.ok) {
      throw new Error(`Failed to fetch compliance statement: ${complianceResponse.status}`);
    }
    const complianceResult = await complianceResponse.json();
    const compliance_statement = complianceResult.data?.[0];

    console.log('Order details fetched:', {
      order,
      items: items.length,
      fees: fees.length,
      shipping_info,
      billing_info,
      shipping_address: shipping_address ? 'Found' : 'Not found',
      billing_address: billing_address ? 'Found' : 'Not found',
      compliance_statement: compliance_statement ? 'Found' : 'Not found'
    });

    return {
      order,
      items,
      fees,
      shipping_info,
      billing_info,
      shipping_address,
      billing_address,
      compliance_statement
    };
  } catch (error) {
    console.error('Error fetching order details:', error);
    throw error;
  }
};

// 获取用户订单列表
export const getUserOrders = async (userId: string): Promise<Order[]> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/orders?where_user_id=${userId}&order_by=order_date&order_dir=DESC`);

    if (!response.ok) {
      throw new Error(`Failed to fetch user orders: ${response.status}`);
    }

    const result = await response.json();
    const orders = result.data || [];

    // 为每个订单获取商品数量
    const ordersWithItemCount = await Promise.all(
      orders.map(async (order: Order) => {
        try {
          const itemsResponse = await fetch(`${DB_API_BASE_URL}/table/order_items?where_order_id=${order.order_id}`);
          if (itemsResponse.ok) {
            const itemsResult = await itemsResponse.json();
            const items = itemsResult.data || [];
            return {
              ...order,
              item_count: items.length
            };
          }
          return {
            ...order,
            item_count: 0
          };
        } catch (error) {
          console.error(`Failed to get item count for order ${order.order_id}:`, error);
          return {
            ...order,
            item_count: 0
          };
        }
      })
    );

    return ordersWithItemCount;
  } catch (error) {
    console.error('Error fetching user orders:', error);
    throw error;
  }
};

// 获取用户地址列表
export const getUserAddresses = async (userId: string, addressType?: 'shipping' | 'billing'): Promise<AddressInfo[]> => {
  try {
    let sql = 'SELECT * FROM user_addresses WHERE user_id = ?';
    const params = [userId];

    if (addressType) {
      sql += ' AND address_type = ?';
      params.push(addressType);
    }

    sql += ' ORDER BY is_default DESC, created_at DESC';

    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql,
        params
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Get user addresses response error:', errorText);
      throw new Error(`Failed to fetch user addresses: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching user addresses:', error);
    throw error;
  }
};

// 获取用户合规声明列表
export const getUserComplianceStatements = async (userId: string): Promise<ComplianceStatement[]> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: 'SELECT * FROM compliance_statement WHERE user_id = ? ORDER BY is_default DESC, created_at DESC',
        params: [userId]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Get compliance statements response error:', errorText);
      throw new Error(`Failed to fetch compliance statements: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching compliance statements:', error);
    throw error;
  }
};

// 添加新的合规声明
export const addComplianceStatement = async (statement: Omit<ComplianceStatement, 'compliance_id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; message: string; compliance_id?: number }> => {
  try {
    const now = new Date().toISOString();

    // 如果设置为默认，先取消其他默认声明
    if (statement.is_default) {
      try {
        await fetch(`${DB_API_BASE_URL}/query/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: 'UPDATE compliance_statement SET is_default = ?, updated_at = ? WHERE user_id = ?',
            params: [0, now, statement.user_id]
          })
        });
      } catch (updateError) {
        console.warn('Failed to update existing default compliance statements:', updateError);
      }
    }

    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: `INSERT INTO compliance_statement (
          user_id, compliance_confirmed, resell_components, ultimate_consignee,
          country, application_type, additional_information, is_default, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: [
          statement.user_id,
          statement.compliance_confirmed ? 1 : 0,
          statement.resell_components ? 1 : 0,
          statement.ultimate_consignee,
          statement.country,
          statement.application_type,
          statement.additional_information || null,
          statement.is_default ? 1 : 0,
          now,
          now
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Add compliance statement response error:', errorText);
      throw new Error(`Failed to add compliance statement: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('Add compliance statement result:', result);

    // 尝试多种可能的ID字段名
    const complianceId = result.data?.insertId ||
                        result.insertId ||
                        result.data?.compliance_id ||
                        result.compliance_id ||
                        result.data?.statement_id ||
                        result.statement_id;

    return {
      success: true,
      message: 'Compliance statement added successfully',
      compliance_id: complianceId
    };
  } catch (error) {
    console.error('Error adding compliance statement:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to add compliance statement'
    };
  }
};

// 更新合规声明
export const updateComplianceStatement = async (complianceId: number, statement: Partial<Omit<ComplianceStatement, 'compliance_id' | 'created_at' | 'updated_at'>>): Promise<{ success: boolean; message: string }> => {
  try {
    const now = new Date().toISOString();

    // 如果设置为默认，先取消其他默认声明
    if (statement.is_default && statement.user_id) {
      try {
        await fetch(`${DB_API_BASE_URL}/query/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: 'UPDATE compliance_statement SET is_default = ?, updated_at = ? WHERE user_id = ? AND compliance_id != ?',
            params: [false, now, statement.user_id, complianceId]
          })
        });
      } catch (updateError) {
        console.warn('Failed to update existing default compliance statements:', updateError);
      }
    }

    // 构建更新的字段和值
    const updateFields = [];
    const updateValues = [];

    Object.entries(statement).forEach(([key, value]) => {
      if (key !== 'user_id') { // user_id 不应该被更新
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });

    updateFields.push('updated_at = ?');
    updateValues.push(now);
    updateValues.push(complianceId); // WHERE 条件的参数

    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: `UPDATE compliance_statement SET ${updateFields.join(', ')} WHERE compliance_id = ?`,
        params: updateValues
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Update compliance statement response error:', errorText);
      throw new Error(`Failed to update compliance statement: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Compliance statement updated successfully'
    };
  } catch (error) {
    console.error('Error updating compliance statement:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update compliance statement'
    };
  }
};

// 删除合规声明
export const deleteComplianceStatement = async (complianceId: number): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: 'DELETE FROM compliance_statement WHERE compliance_id = ?',
        params: [complianceId]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Delete compliance statement response error:', errorText);
      throw new Error(`Failed to delete compliance statement: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Compliance statement deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting compliance statement:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to delete compliance statement'
    };
  }
};

// 添加新地址
export const addUserAddress = async (address: Omit<AddressInfo, 'address_id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; message: string; address_id?: number }> => {
  try {
    const now = new Date().toISOString();

    // 如果设置为默认地址，先取消同类型的其他默认地址
    if (address.is_default) {
      try {
        await fetch(`${DB_API_BASE_URL}/query/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: 'UPDATE user_addresses SET is_default = ?, updated_at = ? WHERE user_id = ? AND address_type = ?',
            params: [false, now, address.user_id, address.address_type]
          })
        });
      } catch (updateError) {
        console.warn('Failed to update existing default addresses:', updateError);
        // 继续执行，不阻止添加新地址
      }
    }

    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: `INSERT INTO user_addresses (
          user_id, address_type, is_default, first_name, last_name, phone,
          street_address, apt_suite_building, country_region, postal_code,
          state_province, city, company_name, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: [
          address.user_id,
          address.address_type,
          address.is_default,
          address.first_name,
          address.last_name,
          address.phone,
          address.street_address,
          address.apt_suite_building || null,
          address.country_region,
          address.postal_code,
          address.state_province,
          address.city,
          address.company_name || null,
          now,
          now
        ]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Add address response error:', errorText);
      throw new Error(`Failed to add address: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    return {
      success: true,
      message: 'Address added successfully',
      address_id: result.data?.insertId || result.insertId
    };
  } catch (error) {
    console.error('Error adding address:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to add address'
    };
  }
};

// 更新地址
export const updateUserAddress = async (addressId: number, address: Partial<Omit<AddressInfo, 'address_id' | 'created_at' | 'updated_at'>>): Promise<{ success: boolean; message: string }> => {
  try {
    const now = new Date().toISOString();

    // 如果设置为默认地址，先取消同类型的其他默认地址
    if (address.is_default && address.user_id && address.address_type) {
      try {
        await fetch(`${DB_API_BASE_URL}/query/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            sql: 'UPDATE user_addresses SET is_default = ?, updated_at = ? WHERE user_id = ? AND address_type = ? AND address_id != ?',
            params: [false, now, address.user_id, address.address_type, addressId]
          })
        });
      } catch (updateError) {
        console.warn('Failed to update existing default addresses:', updateError);
      }
    }

    // 构建更新的字段和值
    const updateFields = [];
    const updateValues = [];

    Object.entries(address).forEach(([key, value]) => {
      if (key !== 'user_id') { // user_id 不应该被更新
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    });

    updateFields.push('updated_at = ?');
    updateValues.push(now);
    updateValues.push(addressId); // WHERE 条件的参数

    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: `UPDATE user_addresses SET ${updateFields.join(', ')} WHERE address_id = ?`,
        params: updateValues
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Update address response error:', errorText);
      throw new Error(`Failed to update address: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Address updated successfully'
    };
  } catch (error) {
    console.error('Error updating address:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update address'
    };
  }
};

// 删除地址
export const deleteUserAddress = async (addressId: number): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: 'DELETE FROM user_addresses WHERE address_id = ?',
        params: [addressId]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Delete address response error:', errorText);
      throw new Error(`Failed to delete address: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Address deleted successfully'
    };
  } catch (error) {
    console.error('Error deleting address:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to delete address'
    };
  }
};

// 设置默认地址
export const setDefaultAddress = async (addressId: number, userId: string, addressType: 'shipping' | 'billing'): Promise<{ success: boolean; message: string }> => {
  try {
    const now = new Date().toISOString();

    // 先取消同类型的其他默认地址
    try {
      await fetch(`${DB_API_BASE_URL}/query/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sql: 'UPDATE user_addresses SET is_default = ?, updated_at = ? WHERE user_id = ? AND address_type = ?',
          params: [false, now, userId, addressType]
        })
      });
    } catch (updateError) {
      console.warn('Failed to update existing default addresses:', updateError);
    }

    // 设置新的默认地址
    const response = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: 'UPDATE user_addresses SET is_default = ?, updated_at = ? WHERE address_id = ?',
        params: [true, now, addressId]
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Set default address response error:', errorText);
      throw new Error(`Failed to set default address: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Default address updated successfully'
    };
  } catch (error) {
    console.error('Error setting default address:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to set default address'
    };
  }
};

// 重试机制辅助函数
const retryWithDelay = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 0
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      console.warn(`Attempt ${attempt}/${maxRetries} failed:`, error);

      if (attempt === maxRetries) {
        throw lastError;
      }

      // 如果设置了延迟，等待指定时间后重试
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError!;
};

// LCSC Express API 相关接口
export interface ExpressOption {
  boxWeight: number | null;
  costOrigin: number;
  days: string;
  expressType: string;
  forwarderType: string;
  expressNameEn: string;
  expressNameCn: string;
  remoteFee: number;
  expressCode: string;
  shipmentType: 'lc' | 'customer' | 'sf';
  remoteMsg: string | null;
  tips: string | null;
  grossWeight: number;
  taxName: string | null;
  currencyCode: string;
}

export interface ExpressApiResponse {
  success: boolean;
  data?: {
    code: number;
    msg: string | null;
    result: {
      expressShoppingMap: {
        sf: ExpressOption[];
        lc: ExpressOption[];
        customer: ExpressOption[];
      };
    };
  };
  requestData?: any;
  timestamp?: string;
  error?: string;
  message?: string;
}

// 获取LCSC快递选项（带重试机制）
export const getLCSCExpressOptions = async (
  countryCode: string,
  weight: number,
  orderAmt: number,
  province?: string,
  city?: string,
  postCode?: string,
  orderItemTotal?: number
): Promise<ExpressOption[]> => {
  const fetchExpressOptions = async (): Promise<ExpressOption[]> => {
    const requestData = {
      countryCode,
      weight,
      orderAmt,
      province: province || '',
      city: city || '',
      postCode: postCode || '',
      currencyCode: 'USD',
      orderItemTotal: orderItemTotal || 1,
      vat: false,
      isFrom: true,
      isHasBuzzer: false,
      isIncludeSpecialCategory: false
    };

    const response = await fetch('https://get-express.chinaelectron.com/api/express', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`Express API responded with status: ${response.status}`);
    }

    const result: ExpressApiResponse = await response.json();

    if (!result.success || !result.data?.result?.expressShoppingMap) {
      throw new Error(result.message || 'Failed to get express options');
    }

    // 只使用lc数组的数据
    const lcOptions = result.data.result.expressShoppingMap.lc || [];

    return lcOptions;
  };

  try {
    // 使用重试机制，最多重试3次，不使用延迟
    return await retryWithDelay(fetchExpressOptions, 3, 0);
  } catch (error) {
    console.error('Error fetching LCSC express options after retries:', error);
    // 不使用静态数据，直接抛出错误
    throw new Error(`Failed to get shipping options after 3 attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// 计算运费（集成LCSC API，使用真实重量）
export const calculateShippingFee = async (
  items: CartItem[],
  selectedExpressCode: string,
  countryCode: string,
  province?: string,
  city?: string,
  postCode?: string
): Promise<{
  shipping_fee: number;
  handling_fee: number;
  discount_amount: number;
  estimated_weight: number;
  express_options: ExpressOption[];
}> => {
  const calculateShippingFeeInternal = async (): Promise<{
    shipping_fee: number;
    handling_fee: number;
    discount_amount: number;
    estimated_weight: number;
    express_options: ExpressOption[];
  }> => {
    // 计算重量（使用真实产品重量，如果没有则使用默认0.1g）
    const estimatedWeight = items.reduce((total, item) => {
      const itemWeight = item.weight || 0.1; // 如果没有重量数据，使用默认0.1g
      return total + (item.cart_quantity * itemWeight);
    }, 0);

    console.log('📊 Weight calculation details:', {
      items: items.map(item => ({
        product_id: item.product_id,
        model: item.model_number,
        quantity: item.cart_quantity,
        weight: item.weight || 0.1,
        totalWeight: item.cart_quantity * (item.weight || 0.1)
      })),
      totalWeight: estimatedWeight
    });

    // 计算订单总额
    const merchandiseTotal = items.reduce((sum, item) => sum + (item.unit_price * item.cart_quantity), 0);

    // 获取LCSC快递选项（已包含重试机制）
    const expressOptions = await getLCSCExpressOptions(
      countryCode,
      Math.max(estimatedWeight, 1), // 最小重量1g
      merchandiseTotal,
      province,
      city,
      postCode,
      items.length
    );

    // 查找选中的快递选项
    const selectedOption = expressOptions.find(option => option.expressCode === selectedExpressCode);

    if (!selectedOption) {
      throw new Error(`Selected shipping method '${selectedExpressCode}' not found in available options`);
    }

    const shippingFee = selectedOption.costOrigin;

    // 处理费
    const handlingFee = 3;

    // 计算运费折扣
    let discountAmount = 0;
    if (merchandiseTotal >= 35.47) {
      discountAmount = Math.min(8, shippingFee);
    }
    if (merchandiseTotal >= 136.47) {
      discountAmount = Math.min(20, shippingFee);
    }

    return {
      shipping_fee: shippingFee,
      handling_fee: handlingFee,
      discount_amount: discountAmount,
      estimated_weight: estimatedWeight,
      express_options: expressOptions
    };
  };

  try {
    // 使用重试机制，最多重试3次，不使用延迟
    return await retryWithDelay(calculateShippingFeeInternal, 3, 0);
  } catch (error) {
    console.error('Error calculating shipping fee after retries:', error);
    // 不使用静态数据，直接抛出错误
    throw new Error(`Failed to calculate shipping fee after 3 attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// ==================== 支付记录相关API ====================

// 获取订单支付记录
export const getOrderPaymentRecords = async (orderId: string): Promise<PaymentRecord[]> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/payment?where_order_id=${orderId}&order_by=payment_date&order_dir=DESC`);

    if (!response.ok) {
      throw new Error(`Failed to fetch payment records: ${response.status}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching payment records:', error);
    throw error;
  }
};

// 获取用户支付记录
export const getUserPaymentRecords = async (userId: string, limit = 50): Promise<PaymentRecord[]> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/payment?where_user_id=${userId}&order_by=payment_date&order_dir=DESC&limit=${limit}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch user payment records: ${response.status}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error('Error fetching user payment records:', error);
    throw error;
  }
};

// 根据交易号查询支付记录
export const getPaymentRecordByTransaction = async (transactionNumber: string): Promise<PaymentRecord | null> => {
  try {
    const response = await fetch(`${DB_API_BASE_URL}/table/payment?where_transaction_number=${transactionNumber}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch payment record: ${response.status}`);
    }

    const result = await response.json();
    const records = result.data || [];
    return records.length > 0 ? records[0] : null;
  } catch (error) {
    console.error('Error fetching payment record by transaction:', error);
    throw error;
  }
};

// 创建支付记录
export const createPaymentRecord = async (paymentData: Omit<PaymentRecord, 'id' | 'created_at' | 'updated_at'>): Promise<{ success: boolean; message: string; payment_id?: number }> => {
  try {
    const now = new Date().toISOString();
    const fullPaymentData = {
      ...paymentData,
      created_at: now,
      updated_at: now
    };

    const response = await fetch(`${DB_API_BASE_URL}/table/payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(fullPaymentData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to create payment record: ${response.status} - ${errorText}`);
    }

    const result = await response.json();

    return {
      success: true,
      message: 'Payment record created successfully',
      payment_id: result.data?.id
    };
  } catch (error) {
    console.error('Error creating payment record:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to create payment record'
    };
  }
};

// 更新支付记录状态
export const updatePaymentRecordStatus = async (
  transactionNumber: string,
  status: number,
  remarks?: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const updateData: any = {
      status,
      updated_at: new Date().toISOString()
    };

    if (remarks) {
      updateData.remarks = remarks;
    }

    const response = await fetch(`${DB_API_BASE_URL}/table/payment?where_transaction_number=${transactionNumber}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to update payment record: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Payment record updated successfully'
    };
  } catch (error) {
    console.error('Error updating payment record:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update payment record'
    };
  }
};

// 支付状态枚举
export const PaymentStatus = {
  PENDING: 0,
  SUCCESS: 1,
  FAILED: 2,
  REFUNDED: 3
} as const;

// 获取支付状态文本
export const getPaymentStatusText = (status: number): string => {
  switch (status) {
    case PaymentStatus.PENDING:
      return 'Pending';
    case PaymentStatus.SUCCESS:
      return 'Success';
    case PaymentStatus.FAILED:
      return 'Failed';
    case PaymentStatus.REFUNDED:
      return 'Refunded';
    default:
      return 'Unknown';
  }
};