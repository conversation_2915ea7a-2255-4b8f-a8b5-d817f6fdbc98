/**
 * 获取产品图片URL
 * @param imageList 图片名称列表
 * @param index 要获取的图片索引，默认为0（第一张图片）
 * @returns 图片URL
 */
export function getProductImageUrl(imageList: string[] | undefined, index: number = 0): string {
  // 检查图片列表是否存在且不为空
  if (!imageList || imageList.length === 0) {
    // 返回默认图片URL
    return `/api/product-image?name=default.jpg`;
  }
  
  // 获取指定索引的图片，如果索引超出范围，则使用第一张图片
  const imageName = index < imageList.length ? imageList[index] : imageList[0];
  
  // 返回图片URL
  return `/api/product-image?name=${encodeURIComponent(imageName)}`;
} 