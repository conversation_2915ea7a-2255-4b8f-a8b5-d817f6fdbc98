'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';

interface BreadcrumbProps {
  pathname: string;
}

interface BreadcrumbItem {
  label: string;
  path: string;
}

interface EntityData {
  name_en?: string;
  model?: string;
  category_path?: string[];
  category_id?: string;
}

interface CategoryData {
  code?: string;
  name_en?: string;
  name_cn?: string;
  name_ru?: string;
  level?: number;
  parent_code?: string;
}

// 从新URL格式中提取产品ID
function extractProductIdFromUrl(urlPath: string): string {
  // 针对CMA格式产品ID的精确匹配
  const cmaMatch = urlPath.match(/CMA\d{9,12}/);
  if (cmaMatch) {
    return cmaMatch[0];
  }
  
  // 通过末尾是否有CMA+数字来识别产品ID
  if (urlPath.includes('-CMA')) {
    const parts = urlPath.split('-');
    for (let i = parts.length - 1; i >= 0; i--) {
      if (parts[i].startsWith('CMA') && /CMA\d+/.test(parts[i])) {
        return parts[i];
      }
    }
  }

  // 新URL格式: {一级分类}-{二级分类}-{三级分类}-{品牌名称(可能包含横杠)}-{model(可能包含横杠)}-{产品id}
  // 提取最后一个部分作为产品ID
  const parts = urlPath.split('-');
  
  // 检查最后一部分是否是产品ID (CMA格式或纯数字)
  const lastPart = parts[parts.length - 1];
  if (lastPart && (lastPart.startsWith('CMA') || /^\d+$/.test(lastPart))) {
    return lastPart;
  }
  
  // 如果我们有足够的部分，假设最后一个是产品ID
  if (parts.length >= 6) {
    return parts[parts.length - 1];
  }
  
  // 如果无法按新格式解析，返回原始路径
  return urlPath;
}

// 服务器组件，用于获取分类名称
async function fetchCategoryName(categoryId: string): Promise<string> {
  try {
    if (!categoryId) return '';
    
    const response = await fetch(`https://webapi.chinaelectron.com/categories/${categoryId}`, {
      // 添加缓存和重新验证策略
      next: { revalidate: 3600 }, // 1小时缓存
      // 确保请求在服务端发出
      headers: {
        'Accept': 'application/json',
        'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
      }
    });
    
    if (!response.ok) return '';
    
    const data: CategoryData = await response.json();
    return data.name_en || '';
  } catch (error) {
    console.error(`Error fetching category data:`, error);
    return '';
  }
}

// 服务器组件，用于获取实体名称
async function fetchEntityName(type: string, id: string): Promise<{name: string, categoryData?: {path: string, id: string}}> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/${type}/${id}`, {
      // 添加缓存和重新验证策略
      next: { revalidate: 3600 }, // 1小时缓存
      // 确保请求在服务端发出
      headers: {
        'Accept': 'application/json',
        'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
      }
    });
    
    if (!response.ok) return { name: id };
    
    const data: EntityData = await response.json();
    
    // 对于产品，返回产品型号和分类信息
    if (type === 'products') {
      return { 
        name: data.model || id,
        categoryData: {
          path: '', // 将由 fetchCategoryName 填充
          id: data.category_id || ''
        }
      };
    }
    
    // 对于其他实体，只返回名称
    return { name: data.name_en || id };
  } catch (error) {
    console.error(`Error fetching ${type} data:`, error);
    return { name: id };
  }
}

// 获取专题信息
async function fetchInsightTopic(id: string): Promise<string> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/insights/${id}`, {
      next: { revalidate: 3600 } // 1小时缓存
    });
    
    if (!response.ok) return id;
    
    const data = await response.json();
    return data.title || id;
  } catch (error) {
    console.error(`Error fetching insight data:`, error);
    return id;
  }
}

// 获取文章信息
async function fetchBlogArticle(id: string): Promise<string> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/blogs/${id}`, {
      next: { revalidate: 3600 } // 1小时缓存
    });
    
    if (!response.ok) return id;
    
    const data = await response.json();
    return data.title || id;
  } catch (error) {
    console.error(`Error fetching article data:`, error);
    return id;
  }
}

// 获取Spotlight信息
async function fetchSpotlight(id: string): Promise<string> {
  try {
    // 此处应调用Spotlight API，暂时使用blog API代替
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/blogs/${id}`, {
      next: { revalidate: 3600 } // 1小时缓存
    });
    
    if (!response.ok) return id;
    
    const data = await response.json();
    return data.title || id;
  } catch (error) {
    console.error(`Error fetching spotlight data:`, error);
    return id;
  }
}

// 生成面包屑函数
async function generateBreadcrumbs(pathname: string): Promise<BreadcrumbItem[]> {
  const paths = pathname.split('/').filter(path => path);
  const items: BreadcrumbItem[] = [];
  let currentPath = '';

  // Always add home as first item
  items.push({ label: 'Home', path: '/' });

  // 处理insights专题和文章页面
  if (paths.length >= 1 && paths[0] === 'insights') {
    // 添加Insights作为第二级
    items.push({ 
      label: 'Insights', 
      path: '/insights' 
    });
    
    // 如果只是/insights路径，不再添加额外的面包屑
    if (paths.length === 1) {
      return items;
    }
    
    // 处理专题详情页
    if (paths.length >= 2) {
      const topicId = paths[1];
      const topicName = await fetchInsightTopic(topicId);
      
      // 添加专题名称
      const topicPath = `/insights/${topicId}`;
      items.push({ 
        label: topicName, 
        path: topicPath 
      });
      
      // 如果是文章详情页，还需要添加文章标题
      if (paths.length >= 3) {
        const articleId = paths[2];
        const articleTitle = await fetchBlogArticle(articleId);
        
        // 添加文章标题
        items.push({ 
          label: articleTitle, 
          path: pathname 
        });
      }
      
      return items;
    }
  }
  
  // 处理spotlight页面
  if (paths.length >= 1 && paths[0] === 'spotlight') {
    // 添加Spotlight作为第二级
    items.push({ 
      label: 'Spotlight', 
      path: '/spotlight' 
    });
    
    // 如果只是/spotlight路径，不再添加额外的面包屑
    if (paths.length === 1) {
      return items;
    }
    
    // 处理spotlight详情页
    if (paths.length >= 2) {
      const spotlightId = paths[1];
      const spotlightTitle = await fetchSpotlight(spotlightId);
      
      // 添加spotlight标题
      items.push({ 
        label: spotlightTitle, 
        path: pathname 
      });
      
      return items;
    }
  }

  // 处理产品详情页的特殊情况 - 适配新的URL格式
  if (paths.length >= 2 && paths[0] === 'product') {
    const productPath = paths[1];
    
    // 提取产品ID，无论是新格式还是旧格式
    const productId = extractProductIdFromUrl(productPath);
    // console.log('Extracted product ID for breadcrumb:', productId);
    
    // 通过API获取产品信息和分类信息
    const { name, categoryData } = await fetchEntityName('products', productId);
    
    // 不再添加"Products"作为第二级面包屑
    
    // 添加分类作为第二级面包屑（如果有）
    if (categoryData && categoryData.id) {
      // 获取分类名称
      const categoryName = await fetchCategoryName(categoryData.id);
      
      if (categoryName) {
        items.push({ 
          label: categoryName, 
          path: `/search?category=${categoryData.id}` 
        });
      }
    }
    
    // 添加产品型号作为最后一级
    items.push({ 
      label: name, 
      path: pathname 
    });
    
    return items;
  }

  // 处理其他页面
  for (let i = 0; i < paths.length; i++) {
    const path = paths[i];
    currentPath += `/${path}`;
    
    let label = path;
    
    // 处理特殊路径
    if (i === 0) {
      // 处理第一级路径
      if (path === 'product') {
        label = 'Product';
      } else {
        label = path.charAt(0).toUpperCase() + path.slice(1);
      }
    } else {
      // 处理第二级及以后的路径
      const prevPath = paths[i - 1].toLowerCase();
      
      if (prevPath === 'manufacturers') {
        const { name } = await fetchEntityName('brands', path);
        label = name;
      } else if (prevPath === 'distributors') {
        const { name } = await fetchEntityName('distributors', path);
        label = name;
      } else if (prevPath === 'product') {
        // 对于新的产品URL格式，使用提取的产品ID
        const productId = extractProductIdFromUrl(path);
        const { name } = await fetchEntityName('products', productId);
        label = name;
      } else {
        // 默认处理：将连字符转换为空格并首字母大写
        label = path
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
      }
    }
    
    items.push({ label, path: currentPath });
  }

  return items;
}

export default function Breadcrumb({ pathname }: BreadcrumbProps) {
  // 确保初始状态在服务器端和客户端一致
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([{ label: 'Home', path: '/' }]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 从服务器获取已处理的面包屑数据
    async function fetchBreadcrumbs() {
      try {
        setIsLoading(true);
        // 使用一个稳定的 key 来避免水合错误
        const cacheKey = encodeURIComponent(pathname);
        const response = await fetch(`/api/breadcrumbs?path=${cacheKey}`);
        if (!response.ok) {
          throw new Error('Failed to fetch breadcrumbs');
        }
        const data = await response.json();
        setBreadcrumbs(data.breadcrumbs);
      } catch (error) {
        console.error('Error fetching breadcrumbs:', error);
        // 设置基本的面包屑，以防API失败
        const fallbackBreadcrumbs = generateFallbackBreadcrumbs(pathname);
        setBreadcrumbs(fallbackBreadcrumbs);
      } finally {
        setIsLoading(false);
      }
    }

    // 仅在客户端执行，防止服务端和客户端渲染差异
    if (typeof window !== 'undefined') {
      fetchBreadcrumbs();
    }
  }, [pathname]);

  // 生成基本的面包屑作为后备方案
  function generateFallbackBreadcrumbs(path: string): BreadcrumbItem[] {
    const paths = path.split('/').filter(p => p);
    const items: BreadcrumbItem[] = [{ label: 'Home', path: '/' }];
    let currentPath = '';

    paths.forEach(path => {
      currentPath += `/${path}`;
      const label = path.charAt(0).toUpperCase() + path.slice(1);
      items.push({ label, path: currentPath });
    });

    return items;
  }

  // 在服务器端渲染时使用一个简单的加载骨架屏，确保一致性
  if (isLoading) {
    return (
      <nav className="" aria-label="Breadcrumb">
        <div className="px-16 py-3">
          <div className="animate-pulse h-4 w-48 bg-gray-200 rounded"></div>
        </div>
      </nav>
    );
  }

  if (breadcrumbs.length <= 1) return null;

  return (
    <nav className="" aria-label="Breadcrumb">
      <div className="px-4 lg:px-14 py-3">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((item, index) => (
            <li key={`breadcrumb-${index}-${item.path}`} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-gray-400">/</span>
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="text-gray-500">{item.label}</span>
              ) : (
                <Link 
                  href={item.path}
                  className="text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
} 