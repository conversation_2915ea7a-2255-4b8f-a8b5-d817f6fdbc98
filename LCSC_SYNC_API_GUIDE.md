# LCSC产品同步API使用指南

## 概述
这个Cloudflare Workers API用于同步LCSC产品信息到本地数据库。它会检查LCSC编码，如果产品不存在则自动获取并创建产品信息。

## 部署步骤

### 1. 创建Cloudflare Worker
```bash
# 使用Wrangler CLI创建新的Worker
wrangler init lcsc-sync-worker
cd lcsc-sync-worker

# 将lcsc-product-sync.js的内容复制到src/index.js
```

### 2. 配置wrangler.toml
```toml
name = "lcsc-sync-worker"
main = "src/index.js"
compatibility_date = "2024-01-01"

# D1数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "your-database-name"
database_id = "your-database-id"

# R2存储桶绑定
[[r2_buckets]]
binding = "lcsc"
bucket_name = "lcsc"
```

### 3. 部署Worker
```bash
wrangler deploy
```

## API接口说明

### 基本信息
- **URL**: `https://check-product.962692556.workers.dev`
- **方法**: GET
- **参数**: lcscCode (必需)

### 请求格式
```
GET https://check-product.962692556.workers.dev?lcscCode=C14663
```

### 请求参数
| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| lcscCode | string | 是 | LCSC产品编码，如C14663 |

## 响应格式

### 成功响应
```json
{
  "product_id": "CMA200000001",
  "status": "created",
  "lcsc_code": "C14663",
  "details": {
    "new_product": true,
    "new_brand": true,
    "new_mapping": true,
    "updated_tables": [
      "component_mapping",
      "brands",
      "brand_mapping",
      "products202503",
      "product_category_map",
      "price",
      "stocks",
      "parameters"
    ],
    "brand_info": {
      "brand_id": "CEB200001",
      "is_new": true,
      "brand_name": "YAGEO",
      "domestic": "international"
    },
    "category_info": {
      "category_code": "CMC0016015001"
    },
    "price_count": 5,
    "parameter_count": 8,
    "stock_info": {
      "stock_quantity": 60691400
    }
  }
}
```

### 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| product_id | string | 生成的CE产品编码 |
| status | string | 处理状态：exists(已存在)/created(新创建)/updated(已更新) |
| lcsc_code | string | 原始LCSC编码 |
| details | object | 详细处理信息 |
| details.new_product | boolean | 是否为新产品 |
| details.new_brand | boolean | 是否创建了新品牌 |
| details.new_mapping | boolean | 是否创建了新的编码映射 |
| details.updated_tables | array | 更新的数据表列表 |
| details.brand_info | object | 品牌信息 |
| details.category_info | object | 分类信息 |
| details.price_count | number | 插入的价格记录数量 |
| details.parameter_count | number | 插入的参数记录数量 |
| details.stock_info | object | 库存信息 |

### 错误响应
```json
{
  "error": "Missing lcscCode parameter"
}
```

## 使用示例

### JavaScript/Node.js
```javascript
async function syncLcscProduct(lcscCode) {
  try {
    const response = await fetch(`https://check-product.962692556.workers.dev?lcscCode=${lcscCode}`);
    const result = await response.json();

    if (response.ok) {
      console.log('同步成功:', result);

      // 处理详细信息
      const { details } = result;
      console.log(`产品ID: ${result.product_id}`);
      console.log(`状态: ${result.status}`);

      if (details.new_product) {
        console.log('✓ 创建了新产品');
      }

      if (details.new_brand) {
        console.log(`✓ 创建了新品牌: ${details.brand_info.brand_name} (${details.brand_info.brand_id})`);
      }

      if (details.new_mapping) {
        console.log('✓ 创建了新的编码映射');
      }

      console.log(`更新的表: ${details.updated_tables.join(', ')}`);
      console.log(`价格记录数: ${details.price_count}`);
      console.log(`参数记录数: ${details.parameter_count}`);
      console.log(`库存数量: ${details.stock_info.stock_quantity}`);

      return result.product_id;
    } else {
      console.error('同步失败:', result.error);
      return null;
    }
  } catch (error) {
    console.error('请求失败:', error);
    return null;
  }
}

// 使用示例
syncLcscProduct('C14663').then(productId => {
  if (productId) {
    console.log('产品同步完成，产品ID:', productId);
  }
});
```

### Python
```python
import requests

def sync_lcsc_product(lcsc_code):
    try:
        url = f"https://check-product.962692556.workers.dev"
        params = {'lcscCode': lcsc_code}
        
        response = requests.get(url, params=params)
        result = response.json()
        
        if response.status_code == 200:
            print(f"同步成功: {result}")
            return result['product_id']
        else:
            print(f"同步失败: {result['error']}")
            return None
    except Exception as e:
        print(f"请求失败: {e}")
        return None

# 使用示例
product_id = sync_lcsc_product('C14663')
if product_id:
    print(f"产品ID: {product_id}")
```

### cURL
```bash
# 基本请求
curl "https://check-product.962692556.workers.dev?lcscCode=C14663"

# 带错误处理的请求
curl -s "https://check-product.962692556.workers.dev?lcscCode=C14663" | jq '.'
```

## 处理流程说明

### 1. 产品存在检查
- 检查`component_mapping`表中是否存在LCSC编码对应的CE编码
- 如果存在CE编码，检查`products202503`表中是否存在该产品

### 2. 数据获取与处理
- 调用LCSC API获取产品详细信息
- 生成CE产品编码（CMA + 9位数字，从200000001开始）
- 处理品牌信息（生成CEB + 6位数字编码，从200001开始）

### 3. 数据库更新
- 更新以下数据表：
  - `products202503` - 主产品信息
  - `brands` - 品牌信息（如需要）
  - `component_mapping` - LCSC到CE编码映射
  - `product_category_map` - 产品分类映射
  - `price` - 价格信息（阶梯价格）
  - `stocks` - 库存信息
  - `parameters` - 产品参数（中英文）

### 4. 数据存储
- 将原始LCSC数据以JSON格式保存到R2存储桶

## 错误处理

### 常见错误码
| HTTP状态码 | 错误信息 | 说明 |
|------------|----------|------|
| 400 | Missing lcscCode parameter | 缺少必需的lcscCode参数 |
| 500 | Failed to fetch product data from LCSC API | LCSC API调用失败 |
| 500 | Database operation failed | 数据库操作失败 |

### 错误处理建议
1. **网络错误**: 实现重试机制，建议3次重试
2. **API限制**: 添加请求频率控制
3. **数据库错误**: 检查数据库连接和权限
4. **LCSC API错误**: 验证产品编码格式和有效性

## 性能优化建议

### 1. 批量处理
```javascript
async function batchSyncProducts(lcscCodes) {
  const promises = lcscCodes.map(code => syncLcscProduct(code));
  const results = await Promise.allSettled(promises);
  return results;
}
```

### 2. 缓存策略
- 对于已存在的产品，API会直接返回而不重复处理
- 考虑在客户端实现本地缓存减少重复请求

### 3. 监控和日志
- 监控API响应时间和成功率
- 记录失败的LCSC编码用于后续处理

## 注意事项

1. **LCSC API限制**: 请遵守LCSC的API使用条款和频率限制
2. **HTTP头信息**: API调用LCSC时使用了完整的HTTP头信息，包括：
   - Cookie: 固定的认证Cookie
   - User-Agent: PostmanRuntime/7.44.1
   - Accept: */*
   - Accept-Encoding: gzip, deflate, br
   - Connection: keep-alive
   - Host: wmsc.lcsc.com
3. **数据一致性**: API使用事务确保数据一致性
4. **编码唯一性**: CE编码和品牌编码都是自动递增生成，确保唯一性
5. **R2存储**: 原始LCSC数据会保存到R2存储桶，文件名为`{lcscCode}.json`

## 测试建议

### 测试用例
```javascript
// 测试已存在的产品
await syncLcscProduct('existing-lcsc-code');

// 测试新产品
await syncLcscProduct('C14663');

// 测试无效编码
await syncLcscProduct('INVALID');

// 测试空参数
await fetch('https://check-product.962692556.workers.dev');
```

## 联系支持
如果遇到问题，请检查：
1. Cloudflare Worker日志
2. D1数据库连接状态
3. R2存储桶权限
4. LCSC API响应状态
