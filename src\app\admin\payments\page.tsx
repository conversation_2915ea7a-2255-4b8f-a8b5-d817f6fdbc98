'use client'

import { useState, useEffect } from 'react'
import { PaymentRecord, getOrderPaymentRecords, getPaymentStatusText } from '@/services/api'

export default function PaymentRecordsPage() {
  const [orderId, setOrderId] = useState('')
  const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPaymentRecords = async () => {
    if (!orderId.trim()) {
      setError('Please enter an order ID')
      return
    }

    setIsLoading(true)
    setError(null)
    
    try {
      const records = await getOrderPaymentRecords(orderId.trim())
      setPaymentRecords(records)
      
      if (records.length === 0) {
        setError('No payment records found for this order')
      }
    } catch (error) {
      console.error('Error fetching payment records:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch payment records')
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: number) => {
    switch (status) {
      case 0: return 'text-yellow-600 bg-yellow-100'
      case 1: return 'text-green-600 bg-green-100'
      case 2: return 'text-red-600 bg-red-100'
      case 3: return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border p-8 mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Payment Records Management</h1>
          
          <div className="flex gap-4 mb-6">
            <input
              type="text"
              value={orderId}
              onChange={(e) => setOrderId(e.target.value)}
              placeholder="Enter Order ID (e.g., ORD20250627001)"
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#DE2910] focus:border-transparent"
              onKeyPress={(e) => e.key === 'Enter' && fetchPaymentRecords()}
            />
            <button
              onClick={fetchPaymentRecords}
              disabled={isLoading}
              className="px-6 py-2 bg-[#DE2910] text-white rounded-lg hover:bg-[#DE2910]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Loading...' : 'Search'}
            </button>
          </div>

          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">{error}</p>
            </div>
          )}
        </div>

        {paymentRecords.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">
                Payment Records for Order: {orderId}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Found {paymentRecords.length} payment record(s)
              </p>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment ID
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Transaction Number
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Method
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Payment Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Remarks
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paymentRecords.map((record) => (
                    <tr key={record.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {record.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                        {record.transaction_number}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {record.payment_method}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                        ${record.amount.toFixed(2)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(record.status)}`}>
                          {getPaymentStatusText(record.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(record.payment_date).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                        <div className="truncate" title={record.remarks || ''}>
                          {record.remarks || '-'}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <div className="mt-8 bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Status Reference</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-yellow-600 bg-yellow-100">
                Pending
              </span>
              <span className="text-sm text-gray-600">Status: 0</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-green-600 bg-green-100">
                Success
              </span>
              <span className="text-sm text-gray-600">Status: 1</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-red-600 bg-red-100">
                Failed
              </span>
              <span className="text-sm text-gray-600">Status: 2</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full text-blue-600 bg-blue-100">
                Refunded
              </span>
              <span className="text-sm text-gray-600">Status: 3</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
