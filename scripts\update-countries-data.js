#!/usr/bin/env node

/**
 * 更新国家和省州数据的脚本
 * 
 * 数据源选项：
 * 1. Countries States Cities Database (GitHub)
 * 2. REST Countries API
 * 3. GeoNames API
 * 
 * 使用方法：
 * node scripts/update-countries-data.js [source]
 * 
 * 示例：
 * node scripts/update-countries-data.js github
 * node scripts/update-countries-data.js restcountries
 * node scripts/update-countries-data.js geonames
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// 配置
const CONFIG = {
  github: {
    url: 'https://raw.githubusercontent.com/hiiamrohit/Countries-States-Cities-database/master/countries%2Bstates.json',
    description: 'GitHub开源数据库 - 最全面'
  },
  restcountries: {
    url: 'https://restcountries.com/v3.1/all',
    description: 'REST Countries API - 基础数据'
  },
  geonames: {
    // 需要注册账号获取用户名
    username: 'YOUR_GEONAMES_USERNAME',
    description: 'GeoNames API - 需要注册'
  }
};

// 输出文件路径
const OUTPUT_FILE = path.join(__dirname, '../src/data/countries-states.ts');

/**
 * 从GitHub获取数据
 */
async function fetchFromGitHub() {
  console.log('📡 从GitHub获取数据...');
  
  try {
    const data = await fetchJSON(CONFIG.github.url);
    
    if (!Array.isArray(data)) {
      throw new Error('数据格式不正确');
    }
    
    console.log(`✅ 成功获取 ${data.length} 个国家的数据`);
    
    // 转换数据格式
    const countries = data.map(country => ({
      code: country.iso2,
      name: country.name,
      states: country.states ? country.states.map(state => ({
        code: state.state_code,
        name: state.name
      })) : undefined
    })).filter(country => country.code && country.name);
    
    return countries;
    
  } catch (error) {
    console.error('❌ GitHub数据获取失败:', error.message);
    throw error;
  }
}

/**
 * 从REST Countries获取数据
 */
async function fetchFromRestCountries() {
  console.log('📡 从REST Countries API获取数据...');
  
  try {
    const data = await fetchJSON(CONFIG.restcountries.url);
    
    if (!Array.isArray(data)) {
      throw new Error('数据格式不正确');
    }
    
    console.log(`✅ 成功获取 ${data.length} 个国家的数据`);
    
    // REST Countries API 不包含省州数据，只有基础国家信息
    const countries = data.map(country => ({
      code: country.cca2,
      name: country.name.common
    })).filter(country => country.code && country.name)
      .sort((a, b) => a.name.localeCompare(b.name));
    
    console.log('⚠️  注意: REST Countries API 不包含省州数据');
    
    return countries;
    
  } catch (error) {
    console.error('❌ REST Countries数据获取失败:', error.message);
    throw error;
  }
}

/**
 * HTTP请求辅助函数
 */
function fetchJSON(url) {
  return new Promise((resolve, reject) => {
    const request = https.get(url, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}`));
        }
      });
    });
    
    request.on('error', (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });
    
    request.setTimeout(30000, () => {
      request.destroy();
      reject(new Error('请求超时'));
    });
  });
}

/**
 * 生成TypeScript文件
 */
function generateTypeScriptFile(countries) {
  console.log('📝 生成TypeScript文件...');
  
  // 分离有省州数据和无省州数据的国家
  const countriesWithStates = countries.filter(c => c.states && c.states.length > 0);
  const countriesWithoutStates = countries.filter(c => !c.states || c.states.length === 0);
  
  const content = `// 自动生成的国家和省/州数据
// 生成时间: ${new Date().toISOString()}
// 数据来源: 请查看 scripts/update-countries-data.js

export interface Country {
  code: string;
  name: string;
  states?: State[];
}

export interface State {
  code: string;
  name: string;
}

// 有预定义省/州的国家 (${countriesWithStates.length}个)
export const countriesWithStates: Country[] = ${JSON.stringify(countriesWithStates, null, 2)};

// 其他国家 (${countriesWithoutStates.length}个)
export const otherCountries: Country[] = ${JSON.stringify(countriesWithoutStates, null, 2)};

// 合并所有国家
export const allCountries: Country[] = [...countriesWithStates, ...otherCountries];

// 根据国家代码获取省/州列表
export const getStatesByCountry = (countryCode: string): State[] => {
  const country = countriesWithStates.find(c => c.code === countryCode);
  return country?.states || [];
};

// 检查国家是否有预定义的省/州
export const hasStates = (countryCode: string): boolean => {
  return countriesWithStates.some(c => c.code === countryCode);
};
`;
  
  return content;
}

/**
 * 保存文件
 */
function saveFile(content) {
  console.log('💾 保存文件...');
  
  try {
    // 确保目录存在
    const dir = path.dirname(OUTPUT_FILE);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 备份现有文件
    if (fs.existsSync(OUTPUT_FILE)) {
      const backupFile = OUTPUT_FILE + '.backup.' + Date.now();
      fs.copyFileSync(OUTPUT_FILE, backupFile);
      console.log(`📋 已备份现有文件: ${path.basename(backupFile)}`);
    }
    
    // 写入新文件
    fs.writeFileSync(OUTPUT_FILE, content, 'utf8');
    console.log(`✅ 文件已保存: ${OUTPUT_FILE}`);
    
  } catch (error) {
    console.error('❌ 文件保存失败:', error.message);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  const source = process.argv[2] || 'github';
  
  console.log('🌍 国家省州数据更新工具');
  console.log('================================');
  console.log(`📊 数据源: ${source}`);
  console.log(`📝 输出文件: ${OUTPUT_FILE}`);
  console.log('');
  
  try {
    let countries;
    
    switch (source.toLowerCase()) {
      case 'github':
        countries = await fetchFromGitHub();
        break;
      case 'restcountries':
        countries = await fetchFromRestCountries();
        break;
      case 'geonames':
        console.log('❌ GeoNames API 需要额外配置，请参考文档');
        process.exit(1);
      default:
        console.log('❌ 不支持的数据源:', source);
        console.log('支持的数据源: github, restcountries, geonames');
        process.exit(1);
    }
    
    if (!countries || countries.length === 0) {
      throw new Error('未获取到有效数据');
    }
    
    const content = generateTypeScriptFile(countries);
    saveFile(content);
    
    console.log('');
    console.log('🎉 数据更新完成!');
    console.log(`📊 总计: ${countries.length} 个国家`);
    console.log(`🏛️  有省州数据: ${countries.filter(c => c.states && c.states.length > 0).length} 个国家`);
    console.log(`🌐 无省州数据: ${countries.filter(c => !c.states || c.states.length === 0).length} 个国家`);
    
  } catch (error) {
    console.error('');
    console.error('❌ 更新失败:', error.message);
    process.exit(1);
  }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('🌍 国家省州数据更新工具');
  console.log('');
  console.log('使用方法:');
  console.log('  node scripts/update-countries-data.js [source]');
  console.log('');
  console.log('数据源选项:');
  Object.entries(CONFIG).forEach(([key, config]) => {
    console.log(`  ${key.padEnd(12)} - ${config.description}`);
  });
  console.log('');
  console.log('示例:');
  console.log('  node scripts/update-countries-data.js github');
  console.log('  node scripts/update-countries-data.js restcountries');
  process.exit(0);
}

// 运行主函数
if (require.main === module) {
  main();
}
