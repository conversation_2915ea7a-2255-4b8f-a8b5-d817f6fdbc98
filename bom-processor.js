/**
 * Cloudflare Workers script for BOM (Bill of Materials) processing
 * Handles file upload, CSV parsing, LCSC matching, and product synchronization
 *
 * Environment Variables:
 * - DB: Cloudflare D1 database binding
 * - BOM_FILES: https://bom-files.962692556.workers.dev
 * - CHECK_PRODUCT: https://check-product.962692556.workers.dev
 * - WEB_API: https://webapi.962692556.workers.dev
 *
 * Note: Currently supports CSV format. XLSX support will be added later.
 */

/**
 * 生成UUID v4
 * @returns {string} 返回一个UUID字符串
 */
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    try {
      // CORS headers
      const corsHeaders = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      };

      if (method === 'OPTIONS') {
        return new Response(null, { headers: corsHeaders });
      }

      // Route handling
      if (path === '/upload' && method === 'POST') {
        return await handleFileUpload(request, env, corsHeaders);
      } else if (path === '/parse-excel' && method === 'POST') {
        return await handleExcelParsing(request, env, corsHeaders);
      } else if (path === '/process-bom' && method === 'POST') {
        return await handleBomProcessing(request, env, corsHeaders);
      } else if (path === '/bom-list' && method === 'GET') {
        return await handleBomList(request, env, corsHeaders);
      } else if (path.startsWith('/bom/') && method === 'GET') {
        return await handleBomDetails(request, env, corsHeaders);
      } else if (path === '/bom-detail' && method === 'GET') {
        return await handleBomDetailById(request, env, corsHeaders);
      } else if (path === '/user-boms' && method === 'GET') {
        return await handleUserBomList(request, env, corsHeaders);
      } else {
        return new Response(JSON.stringify({ error: 'Not found' }), {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    } catch (error) {
      console.error('Error processing request:', error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  }
};

/**
 * Handle file upload to R2 and create BOM main record
 */
async function handleFileUpload(request, env, corsHeaders) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const bomName = formData.get('bomName') || file.name;
    const userId = formData.get('userId');

    if (!file || !userId) {
      return new Response(JSON.stringify({ error: 'Missing file or userId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 打印环境变量类型信息，帮助调试
    console.log('Environment variables types:', {
      BOM_FILES: env.BOM_FILES ? (typeof env.BOM_FILES) : 'undefined',
      DB: env.DB ? (typeof env.DB) : 'undefined'
    });

    // 检查环境变量是否配置
    if (!env.BOM_FILES) {
      throw new Error('BOM_FILES 环境变量未配置');
    }

    // 生成唯一的文件名
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 10);
    const originalFilename = file.name || 'unknown';
    
    // 提取文件扩展名
    let extension = '';
    const lastDotIndex = originalFilename.lastIndexOf('.');
    if (lastDotIndex !== -1) {
      extension = originalFilename.substring(lastDotIndex);
    }
    
    // 构建最终的文件名
    const fileName = `bom_${timestamp}_${randomString}${extension}`;
    
    let fileUrl;
    
    // 检查BOM_FILES是什么类型的绑定
    if (typeof env.BOM_FILES.put === 'function') {
      // 如果是R2存储桶，直接使用put方法
      await env.BOM_FILES.put(fileName, file, {
        httpMetadata: {
          contentType: file.type
        },
        customMetadata: {
          originalName: originalFilename
        }
      });
      
      // 构建访问URL
      const url = new URL(request.url);
      fileUrl = `${url.protocol}//${url.host}/${fileName}`;
      
    } else {
      throw new Error('BOM_FILES 不是有效的R2存储桶绑定');
    }

    // 验证DB环境变量
    if (!env.DB) {
      throw new Error('DB环境变量未配置，无法创建BOM记录');
    }

    // 生成UUID作为BOM ID
    const bomId = generateUUID();

    // 创建BOM主记录
    const bomResult = await env.DB.prepare(`
      INSERT INTO bom_main (
        bom_id, bom_name, status, user_id, file_url, item_count, success_count,
        exact_match_count, partial_match_count, no_match_count, est_total_price,
        each_price, priority_matching, quantity_multiplier, create_at, update_at
      ) VALUES (?, ?, 0, ?, ?, 0, 0, 0, 0, 0, 0, 0, 0, 1, datetime('now'), datetime('now'))
    `).bind(
      bomId,
      bomName,
      userId,
      fileUrl
    ).run();

    return new Response(JSON.stringify({
      success: true,
      bom_id: bomId,
      file_info: {
        url: fileUrl,
        filename: fileName,
        originalName: originalFilename
      },
      message: '文件上传并创建BOM记录成功'
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('File upload error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Parse CSV file and return first 10 rows for column mapping
 */
async function handleExcelParsing(request, env, corsHeaders) {
  try {
    const { fileUrl, startLine = 2 } = await request.json();

    if (!fileUrl) {
      return new Response(JSON.stringify({ error: 'Missing fileUrl' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 打印环境变量和URL信息，帮助调试
    console.log('Excel parsing:', {
      fileUrl,
      BOM_FILES: env.BOM_FILES ? (typeof env.BOM_FILES) : 'undefined'
    });

    let fileText;

    // 获取文件内容
    try {
      // 检查BOM_FILES是否为R2存储桶
      if (env.BOM_FILES && typeof env.BOM_FILES.get === 'function') {
        // 从URL中提取文件名
        const fileName = fileUrl.split('/').pop();
        
        if (!fileName) {
          throw new Error('无法从URL中提取文件名');
        }
        
        console.log('从R2获取文件:', fileName);
        
        // 直接从R2获取文件
        const object = await env.BOM_FILES.get(fileName);
        
        if (!object) {
          throw new Error(`R2中未找到文件: ${fileName}`);
        }
        
        // 获取文件内容
        fileText = await object.text();
      } else {
        // 回退到HTTP请求
        console.log('通过HTTP获取文件:', fileUrl);
        const fileResponse = await fetch(fileUrl);
        
        if (!fileResponse.ok) {
          throw new Error('Failed to fetch file from R2');
        }
        
        fileText = await fileResponse.text();
      }
    } catch (error) {
      console.error('文件获取错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: `文件获取失败: ${error.message}`
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 解析CSV数据
    let csvData;
    try {
      csvData = parseCSV(fileText);
      
      // 简单验证文件内容
      if (!csvData || csvData.length === 0) {
        throw new Error('文件内容为空');
      }
      
      if (csvData.length < startLine) {
        throw new Error(`文件行数(${csvData.length})小于起始行(${startLine})`);
      }

      // 获取表头行
      const headers = csvData[0] || [];
      if (headers.length === 0) {
        throw new Error('无法识别表格标题行');
      }
      
      // 检查是否有太多项目 (预警，不阻止)
      if (csvData.length > 500) {
        console.warn(`BOM文件包含${csvData.length}行，超过LCSC API 500行的处理限制`);
      }

      // 准备返回数据
      const previewData = csvData.slice(0, Math.min(10, csvData.length));
      const dataRows = csvData.slice(startLine - 1, Math.min(startLine + 8, csvData.length));

      // 返回解析结果
      return new Response(JSON.stringify({
        success: true,
        headers: headers,
        preview_data: previewData,
        data_rows: dataRows,
        total_rows: csvData.length,
        suggested_mapping: {
          description: "建议的列映射配置",
          selection: [[0,1],[1,4],[2,0],[3,5],[4,0],[5,0],[6,0]],
          mapping_info: {
            0: "物料编号/型号",
            1: "数量",
            2: "参考价格",
            3: "品牌",
            4: "封装",
            5: "描述",
            6: "备注"
          }
        }
      }), {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('CSV解析错误:', error);
      return new Response(JSON.stringify({
        success: false,
        error: `解析CSV文件失败: ${error.message}`,
        details: '请确保您上传的是有效的CSV格式文件，并且编码正确（建议UTF-8编码）'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }
  } catch (error) {
    console.error('Excel解析总体错误:', error);
    return new Response(JSON.stringify({ 
      error: error.message,
      type: error.name
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Process BOM through LCSC matching and product synchronization
 */
async function handleBomProcessing(request, env, corsHeaders) {
  try {
    const {
      bomId,
      startLine = 2,
      selection = [[0,1],[1,4],[2,0],[3,5],[4,0],[5,0],[6,0]],
      priorityMatch = 'in_stock',
      coefficientTotal = 1,
      userId
    } = await request.json();

    if (!bomId || !userId) {
      return new Response(JSON.stringify({ error: 'Missing bomId or userId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Validate DB environment variable
    if (!env.DB) {
      throw new Error('DB environment variable is not configured. Cannot process BOM.');
    }

    // Step 1: Get BOM record and file URL
    const bomRecord = await env.DB.prepare(`
      SELECT file_url FROM bom_main WHERE bom_id = ? AND user_id = ?
    `).bind(bomId, userId).first();

    if (!bomRecord || !bomRecord.file_url) {
      return new Response(JSON.stringify({ error: 'BOM record not found or no file URL' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Step 2: Get file from R2 or fetch from URL
    let fileBlob;
    let originalFileName = 'bom.xlsx'; // 默认文件名，以防无法获取真实文件名
    
    try {
      // 检查BOM_FILES是否为R2存储桶
      if (env.BOM_FILES && typeof env.BOM_FILES.get === 'function') {
        // 从URL中提取文件名
        const fileUrl = bomRecord.file_url;
        const fileName = fileUrl.split('/').pop();
        
        if (!fileName) {
          throw new Error('无法从URL中提取文件名');
        }
        
        // 保存原始文件名
        originalFileName = fileName;
        
        console.log('从R2获取BOM文件:', fileName);
        
        // 直接从R2获取文件
        const object = await env.BOM_FILES.get(fileName);
        
        if (!object) {
          throw new Error(`R2中未找到BOM文件: ${fileName}`);
        }
        
        // 获取文件内容为Blob
        const arrayBuffer = await object.arrayBuffer();
        fileBlob = new Blob([arrayBuffer], { type: object.httpMetadata?.contentType || 'application/octet-stream' });
        
        console.log('成功获取文件，大小:', fileBlob.size, '字节');
      } else {
        // 回退到HTTP请求
        console.log('通过HTTP获取BOM文件:', bomRecord.file_url);
        const fileResponse = await fetch(bomRecord.file_url);
        
        if (!fileResponse.ok) {
          throw new Error(`Failed to fetch file from R2: ${fileResponse.status} ${fileResponse.statusText}`);
        }
        
        // 尝试从URL获取文件名
        const urlParts = bomRecord.file_url.split('/');
        if (urlParts.length > 0) {
          originalFileName = urlParts[urlParts.length - 1];
        }
        
        fileBlob = await fileResponse.blob();
        console.log('成功通过HTTP获取文件，大小:', fileBlob.size, '字节');
      }
    } catch (error) {
      console.error('获取BOM文件失败:', error);
      return new Response(JSON.stringify({
        success: false,
        error: `获取BOM文件失败: ${error.message}`
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Step 3: Call LCSC BOM API with dynamic parameters
    const lcscFormData = new FormData();
    lcscFormData.append('file', fileBlob, originalFileName);  // 使用原始文件名
    lcscFormData.append('startLine', startLine.toString());
    lcscFormData.append('selection', JSON.stringify(selection));
    lcscFormData.append('priorityMatch', priorityMatch);
    lcscFormData.append('coefficientTotal', coefficientTotal.toString());
    lcscFormData.append('cookie', 'wmsc_cart_key=5BFC271AC91EE8707B9C39A08F0A22EAB9089FEE2B0EF750F86CD5F08EE942A905C4310741CC19D3');

    let lcscResponse;
    
    try {
      // 检查BOM是否为服务绑定
      if (env.BOM && typeof env.BOM.fetch === 'function') {
        // 使用服务绑定直接调用
        console.log('使用BOM服务绑定处理BOM', {
          startLine,
          selectionLength: selection?.length || 0,
          priorityMatch,
          fileSize: fileBlob.size
        });
        
        // 服务绑定调用时，URL应该简化
        lcscResponse = await env.BOM.fetch(new Request('http://bom', {
          method: 'POST',
          body: lcscFormData
        }));
        
        console.log('BOM服务绑定响应状态:', lcscResponse.status);
      } else {
        // 回退到HTTP请求
        console.log('通过HTTP调用BOM服务');
        lcscResponse = await fetch('https://bom.962692556.workers.dev/', {
          method: 'POST',
          body: lcscFormData
        });
        
        console.log('HTTP BOM服务响应状态:', lcscResponse.status);
      }
    } catch (error) {
      console.error('调用BOM服务出错:', error);
      throw new Error(`调用BOM服务时出错: ${error.message}`);
    }

    if (!lcscResponse.ok) {
      const errorText = await lcscResponse.text().catch(e => '无法获取错误详情');
      console.error('BOM API响应错误:', {
        status: lcscResponse.status,
        statusText: lcscResponse.statusText,
        errorText: errorText
      });

      // 尝试解析错误信息
      let errorJson;
      try {
        errorJson = JSON.parse(errorText);
      } catch (e) {
        errorJson = { error: errorText };
      }

      // 根据错误类型提供友好的错误消息
      const errorMessage = errorJson.error || '';
      
      // 特殊错误类型处理
      if (errorMessage.includes('Bom Items Exceeded') || errorMessage.includes('exceeds the 500 maximum')) {
        // 物料项数量超出限制
        return new Response(JSON.stringify({
          success: false,
          error: '您的BOM文件中物料项超过500个，这超出了LCSC API的处理限制。请减少物料项数量后重试。',
          original_error: errorText
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      } 
      else if (errorMessage.includes('File parse failed')) {
        // 文件解析失败
        return new Response(JSON.stringify({
          success: false,
          error: '文件解析失败。请确保您上传的是有效的BOM文件（Excel或CSV格式），并且格式正确。',
          details: '可能的原因：1) 文件格式不支持 2) 表格结构有误 3) 文件编码问题 4) 文件可能损坏',
          original_error: errorText
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
      
      // 其他未知错误
      throw new Error(`LCSC BOM API请求失败: ${lcscResponse.status} ${lcscResponse.statusText}. ${errorText}`);
    }

    const lcscResult = await lcscResponse.json();

    if (!lcscResult.success) {
      throw new Error('LCSC BOM processing failed');
    }

    // Step 4: Extract product codes and sync products
    const bomDetails = lcscResult.data.bomDetails;
    const projectItems = bomDetails.projectIndexVO || [];
    
    console.log(`从LCSC获取到${projectItems.length}个BOM项目`);

    const productCodes = projectItems
      .filter(item => item.productCode)
      .map(item => item.productCode);
    
    console.log(`提取的产品代码: ${productCodes.join(', ')}`);

    // Step 5: Sync products with our database using CHECK_PRODUCT service
    const syncResults = await syncProductsFromLcsc(productCodes, env);
    
    console.log(`产品同步结果: 成功=${syncResults.filter(r => r.product_id).length}, 失败=${syncResults.filter(r => !r.product_id).length}`);

    // 创建LCSC代码到我们产品ID的映射
    const lcscToProductIdMap = {};
    syncResults.forEach(result => {
      if (result.lcsc_code && result.product_id) {
        lcscToProductIdMap[result.lcsc_code] = result.product_id;
      }
    });
    
    console.log('LCSC代码到产品ID的映射:', lcscToProductIdMap);

    // Step 6: Get detailed product information using WEB_API service
    // 使用我们自己的产品ID获取详情
    const productIds = syncResults
      .filter(result => result.product_id)
      .map(result => result.product_id);

    console.log('获取详情的产品ID列表:', productIds);

    const productDetails = await getProductDetails(productIds, env);
    console.log('从WEB_API获取的产品详情:', JSON.stringify(productDetails).substring(0, 200) + '...');

    // 确保产品详情使用LCSC代码作为键，以便在saveBomItems中能匹配到
    const enhancedProductDetails = {};
    syncResults.forEach(result => {
      if (result.lcsc_code && result.product_id) {
        // 即使WEB_API返回的详情为空，也确保映射product_id
        enhancedProductDetails[result.lcsc_code] = {
          ...(productDetails[result.product_id] || {}),
          product_id: result.product_id,
          exists: !!productDetails[result.product_id]
        };
        console.log(`为LCSC代码 ${result.lcsc_code} 映射产品ID ${result.product_id}, 详情存在: ${!!productDetails[result.product_id]}`);
      }
    });

    console.log('增强的产品详情映射:', JSON.stringify(enhancedProductDetails).substring(0, 200) + '...');
    console.log(`映射的产品总数: ${Object.keys(enhancedProductDetails).length}`);

    // Step 7: Calculate statistics and save to database
    const stats = calculateBomStatistics(projectItems, enhancedProductDetails);

    // Update BOM main record
    await env.DB.prepare(`
      UPDATE bom_main SET
        item_count = ?,
        success_count = ?,
        exact_match_count = ?,
        partial_match_count = ?,
        no_match_count = ?,
        est_total_price = ?,
        each_price = ?,
        priority_matching = ?,
        quantity_multiplier = ?,
        update_at = datetime('now')
      WHERE bom_id = ?
    `).bind(
      stats.item_count,
      stats.success_count,
      stats.exact_match_count,
      stats.partial_match_count,
      stats.no_match_count,
      stats.est_total_price,
      stats.each_price,
      priorityMatch === 'in_stock' ? 1 : 0,
      coefficientTotal,
      bomId
    ).run();

    // Step 8: Save BOM items
    await saveBomItems(bomId, userId, projectItems, enhancedProductDetails, env);

    // 从数据库获取已保存的BOM项目
    const bomItemsResult = await env.DB.prepare(`
      SELECT * FROM bom_items WHERE bom_id = ? AND user_id = ?
    `).bind(bomId, userId).all();

    // 确保bomItemsResult.results存在
    const bomItems = bomItemsResult.results || [];
    
    // 处理BOM项目，移除任何可能包含LCSC信息的字段
    const sanitizedBomItems = bomItems.map(item => {
      // 解析notes字段，移除lcsc_code相关信息
      let notesObj = {};
      try {
        const parsed = JSON.parse(item.notes || '{}');
        // 提取客户需要的信息，排除lcsc_code但保留数量信息
        const { lcsc_code, original_quantity, quantity_multiplier, moq, spq, ...otherInfo } = parsed;
        notesObj = {
          ...otherInfo,
          // 保留数量相关信息
          original_quantity,
          quantity_multiplier,
          moq,
          spq
        };
      } catch (e) {
        console.error('解析notes字段失败:', e);
      }
      
      // 返回处理后的项目，包含所有原始字段但notes已清理
      return {
        ...item,
        notes: JSON.stringify(notesObj)
      };
    });

    return new Response(JSON.stringify({
      success: true,
      bom_id: bomId,
      statistics: stats,
      bom_items: sanitizedBomItems,
      message: 'BOM处理成功完成'
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('BOM processing error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Sync products from LCSC codes using CHECK_PRODUCT service
 */
async function syncProductsFromLcsc(productCodes, env) {
  const syncResults = [];
  
  // Skip if no product codes
  if (!productCodes || productCodes.length === 0) {
    return syncResults;
  }

  for (const lcscCode of productCodes) {
    try {
      let response;
      
      // Check if CHECK_PRODUCT is a service binding (Fetcher object)
      if (env.CHECK_PRODUCT && typeof env.CHECK_PRODUCT === 'object') {
        // Use the binding directly
        response = await env.CHECK_PRODUCT.fetch(new Request(`https://check-product?lcscCode=${lcscCode}`));
      } else {
        // Fall back to URL if it's a string
        const checkProductUrl = env.CHECK_PRODUCT || 'https://check-product.962692556.workers.dev';
        response = await fetch(`${checkProductUrl}?lcscCode=${lcscCode}`);
      }
      
      if (response.ok) {
        const result = await response.json();
        // 确保结果中包含lcsc_code和product_id
        syncResults.push({
          lcsc_code: lcscCode,
          product_id: result.product_id,
          ...result
        });
      } else {
        syncResults.push({ 
          lcsc_code: lcscCode, 
          product_id: null,
          error: 'Sync failed' 
        });
      }
    } catch (error) {
      syncResults.push({ 
        lcsc_code: lcscCode, 
        product_id: null,
        error: error.message 
      });
    }
  }

  return syncResults;
}

/**
 * Get detailed product information using WEB_API service
 */
async function getProductDetails(productIds, env) {
  if (productIds.length === 0) return {};

  try {
    let response;
    const requestBody = JSON.stringify({
      models: [],
      product_ids: productIds
    });

    console.log(`请求产品详情，产品IDs: ${productIds.join(', ')}`);

    // Check if WEB_API is a service binding (Fetcher object)
    if (env.WEB_API && typeof env.WEB_API === 'object') {
      // Use the binding directly
      response = await env.WEB_API.fetch(new Request('https://webapi/products/models/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: requestBody
      }));
    } else {
      // Fall back to URL if it's a string
      const webApiUrl = env.WEB_API || 'https://webapi.962692556.workers.dev';
      response = await fetch(`${webApiUrl}/products/models/check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: requestBody
      });
    }

    console.log(`产品详情API响应状态: ${response.status}`);

    if (response.ok) {
      const responseData = await response.json();
      console.log(`API返回的产品详情格式: ${JSON.stringify(Object.keys(responseData))}`);
      
      // 确保返回的数据结构是以产品ID为键
      const formattedDetails = {};
      
      // 如果API返回的是数组，转换为以产品ID为键的对象
      if (Array.isArray(responseData)) {
        responseData.forEach(item => {
          if (item && item.product_id) {
            formattedDetails[item.product_id] = item;
          }
        });
        console.log(`将产品详情数组(${responseData.length}项)转换为对象(${Object.keys(formattedDetails).length}键)`);
        return formattedDetails;
      }
      
      // 如果API返回的是以产品ID为键的对象，直接返回
      else if (typeof responseData === 'object' && !Array.isArray(responseData)) {
        // 检查数据格式是否需要转换
        const productIdExists = productIds.some(id => responseData[id]);
        
        if (productIdExists) {
          console.log('API返回了正确格式的产品详情对象');
          return responseData;
        } else {
          console.log('API返回的对象格式不匹配，尝试提取产品详情');
          
          // 尝试提取数据（API可能有不同的数据结构）
          const products = responseData.products || responseData.data || responseData.items || [];
          
          if (Array.isArray(products)) {
            products.forEach(item => {
              if (item && item.product_id) {
                formattedDetails[item.product_id] = item;
              }
            });
            console.log(`从API响应中提取并转换了${Object.keys(formattedDetails).length}个产品详情`);
          }
          
          return formattedDetails;
        }
      }
      
      // 无法识别的格式，返回空对象
      console.log('无法识别的API响应格式，返回空对象');
      return {};
    } else {
      console.error(`获取产品详情失败: ${response.status} ${response.statusText}`);
      const errorText = await response.text().catch(() => '无法读取错误详情');
      console.error(`错误详情: ${errorText.substring(0, 200)}`);
      throw new Error('Failed to get product details');
    }
  } catch (error) {
    console.error('Error getting product details:', error);
    return {};
  }
}

/**
 * Calculate BOM statistics
 */
function calculateBomStatistics(projectItems, productDetails) {
  let exactMatchCount = 0;
  let partialMatchCount = 0;
  let noMatchCount = 0;
  let totalPrice = 0;

  projectItems.forEach(item => {
    if (item.matchScore === 'Exact Matches') {
      exactMatchCount++;
    } else if (item.matchScore === 'Partial Matches') {
      partialMatchCount++;
    } else {
      noMatchCount++;
    }

    if (item.priceVO && item.priceVO.realTotalPrice) {
      totalPrice += item.priceVO.realTotalPrice;
    }
  });

  const successCount = exactMatchCount + partialMatchCount;
  const itemCount = projectItems.length;
  const eachPrice = itemCount > 0 ? totalPrice / itemCount : 0;

  return {
    item_count: itemCount,
    success_count: successCount,
    exact_match_count: exactMatchCount,
    partial_match_count: partialMatchCount,
    no_match_count: noMatchCount,
    est_total_price: totalPrice,
    each_price: eachPrice
  };
}

/**
 * Save BOM items to database
 */
async function saveBomItems(bomId, userId, projectItems, productDetails, env) {
  // Clear existing items
  await env.DB.prepare('DELETE FROM bom_items WHERE bom_id = ?').bind(bomId).run();

  console.log(`保存BOM项，总数: ${projectItems.length}`);

  // Insert new items
  for (const item of projectItems) {
    const lcscCode = item.productCode || '';
    // 获取我们系统中对应的产品信息
    const productInfo = productDetails[lcscCode] || {};
    // 使用我们自己的product_id而不是LCSC的product_code
    const ourProductId = productInfo.product_id || '';
    
    // 获取原始数量和数量乘数
    const originalQuantity = item.quantity || 0;
    const quantityMultiplier = item.coefficientTotal || 1;
    // 计算最终数量 = 原始数量 * 数量乘数
    const finalQuantity = originalQuantity * quantityMultiplier;
    
    // 从产品信息中获取MOQ
    const moq = productInfo.full?.moq || productInfo.moq || 1;
    const spq = productInfo.full?.spq || productInfo.spq || 1;
    
    // 根据MOQ调整订单数量
    const orderQty = finalQuantity < moq ? moq : finalQuantity;
    
    // 生成UUID作为BOM项ID
    const itemId = generateUUID();
    
    console.log(`保存项目: ID=${itemId}, LCSC代码=${lcscCode}, 产品ID=${ourProductId}, 客户料号=${item.matchValueMap?.['Mfr.#'] || ''}, 数量=${originalQuantity}, 乘数=${quantityMultiplier}, 请求数量=${finalQuantity}, MOQ=${moq}, 订单数量=${orderQty}`);
    
    const matchStatus = getMatchStatusCode(item.matchScore);

    await env.DB.prepare(`
      INSERT INTO bom_items (
        item_id, user_id, bom_id, product_code, customer_part_number, match_status,
        requested_qty, order_qty, stock_availability, target_price, lead_time,
        unit_price, ext_price, offer_availability, packaging_choice, notes,
        create_at, update_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
    `).bind(
      itemId,
      userId,
      bomId,
      ourProductId, // 使用我们自己的product_id
      item.matchValueMap?.['Mfr.#'] || '',
      matchStatus,
      finalQuantity, // requested_qty 使用计算后的最终数量
      orderQty, // order_qty 根据MOQ调整后的订单数量
      getStockAvailability(item.productDetail),
      item.targetPrice || 0,
      item.targetDay || '',
      item.priceVO?.realPrice || 0,
      (orderQty * (item.priceVO?.realPrice || 0)), // ext_price = order_qty * unit_price
      productInfo.exists ? 'Available' : 'Not Available',
      item.productDetail?.productArrange || '',
      JSON.stringify({
        lcsc_code: lcscCode,
        original_quantity: originalQuantity, // 保存原始数量
        quantity_multiplier: quantityMultiplier, // 保存数量乘数
        moq: moq,  // 保存最小订购量
        spq: spq,  // 保存标准包装数量
        ...item.matchValueMap || {}
      }), // 保存LCSC原始代码和数量信息到notes字段
    ).run();
  }

  console.log(`BOM项保存完成，共 ${projectItems.length} 项`);
}

/**
 * Get match status code
 */
function getMatchStatusCode(matchScore) {
  switch (matchScore) {
    case 'Exact Matches': return 2;
    case 'Partial Matches': return 1;
    default: return 0;
  }
}

/**
 * Get stock availability text
 */
function getStockAvailability(productDetail) {
  if (!productDetail) return 'Unknown';

  const stock = productDetail.stockNumber || 0;
  if (stock > 1000) return 'In Stock';
  if (stock > 0) return 'Limited Stock';
  return 'Out of Stock';
}

/**
 * Handle BOM list retrieval
 */
async function handleBomList(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    if (!userId) {
      return new Response(JSON.stringify({ error: 'Missing userId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    const bomList = await env.DB.prepare(`
      SELECT * FROM bom_main
      WHERE user_id = ?
      ORDER BY create_at DESC
      LIMIT ? OFFSET ?
    `).bind(userId, limit, offset).all();

    const totalCount = await env.DB.prepare(`
      SELECT COUNT(*) as count FROM bom_main WHERE user_id = ?
    `).bind(userId).first();

    return new Response(JSON.stringify({
      success: true,
      data: bomList.results,
      pagination: {
        page,
        limit,
        total: totalCount.count,
        pages: Math.ceil(totalCount.count / limit)
      }
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('BOM list error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle BOM details retrieval
 */
async function handleBomDetails(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const bomId = url.pathname.split('/')[2];
    const userId = url.searchParams.get('userId');

    if (!bomId || !userId) {
      return new Response(JSON.stringify({ error: 'Missing bomId or userId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Get BOM main info
    const bomMain = await env.DB.prepare(`
      SELECT * FROM bom_main WHERE bom_id = ? AND user_id = ?
    `).bind(bomId, userId).first();

    if (!bomMain) {
      return new Response(JSON.stringify({ error: 'BOM not found' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // Get BOM items
    const bomItemsResult = await env.DB.prepare(`
      SELECT * FROM bom_items WHERE bom_id = ? AND user_id = ?
    `).bind(bomId, userId).all();
    
    // 确保bomItemsResult.results存在
    const bomItems = bomItemsResult.results || [];
    
          // 处理BOM项目，移除任何可能包含敏感信息的字段
    const sanitizedBomItems = bomItems.map(item => {
      // 解析notes字段，移除lcsc_code相关信息
      let notesObj = {};
      try {
        const parsed = JSON.parse(item.notes || '{}');
        // 提取客户需要的信息，排除lcsc_code但保留数量信息
        const { lcsc_code, original_quantity, quantity_multiplier, moq, spq, ...otherInfo } = parsed;
        notesObj = {
          ...otherInfo,
          // 保留数量相关信息
          original_quantity,
          quantity_multiplier,
          moq,
          spq
        };
      } catch (e) {
        console.error('解析notes字段失败:', e);
      }
      
      // 返回处理后的项目，包含所有原始字段但notes已清理
      return {
        ...item,
        notes: JSON.stringify(notesObj)
      };
    });

    return new Response(JSON.stringify({
      success: true,
      bom_id: bomId,
      bom_main: bomMain,
      bom_items: sanitizedBomItems
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('BOM details retrieval error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Parse CSV text into array of arrays
 */
function parseCSV(text) {
  const lines = text.split('\n');
  const result = [];

  for (let line of lines) {
    line = line.trim();
    if (!line) continue;

    const row = [];
    let current = "";
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        row.push(current.trim());
        current = "";
      } else {
        current += char;
      }
    }

    // Add the last field
    row.push(current.trim());
    result.push(row);
  }

  return result;
}

/**
 * Handle BOM details retrieval by bomId
 * 根据bomId获取BOM详情，不需要userId参数
 */
async function handleBomDetailById(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const bomId = url.searchParams.get('bomId');

    if (!bomId) {
      return new Response(JSON.stringify({ error: '缺少必要参数bomId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证DB环境变量
    if (!env.DB) {
      throw new Error('DB环境变量未配置，无法查询BOM详情');
    }

    // 获取BOM主表信息
    const bomMain = await env.DB.prepare(`
      SELECT * FROM bom_main WHERE bom_id = ?
    `).bind(bomId).first();

    if (!bomMain) {
      return new Response(JSON.stringify({ error: '未找到指定的BOM记录' }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 获取BOM项目列表
    const bomItemsResult = await env.DB.prepare(`
      SELECT * FROM bom_items WHERE bom_id = ?
    `).bind(bomId).all();

    // 确保bomItemsResult.results存在
    const bomItems = bomItemsResult.results || [];
    
    // 处理BOM项目，移除任何可能包含LCSC信息的字段
    const sanitizedBomItems = bomItems.map(item => {
      // 解析notes字段，移除lcsc_code相关信息
      let notesObj = {};
      try {
        const parsed = JSON.parse(item.notes || '{}');
        // 提取客户需要的信息，排除lcsc_code但保留数量信息
        const { lcsc_code, original_quantity, quantity_multiplier, moq, spq, ...otherInfo } = parsed;
        notesObj = {
          ...otherInfo,
          // 保留数量相关信息
          original_quantity,
          quantity_multiplier,
          moq,
          spq
        };
      } catch (e) {
        console.error('解析notes字段失败:', e);
      }
      
      // 返回处理后的项目，包含所有原始字段但notes已清理
      return {
        ...item,
        notes: JSON.stringify(notesObj)
      };
    });

    return new Response(JSON.stringify({
      success: true,
      bom_id: bomId,
      bom_main: bomMain,
      bom_items: sanitizedBomItems
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('BOM详情获取错误:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}

/**
 * 根据user_id获取用户的所有BOM列表
 */
async function handleUserBomList(request, env, corsHeaders) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (!userId) {
      return new Response(JSON.stringify({ error: '缺少必要参数userId' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      });
    }

    // 验证DB环境变量
    if (!env.DB) {
      throw new Error('DB环境变量未配置，无法查询用户BOM列表');
    }

    // 获取用户的所有BOM记录
    const bomListResult = await env.DB.prepare(`
      SELECT * FROM bom_main
      WHERE user_id = ?
      ORDER BY create_at DESC
    `).bind(userId).all();

    const bomList = bomListResult.results || [];

    return new Response(JSON.stringify({
      success: true,
      user_id: userId,
      bom_count: bomList.length,
      bom_list: bomList
    }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('获取用户BOM列表错误:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
}