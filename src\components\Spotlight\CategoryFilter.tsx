/**
 * Spotlight Category Filter Component
 * 
 * Allows users to filter articles by category
 */

'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { fetchBlogArticles } from '@/services/api'

interface Category {
  id: string;
  name: string;
}

export default function CategoryFilter() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [categories, setCategories] = useState<Category[]>([
    { id: 'all', name: 'All Categories' }
  ])
  const [isLoading, setIsLoading] = useState(true)
  
  // Get current category from URL or default to 'all'
  const currentCategory = searchParams.get('category') || 'all'
  
  // Fetch unique categories from API
  useEffect(() => {
    const getCategories = async () => {
      try {
        setIsLoading(true)
        const response = await fetchBlogArticles(1, 100) // 获取足够多的文章以提取分类
        
        // 提取唯一的分类
        const uniqueCategories = new Set<string>()
        response.data.forEach(article => {
          if (article.category) {
            uniqueCategories.add(article.category)
          }
        })
        
        // 将分类转换为我们需要的格式
        const categoryList: Category[] = [
          { id: 'all', name: 'All Categories' },
          ...Array.from(uniqueCategories).map(cat => ({
            id: cat.toLowerCase().replace(/[& ]/g, '-'),
            name: cat
          }))
        ]
        
        setCategories(categoryList)
      } catch (error) {
        console.error('Failed to fetch categories:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    getCategories()
  }, [])
  
  // Handle category change
  const handleCategoryChange = (categoryId: string) => {
    // Preserve page number if it exists
    const page = searchParams.get('page') || ''
    
    // If 'all', remove the category param, otherwise set it
    if (categoryId === 'all') {
      if (page) {
        router.push(`/spotlight?page=${page}`)
      } else {
        router.push('/spotlight')
      }
    } else {
      if (page) {
        router.push(`/spotlight?category=${categoryId}&page=${page}`)
      } else {
        router.push(`/spotlight?category=${categoryId}`)
      }
    }
  }
  
  if (isLoading) {
    return (
      <div className="my-8">
        <div className="flex flex-wrap gap-3">
          {[1, 2, 3, 4, 5].map((i) => (
            <div 
              key={i}
              className="w-28 h-10 bg-gray-200 rounded-full animate-pulse"
            />
          ))}
        </div>
      </div>
    )
  }
  
  return (
    <div className="my-8">
      <div className="flex flex-wrap gap-3">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => handleCategoryChange(category.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              currentCategory === category.id
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category.name}
          </button>
        ))}
      </div>
    </div>
  )
} 