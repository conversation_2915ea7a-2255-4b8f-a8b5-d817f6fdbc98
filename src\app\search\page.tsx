// 将此文件改为服务器组件
import { Suspense } from 'react'
import ProductTable from '@/components/Search/ProductTable'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import Link from 'next/link'
import { SearchPagination } from '@/components/Search/SearchPagination'

export const generateMetadata = (): Metadata => {
  return {
    title: 'Search Electronic Components | China Electron',
    description: 'Search for electronic components, raw materials, and suppliers on China Electron. Find detailed specifications, datasheets, and connect with distributors.',
    keywords: 'electronic component search, component database, raw material search, electronic parts search, China Electron search',
    alternates: {
      canonical: getCanonicalUrl('search'),
    },
  };
};

// 定义产品相关接口
interface ProductPrice {
  quantity: number
  price: number
  created_at?: string
}

interface Brand {
  name_cn: string | null
  name_en: string | null
  name_ru: string | null
  logo_url?: string
  website?: string
}

interface Parameter {
  param_name: string
  param_value: string
}

interface Product {
  product_id: string;
  model: string;
  url?: string; // 添加url字段
  brand_id: string;
  brand: Brand;
  price_key: string;
  stock_key: string;
  datasheet_url: string;
  parameters_key: string;
  parameters?: {
    english?: Parameter[];
    chinese?: Parameter[];
    russian?: Parameter[];
  };
  description: string;
  prices: ProductPrice[];
  stock: number;
  image_list?: string[];
  category_path?: string[];
  category_names?: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
  category_id?: string;
}

interface SearchResponse {
  total: number
  page: number
  results: Product[]
  keyword?: string;
}

// 创建带查询参数的URL
function createQueryUrl(baseUrl: string, params: Record<string, string>) {
  const url = new URL(baseUrl);
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      url.searchParams.append(key, value);
    }
  });
  return url.toString();
}

// 服务端获取搜索数据
async function getSearchResults(query: string, category: string, brand: string, page: number) {
  try {
    let searchUrl = '';
    
    // 根据参数确定使用哪个搜索API
    if (query) {
      searchUrl = createQueryUrl('https://webapi.chinaelectron.com/products', {
        search: query,
        page: page.toString()
      });
    } else if (category) {
      searchUrl = createQueryUrl('https://webapi.chinaelectron.com/products', {
        category: category,
        page: page.toString()
      });
    } else if (brand) {
      searchUrl = createQueryUrl('https://webapi.chinaelectron.com/products', {
        brand: brand,
        page: page.toString()
      });
    }
    
    if (!searchUrl) {
      return {
        total: 0,
        page: 1,
        results: []
      };
    }
    
    const response = await fetch(searchUrl, { next: { revalidate: 3600 } }); // 1小时缓存
    
    if (!response.ok) {
      throw new Error(`Error fetching data: ${response.statusText}`);
    }
    
    const data = await response.json() as SearchResponse;
    
    // 确保每个产品数据包含必要字段
    const processedResults = data.results.map(product => {
      // 处理可能的品牌信息缺失
      if (!product.brand) {
        product.brand = {
          name_cn: null,
          name_en: null,
          name_ru: null
        };
      }
      
      // 如果没有分类路径，添加默认路径
      if (!product.category_path || product.category_path.length === 0) {
        product.category_path = ['component', 'electronic', 'general'];
      }
      
      // 确保参数字段存在
      if (!product.parameters) {
        product.parameters = {
          english: [],
          chinese: [],
          russian: []
        };
      }
      
      return product;
    });
    
    return {
      total: data.total,
      page: data.page || page,
      results: processedResults
    };
  } catch (error) {
    console.error('Search error:', error);
    return {
      total: 0,
      page: 1,
      results: [],
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

// 获取搜索标题
function getSearchTitle(query: string, category: string, brand: string) {
  if (query) return `Search results for "${query}"`;
  if (category) return 'Category results';
  if (brand) return 'Brand results';
  return 'Search results';
}

// 获取品类信息
async function getCategoryInfo(categoryId: string) {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/categories/${categoryId}`, {
      next: { revalidate: 86400 } // 24小时缓存
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch category info: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching category info:', error);
    return null;
  }
}

// 获取品牌信息
async function getBrandInfo(brandId: string) {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/brands/${brandId}`, {
      next: { revalidate: 86400 } // 24小时缓存
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch brand info: ${response.statusText}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching brand info:', error);
    return null;
  }
}

// 服务器端页面组件
export default async function SearchPage({ searchParams }: { searchParams: Promise<{ q?: string, category?: string, brand?: string, page?: string }> }) {
  const resolvedSearchParams = await searchParams;
  const query = resolvedSearchParams.q || '';
  const category = resolvedSearchParams.category || '';
  const brand = resolvedSearchParams.brand || '';
  const page = parseInt(resolvedSearchParams.page || '1', 10);
  
  // 获取搜索结果
  const searchResults = await getSearchResults(query, category, brand, page);
  
  // 获取品类或品牌信息
  let categoryInfo = null;
  let brandInfo = null;
  
  if (category) {
    categoryInfo = await getCategoryInfo(category);
  }
  
  if (brand) {
    brandInfo = await getBrandInfo(brand);
  }
  
  // 获取自定义标题
  const getCustomTitle = () => {
    if (query) return `Search results for "${query}"`;
    if (category && categoryInfo) {
      const categoryName = categoryInfo.name_en || categoryInfo.name_cn || 'Category';
      return `${categoryName} Components`;
    }
    if (brand && brandInfo) {
      const brandName = brandInfo.name_en || brandInfo.name_cn || 'Brand';
      return `${brandName} Products`;
    }
    return 'Search results';
  };
  
  const customTitle = getCustomTitle();
  
  return (
    <div className="min-h-screen w-full relative">
      {/* 统一背景渐变 */}
      <div className="fixed inset-0 bg-gradient-to-b from-gray-50 to-gray-100 z-[-2]" />
      
      {/* 装饰背景 */}
      <div className="fixed inset-0 z-[-1]">
        {/* 网格背景 */}
        <div className="absolute inset-0" 
             style={{
               backgroundImage: `radial-gradient(circle at center, #64748B 2px, transparent 0.7px)`,
               backgroundSize: '24px 24px',
               backgroundPosition: '0 0',
               opacity: 0.12
             }} 
        />
        
        {/* 顶部装饰渐变 */}
        <div className="absolute inset-0 bg-gradient-to-b from-[#DE2910]/3 via-transparent to-transparent" />
        
        {/* 渐变光晕效果 */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[1200px] h-[700px]
                       bg-gradient-radial from-[#DE2910]/15 via-[#DE2910]/5 to-transparent 
                       blur-[100px] rounded-full" />
      </div>

      <div className="relative w-full">
        <div className="container-fluid px-4 sm:px-6 lg:px-14 py-6">
          <h1 className="text-2xl font-bold mb-4">{customTitle}</h1>
          
          {/* 品牌描述 */}
          {brandInfo && brandInfo.description && (
            <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
              <h2 className="text-lg font-medium mb-2">About {brandInfo.name_en || brandInfo.name_cn}</h2>
              <p className="text-gray-700">{brandInfo.description}</p>
            </div>
          )}
          
          {/* 搜索结果 */}
          {searchResults.error ? (
            <div className="text-center py-8">
              <p className="text-red-500 mb-4">{searchResults.error}</p>
              <Link 
                href={`/search?${new URLSearchParams(resolvedSearchParams as Record<string, string>).toString()}`}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Try Again
              </Link>
            </div>
          ) : searchResults.results.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-2">No products found matching your search criteria.</p>
              <p className="text-gray-500">Try a different search term or browse categories.</p>
            </div>
          ) : (
            <Suspense fallback={
              <div className="flex justify-center items-center h-64">
                <LoadingSpinner size="lg" />
              </div>
            }>
              <SearchPagination 
                products={searchResults.results} 
                totalResults={searchResults.total} 
                currentPage={page}
                query={query}
                category={category}
                brand={brand}
              />
            </Suspense>
          )}
        </div>
      </div>
    </div>
  )
} 