import Image from 'next/image'
import Link from 'next/link'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { BrandLink, ContactEmailDisplay } from './ClientComponents'
import { getCanonicalUrl } from '@/utils/canonicalUrl'

// API接口类型定义
interface DistributorResponse {
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  category_id: string;
  description_cn: string;
  description_en: string;
  description_ru: string;
  logo: string;
  img_list: string[];
  contact: {
    [key: string]: string[];
  };
  certification: string[];
  address_cn: string;
  address_en: string;
  address_ru: string;
}

interface Brand {
  id: string;
  name_en: string;
  name_cn: string;
  name_ru: string;
  logo_url: string;
  website: string;
  introduction_en: string;
  introduction_cn: string;
  introduction_ru: string;
}

interface BrandResponse {
  distributor_id: string;
  brands: Brand[];
}

// 构建分销商图片URL
function getDistributorImageUrl(imageName: string): string {
  // 如果是完整URL则直接返回
  if (imageName.startsWith('http')) {
    return imageName;
  }
  
  // 否则直接拼接原始API URL返回给前端
  return `https://webapi.chinaelectron.com/distributors/image/${imageName}`;
}

// 从API获取分销商详情
async function getDistributorDetails(id: string): Promise<DistributorResponse | null> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/distributors/${id}`, { 
      next: { revalidate: 86400 } // 缓存24小时
    });
    
    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch distributor: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching distributor details:', error);
    return null;
  }
}

// 从API获取分销商相关品牌
async function getDistributorBrands(id: string): Promise<Brand[]> {
  try {
    const response = await fetch(`https://webapi.chinaelectron.com/brand-distributors?distributor_id=${id}`, {
      next: { revalidate: 86400 } // 缓存24小时
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch distributor brands: ${response.statusText}`);
    }
    
    const data: BrandResponse = await response.json();
    return data.brands || [];
  } catch (error) {
    console.error('Error fetching distributor brands:', error);
    return [];
  }
}

// 获取动态元数据 - 更新为处理异步params
export async function generateMetadata(props: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const params = await props.params;
  const distributor = await getDistributorDetails(params.id);
  
  if (!distributor) {
    return {
      title: 'Distributor Not Found',
      description: 'The requested distributor could not be found'
    };
  }
  
  const distributorName = distributor.name_en || distributor.name_cn || 'Unknown Distributor';
  // 确定简称：使用第一个名称部分或全名如果没有空格
  let shortName = distributorName;
  if (distributorName.includes(' ')) {
    shortName = distributorName.split(' ')[0];
  }
  
  return {
    title: `${distributorName} (${shortName}) - Electronic Component Sourcing | China Electron`,
    description: `Connect with ${distributorName} (${shortName}) for electronic component raw materials through China Electron. View their offerings and capabilities.`,
    keywords: `${distributorName}, ${shortName}, ${shortName} components, electronic distribution, raw material sourcing, China Electron, ${shortName} products`,
    alternates: {
      canonical: getCanonicalUrl(`distributors/${params.id}`),
    },
  };
}

// 创建安全版本的分销商数据，处理敏感信息
function createSecureDistributorData(distributor: DistributorResponse): DistributorResponse {
  // 深拷贝以避免修改原始数据
  const secureDistributor = JSON.parse(JSON.stringify(distributor)) as DistributorResponse;
  
  // 处理邮箱信息 - 如果没有登录凭证，将邮箱替换为占位符
  // 注意：我们仍然保留邮箱存在的信息，但不保留实际内容
  if (secureDistributor.contact && secureDistributor.contact['Email'] && secureDistributor.contact['Email'].length > 0) {
    secureDistributor.contact['Email'] = ['SECURED_EMAIL_REQUIRES_LOGIN'];
  }
  
  return secureDistributor;
}

// 主页面组件 - 更新为处理异步params
export default async function DistributorDetailPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const originalDistributor = await getDistributorDetails(params.id);
  
  // 如果分销商不存在，返回404
  if (!originalDistributor) {
    notFound();
  }
  
  // 获取分销商相关品牌
  const brands = await getDistributorBrands(params.id);
  
  // 由于无法在服务器端可靠地检查用户登录状态，我们始终创建安全版本的数据
  // 将由客户端组件根据本地存储中的登录状态决定是否显示敏感信息
  const distributor = createSecureDistributorData(originalDistributor);
  
  // 准备邮箱信息 - 始终使用安全版本
  let emailContact = null;
  if (distributor.contact && distributor.contact['Email'] && distributor.contact['Email'].length > 0) {
    emailContact = distributor.contact['Email'][0];
    
    // 如果是安全版本的邮箱占位符
    if (emailContact === 'SECURED_EMAIL_REQUIRES_LOGIN' && originalDistributor.contact['Email']?.[0]) {
      // 获取原始邮箱信息来辅助掩码生成
      const originalEmail = originalDistributor.contact['Email'][0];
      const firstChar = originalEmail.charAt(0);
      const atIndex = originalEmail.indexOf('@');
      const username = atIndex > 0 ? originalEmail.substring(0, atIndex) : '';
      const domain = atIndex > 0 ? originalEmail.substring(atIndex + 1) : '';
      const domainParts = domain.split('.');
      const extension = domainParts.length > 1 ? domainParts[domainParts.length - 1] : '';
      
      // 编码原始邮箱，使用Base64但适用于浏览器端解码
      const base64Email = Buffer.from(originalEmail).toString('base64');
      
      // 创建包含掩码信息的安全邮箱格式，包含编码后的原始邮箱
      emailContact = `MASK:${firstChar}:${username.length}:${domain}:${extension}:${base64Email}`;
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 企业基本信息 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row gap-8 items-start">
            <div className="w-full md:w-1/3 relative">
              <div className="aspect-[2/1] relative rounded-xl overflow-hidden bg-white">
                <Image
                  src={getDistributorImageUrl(distributor.logo)}
                  alt={distributor.name_en || distributor.name_cn}
                  fill
                  className="object-contain p-4"
                />
              </div>
            </div>
            <div className="flex-1">
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                {distributor.name_en || distributor.name_cn}
              </h1>
              <p className="text-gray-600 mb-6">
                {distributor.description_en || distributor.description_cn || 'Electronic components distributor'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 品牌展示 */}
      {brands.length > 0 && (
        <div className="bg-white border-b border-gray-200 py-8">
          <div className="max-w-7xl mx-auto px-4">
            <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-6">Represented Brands</h2>
            <div className="overflow-x-auto pb-4">
              <div className="flex gap-4 min-w-max">
                {brands.map(brand => (
                  <div
                    key={brand.id}
                    className="bg-white rounded-xl p-4 border border-gray-100 hover:border-[#DE2910]/20 hover:shadow-md transition-all flex flex-col min-w-[180px] max-w-[200px]"
                  >
                    <div className="h-16 mb-4 relative">
                      {brand.logo_url && (
                <Image
                          src={brand.logo_url}
                          alt={brand.name_en}
                  fill
                  className="object-contain"
                          sizes="180px"
                        />
                      )}
                    </div>
                    <h3 className="text-base font-medium text-gray-900 mb-1">{brand.name_en}</h3>
                    <div className="mt-auto flex items-center justify-between">
                      <Link 
                        href={`/manufacturers/${brand.id}`}
                        prefetch={false}
                        className="text-sm text-[#DE2910] hover:underline"
                      >
                        Details
                      </Link>
                      {brand.website && (
                        <BrandLink website={brand.website} />
                      )}
              </div>
            </div>
          ))}
        </div>
      </div>
          </div>
        </div>
      )}

      {/* 联系信息 */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <h2 className="text-xl md:text-2xl font-bold text-gray-900 mb-6">Contact Information</h2>
        <div className="bg-white rounded-xl p-6 border border-gray-100">
          <div className="grid grid-cols-1 gap-6">
            {emailContact && (
              <div className="text-gray-600">
                <span className="font-medium text-gray-700 mr-2">Email:</span>
                <ContactEmailDisplay email={emailContact} />
              </div>
            )}
            
            {!emailContact && (
              <div className="text-gray-500 italic">
                No contact information available. Please check back later.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
} 