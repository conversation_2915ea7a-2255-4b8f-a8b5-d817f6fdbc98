import { ComponentDetail, FullData } from '@/types/component'
import Link from 'next/link'

interface DocumentsAndFilesProps {
  component: ComponentDetail
  parsedData: FullData
}

export default function DocumentsAndFiles({ component, parsedData }: DocumentsAndFilesProps) {
  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Documents</h2>
      
      {component.metadata.datasheet && (
        <Link
          href={`/pdf/${encodeURIComponent(component.metadata.datasheet.split('/').pop() || '')}`}
          className="inline-flex items-center gap-2 text-[#DE2910] hover:text-[#FF3B20] transition-colors"
          target="_blank"
          rel="noopener noreferrer"
        >
          <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
          <span>Datasheet</span>
        </Link>
      )}
    </div>
  )
} 