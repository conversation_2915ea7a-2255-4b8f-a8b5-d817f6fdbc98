import Link from 'next/link';
import { type BreadcrumbItem } from '@/utils/breadcrumbs';

interface ServerBreadcrumbProps {
  breadcrumbs: BreadcrumbItem[];
}

export default function ServerBreadcrumb({ breadcrumbs }: ServerBreadcrumbProps) {
  // 如果只有首页，不显示面包屑
  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <nav className="" aria-label="Breadcrumb">
      <div className="px-4 lg:px-14 py-3">
        <ol className="flex items-center space-x-2 text-sm">
          {breadcrumbs.map((item, index) => (
            <li key={`breadcrumb-${index}-${item.path}`} className="flex items-center">
              {index > 0 && (
                <span className="mx-2 text-gray-400">/</span>
              )}
              {index === breadcrumbs.length - 1 ? (
                <span className="text-gray-500">{item.label}</span>
              ) : (
                <Link
                  href={item.path}
                  className="text-blue-600 hover:text-blue-800 hover:underline"
                >
                  {item.label}
                </Link>
              )}
            </li>
          ))}
        </ol>
      </div>
    </nav>
  );
}

// 导出类型供其他组件使用
export type { BreadcrumbItem };
