'use client'

import Link from 'next/link'
import Image from 'next/image'
import { getProductImageUrl } from '@/utils/imageUtils'
import { buildProductUrl } from '@/utils/productUrl'

interface ProductPrice {
  quantity: number
  price: number
}

interface Brand {
  name_cn: string | null
  name_en: string | null
  name_ru: string | null
}

interface Product {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_id: string
  brand: Brand | null
  price_key: string
  stock_key: string
  datasheet_url: string
  parameters_key: string
  description: string
  prices: ProductPrice[]
  stock: number
  image_list?: string[]
  category_path?: string[]
  category_names?: {
    [key: string]: {
      name_cn: string
      name_en: string
      name_ru: string
    }
  }
  category_id?: string
}

interface ProductListProps {
  products: Product[]
}

// 格式化价格 - 直接使用美元价格
const formatPrice = (price: number): string => {
  // 使用美元格式化，包含千位分隔符和可能的七位小数
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 7
  }).format(price);
}

// 获取品牌显示名称
const getBrandDisplayName = (brand: Brand | null): string => {
  if (!brand) return 'Unknown'
  return brand.name_en || brand.name_cn || brand.name_ru || 'Unknown'
}

// 将Product类型转换为BaseProductData类型，解决Brand接口不匹配的问题
const toBaseProductData = (product: Product) => {
  return {
    ...product,
    brand: product.brand ? {
      name_en: product.brand.name_en || undefined,
      name_cn: product.brand.name_cn || undefined,
      name_ru: product.brand.name_ru || undefined
    } : undefined
  };
};

export default function ProductList({ products }: ProductListProps) {
  if (!products || products.length === 0) {
    return (
      <div className="text-center p-8 rounded-2xl bg-gray-50 border border-gray-100">
        <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p className="text-gray-600 font-medium">No products found</p>
      </div>
    )
  }

  return (
    <div className="space-y-6 mb-8">
      {products.map((product) => (
        <Link
          href={product.url || buildProductUrl(toBaseProductData(product))}
          key={product.product_id}
          className="block"
          prefetch={false}
        >
          <div
             className="bg-white rounded-2xl shadow-sm border border-gray-100 
                      hover:border-[#DE2910]/20 hover:shadow-lg hover:shadow-[#DE2910]/10 
                      transition-all duration-300 p-6 cursor-pointer"
          >
            <div className="flex items-start gap-6">
              <div className="w-32 h-32 bg-gray-100 rounded-xl flex items-center justify-center overflow-hidden">
                <Image 
                  src={getProductImageUrl(product.image_list)}
                  alt={product.model}
                  width={128}
                  height={128}
                  className="object-contain w-full h-full"
                  unoptimized
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">{product.model}</h3>
                  <div className="text-[#DE2910]">
                    <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
                <p className="text-gray-500 mb-4">{product.description}</p>
                
                <div className="flex items-center gap-4 mb-4">
                  <span className="px-3 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium">
                    {getBrandDisplayName(product.brand)}
                  </span>
                  {product.datasheet_url && (
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        window.open(product.datasheet_url, '_blank', 'noopener,noreferrer');
                      }}
                      className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-sm font-medium hover:bg-gray-200 transition-colors flex items-center gap-1 cursor-pointer"
                    >
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                              d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                      </svg>
                      Datasheet
                    </div>
                  )}
                </div>
                
                {product.prices && product.prices.length > 0 && (
                  <div className="mb-3">
                    <h4 className="text-sm font-medium text-gray-500 mb-2">Price Tiers:</h4>
                    <div className="flex flex-wrap gap-2">
                      {product.prices.map((price, idx) => (
                        <div key={idx} className="px-3 py-1.5 bg-gray-50 rounded-lg">
                          <span className="text-gray-600 text-sm">{price.quantity}+: </span>
                          <span className="text-[#DE2910] font-semibold">{formatPrice(price.price)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="flex items-center">
                  <span className="text-sm text-gray-500">Stock: </span>
                  <span className="ml-2 font-semibold text-green-600">{product.stock.toLocaleString()} available</span>
                </div>
              </div>
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
} 