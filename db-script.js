/**
 * Cloudflare Workers D1 数据库操作脚本
 * 支持批量增删改查和多表联查
 * 数据库绑定变量名: DB
 */

export default {
  async fetch(request, env, ctx) {
    const { DB } = env;
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    // CORS 处理
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    if (method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    try {
      let response;

      // 路由处理
      switch (true) {
        // 单表操作
        case path.startsWith('/api/table/'):
          response = await handleTableOperations(request, DB, path, method);
          break;
        
        // 批量操作
        case path.startsWith('/api/batch/'):
          response = await handleBatchOperations(request, DB, path, method);
          break;
        
        // 多表联查
        case path.startsWith('/api/join/'):
          response = await handleJoinQueries(request, DB, path, method);
          break;
        
        // 自定义 SQL 查询
        case path.startsWith('/api/query/'):
          response = await handleCustomQuery(request, DB, method);
          break;
        
        // 数据库信息
        case path === '/api/info':
          response = await getDatabaseInfo(DB);
          break;
        
        // API 文档
        case path === '/api/docs' || path === '/':
          response = getApiDocumentation();
          break;
        
        default:
          response = new Response(JSON.stringify({ error: 'Route not found' }), {
            status: 404,
            headers: { 'Content-Type': 'application/json' }
          });
      }

      // 添加 CORS 头
      Object.entries(corsHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      return response;

    } catch (error) {
      console.error('Error:', error);
      return new Response(JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
  }
};

/**
 * 处理单表操作
 */
async function handleTableOperations(request, DB, path, method) {
  const tableName = path.split('/')[3];
  const url = new URL(request.url);
  
  if (!tableName) {
    return jsonResponse({ error: 'Table name is required' }, 400);
  }

  switch (method) {
    case 'GET':
      return await getTableData(DB, tableName, url.searchParams);
    
    case 'POST':
      const insertData = await request.json();
      return await insertTableData(DB, tableName, insertData);
    
    case 'PUT':
      const updateData = await request.json();
      return await updateTableData(DB, tableName, updateData, url.searchParams);
    
    case 'DELETE':
      return await deleteTableData(DB, tableName, url.searchParams);
    
    default:
      return jsonResponse({ error: 'Method not allowed' }, 405);
  }
}

/**
 * 获取表数据
 */
async function getTableData(DB, tableName, params) {
  try {
    let query = `SELECT * FROM ${tableName}`;
    const conditions = [];
    const values = [];

    // 构建 WHERE 条件
    for (const [key, value] of params.entries()) {
      if (key.startsWith('where_')) {
        const field = key.replace('where_', '');
        conditions.push(`${field} = ?`);
        values.push(value);
      }
    }

    if (conditions.length > 0) {
      query += ` WHERE ${conditions.join(' AND ')}`;
    }

    // 排序
    if (params.get('order_by')) {
      const orderBy = params.get('order_by');
      const orderDir = params.get('order_dir') || 'ASC';
      query += ` ORDER BY ${orderBy} ${orderDir}`;
    }

    // 分页
    const limit = params.get('limit') || 100;
    const offset = params.get('offset') || 0;
    query += ` LIMIT ${limit} OFFSET ${offset}`;

    const stmt = DB.prepare(query);
    const result = await stmt.bind(...values).all();

    return jsonResponse({
      success: true,
      data: result.results,
      count: result.results.length,
      meta: result.meta
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 插入表数据
 */
async function insertTableData(DB, tableName, data) {
  try {
    if (Array.isArray(data)) {
      // 批量插入
      return await batchInsert(DB, tableName, data);
    } else {
      // 单条插入
      const fields = Object.keys(data);
      const placeholders = fields.map(() => '?').join(', ');
      const query = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES (${placeholders})`;
      
      const stmt = DB.prepare(query);
      const result = await stmt.bind(...Object.values(data)).run();
      
      return jsonResponse({
        success: true,
        inserted_id: result.meta.last_row_id,
        changes: result.meta.changes
      });
    }
  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 更新表数据
 */
async function updateTableData(DB, tableName, data, params) {
  try {
    const fields = Object.keys(data);
    const setClause = fields.map(field => `${field} = ?`).join(', ');
    
    let query = `UPDATE ${tableName} SET ${setClause}`;
    const values = [...Object.values(data)];
    
    // WHERE 条件
    const conditions = [];
    for (const [key, value] of params.entries()) {
      if (key.startsWith('where_')) {
        const field = key.replace('where_', '');
        conditions.push(`${field} = ?`);
        values.push(value);
      }
    }
    
    if (conditions.length === 0) {
      return jsonResponse({ error: 'WHERE condition is required for UPDATE' }, 400);
    }
    
    query += ` WHERE ${conditions.join(' AND ')}`;
    
    const stmt = DB.prepare(query);
    const result = await stmt.bind(...values).run();
    
    return jsonResponse({
      success: true,
      changes: result.meta.changes
    });
    
  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 删除表数据
 */
async function deleteTableData(DB, tableName, params) {
  try {
    let query = `DELETE FROM ${tableName}`;
    const conditions = [];
    const values = [];
    
    // WHERE 条件
    for (const [key, value] of params.entries()) {
      if (key.startsWith('where_')) {
        const field = key.replace('where_', '');
        conditions.push(`${field} = ?`);
        values.push(value);
      }
    }
    
    if (conditions.length === 0) {
      return jsonResponse({ error: 'WHERE condition is required for DELETE' }, 400);
    }
    
    query += ` WHERE ${conditions.join(' AND ')}`;
    
    const stmt = DB.prepare(query);
    const result = await stmt.bind(...values).run();
    
    return jsonResponse({
      success: true,
      deleted: result.meta.changes
    });
    
  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 批量操作处理
 */
async function handleBatchOperations(request, DB, path, method) {
  const operation = path.split('/')[3]; // insert, update, delete
  const tableName = path.split('/')[4];
  
  if (!tableName) {
    return jsonResponse({ error: 'Table name is required' }, 400);
  }
  
  const data = await request.json();
  
  switch (operation) {
    case 'insert':
      return await batchInsert(DB, tableName, data);
    case 'update':
      return await batchUpdate(DB, tableName, data);
    case 'delete':
      return await batchDelete(DB, tableName, data);
    default:
      return jsonResponse({ error: 'Invalid batch operation' }, 400);
  }
}

/**
 * 批量插入
 */
async function batchInsert(DB, tableName, dataArray) {
  try {
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      return jsonResponse({ error: 'Data array is required' }, 400);
    }

    const fields = Object.keys(dataArray[0]);
    const placeholders = fields.map(() => '?').join(', ');
    const query = `INSERT INTO ${tableName} (${fields.join(', ')}) VALUES (${placeholders})`;

    const stmt = DB.prepare(query);
    const results = [];

    for (const item of dataArray) {
      const result = await stmt.bind(...Object.values(item)).run();
      results.push({
        inserted_id: result.meta.last_row_id,
        changes: result.meta.changes
      });
    }

    return jsonResponse({
      success: true,
      inserted_count: results.length,
      results: results
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 批量更新
 */
async function batchUpdate(DB, tableName, dataArray) {
  try {
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      return jsonResponse({ error: 'Data array is required' }, 400);
    }

    const results = [];

    for (const item of dataArray) {
      const { where, data } = item;
      if (!where || !data) {
        results.push({ error: 'Both where and data are required' });
        continue;
      }

      const fields = Object.keys(data);
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const whereFields = Object.keys(where);
      const whereClause = whereFields.map(field => `${field} = ?`).join(' AND ');

      const query = `UPDATE ${tableName} SET ${setClause} WHERE ${whereClause}`;
      const values = [...Object.values(data), ...Object.values(where)];

      const stmt = DB.prepare(query);
      const result = await stmt.bind(...values).run();
      results.push({
        changes: result.meta.changes,
        where: where
      });
    }

    return jsonResponse({
      success: true,
      updated_count: results.length,
      results: results
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 批量删除
 */
async function batchDelete(DB, tableName, whereArray) {
  try {
    if (!Array.isArray(whereArray) || whereArray.length === 0) {
      return jsonResponse({ error: 'Where conditions array is required' }, 400);
    }

    const results = [];

    for (const where of whereArray) {
      const whereFields = Object.keys(where);
      const whereClause = whereFields.map(field => `${field} = ?`).join(' AND ');
      const query = `DELETE FROM ${tableName} WHERE ${whereClause}`;

      const stmt = DB.prepare(query);
      const result = await stmt.bind(...Object.values(where)).run();
      results.push({
        deleted: result.meta.changes,
        where: where
      });
    }

    return jsonResponse({
      success: true,
      deleted_count: results.length,
      results: results
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 处理多表联查
 */
async function handleJoinQueries(request, DB, path, method) {
  if (method !== 'POST') {
    return jsonResponse({ error: 'Only POST method is allowed for join queries' }, 405);
  }

  try {
    const queryConfig = await request.json();
    return await executeJoinQuery(DB, queryConfig);
  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 处理自定义 SQL 查询
 */
async function handleCustomQuery(request, DB, method) {
  if (method !== 'POST') {
    return jsonResponse({ error: 'Only POST method is allowed for custom queries' }, 405);
  }

  try {
    const { sql, params = [] } = await request.json();

    if (!sql) {
      return jsonResponse({ error: 'SQL query is required' }, 400);
    }

    const stmt = DB.prepare(sql);
    const result = await stmt.bind(...params).all();

    return jsonResponse({
      success: true,
      data: result.results,
      count: result.results.length,
      meta: result.meta
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 获取数据库信息
 */
async function getDatabaseInfo(DB) {
  try {
    // 获取所有表
    const tablesResult = await DB.prepare(
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '_cf_%'"
    ).all();

    const tables = [];

    for (const table of tablesResult.results) {
      const tableName = table.name;

      // 获取表结构
      const columnsResult = await DB.prepare(`PRAGMA table_info('${tableName}')`).all();

      // 获取索引信息
      const indexesResult = await DB.prepare(`PRAGMA index_list('${tableName}')`).all();

      // 获取行数
      const countResult = await DB.prepare(`SELECT COUNT(*) as count FROM ${tableName}`).first();

      tables.push({
        name: tableName,
        columns: columnsResult.results,
        indexes: indexesResult.results,
        row_count: countResult.count
      });
    }

    return jsonResponse({
      success: true,
      database_info: {
        table_count: tables.length,
        tables: tables
      }
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * 获取 API 文档
 */
function getApiDocumentation() {
  const docs = {
    title: "Cloudflare D1 Database API",
    version: "1.0.0",
    description: "批量数据库操作和多表联查 API",
    endpoints: {
      "GET /api/info": {
        description: "获取数据库信息",
        response: "数据库表结构和统计信息"
      },
      "GET /api/table/{tableName}": {
        description: "查询表数据",
        parameters: {
          "where_{field}": "WHERE 条件",
          "order_by": "排序字段",
          "order_dir": "排序方向 (ASC/DESC)",
          "limit": "限制条数 (默认100)",
          "offset": "偏移量 (默认0)"
        },
        example: "/api/table/users?where_status=active&order_by=created_at&limit=50"
      },
      "POST /api/table/{tableName}": {
        description: "插入数据",
        body: "单条数据对象或数据数组",
        example: { "name": "John", "email": "<EMAIL>" }
      },
      "PUT /api/table/{tableName}": {
        description: "更新数据",
        parameters: { "where_{field}": "WHERE 条件 (必需)" },
        body: "要更新的数据",
        example: { "name": "Jane" }
      },
      "DELETE /api/table/{tableName}": {
        description: "删除数据",
        parameters: { "where_{field}": "WHERE 条件 (必需)" }
      },
      "POST /api/batch/insert/{tableName}": {
        description: "批量插入",
        body: "数据数组",
        example: [{ "name": "User1" }, { "name": "User2" }]
      },
      "POST /api/batch/update/{tableName}": {
        description: "批量更新",
        body: "包含 where 和 data 的对象数组",
        example: [{ "where": { "id": 1 }, "data": { "name": "Updated" } }]
      },
      "POST /api/batch/delete/{tableName}": {
        description: "批量删除",
        body: "WHERE 条件数组",
        example: [{ "id": 1 }, { "id": 2 }]
      },
      "POST /api/join/": {
        description: "多表联查",
        body: {
          "select": ["字段列表或*"],
          "from": "主表名",
          "joins": [
            {
              "type": "INNER|LEFT|RIGHT",
              "table": "关联表名",
              "on": "关联条件"
            }
          ],
          "where": {
            "field": "值",
            "field2": { "gt": 100 },
            "field3": { "like": "%search%" }
          },
          "orderBy": "排序字段",
          "limit": 100,
          "offset": 0
        }
      },
      "POST /api/query/": {
        description: "自定义 SQL 查询",
        body: {
          "sql": "SELECT * FROM table WHERE field = ?",
          "params": ["参数值"]
        }
      }
    },
    examples: {
      "多表联查示例": {
        "select": ["u.name", "o.total", "p.name as product_name"],
        "from": "users u",
        "joins": [
          {
            "type": "INNER",
            "table": "orders o",
            "on": "u.user_id = o.user_id"
          },
          {
            "type": "LEFT",
            "table": "products p",
            "on": "o.product_id = p.product_id"
          }
        ],
        "where": {
          "u.status": "active",
          "o.total": { "gt": 100 }
        },
        "orderBy": "o.created_at DESC",
        "limit": 50
      }
    }
  };

  return new Response(JSON.stringify(docs, null, 2), {
    headers: { 'Content-Type': 'application/json' }
  });
}

/**
 * 辅助函数：返回 JSON 响应
 */
function jsonResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: { 'Content-Type': 'application/json' }
  });
}

/**
 * 执行多表联查
 */
async function executeJoinQuery(DB, config) {
  try {
    const {
      select = ['*'],
      from,
      joins = [],
      where = {},
      orderBy,
      limit = 100,
      offset = 0
    } = config;

    if (!from) {
      return jsonResponse({ error: 'from table is required' }, 400);
    }

    // 构建 SELECT 子句
    const selectClause = Array.isArray(select) ? select.join(', ') : select;

    // 构建基础查询
    let query = `SELECT ${selectClause} FROM ${from}`;
    const values = [];

    // 构建 JOIN 子句
    for (const join of joins) {
      const { type = 'INNER', table, on } = join;
      query += ` ${type.toUpperCase()} JOIN ${table} ON ${on}`;
    }

    // 构建 WHERE 子句
    const whereConditions = [];
    for (const [field, value] of Object.entries(where)) {
      if (Array.isArray(value)) {
        // IN 查询
        const placeholders = value.map(() => '?').join(', ');
        whereConditions.push(`${field} IN (${placeholders})`);
        values.push(...value);
      } else if (typeof value === 'object' && value !== null) {
        // 操作符查询 (gt, lt, like 等)
        for (const [op, val] of Object.entries(value)) {
          switch (op) {
            case 'gt':
              whereConditions.push(`${field} > ?`);
              values.push(val);
              break;
            case 'lt':
              whereConditions.push(`${field} < ?`);
              values.push(val);
              break;
            case 'gte':
              whereConditions.push(`${field} >= ?`);
              values.push(val);
              break;
            case 'lte':
              whereConditions.push(`${field} <= ?`);
              values.push(val);
              break;
            case 'like':
              whereConditions.push(`${field} LIKE ?`);
              values.push(val);
              break;
            case 'ne':
              whereConditions.push(`${field} != ?`);
              values.push(val);
              break;
          }
        }
      } else {
        // 等值查询
        whereConditions.push(`${field} = ?`);
        values.push(value);
      }
    }

    if (whereConditions.length > 0) {
      query += ` WHERE ${whereConditions.join(' AND ')}`;
    }

    // 构建 ORDER BY 子句
    if (orderBy) {
      if (Array.isArray(orderBy)) {
        query += ` ORDER BY ${orderBy.join(', ')}`;
      } else {
        query += ` ORDER BY ${orderBy}`;
      }
    }

    // 构建 LIMIT 和 OFFSET
    query += ` LIMIT ${limit} OFFSET ${offset}`;

    const stmt = DB.prepare(query);
    const result = await stmt.bind(...values).all();

    return jsonResponse({
      success: true,
      data: result.results,
      count: result.results.length,
      query: query,
      meta: result.meta
    });

  } catch (error) {
    return jsonResponse({ error: error.message }, 500);
  }
}
