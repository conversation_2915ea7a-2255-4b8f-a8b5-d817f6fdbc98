# BOM处理器API文档

本文档详细说明了BOM处理器API的使用方法，包括所有可用的端点、请求参数和响应格式。

## 基本信息

- **基础URL**: `https://bom-processor.962692556.workers.dev`
- **认证方式**: 无需认证，但需要提供用户ID参数
- **响应格式**: 所有API响应均为JSON格式

## API端点

### 1. 上传BOM文件

上传Excel或CSV格式的BOM文件进行后续处理。

**请求**:

```
POST /upload
```

**参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| file | File | 是 | 要上传的BOM文件（Excel或CSV格式） |
| userId | String | 是 | 用户ID |
| fileName | String | 否 | 自定义文件名，如不提供则使用原始文件名 |

**cURL示例**:

```bash
curl -X POST https://bom-processor.962692556.workers.dev/upload \
  -F "file=@path/to/your/bom.xlsx" \
  -F "userId=user123" \
  -F "fileName=my-custom-bom-name.xlsx"
```

**成功响应**:

```json
{
  "success": true,
  "bom_id": 42,
  "file_url": "https://storage-url.com/boms/my-custom-bom-name.xlsx",
  "file_name": "my-custom-bom-name.xlsx",
  "message": "文件上传成功"
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "文件上传失败: 无效的文件格式"
}
```

### 2. 解析BOM文件

解析已上传的BOM文件，提取表头信息和预览数据。

**请求**:

```
POST /parse
```

**参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| bomId | Number | 是 | BOM文件ID |
| userId | String | 是 | 用户ID |

**cURL示例**:

```bash
curl -X POST https://bom-processor.962692556.workers.dev/parse \
  -H "Content-Type: application/json" \
  -d '{
    "bomId": 42,
    "userId": "user123"
  }'
```

**成功响应**:

```json
{
  "success": true,
  "bom_id": 42,
  "headers": ["序号", "物料编码", "规格型号", "品牌", "数量", "备注"],
  "preview_data": [
    ["1", "C42433400", "P7", "JUXING", "25", ""],
    ["2", "C4245484", "WSR2R5000FBA", "Vishay Intertech", "25", ""],
    ["3", "C17409730", "BY6", "(DIOTEC)", "25", ""]
  ],
  "suggested_mapping": [
    {"index": 0, "name": "Index", "suggested_column": 0},
    {"index": 1, "name": "Part Number", "suggested_column": 2},
    {"index": 2, "name": "Manufacturer", "suggested_column": 3},
    {"index": 3, "name": "Quantity", "suggested_column": 4}
  ],
  "message": "BOM文件解析成功"
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "BOM文件解析失败: 无法读取文件内容"
}
```

### 3. 处理BOM数据

处理已解析的BOM数据，匹配产品，并保存到数据库。

**请求**:

```
POST /process
```

**参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| bomId | Number | 是 | BOM文件ID |
| userId | String | 是 | 用户ID |
| startLine | Number | 否 | 数据起始行（默认为2，跳过表头） |
| selection | Array | 否 | 列映射，格式为[[0,1],[1,4],[2,0],[3,5]]，表示[LCSC列索引, Excel列索引] |
| priorityMatch | String | 否 | 匹配优先级，可选值: "in_stock"（有库存优先）或 "exact"（精确匹配优先） |
| coefficientTotal | Number | 否 | 数量乘数，默认为1 |

**cURL示例**:

```bash
curl -X POST https://bom-processor.962692556.workers.dev/process \
  -H "Content-Type: application/json" \
  -d '{
    "bomId": 42,
    "userId": "user123",
    "startLine": 2,
    "selection": [[0,1],[1,4],[2,0],[3,5]],
    "priorityMatch": "in_stock",
    "coefficientTotal": 1
  }'
```

**成功响应**:

```json
{
  "success": true,
  "bom_id": 42,
  "statistics": {
    "item_count": 12,
    "success_count": 12,
    "exact_match_count": 10,
    "partial_match_count": 2,
    "no_match_count": 0,
    "est_total_price": 7246.97,
    "each_price": 603.91
  },
  "lcsc_result": {
    "uploadUuid": "C99DAE8F25E1BEE987E0A2FFD417225E",
    "matchUuid": "6822B097209404239FEFDDB21FA300AC",
    "bomDetails": {
      "bomId": 161758,
      "uuid": "6822B097209404239FEFDDB21FA300AC",
      "bomName": "bom_1751451321497_wlcvyuon.csv",
      "projectIndexVO": [
        {
          "bomId": 161758,
          "coefficientTotal": 1,
          "bomProductId": 5538740,
          "productId": 44466188,
          "productCode": "C42433400",
          "productMpn": "P7",
          "brandNameEn": "JUXING",
          "quantity": 25,
          "matchScore": "Partial Matches",
          "priceVO": {
            "realPrice": 0.0078,
            "realTotalPrice": 0.39
          }
        }
      ]
    }
  },
  "sync_results": [
    {
      "lcsc_code": "C42433400",
      "product_id": "CMA100398764",
      "status": "exists",
      "details": {
        "new_product": false,
        "new_brand": false,
        "new_mapping": false,
        "updated_tables": []
      }
    }
  ],
  "message": "BOM处理成功完成"
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "BOM处理失败: 无法匹配产品"
}
```

### 4. 获取BOM列表

获取用户的所有BOM文件列表。

**请求**:

```
GET /boms?userId=user123&page=1&limit=20
```

**参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| userId | String | 是 | 用户ID |
| page | Number | 否 | 页码，默认为1 |
| limit | Number | 否 | 每页记录数，默认为20 |

**cURL示例**:

```bash
curl -X GET "https://bom-processor.962692556.workers.dev/boms?userId=user123&page=1&limit=20"
```

**成功响应**:

```json
{
  "success": true,
  "data": [
    {
      "bom_id": 42,
      "user_id": "user123",
      "file_name": "my-custom-bom-name.xlsx",
      "file_url": "https://storage-url.com/boms/my-custom-bom-name.xlsx",
      "item_count": 12,
      "success_count": 12,
      "exact_match_count": 10,
      "partial_match_count": 2,
      "no_match_count": 0,
      "est_total_price": 7246.97,
      "each_price": 603.91,
      "create_at": "2023-06-15 10:30:45",
      "update_at": "2023-06-15 10:31:20"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "pages": 1
  }
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "获取BOM列表失败: 缺少用户ID"
}
```

### 5. 获取BOM详情

获取特定BOM文件的详细信息和物料清单。

**请求**:

```
GET /boms/{bomId}?userId=user123
```

**参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| bomId | Number | 是 | BOM文件ID（URL路径参数） |
| userId | String | 是 | 用户ID（查询参数） |

**cURL示例**:

```bash
curl -X GET "https://bom-processor.962692556.workers.dev/boms/42?userId=user123"
```

**成功响应**:

```json
{
  "success": true,
  "bom_id": 42,
  "bom_main": {
    "bom_id": 42,
    "user_id": "user123",
    "file_name": "my-custom-bom-name.xlsx",
    "file_url": "https://storage-url.com/boms/my-custom-bom-name.xlsx",
    "item_count": 12,
    "success_count": 12,
    "exact_match_count": 10,
    "partial_match_count": 2,
    "no_match_count": 0,
    "est_total_price": 7246.97,
    "each_price": 603.91,
    "create_at": "2023-06-15 10:30:45",
    "update_at": "2023-06-15 10:31:20"
  },
  "bom_items": [
    {
      "bom_items_id": 13,
      "user_id": "user123",
      "bom_id": 42,
      "product_code": "CMA100398764",
      "customer_part_number": "R3000FGP-TP",
      "match_status": 1,
      "requested_qty": 1,
      "order_qty": 1,
      "stock_availability": "In Stock",
      "target_price": 0,
      "lead_time": "",
      "unit_price": 0.0078,
      "ext_price": 0.39,
      "offer_availability": "Available",
      "packaging_choice": "Tape & Reel (TR)",
      "notes": "{\"lcsc_code\":\"C42433400\",\"Mfr.#\":\"R3000FGP-TP\",\"Description\":\"Micro Commercial Components (MCC) Rectifiers 0.2A 3000V 2100V 3000V 200mA 20A / R3000FGP-TP\"}",
      "create_at": "2023-06-15 10:31:20",
      "update_at": "2023-06-15 10:31:20"
    }
  ]
}
```

**错误响应**:

```json
{
  "success": false,
  "error": "获取BOM详情失败: BOM不存在或无权访问"
}
```

## 错误代码和说明

| 错误代码 | 描述 |
|----------|------|
| 400 | 请求参数错误，请检查必填参数 |
| 404 | 资源不存在，如BOM文件未找到 |
| 500 | 服务器内部错误，请联系管理员 |

## 数据结构

### BOM主表 (bom_main)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| bom_id | Integer | BOM ID，主键 |
| user_id | String | 用户ID |
| file_name | String | 文件名 |
| file_url | String | 文件URL |
| item_count | Integer | 物料总数 |
| success_count | Integer | 匹配成功数量 |
| exact_match_count | Integer | 精确匹配数量 |
| partial_match_count | Integer | 部分匹配数量 |
| no_match_count | Integer | 未匹配数量 |
| est_total_price | Float | 预估总价 |
| each_price | Float | 单价 |
| priority_matching | Integer | 匹配优先级（1=有库存优先，0=精确匹配优先） |
| quantity_multiplier | Float | 数量乘数 |
| create_at | DateTime | 创建时间 |
| update_at | DateTime | 更新时间 |

### BOM物料表 (bom_items)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| bom_items_id | Integer | 物料ID，主键 |
| user_id | String | 用户ID |
| bom_id | Integer | BOM ID，外键 |
| product_code | String | 产品代码（系统内部产品ID） |
| customer_part_number | String | 客户料号 |
| match_status | Integer | 匹配状态（2=精确匹配，1=部分匹配，0=未匹配） |
| requested_qty | Integer | 请求数量 |
| order_qty | Integer | 订购数量 |
| stock_availability | String | 库存状态 |
| target_price | Float | 目标价格 |
| lead_time | String | 交货周期 |
| unit_price | Float | 单价 |
| ext_price | Float | 总价 |
| offer_availability | String | 报价可用性 |
| packaging_choice | String | 包装选择 |
| notes | String | 备注（JSON格式，包含LCSC原始代码等信息） |
| create_at | DateTime | 创建时间 |
| update_at | DateTime | 更新时间 |

## 注意事项

1. 所有API请求和响应均使用UTF-8编码
2. 文件上传大小限制为10MB
3. BOM文件支持的格式包括：.xlsx, .xls, .csv
4. 处理大型BOM文件（超过500个物料项）可能会导致超时错误
5. 所有时间戳使用ISO 8601格式 




### CURL 请求示例

```bash
curl -X GET "https://bom-processor.962692556.workers.dev/user-boms?userId=123456" \
  -H "Content-Type: application/json"
```

### 返回示例

```json
{
    "success": true,
    "user_id": "2e77ff8d-ab4b-46b5-a55e-79ef50485bb6",
    "bom_count": 2,
    "bom_list": [
        {
            "bom_id": "2497ed30-35ff-47d7-96f4-b1d25501edb2",
            "bom_name": "test",
            "status": 0,
            "user_id": "2e77ff8d-ab4b-46b5-a55e-79ef50485bb6",
            "file_url": "https://bom-processor.962692556.workers.dev/bom_1751472648035_dbvwuqq0.csv",
            "item_count": 12,
            "success_count": 12,
            "exact_match_count": 10,
            "partial_match_count": 2,
            "no_match_count": 0,
            "est_total_price": 13621.739999999998,
            "each_price": 1135.1449999999998,
            "priority_matching": 1,
            "quantity_multiplier": 10,
            "create_at": "2025-07-02 16:10:49",
            "update_at": "2025-07-02 16:31:01"
        },
        {
            "bom_id": "f726d789-3fee-4e61-8e9f-baf0eaee4ff1",
            "bom_name": "test",
            "status": 0,
            "user_id": "2e77ff8d-ab4b-46b5-a55e-79ef50485bb6",
            "file_url": "https://bom-processor.962692556.workers.dev/bom_1751469159549_hiqsmttc.csv",
            "item_count": 12,
            "success_count": 12,
            "exact_match_count": 10,
            "partial_match_count": 2,
            "no_match_count": 0,
            "est_total_price": 10104.12,
            "each_price": 842.0100000000001,
            "priority_matching": 1,
            "quantity_multiplier": 5,
            "create_at": "2025-07-02 15:12:41",
            "update_at": "2025-07-02 15:28:58"
        }
    ]
}
```

错误返回示例（缺少userId参数）：

```json
{
  "error": "缺少必要参数userId"
}
```
