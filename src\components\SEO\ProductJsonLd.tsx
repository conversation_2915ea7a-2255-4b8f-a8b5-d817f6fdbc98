'use client'

import Script from 'next/script'
import { useState, useEffect } from 'react'

interface ProductDetail {
  product_id: string
  model: string
  brand: {
    id: string
    name_en: string
    name_cn: string
    name_ru: string
    logo_url: string
    website: string
  }
  prices: {
    quantity: number
    price: number
  }[]
  stock: number
  parameters: {
    en: Array<{
      param_name: string
      param_value: string
    }>
    cn: Array<{
      param_name: string
      param_value: string
    }>
    ru: Array<{
      param_name: string
      param_value: string
    }>
  }
  category_path: string[]
  category_id: string
  category_names: {
    [key: string]: {
      name_cn: string
      name_en: string
      name_ru: string
    }
  }
  datasheet_url: string
  image_list: string[]
  description: string
  rohs?: string | null
}

interface ProductJsonLdProps {
  productDetail: ProductDetail
  productUrl: string
  componentId?: string // 添加组件ID参数
}

export default function ProductJsonLd({ productDetail, productUrl, componentId }: ProductJsonLdProps) {
  const [datasheetPageUrl, setDatasheetPageUrl] = useState<string | null>(null);
  const [isDatasheetLoaded, setIsDatasheetLoaded] = useState(false);

  // 获取datasheet详情页URL
  useEffect(() => {
    const fetchDatasheetPageUrl = async () => {
      const idToUse = componentId || productDetail.product_id;
      if (!idToUse) {
        setIsDatasheetLoaded(true);
        return;
      }

      try {
        const response = await fetch(
          `https://webapi.chinaelectron.com/datasheet/product?product_id=${encodeURIComponent(idToUse)}`
        );

        if (response.ok) {
          const data = await response.json();
          if (data.exists && data.datasheet_url) {
            setDatasheetPageUrl(data.datasheet_url);
          }
        }
      } catch (error) {
        console.error('Error fetching datasheet URL:', error);
      } finally {
        setIsDatasheetLoaded(true);
      }
    };

    fetchDatasheetPageUrl();
  }, [componentId, productDetail.product_id]);

  // 生成产品的 JSON-LD 结构化数据
  const generateProductJsonLd = () => {
    const brandName = productDetail.brand?.name_en || 'Unknown Brand'
    const productName = productDetail.model
    const productImage = productDetail.image_list?.[0] || ''
    const categoryName = productDetail.category_names?.[productDetail.category_id]?.name_en || 'Electronic Component'
    
    // 获取最低价格 - 确保正确获取阶梯价格中的最低价
    let lowestPrice = 0
    if (productDetail.prices && productDetail.prices.length > 0) {
      // 过滤掉无效价格，然后获取最低价格
      const validPrices = productDetail.prices
        .map(p => p.price)
        .filter(price => price > 0)

      if (validPrices.length > 0) {
        lowestPrice = Math.min(...validPrices)
      }
    }

    // 将价格转换为字符串，保留原始精度，不进行四舍五入
    const priceString = lowestPrice > 0 ? lowestPrice.toString() : "0"
    
    // 构建产品描述
    let productDescription = productDetail.description || `${productName} by ${brandName}`
    
    // 添加主要参数到描述中
    if (productDetail.parameters?.en && productDetail.parameters.en.length > 0) {
      const mainParams = productDetail.parameters.en.slice(0, 3).map(p => `${p.param_name}: ${p.param_value}`).join(', ')
      productDescription += ` - ${mainParams}`
    }
    
    // 添加 RoHS 信息
    if (productDetail.rohs) {
      productDescription += ` RoHS`
    }
    
    // 构建 JSON-LD 数据
    const jsonLdData: any = {
      "@context": "http://schema.org",
      "@type": "Product",
      "mpn": productName,
      "sku": `${productName}_${brandName.toUpperCase().replace(/\s+/g, '_')}`,
      "name": productName,
      "image": productImage,
      "description": productDescription,
      "brand": {
        "@type": "Brand",
        "name": brandName
      },
      "category": categoryName,
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "ratingCount": "13"
      },
      "offers": {
        "@type": "Offer",
        "itemCondition": "http://schema.org/NewCondition",
        "inventoryLevel": productDetail.stock?.toString() || "0",
        "availability": productDetail.stock > 0 ? "http://schema.org/InStock" : "http://schema.org/OutOfStock",
        "url": productUrl,
        "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30天后
        "priceCurrency": "USD",
        "price": priceString,
        "priceSpecification": {
          "@type": "PriceSpecification",
          "price": priceString,
          "priceCurrency": "USD",
          "valueAddedTaxIncluded": false
        },
        "hasMerchantReturnPolicy": {
          "@type": "MerchantReturnPolicy",
          "applicableCountry": "US",
          "returnPolicyCategory": "https://schema.org/MerchantReturnFiniteReturnWindow",
          "merchantReturnDays": 30,
          "returnMethod": "https://schema.org/ReturnByMail",
          "returnFees": "https://schema.org/FreeReturn"
        },
        "shippingDetails": {
          "@type": "OfferShippingDetails",
          "shippingRate": {
            "@type": "MonetaryAmount",
            "value": 6,
            "currency": "USD"
          },
          "shippingDestination": {
            "@type": "DefinedRegion",
            "addressCountry": [
              "AE", "AO", "AR", "AT", "AU", "BE", "BG", "BH", "BR", "CA",
              "CH", "CL", "CO", "CR", "CY", "CZ", "DE", "DK", "EC", "EE",
              "ES", "FI", "FR", "GH", "GR", "HR", "HU", "ID", "IE", "IL",
              "IT", "JO", "JP", "KW", "GB", "KR", "KE", "LT", "LU", "LV",
              "MA", "MT", "MX", "MY", "NG", "NL", "NO", "PE", "PH", "PL",
              "NZ", "PT", "QA", "RO", "RW", "SE", "SG", "SI", "SK", "SV",
              "TH", "TZ", "UG", "US", "SA", "VN", "ZA"
            ]
          },
          "deliveryTime": {
            "@type": "ShippingDeliveryTime",
            "handlingTime": {
              "@type": "QuantitativeValue",
              "minValue": 1,
              "maxValue": 1,
              "unitCode": "DAY"
            },
            "transitTime": {
              "@type": "QuantitativeValue",
              "minValue": 3,
              "maxValue": 7,
              "unitCode": "DAY"
            }
          }
        }
      }
    }
    
    // 如果有数据表，添加相关信息
    if (productDetail.datasheet_url) {
      // 使用与产品详情页面相同的datasheet URL逻辑
      // datasheetPageUrl 是从 API 获取的完整 SEO 友好 URL
      // 如果没有获取到，则使用简单的 /datasheet/{id} 格式作为回退
      const idToUse = componentId || productDetail.product_id;
      const baseUrl = productUrl.includes('/product/')
        ? productUrl.split('/product/')[0]
        : productUrl.split('/components/')[0];
      const finalDatasheetUrl = datasheetPageUrl || `${baseUrl}/datasheet/${idToUse}`

      jsonLdData["additionalProperty"] = [
        {
          "@type": "PropertyValue",
          "name": "Datasheet",
          "value": "Available",
          "url": finalDatasheetUrl
        }
      ]
    }
    
    // 添加主要技术参数
    if (productDetail.parameters?.en && productDetail.parameters.en.length > 0) {
      const additionalProperties = productDetail.parameters.en.slice(0, 10).map(param => ({
        "@type": "PropertyValue",
        "name": param.param_name,
        "value": param.param_value
      }))
      
      if (jsonLdData["additionalProperty"]) {
        jsonLdData["additionalProperty"].push(...additionalProperties)
      } else {
        jsonLdData["additionalProperty"] = additionalProperties
      }
    }
    
    return jsonLdData
  }

  // 只有在 datasheet URL 加载完成后才生成 JSON-LD
  if (!isDatasheetLoaded) {
    return null;
  }

  const jsonLd = generateProductJsonLd()

  return (
    <Script
      id="product-jsonld"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(jsonLd, null, 2)
      }}
      strategy="afterInteractive"
    />
  )
}
