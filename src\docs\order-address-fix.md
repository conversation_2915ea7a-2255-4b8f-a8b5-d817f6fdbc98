# 订单地址修复文档

## 问题描述

之前的订单系统存在一个bug：创建订单时，shipping_info表和billing_info表的字段结构与user_addresses表不匹配，导致订单创建时地址信息为NULL。

## 解决方案

创建了新的`order_address`表来存储订单的收货地址和账单地址信息，该表的字段结构与`user_addresses`表完全对应。

## 修改内容

### 1. API接口修改 (`src/services/api.ts`)

#### 1.1 修改CreateOrderRequest接口
```typescript
// 修改前
export interface CreateOrderRequest {
  user_id: string;
  shipping_address_id: number;  // 使用地址ID
  billing_address_id: number;   // 使用地址ID
  // ...其他字段
}

// 修改后
export interface CreateOrderRequest {
  user_id: string;
  shipping_address: AddressInfo;  // 直接传递完整地址信息
  billing_address: AddressInfo;   // 直接传递完整地址信息
  // ...其他字段
}
```

#### 1.2 修改createOrder函数
- 添加了创建`order_address`记录的逻辑
- 在创建订单时，将收货地址和账单地址分别保存到`order_address`表
- **移除了创建`shipping_info`和`billing_info`记录的逻辑**，避免冗余数据和NULL值问题

#### 1.3 修改getOrderDetails函数
- 从`order_address`表获取地址信息，而不是从`user_addresses`表
- **移除了从`shipping_info`和`billing_info`表获取数据的逻辑**
- 从`order_address`表构造`shipping_info`对象以保持向后兼容
- 确保订单详情页面显示的是订单创建时的实际地址信息

### 2. Checkout页面修改 (`src/app/checkout/page.tsx`)

#### 2.1 修改订单创建逻辑
- 在提交订单前，获取选中的完整地址信息
- 传递完整的地址对象而不是地址ID

#### 2.2 添加安全验证
- 验证必须选择物流方式
- 验证运费必须大于0
- 在切换收货地址时重置运费和物流方式

## 数据库表结构

### order_address表
```sql
CREATE TABLE order_address (
    shipping_id INTEGER PRIMARY KEY,
    order_id TEXT,
    shipping_method TEXT,
    user_id TEXT NOT NULL,
    address_type TEXT NOT NULL,  -- 'shipping' 或 'billing'
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    street_address TEXT,
    apt_suite_building TEXT,
    country_region TEXT,
    postal_code TEXT,
    state_province TEXT,
    city TEXT,
    company_name TEXT,
    created_at DATETIME,
    updated_at DATETIME
);
```

## 修复效果

1. **解决地址信息为NULL的问题**：订单创建时会正确保存地址信息
2. **提高数据完整性**：每个订单都有完整的收货和账单地址记录
3. **支持历史订单查看**：即使用户修改了地址，历史订单仍能显示创建时的地址
4. **增强安全性**：防止运费为0的订单和未选择物流方式的订单

## 注意事项

1. **不再创建`shipping_info`和`billing_info`记录**，避免了字段不匹配和NULL值问题
2. 所有地址信息现在统一存储在`order_address`表中
3. 订单详情页面现在从`order_address`表获取地址信息
4. 地址信息在订单创建时被"快照"保存，不会因用户后续修改地址而改变
5. 为保持向后兼容，`getOrderDetails`函数仍返回`shipping_info`对象，但数据来源于`order_address`表
