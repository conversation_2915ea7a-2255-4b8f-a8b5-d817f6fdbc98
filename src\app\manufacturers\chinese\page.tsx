import ManufacturersLayout from '../ManufacturersLayout'
import { fetchChineseBrands } from '../fetchBrands'
import ErrorDisplay from '../ErrorDisplay'

// 定义searchParams类型
type SearchParams = {
  page?: string;
  limit?: string;
}

// Chinese manufacturers page
export default async function ChineseBrandsPage({
  searchParams
}: {
  searchParams?: Promise<SearchParams>
}) {
  // 从URL查询参数获取页码，默认为第1页
  const resolvedSearchParams = await searchParams || {};
  const page = resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1;
  // 每页显示数量，默认为20
  const limit = resolvedSearchParams.limit ? parseInt(resolvedSearchParams.limit) : 20;
  
  // console.log('Chinese page params:', { page, limit, searchParams: resolvedSearchParams });
  
  // 获取分页数据
  const { brands, total, currentPage, totalPages, error } = await fetchChineseBrands(page, limit);
  
  /* console.log('Chinese brands data:', { 
    brandsLength: brands.length, 
    total, 
    currentPage, 
    totalPages,
    hasError: !!error 
  }); */
  
  if (error) {
    return <ErrorDisplay error={error} />;
  }
  
  return (
    <ManufacturersLayout 
      brands={brands} 
      filterType="chinese" 
      total={total}
      currentPage={currentPage}
      totalPages={totalPages}
    />
  );
} 