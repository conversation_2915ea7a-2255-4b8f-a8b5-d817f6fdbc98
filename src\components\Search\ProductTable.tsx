'use client'

import Image from 'next/image'
import Link from 'next/link'
import { getProductImageUrl } from '@/utils/imageUtils'
import { buildProductUrl } from '@/utils/productUrl'
import { useState, useMemo, useRef } from 'react'

interface ProductPrice {
  quantity: number
  price: number
  created_at?: string
}

interface Brand {
  name_cn: string | null
  name_en: string | null
  name_ru: string | null
  logo_url?: string
  website?: string
}

interface Parameter {
  param_name: string
  param_value: string
}

interface Product {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand_id: string
  brand: Brand
  price_key: string
  stock_key: string
  datasheet_url: string
  parameters_key: string
  parameters?: {
    english?: Parameter[]
    chinese?: Parameter[]
    russian?: Parameter[]
  }
  description: string
  prices: ProductPrice[]
  stock: number
  image_list?: string[]
  category_path?: string[]
  category_names?: {
    [key: string]: {
      name_cn: string
      name_en: string
      name_ru: string
    }
  }
  category_id?: string
  full?: Product
}

interface ProductTableProps {
  products: Product[]
  totalResults: number
  currentPage: number
  navigateToPage: (page: number) => void
}

// 格式化价格 - 直接使用美元价格
const formatPrice = (price: number): string => {
  // 使用美元格式化，包含千位分隔符和两位小数
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 4
  }).format(price);
}

// 获取品牌显示名称
const getBrandDisplayName = (brand: Brand | null): string => {
  if (!brand) return 'Unknown'
  return brand.name_en || brand.name_cn || brand.name_ru || 'Unknown'
}

// 将Product类型转换为BaseProductData类型
const toBaseProductData = (product: Product) => {
  return {
    ...product,
    brand: product.brand ? {
      name_en: product.brand.name_en || undefined,
      name_cn: product.brand.name_cn || undefined,
      name_ru: product.brand.name_ru || undefined
    } : undefined
  };
};

export default function ProductTable({ products, totalResults, currentPage, navigateToPage }: ProductTableProps) {
  // 用于跟踪哪些产品展开了完整价格
  const [expandedPrices, setExpandedPrices] = useState<Record<string, boolean>>({});
  const [jumpToPage, setJumpToPage] = useState<string>('');
  const jumpInputRef = useRef<HTMLInputElement>(null);

  // 切换价格展开状态
  const togglePriceExpand = (productId: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发行点击事件
    setExpandedPrices(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  // 获取共有的参数名称（只展示所有产品都有的参数）
  const commonParameterNames = useMemo(() => {
    // 首先统计每个参数名出现的次数
    const paramCounts: Record<string, number> = {};
    
    products.forEach(product => {
      if (product.parameters?.english) {
        product.parameters.english.forEach(param => {
          paramCounts[param.param_name] = (paramCounts[param.param_name] || 0) + 1;
        });
      }
    });
    
    // 只保留出现次数等于产品数量的参数名（即所有产品都有的参数）
    // 或者至少出现在50%以上产品中的参数
    const minOccurrence = Math.max(1, Math.floor(products.length * 0.5));
    
    return Object.entries(paramCounts)
      .filter(([_, count]) => count >= minOccurrence)
      .map(([name]) => name)
      .sort();
  }, [products]);

  // 处理制造商点击
  const handleManufacturerClick = (e: React.MouseEvent, brandId: string) => {
    e.stopPropagation(); // 阻止事件冒泡
    // 跳转到制造商详情页
    window.location.href = `/manufacturer/${brandId}`;
  };

  // 处理产品ID点击
  const handlePartClick = (e: React.MouseEvent, product: Product) => {
    e.stopPropagation(); // 阻止事件冒泡
    // 跳转到产品详情页 - 使用url字段或回退到buildProductUrl
    const productUrl = product.url || buildProductUrl({
      product_id: product.product_id,
      model: product.model,
      brand_id: product.brand_id,
      brand: product.brand ? {
        name_en: product.brand.name_en || undefined,
        name_cn: product.brand.name_cn || undefined,
        name_ru: product.brand.name_ru || undefined
      } : undefined,
      category_path: product.category_path,
      category_names: product.category_names,
      category_id: product.category_id
    });
    window.location.href = productUrl;
  };

  // 处理页面跳转
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum > 0 && pageNum <= Math.ceil(totalResults / 20)) {
      navigateToPage(pageNum);
    }
    setJumpToPage('');
  };

  // 处理输入框按键事件
  const handleJumpKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage();
    }
  };

  // 渲染分页控件
  const renderPagination = () => {
    if (totalResults <= 0) return null;
    
    const totalPages = Math.ceil(totalResults / 20); // 假设每页20个结果
    
    return (
      <div className="flex items-center space-x-3">
        {/* 上一页按钮 */}
        <button
          onClick={() => navigateToPage(currentPage - 1)}
          disabled={currentPage <= 1}
          className={`px-2 py-1 rounded border ${
            currentPage <= 1
              ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
        >
          &lt;
        </button>
        
        {/* 页码 - 显示最多5页 */}
        <div className="flex items-center space-x-1">
          {currentPage > 3 && totalPages > 5 && (
            <>
              <button
                onClick={() => navigateToPage(1)}
                className="px-2 py-1 rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              >
                1
              </button>
              {currentPage > 4 && <span className="text-gray-500">...</span>}
            </>
          )}
          
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            // 计算要显示哪些页码
            let pageNum = i + 1;
            if (currentPage > 3 && totalPages > 5) {
              pageNum = currentPage + i - 2;
              if (pageNum > totalPages) return null;
              if (pageNum < 1) return null;
            }
            
            return (
              <button
                key={pageNum}
                onClick={() => navigateToPage(pageNum)}
                className={`px-2 py-1 rounded border ${
                  currentPage === pageNum
                    ? 'bg-[#DE2910] text-white border-[#DE2910]'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {pageNum}
              </button>
            );
          })}
          
          {currentPage < totalPages - 2 && totalPages > 5 && (
            <>
              {currentPage < totalPages - 3 && <span className="text-gray-500">...</span>}
              <button
                onClick={() => navigateToPage(totalPages)}
                className="px-2 py-1 rounded border bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              >
                {totalPages}
              </button>
            </>
          )}
        </div>
        
        {/* 下一页按钮 */}
        <button
          onClick={() => navigateToPage(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className={`px-2 py-1 rounded border ${
            currentPage >= totalPages
              ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          }`}
        >
          &gt;
        </button>
        
        {/* 页面跳转 */}
        <div className="flex items-center space-x-2 ml-3">
          <span className="text-sm text-gray-600">Go to</span>
          <input
            ref={jumpInputRef}
            type="text"
            value={jumpToPage}
            onChange={(e) => setJumpToPage(e.target.value)}
            onKeyPress={handleJumpKeyPress}
            className="w-12 h-8 px-2 border border-gray-300 rounded text-sm"
          />
          <button
            onClick={handleJumpToPage}
            className="px-2 py-1 bg-gray-200 text-gray-700 rounded border border-gray-300 hover:bg-gray-300 text-sm"
          >
            Go
          </button>
          <span className="text-sm text-gray-600">of {totalPages} pages</span>
        </div>
      </div>
    );
  };

  if (!products || products.length === 0) {
    return (
      <div className="text-center p-8 rounded-2xl bg-gray-50 border border-gray-100">
        <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <p className="text-gray-600 font-medium">No products found</p>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden w-full">
      <div className="flex justify-between items-center p-3 border-b">
        <div className="text-sm text-gray-600">
          Results: <span className="font-medium">{totalResults}</span>
        </div>
        <div>{renderPagination()}</div>
      </div>
      
      <div className="relative overflow-hidden">
        {/* 表格容器，显示水平滚动条 */}
        <div className="overflow-x-auto" style={{ overflowX: 'auto', scrollbarWidth: 'thin' }}>
          <table className="w-full table-fixed divide-y divide-gray-200">
            <thead className="sticky top-0 z-30 bg-gray-800">
              <tr>
                {/* 固定列 - 前6列 */}
                <th scope="col" className="sticky left-0 top-0 z-50 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800" style={{ width: '80px', minWidth: '80px', maxWidth: '80px' }}>
                  Images
                </th>
                <th 
                  scope="col" 
                  className="sticky left-[80px] top-0 z-50 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800"
                  style={{ width: '160px', minWidth: '160px', maxWidth: '160px' }}
                >
                  Pricing
                </th>
                <th scope="col" className="sticky left-[240px] top-0 z-50 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800" style={{ width: '100px', minWidth: '100px', maxWidth: '100px' }}>
                  Quantity
                </th>
                <th 
                  scope="col" 
                  className="sticky left-[340px] top-0 z-50 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800"
                  style={{ width: '120px', minWidth: '120px', maxWidth: '120px' }}
                >
                  Availability
                </th>
                <th 
                  scope="col" 
                  className="sticky left-[460px] top-0 z-50 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800"
                  style={{ width: '200px', minWidth: '200px', maxWidth: '200px' }}
                >
                  MFR Part #
                </th>
                <th 
                  scope="col" 
                  className="sticky left-[660px] top-0 z-50 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800"
                  style={{ width: '150px', minWidth: '150px', maxWidth: '150px' }}
                >
                  Manufacturer
                </th>
                
                {/* 可滚动列，但表头仍然固定 */}
                <th scope="col" className="sticky top-0 z-30 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800" style={{ width: '250px' }}>
                  Description
                </th>
                <th scope="col" className="sticky top-0 z-30 border-r border-gray-700 px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800" style={{ width: '150px' }}>
                  CE Part#
                </th>
                
                {/* 动态参数列 - 只显示共有参数 */}
                {commonParameterNames.map((paramName, index) => (
                  <th 
                    key={paramName} 
                    scope="col" 
                    className={`sticky top-0 z-30 ${index < commonParameterNames.length - 1 ? 'border-r border-gray-700' : ''} px-3 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-gray-800`}
                    style={{ width: '150px' }}
                  >
                    {paramName}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product, rowIndex) => {
                // 创建参数映射，方便查找
                const paramMap: Record<string, string> = {};
                if (product.parameters?.english) {
                  product.parameters.english.forEach(param => {
                    paramMap[param.param_name] = param.param_value;
                  });
                }

                // 处理阶梯价格显示
                const isExpanded = expandedPrices[product.product_id] || false;
                const prices = product.prices || [];
                const displayPrices = isExpanded ? prices : prices.slice(0, 4);
                const hasMorePrices = prices.length > 4;

                // 交替行背景色
                const rowBgClass = rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50';

                return (
                  <tr 
                    key={product.product_id}
                    className="hover:bg-blue-50"
                  >
                    {/* 固定列 - 前6列 */}
                    <td className={`sticky left-0 z-20 ${rowBgClass} border-r border-gray-200 px-3 py-3 hover:bg-blue-50`} style={{ width: '80px', minWidth: '80px', maxWidth: '80px' }}>
                      <div className="flex items-center justify-center w-full h-full">
                        <div className="w-14 h-14 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                          <Image 
                            src={getProductImageUrl(product.image_list)}
                            alt={product.model}
                            width={56}
                            height={56}
                            className="object-contain w-full h-full"
                            unoptimized
                          />
                        </div>
                      </div>
                    </td>
                    <td className={`sticky left-[80px] z-20 ${rowBgClass} border-r border-gray-200 px-3 py-3 hover:bg-blue-50`} style={{ width: '160px', minWidth: '160px', maxWidth: '160px' }}>
                      {displayPrices.length > 0 ? (
                        <div className="space-y-1">
                          {displayPrices.map((price, idx) => (
                            <div key={idx} className="text-xs">
                              <span className="text-gray-500 font-bold">{price.quantity}+: </span>
                              <span className="text-[#DE2910] font-bold">{formatPrice(price.price)}</span>
                            </div>
                          ))}
                          {hasMorePrices && (
                            <button
                              onClick={(e) => togglePriceExpand(product.product_id, e)}
                              className="text-xs text-blue-600 hover:text-blue-800 mt-1 font-medium"
                            >
                              {isExpanded ? 'Less' : 'More'}
                            </button>
                          )}
                        </div>
                      ) : (
                        <span className="text-gray-400 text-xs">No pricing</span>
                      )}
                    </td>
                    <td className={`sticky left-[240px] z-20 ${rowBgClass} border-r border-gray-200 px-3 py-3 hover:bg-blue-50`} style={{ width: '100px', minWidth: '100px', maxWidth: '100px' }}>
                      {/* 留空，用于后续询价功能 */}
                    </td>
                    <td className={`sticky left-[340px] z-20 ${rowBgClass} border-r border-gray-200 px-3 py-3 hover:bg-blue-50`} style={{ width: '120px', minWidth: '120px', maxWidth: '120px' }}>
                      <div className="text-sm text-gray-900">
                        {product.stock > 0 ? (
                          <span className="text-green-600 font-medium">{product.stock} in stock</span>
                        ) : (
                          <span className="text-red-600 font-medium">Out of stock</span>
                        )}
                      </div>
                    </td>
                    <td className={`sticky left-[460px] z-20 ${rowBgClass} border-r border-gray-200 px-3 py-3 hover:bg-blue-50`} style={{ width: '200px', minWidth: '200px', maxWidth: '200px' }}>
                      <Link
                        href={product.url || buildProductUrl({
                          product_id: product.product_id,
                          model: product.model,
                          brand_id: product.brand_id,
                          brand: product.brand ? {
                            name_en: product.brand.name_en || undefined,
                            name_cn: product.brand.name_cn || undefined,
                            name_ru: product.brand.name_ru || undefined
                          } : undefined,
                          category_path: product.category_path,
                          category_names: product.category_names,
                          category_id: product.category_id
                        })}
                        className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline break-words"
                      >
                        {product.model}
                      </Link>
                    </td>
                    <td className={`sticky left-[660px] z-20 ${rowBgClass} border-r border-gray-200 px-3 py-3 hover:bg-blue-50`} style={{ width: '150px', minWidth: '150px', maxWidth: '150px' }}>
                      <Link 
                        href={`/manufacturers/${product.brand_id}`}
                        className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline break-words"
                      >
                        {getBrandDisplayName(product.brand)}
                      </Link>
                    </td>
                    
                    {/* 可滚动列 */}
                    <td className={`${rowBgClass} border-r border-gray-200 px-3 py-3`} style={{ width: '250px' }}>
                      <div className="text-sm text-gray-900 truncate" title={product.description}>
                        {product.description}
                      </div>
                    </td>
                    <td className={`${rowBgClass} border-r border-gray-200 px-3 py-3 whitespace-nowrap`} style={{ width: '150px' }}>
                      <Link
                        href={product.url || buildProductUrl({
                          product_id: product.product_id,
                          model: product.model,
                          brand_id: product.brand_id,
                          brand: product.brand ? {
                            name_en: product.brand.name_en || undefined,
                            name_cn: product.brand.name_cn || undefined,
                            name_ru: product.brand.name_ru || undefined
                          } : undefined,
                          category_path: product.category_path,
                          category_names: product.category_names,
                          category_id: product.category_id
                        })}
                        className="text-sm font-medium text-blue-600 hover:text-blue-800 hover:underline"
                      >
                        {product.product_id}
                      </Link>
                    </td>
                    
                    {/* 动态参数值 - 只显示共有参数 */}
                    {commonParameterNames.map((paramName, index) => (
                      <td 
                        key={paramName} 
                        className={`${rowBgClass} ${index < commonParameterNames.length - 1 ? 'border-r border-gray-200' : ''} px-3 py-3 whitespace-nowrap`} 
                        style={{ width: '150px' }}
                      >
                        <div className="text-sm text-gray-900 truncate" title={paramMap[paramName] || 'N/A'}>
                          {paramMap[paramName] || 'N/A'}
                        </div>
                      </td>
                    ))}
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="px-3 py-2 border-t border-gray-200 flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Showing <span className="font-medium">{((currentPage - 1) * 20) + 1}</span> to <span className="font-medium">{Math.min(currentPage * 20, totalResults)}</span> of <span className="font-medium">{totalResults}</span> results
        </div>
        <div>{renderPagination()}</div>
      </div>
    </div>
  )
} 