'use client'

import { useState, useEffect, useRef } from 'react'
import Image from 'next/image'
import Link from 'next/link'

// Brand item interface
interface Brand {
  brand_id: string
  brand_cn: string
  brand_en: string
  brand_ru: string
  domestic: string
  product_count: number
  distributor_count: number
  logo_url?: string
}

// Brands response interface
interface BrandsResponse {
  success: boolean
  date: string
  summary: {
    total_brands: number
    total_products: number
    domestic_stats: {
      domestic: {
        count: number
        products: number
      }
      imported: {
        count: number
        products: number
      }
      unknown: {
        count: number
        products: number
      }
    }
  }
  brands: Brand[]
}

// Brand detail interface
interface BrandDetail {
  id: string
  name_cn: string
  name_en: string
  name_ru: string
  website: string
  logo_url: string
  introduction_cn: string
  introduction_en: string
  introduction_ru: string
}

// Ad item interface
interface AdItem {
  id: string
  title: string
  image_url: string
  link_url: string
  alt_text: string
  priority?: number
  status?: string
  page_location?: string
}

interface PreferredManufactureProps {
  initialBrands?: Brand[]
  adLocations?: string[]
  adItems?: AdItem[]
}

export default function PreferredManufacture({ 
  initialBrands = [], 
  adLocations = [],
  adItems = []
}: PreferredManufactureProps) {
  // State management
  const [brands, setBrands] = useState<Brand[]>(initialBrands)
  const [isLoading, setIsLoading] = useState<boolean>(initialBrands.length === 0)
  const [error, setError] = useState<string | null>(null)
  const [showLeftArrow, setShowLeftArrow] = useState<boolean>(false)
  const [showRightArrow, setShowRightArrow] = useState<boolean>(true)
  
  // References
  const scrollRef = useRef<HTMLDivElement>(null)

  // Fetch brands data
  const fetchBrands = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Prepare request headers
      const headers: HeadersInit = {
        'Accept': 'application/json',
        'Origin': typeof window !== 'undefined' ? window.location.origin : 'https://example.com'
      }

      // Set up AbortController for timeout handling
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 8000) // 8-second timeout
      
      const response = await fetch('https://get-hot.chinaelectron.com/brands', { 
        headers,
        signal: controller.signal,
        credentials: 'omit',
        mode: 'cors',
        next: { revalidate: 3600 }
      })
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`Server response status: ${response.status} ${response.statusText}`)
      }
      
      const data = await response.json() as BrandsResponse
      
      if (data && data.success && Array.isArray(data.brands)) {
        // 恢复原有筛选条件，筛选distributor_count >= 10且domestic === "chinese"的品牌
        const filteredBrands = [...data.brands]
          .filter(brand => brand.distributor_count >= 10 && brand.domestic === "chinese")
          .sort((a, b) => b.distributor_count - a.distributor_count);
        
        // 添加日志查看筛选后的品牌数据
        // console.log('Filtered brands:', filteredBrands);
        
        // 确保每个品牌对象的logo_url属性有效
        const brandsWithValidLogos = filteredBrands.slice(0, 10).map(brand => {
          // 改进判断条件：更严谨地检测无效logo_url
          if (!brand.logo_url || 
              brand.logo_url === 'undefined' || 
              brand.logo_url === 'null' || 
              brand.logo_url.trim() === '') {
            // 增加详细日志，帮助调试
            // console.log(`Brand ${brand.brand_en || brand.brand_cn} has invalid logo_url:`, brand.logo_url);
            return {
              ...brand,
              logo_url: 'no_brand_logo.png'  // 设置为no_brand_logo.png，让渲染时使用默认图片
            };
          }
          // 为调试添加所有品牌的logo_url日志
          // console.log(`Brand ${brand.brand_en || brand.brand_cn} logo_url:`, brand.logo_url);
          return brand;
        });
        
        setBrands(brandsWithValidLogos);
      } else {
        throw new Error('Invalid data format from server')
      }
    } catch (error: any) {
      console.error('Error fetching brand data:', error)
      setError(error.message || 'Failed to load brand data')
    } finally {
      setIsLoading(false)
    }
  }

  // Initialization
  useEffect(() => {
    if (initialBrands.length === 0) {
      fetchBrands()
    } else {
      // 恢复原有筛选条件
      const filteredBrands = [...initialBrands]
        .filter(brand => brand.distributor_count >= 10 && brand.domestic === "chinese")
        .sort((a, b) => b.distributor_count - a.distributor_count);
      
      // 确保每个品牌对象的logo_url属性有效
      const brandsWithValidLogos = filteredBrands.slice(0, 10).map(brand => {
        // 改进判断条件：更严谨地检测无效logo_url
        if (!brand.logo_url || 
            brand.logo_url === 'undefined' || 
            brand.logo_url === 'null' || 
            brand.logo_url.trim() === '') {
          return {
            ...brand,
            logo_url: 'no_brand_logo.png'  // 设置为no_brand_logo.png，让渲染时使用默认图片
          };
        }
        return brand;
      });
      
      setBrands(brandsWithValidLogos);
      setIsLoading(false)
    }
  }, [initialBrands.length])

  // Monitor scroll position to toggle arrow visibility
  useEffect(() => {
    const handleScroll = () => {
      if (!scrollRef.current) return;
      
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10); // 10px tolerance
    };
    
    const scrollContainer = scrollRef.current;
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', handleScroll);
      // Initial check
      handleScroll();
    }
    
    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll);
      }
    };
  }, [brands.length]);

  // Handle arrow clicks
  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -300, behavior: 'smooth' });
    }
  };
  
  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 300, behavior: 'smooth' });
    }
  };

  // 查找对应位置的广告
  const getAdByLocation = (location: string) => {
    if (!location || !adItems || adItems.length === 0) return null;
    
    // 首先尝试精确匹配 page_location
    const adByPageLocation = adItems.find(ad => ad.page_location === location);
    if (adByPageLocation) return adByPageLocation;
    
    // 其次尝试匹配 id
    const adById = adItems.find(ad => ad.id.toString() === location);
    if (adById) return adById;
    
    // 如果没有精确匹配，返回第一个广告项
    return adItems.length > 0 ? adItems[0] : null;
  };

  // 获取左侧广告项
  const leftAd = getAdByLocation(adLocations[0]);
  // 获取右侧广告项
  const rightAd = getAdByLocation(adLocations[1]);

  return (
    <div className="component-card p-6 bg-white rounded-lg shadow-sm min-h-[500px]">
      {/* Ad space */}
      <div className="h-44 mb-8 overflow-hidden rounded-lg">
        <div className="flex space-x-4 h-full">
          <div className="w-1/2 relative bg-[#0051a3] rounded-lg overflow-hidden" data-location={adLocations[0] || ''}>
            {leftAd ? (
              <Link href={leftAd.link_url || '#'}>
                <Image 
                  src={leftAd.image_url} 
                  alt={leftAd.alt_text || leftAd.title} 
                  fill 
                  className="object-cover"
                />
              </Link>
            ) : (
              <Image 
                src="https://dummyimage.com/600x250/0051a3/ffffff.png&text=Ad+Space+1" 
                alt="Advertisement 1" 
                fill 
                className="object-cover"
              />
            )}
          </div>
          <div className="w-1/2 relative bg-[#0051a3] rounded-lg overflow-hidden" data-location={adLocations[1] || ''}>
            {rightAd ? (
              <Link href={rightAd.link_url || '#'}>
                <Image 
                  src={rightAd.image_url} 
                  alt={rightAd.alt_text || rightAd.title} 
                  fill 
                  className="object-cover"
                />
              </Link>
            ) : (
              <Image 
                src="https://dummyimage.com/600x250/0051a3/ffffff.png&text=Ad+Space+2" 
                alt="Advertisement 2" 
                fill 
                className="object-cover"
              />
            )}
          </div>
        </div>
      </div>
      
      {/* Brand list */}
      <div className="relative group">
        {/* Loading state */}
        {isLoading && (
          <div className="h-36 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          </div>
        )}
        
        {/* Error state */}
        {error && !isLoading && (
          <div className="h-36 flex items-center justify-center p-4 text-center">
            <p className="text-red-500">Unable to load manufacturer data</p>
          </div>
        )}
        
        {/* No data state */}
        {!error && !isLoading && brands.length === 0 && (
          <div className="h-36 flex items-center justify-center p-4 text-center">
            <p className="text-gray-500">No manufacturer data available</p>
          </div>
        )}
        
        {/* Data display header */}
        {!error && !isLoading && brands.length > 0 && (
          <div className="flex justify-end items-center mb-3">
            <Link 
              href="/manufacturers" 
              target="_blank"
              className="flex items-center px-4 py-1.5 border border-blue-500 rounded-md text-sm text-blue-600 hover:text-white hover:bg-blue-600 transition-all duration-300 group"
            >
              <span>More Manufacturers</span>
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-4 w-4 ml-1 group-hover:translate-x-0.5 transition-transform" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        )}
        
        {/* Left scroll arrow */}
        {showLeftArrow && (
          <button
            className="absolute left-1 top-1/2 transform -translate-y-1/2 z-10 bg-white w-10 h-10 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 active:shadow-md transition-all border border-gray-200 opacity-90 hover:opacity-100"
            onClick={scrollLeft}
            aria-label="Scroll left"
            style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
        )}
        
        {/* Right scroll arrow */}
        {showRightArrow && (
          <button
            className="absolute right-1 top-1/2 transform -translate-y-1/2 z-10 bg-white w-10 h-10 rounded-full shadow-lg flex items-center justify-center hover:bg-gray-50 active:shadow-md transition-all border border-gray-200 opacity-90 hover:opacity-100"
            onClick={scrollRight}
            aria-label="Scroll right"
            style={{ boxShadow: '0 4px 12px rgba(0,0,0,0.15)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        )}
        
        {/* Brands grid */}
        {!error && !isLoading && brands.length > 0 && (
          <div 
            ref={scrollRef} 
            className="flex overflow-x-auto no-scrollbar py-3 px-10 -mx-2 scroll-smooth"
          >
            <div className="grid grid-flow-col auto-cols-max gap-5">
              {brands.map((brand) => (
                <div 
                  key={brand.brand_id}
                  className="w-44 p-4 bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-300"
                >
                  <div className="h-16 mb-4 flex items-center justify-center">
                    <Image 
                      src={brand.logo_url ? 
                        `https://brandimg.chinaelectron.com/${encodeURIComponent(brand.logo_url)}` : 
                        "https://brandimg.chinaelectron.com/no_brand_logo.png"}
                      alt={brand.brand_en || brand.brand_cn} 
                      width={150}
                      height={50}
                      className="object-contain max-h-14 max-w-full"
                      priority={false}
                      unoptimized={true}
                      onError={(e) => {
                        // 图片加载失败时，将src替换为默认图片
                        const target = e.target as HTMLImageElement;
                        if (target && !target.src.includes('no_brand_logo.png')) {
                          // console.log(`Image load failed for: ${target.src}, using fallback`);
                          target.src = "https://brandimg.chinaelectron.com/no_brand_logo.png";
                        }
                      }}
                    />
                  </div>
                  <div className="text-center">
                    <h3 className="text-sm font-medium text-gray-900 truncate mb-2">{brand.brand_en || brand.brand_cn}</h3>
                    <div className="flex items-center justify-center text-xs text-gray-500 mb-3">
                      <span className="inline-block">{brand.distributor_count} Distributors for RFQ</span>
                    </div>
                    <Link 
                      href={`/manufacturers/${brand.brand_id}`}
                      target="_blank" 
                      className="block w-full text-center px-4 py-2 rounded text-xs bg-[#0062cc] text-white hover:bg-[#0051a8] transition-colors"
                    >
                      Detail
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}