'use client'

import { useState, useEffect, Suspense } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import LoadingSpinner from '@/components/common/LoadingSpinner'

// 动态导入 SimplePDFViewer 以减少编译负担
const SimplePDFViewer = dynamic(() => import('@/components/SimplePDFViewer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-96">
      <LoadingSpinner />
    </div>
  )
})

function PDFViewerContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [pdfUrl, setPdfUrl] = useState<string>('')
  const [productName, setProductName] = useState<string>('Document')
  const [productId, setProductId] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    try {
      // 从URL参数获取PDF URL、产品名称和产品ID
      const url = searchParams.get('url')
      const name = searchParams.get('name') || 'Document'
      const id = searchParams.get('product_id') || ''

      if (!url) {
        setError('No PDF URL provided')
        setLoading(false)
        return
      }

      // 验证URL格式
      try {
        new URL(url)
      } catch {
        setError('Invalid PDF URL format')
        setLoading(false)
        return
      }

      setPdfUrl(decodeURIComponent(url))
      setProductName(decodeURIComponent(name))
      setProductId(id)
      setLoading(false)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load PDF')
      setLoading(false)
    }
  }, [searchParams])

  const handleGoBack = () => {
    const returnUrl = searchParams.get('return')
    if (returnUrl) {
      router.push(decodeURIComponent(returnUrl))
    } else {
      router.back()
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20 flex justify-center items-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-20">
        <div className="max-w-screen-2xl mx-auto px-6 py-8">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading PDF</h2>
              <p className="text-gray-600">{error}</p>
            </div>
            <button
              onClick={handleGoBack}
              className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Go Back
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-20">
      <div className="max-w-screen-2xl mx-auto px-6 py-8">
        {/* 页面头部 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6 p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {productName} - PDF Viewer
              </h1>
              <p className="text-gray-600 text-sm break-all">
                {pdfUrl}
              </p>
            </div>
            <button
              onClick={handleGoBack}
              className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Go Back
            </button>
          </div>
        </div>

        {/* PDF查看器 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <SimplePDFViewer
            url={pdfUrl}
            productName={productName}
            productId={productId}
          />
        </div>
      </div>
    </div>
  )
}

export default function PDFViewerPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 pt-20 flex justify-center items-center">
        <LoadingSpinner />
      </div>
    }>
      <PDFViewerContent />
    </Suspense>
  )
}
