'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import {
  getCartItems,
  updateCartItemQuantity,
  removeFromCart,
  clearCart,
  CartItem
} from '@/services/api'
import StockWarningModal from '@/components/Cart/StockWarningModal'
// 使用内联SVG图标替代lucide-react
const ShoppingCartIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9M6 19a1 1 0 100 2 1 1 0 000-2zm10 0a1 1 0 100 2 1 1 0 000-2z" />
  </svg>
)

const PlusIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v14m7-7H5" />
  </svg>
)

const MinusIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
  </svg>
)

const Trash2Icon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 6h18m-2 0v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2m-6 4v6m4-6v6" />
  </svg>
)

const ArrowRightIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14m-7-7l7 7-7 7" />
  </svg>
)

const DownloadIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m7-10v12m-4-4l4 4 4-4" />
  </svg>
)

const RefreshCwIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M1 4v6h6m16 10v-6h-6M7.5 3.5A9 9 0 0121 12a9 9 0 01-2.5 6.5M16.5 20.5A9 9 0 003 12a9 9 0 012.5-6.5" />
  </svg>
)
import Image from 'next/image'
import Link from 'next/link'
import { getProductImageUrl } from '@/utils/imageUtils'
import LoadingSpinner from '@/components/common/LoadingSpinner'

export default function CartPage() {
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [cartItems, setCartItems] = useState<CartItem[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<number | null>(null)
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set())
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning' | null; text: string }>({
    type: null,
    text: ''
  })
  const [showStockWarning, setShowStockWarning] = useState(false)
  const [stockWarningData, setStockWarningData] = useState<{
    insufficientStockItems: CartItem[]
    validItems: CartItem[]
  }>({ insufficientStockItems: [], validItems: [] })

  // 获取购物车数据
  const fetchCartItems = async () => {
    if (!userId) return
    
    try {
      setLoading(true)
      const items = await getCartItems(userId)
      setCartItems(items)
    } catch (error) {
      console.error('Error fetching cart items:', error)
      setMessage({ type: 'error', text: 'Failed to load cart items' })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!authLoading && isAuthenticated && userId) {
      fetchCartItems()
    } else if (!authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [authLoading, isAuthenticated, userId, router])

  // 自动清除消息
  useEffect(() => {
    if (message.text) {
      const timer = setTimeout(() => {
        setMessage({ type: null, text: '' })
      }, 5000) // 5秒后自动清除消息

      return () => clearTimeout(timer)
    }
  }, [message.text])

  // 更新商品数量
  const handleUpdateQuantity = async (cartId: number, newQuantity: number) => {
    if (newQuantity < 1) return
    
    const item = cartItems.find(item => item.cart_id === cartId)
    if (!item) return
    
    if (newQuantity > (item.available_stock || item.stock_quantity)) {
      setMessage({ 
        type: 'error', 
        text: `Maximum available quantity is ${item.available_stock || item.stock_quantity}` 
      })
      return
    }

    try {
      setUpdating(cartId)
      const result = await updateCartItemQuantity(cartId, newQuantity)
      
      if (result.success) {
        setCartItems(items => 
          items.map(item => 
            item.cart_id === cartId 
              ? { ...item, cart_quantity: newQuantity }
              : item
          )
        )
        setMessage({ type: 'success', text: 'Quantity updated successfully' })
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to update quantity' })
    } finally {
      setUpdating(null)
    }
  }

  // 删除商品
  const handleRemoveItem = async (cartId: number) => {
    try {
      setUpdating(cartId)
      const result = await removeFromCart(cartId)
      
      if (result.success) {
        setCartItems(items => items.filter(item => item.cart_id !== cartId))
        setMessage({ type: 'success', text: 'Item removed from cart' })
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to remove item' })
    } finally {
      setUpdating(null)
    }
  }

  // 清空购物车
  const handleClearCart = async () => {
    if (!userId || cartItems.length === 0) return
    
    if (!confirm('Are you sure you want to clear all items from your cart?')) return

    try {
      setLoading(true)
      const result = await clearCart(userId)
      
      if (result.success) {
        setCartItems([])
        setMessage({ type: 'success', text: 'Cart cleared successfully' })
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to clear cart' })
    } finally {
      setLoading(false)
    }
  }

  // 处理单个商品选择
  const handleSelectItem = (cartId: number) => {
    const newSelected = new Set(selectedItems)
    if (newSelected.has(cartId)) {
      newSelected.delete(cartId)
    } else {
      newSelected.add(cartId)
    }
    setSelectedItems(newSelected)
  }

  // 处理全选/取消全选
  const handleSelectAll = () => {
    if (selectedItems.size === cartItems.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(cartItems.map(item => item.cart_id)))
    }
  }

  // 检查库存并处理结账
  const handleSecureCheckout = () => {
    if (selectedItems.size === 0) {
      setMessage({ type: 'warning', text: 'Please select items to checkout' })
      return
    }

    const selectedCartItems = cartItems.filter(item => selectedItems.has(item.cart_id))
    const insufficientStockItems: CartItem[] = []
    const validItems: CartItem[] = []

    selectedCartItems.forEach(item => {
      const availableStock = item.available_stock || item.stock_quantity
      if (item.cart_quantity > availableStock) {
        insufficientStockItems.push(item)
      } else {
        validItems.push(item)
      }
    })

    if (insufficientStockItems.length > 0) {
      if (validItems.length === 0) {
        setMessage({
          type: 'error',
          text: 'All selected items have insufficient stock. Please adjust quantities or refresh cart.'
        })
        return
      }

      // 显示自定义库存警告Modal
      setStockWarningData({ insufficientStockItems, validItems })
      setShowStockWarning(true)
    } else {
      // 所有商品库存充足，直接结账
      proceedToCheckout(validItems)
    }
  }

  // 处理库存警告Modal的确认
  const handleStockWarningConfirm = () => {
    setShowStockWarning(false)
    proceedToCheckout(stockWarningData.validItems)
  }

  // 处理库存警告Modal的关闭
  const handleStockWarningClose = () => {
    setShowStockWarning(false)
    setStockWarningData({ insufficientStockItems: [], validItems: [] })
  }

  // 执行结账流程
  const proceedToCheckout = (items: CartItem[]) => {
    console.log('Proceeding to checkout with items:', items)
    console.log('Items count:', items.length)
    items.forEach(item => {
      const availableStock = item.available_stock || item.stock_quantity
      console.log(`Item ${item.model_number}: cart_quantity=${item.cart_quantity}, available_stock=${availableStock}`)
    })

    // 将选中的商品信息存储到sessionStorage，供checkout页面使用
    sessionStorage.setItem('checkoutItems', JSON.stringify(items))
    router.push('/checkout')
  }

  // 计算总价（只计算选中的商品）
  const calculateTotals = () => {
    const selectedCartItems = cartItems.filter(item => selectedItems.has(item.cart_id))
    const merchandiseTotal = selectedCartItems.reduce((sum, item) =>
      sum + (item.unit_price * item.cart_quantity), 0
    )
    const totalQuantity = selectedCartItems.reduce((sum, item) => sum + item.cart_quantity, 0)
    const allItemsCount = cartItems.length // 购物车中商品种类数量

    return {
      merchandiseTotal,
      totalQuantity,
      allItemsCount,
      selectedCount: selectedItems.size
    }
  }

  const { merchandiseTotal, totalQuantity, allItemsCount, selectedCount } = calculateTotals()

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // 会被重定向到登录页
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <ShoppingCartIcon className="w-8 h-8 text-[#DE2910]" />
            <h1 className="text-3xl font-bold text-gray-900">Cart</h1>
            {cartItems.length > 0 && (
              <span className="bg-[#DE2910] text-white px-3 py-1 rounded-full text-sm font-medium">
                {allItemsCount} items
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-4">
            <button
              onClick={fetchCartItems}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
            >
              <RefreshCwIcon className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </button>

            {cartItems.length > 0 && (
              <button
                onClick={handleClearCart}
                className="flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-800 transition-colors"
              >
                <Trash2Icon className="w-4 h-4" />
                Clear Cart
              </button>
            )}
          </div>
        </div>

        {/* 消息显示 */}
        {message.text && (
          <div className={`p-4 rounded-lg mb-6 ${
            message.type === 'success' ? 'bg-green-100 text-green-700' :
            message.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
            'bg-red-100 text-red-700'
          }`}>
            {message.text}
          </div>
        )}

        {cartItems.length === 0 ? (
          // 空购物车状态
          <div className="text-center py-16">
            <ShoppingCartIcon className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-semibold text-gray-600 mb-4">Your cart is empty</h2>
            <p className="text-gray-500 mb-8">Start shopping to add items to your cart</p>
            <Link
              href="/search"
              className="inline-flex items-center gap-2 bg-[#DE2910] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#DE2910]/90 transition-colors"
            >
              Start Shopping
              <ArrowRightIcon className="w-4 h-4" />
            </Link>
          </div>
        ) : (
          // 购物车内容
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* 商品列表 */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
                {/* 表头 */}
                <div className="grid grid-cols-12 gap-4 p-6 border-b border-gray-100 text-sm font-medium text-gray-600">
                  <div className="col-span-1 flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedItems.size === cartItems.length && cartItems.length > 0}
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-[#DE2910] focus:ring-[#DE2910]"
                    />
                    <span className="ml-2">Select All</span>
                  </div>
                  <div className="col-span-5">Product Details</div>
                  <div className="col-span-2 text-center">Quantity</div>
                  <div className="col-span-2 text-center">Availability</div>
                  <div className="col-span-1 text-center">Unit Price</div>
                  <div className="col-span-1 text-center">Extended Price</div>
                </div>

                {/* 商品列表 */}
                <div className="divide-y divide-gray-100">
                  {cartItems.map((item) => (
                    <div key={item.cart_id} className="grid grid-cols-12 gap-4 p-6 items-center">
                      {/* 选择框 */}
                      <div className="col-span-1 flex items-center">
                        <input
                          type="checkbox"
                          checked={selectedItems.has(item.cart_id)}
                          onChange={() => handleSelectItem(item.cart_id)}
                          className="rounded border-gray-300 text-[#DE2910] focus:ring-[#DE2910]"
                        />
                      </div>
                      {/* 产品信息 */}
                      <div className="col-span-5 flex items-start gap-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                          <Image
                            src={getProductImageUrl([])}
                            alt={item.model_number}
                            width={64}
                            height={64}
                            className="object-contain w-full h-full"
                            unoptimized
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-gray-900 truncate">{item.model_number}</h3>
                          <p className="text-sm text-gray-600 truncate">{item.brand}</p>
                          <p className="text-sm text-gray-500 truncate">{item.description}</p>
                          <button
                            onClick={() => handleRemoveItem(item.cart_id)}
                            disabled={updating === item.cart_id}
                            className="text-red-600 hover:text-red-800 text-sm mt-2 flex items-center gap-1"
                          >
                            <Trash2Icon className="w-3 h-3" />
                            Remove
                          </button>
                        </div>
                      </div>

                      {/* 数量控制 */}
                      <div className="col-span-2 flex items-center justify-center gap-2">
                        <button
                          onClick={() => handleUpdateQuantity(item.cart_id, item.cart_quantity - 1)}
                          disabled={item.cart_quantity <= 1 || updating === item.cart_id}
                          className="w-8 h-8 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50"
                        >
                          <MinusIcon className="w-4 h-4" />
                        </button>
                        <input
                          type="number"
                          value={item.cart_quantity}
                          onChange={(e) => handleUpdateQuantity(item.cart_id, parseInt(e.target.value) || 1)}
                          min="1"
                          max={item.available_stock || item.stock_quantity}
                          disabled={updating === item.cart_id}
                          className="w-16 px-2 py-1 border border-gray-300 rounded text-center text-sm"
                        />
                        <button
                          onClick={() => handleUpdateQuantity(item.cart_id, item.cart_quantity + 1)}
                          disabled={item.cart_quantity >= (item.available_stock || item.stock_quantity) || updating === item.cart_id}
                          className="w-8 h-8 rounded-md border border-gray-300 flex items-center justify-center hover:bg-gray-50 disabled:opacity-50"
                        >
                          <PlusIcon className="w-4 h-4" />
                        </button>
                      </div>

                      {/* 库存状态 */}
                      <div className="col-span-2 text-center">
                        <span className={`text-sm font-medium ${
                          (item.available_stock || item.stock_quantity) > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {(item.available_stock || item.stock_quantity) > 0 
                            ? `${(item.available_stock || item.stock_quantity).toLocaleString()} pcs` 
                            : 'Out of Stock'
                          }
                        </span>
                      </div>

                      {/* 单价 */}
                      <div className="col-span-1 text-center">
                        <span className="text-sm font-medium">${item.unit_price.toFixed(4)}</span>
                      </div>

                      {/* 总价 */}
                      <div className="col-span-1 text-center">
                        <span className="text-sm font-semibold text-[#DE2910]">
                          ${(item.unit_price * item.cart_quantity).toFixed(2)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 订单摘要 */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Summary</h2>
                
                <div className="space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Selected Items</span>
                    <span className="font-semibold">{selectedCount} of {allItemsCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Selected Quantity</span>
                    <span className="font-semibold">{totalQuantity} pcs</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Merchandise Total</span>
                    <span className="font-semibold">${merchandiseTotal.toFixed(2)}</span>
                  </div>
                  <hr className="border-gray-200" />
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Total</span>
                    <span className="text-[#DE2910]">${merchandiseTotal.toFixed(2)}</span>
                  </div>
                </div>

                <button
                  onClick={handleSecureCheckout}
                  disabled={selectedItems.size === 0}
                  className="w-full bg-[#DE2910] text-white py-3 px-4 rounded-lg font-medium hover:bg-[#DE2910]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
                >
                  Secure Checkout ({selectedCount})
                  <ArrowRightIcon className="w-4 h-4" />
                </button>

                {/* <div className="mt-4 text-center">
                  <button className="text-[#DE2910] hover:text-[#DE2910]/80 text-sm font-medium flex items-center gap-1 mx-auto">
                    <DownloadIcon className="w-4 h-4" />
                    Download
                  </button>
                </div> */}

                <div className="mt-6 text-center">
                  <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
                    <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 8 8">
                        <path d="M6.564.75l-3.59 3.612-1.538-1.55L0 4.26l2.974 2.99L8 2.193z"/>
                      </svg>
                    </div>
                    All personal data is securely stored and transmitted using encryption
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 库存不足警告Modal */}
      <StockWarningModal
        isOpen={showStockWarning}
        onClose={handleStockWarningClose}
        onConfirm={handleStockWarningConfirm}
        insufficientStockItems={stockWarningData.insufficientStockItems}
        validItemsCount={stockWarningData.validItems.length}
      />
    </div>
  )
}
