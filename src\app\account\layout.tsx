import type { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import ClientLayout from './ClientLayout'

export const metadata: Metadata = {
  title: 'My Account | China Electron',
  description: 'Manage your China Electron account, view order history, update profile settings, and track requests for electronic component sourcing.',
  keywords: 'account dashboard, electronic component account, user profile, China Electron account, B2B account management',
  alternates: {
    canonical: getCanonicalUrl('account'),
  },
}

export default function AccountLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return <ClientLayout>{children}</ClientLayout>
} 