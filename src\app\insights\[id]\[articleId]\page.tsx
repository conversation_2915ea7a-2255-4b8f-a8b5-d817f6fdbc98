/**
 * Insights Article Detail Page
 * 
 * Displays a single article with its content within the insights context
 * This page is server-side rendered for better SEO performance
 */

import React from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { Metadata } from 'next'
import { getCanonicalUrl } from '@/utils/canonicalUrl'
import { unified } from 'unified'
import remarkParse from 'remark-parse'
import remarkGfm from 'remark-gfm'
import remarkRehype from 'remark-rehype'
import rehypeRaw from 'rehype-raw'
import rehypeStringify from 'rehype-stringify'

// 从URL中提取专题ID
function extractTopicIdFromUrl(urlPath: string): number {
  try {
    // 尝试从"专题名-ID"格式中提取
    const topicParts = urlPath.split('-');
    
    // 如果最后一部分是数字，则使用它作为ID
    if (topicParts.length > 0) {
      const lastPart = topicParts[topicParts.length - 1];
      if (/^\d+$/.test(lastPart)) {
        return parseInt(lastPart, 10);
      }
    }
    
    // 如果不是新格式，尝试直接解析整个路径作为ID
    const directId = parseInt(urlPath, 10);
    if (!isNaN(directId)) {
      return directId;
    }
    
    // 如果无法解析，返回默认值或抛出错误
    throw new Error(`无法从URL路径提取专题ID: ${urlPath}`);
  } catch (error) {
    console.error('解析专题ID时出错:', error);
    // 返回一个不存在的ID，这样页面会显示404
    return -1;
  }
}

// 从URL中提取文章ID
function extractArticleIdFromUrl(urlPath: string): number {
  try {
    // 尝试从"文章标题-ID"格式中提取
    const articleParts = urlPath.split('-');
    
    // 如果最后一部分是数字，则使用它作为ID
    if (articleParts.length > 0) {
      const lastPart = articleParts[articleParts.length - 1];
      if (/^\d+$/.test(lastPart)) {
        return parseInt(lastPart, 10);
      }
    }
    
    // 如果不是新格式，尝试直接解析整个路径作为ID
    const directId = parseInt(urlPath, 10);
    if (!isNaN(directId)) {
      return directId;
    }
    
    // 如果无法解析，返回默认值或抛出错误
    throw new Error(`无法从URL路径提取文章ID: ${urlPath}`);
  } catch (error) {
    console.error('解析文章ID时出错:', error);
    // 返回一个不存在的ID，这样页面会显示404
    return -1;
  }
}

// 将标题转换为URL友好的格式
function slugifyTitle(title: string): string {
  // 将所有符号和空格替换为连字符，并将连续的连字符替换为单个连字符
  return title
    .replace(/[^\w\s]/g, '-') // 将所有非字母数字字符替换为连字符
    .replace(/\s+/g, '-')     // 将空格替换为连字符
    .replace(/-+/g, '-')      // 将连续的连字符替换为单个连字符
    .replace(/^-|-$/g, '')    // 移除开头和结尾的连字符
    .toLowerCase();           // 转为小写
}

// 定义专题类型
interface Topic {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle: string;
  description: string;
  cover_image: string;
  tags: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
}

// 定义文章类型
interface BlogArticle {
  id: number;
  type: number;
  status: number;
  title: string;
  subtitle?: string;
  description: string;
  cover_image: string;
  content_markdown: string;
  tags: string;
  category: string;
  location?: string;
  created_at: string;
  updated_at: string;
  author_id: string;
  author?: {
    author_id: string;
    name: string;
    avatar: string;
    description: string;
    bio: string;
  };
  insights?: Array<{
    id: number;
    title: string;
    description: string;
  }>;
}

// 从API获取专题详情
async function getTopicById(id: string): Promise<Topic | null> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/insights/${id}`, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch insight: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error fetching topic with ID ${id}:`, error);
    return null;
  }
}

// 从API获取专题下的所有文章
async function getArticlesByTopicId(topicId: string): Promise<BlogArticle[]> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/insights/${topicId}/blogs`, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch articles: ${response.status}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error(`Error fetching articles for topic ${topicId}:`, error);
    return [];
  }
}

// 从API获取单个文章详情
async function getBlogArticle(id: string): Promise<BlogArticle | null> {
  try {
    const response = await fetch(`https://blog-manage.chinaelectron.com/api/blogs/${id}`, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch blog article: ${response.status}`);
    }

    // 单个文章API直接返回文章对象，不是包装在data字段中
    return await response.json();
  } catch (error) {
    console.error(`Error fetching blog article with ID ${id}:`, error);
    return null;
  }
}

// 从Markdown URL获取文章内容
async function getMarkdownContent(markdownUrl: string): Promise<string | null> {
  try {
    // 判断是否存在有效的markdown链接
    if (!markdownUrl || (!markdownUrl.startsWith('/') && !markdownUrl.startsWith('http'))) {
      console.log('Invalid markdown URL:', markdownUrl);
      return null;
    }
    
    // 构建完整的API URL
    let fullUrl;
    if (markdownUrl.startsWith('http')) {
      fullUrl = markdownUrl;
    } else {
      fullUrl = `https://blog-manage.chinaelectron.com/api${markdownUrl.startsWith('/') ? '' : '/'}${markdownUrl}`;
    }
    
    console.log('Fetching markdown from:', fullUrl);
    
    const response = await fetch(fullUrl, {
      next: { revalidate: 3600 } // 缓存1小时
    });

    if (!response.ok) {
      console.log('Failed to fetch markdown, status:', response.status);
      return null;
    }

    const markdownContent = await response.text();
    
    if (!markdownContent || markdownContent.trim() === '') {
      return null;
    }
    
    // 预处理图片链接
    let processedMarkdown = markdownContent;
    
    // 处理图片链接 ![alt](./images/x.jpg) 或 ![alt](images/x.jpg) 格式
    processedMarkdown = processedMarkdown.replace(
      /!\[(.*?)\]\(\.(\/images\/.*?)\)/g, 
      `![$1](https://blog-manage.chinaelectron.com/api$2)`
    ).replace(
      /!\[(.*?)\]\((images\/.*?)\)/g, 
      `![$1](https://blog-manage.chinaelectron.com/api/images/$1)`
    ).replace(
      /!\[(.*?)\]\(\.\/(.*?)\)/g,
      `![$1](https://blog-manage.chinaelectron.com/api/$2)`
    ).replace(
      /!\[(.*?)\]\(\.\.\/images\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
    ).replace(
      /!\[(.*?)\]\(\.\.\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/$1"'
    );
    
    // 使用unified处理流程，解析Markdown为HTML
    const processedContent = await unified()
      .use(remarkParse) // 解析Markdown
      .use(remarkGfm) // 支持GitHub风格的Markdown（表格、任务列表等）
      .use(remarkRehype, { allowDangerousHtml: true }) // 转换为rehype（HTML）格式，允许原始HTML
      .use(rehypeRaw) // 处理Markdown中的原始HTML
      .use(rehypeStringify) // 将rehype（HTML）格式转换为字符串
      .process(processedMarkdown);
    
    const result = String(processedContent);
    
    // 添加全局样式
    const globalStyles = `
      <style>
        .markdown-content {
          line-height: 1.8;
          font-size: 1.1rem;
          color: #333;
        }
        .markdown-content h1 {
          font-size: 2.5rem;
          font-weight: 700;
          margin-top: 2rem;
          margin-bottom: 1.5rem;
          color: #111;
          border-bottom: 1px solid #eaeaea;
          padding-bottom: 0.5rem;
        }
        .markdown-content h2 {
          font-size: 2rem;
          font-weight: 700;
          margin-top: 2rem;
          margin-bottom: 1rem;
          color: #222;
        }
        .markdown-content h3 {
          font-size: 1.5rem;
          font-weight: 600;
          margin-top: 1.5rem;
          margin-bottom: 1rem;
          color: #333;
        }
        .markdown-content h4 {
          font-size: 1.25rem;
          font-weight: 600;
          margin-top: 1.5rem;
          margin-bottom: 0.75rem;
        }
        .markdown-content p {
          margin-bottom: 1.5rem;
          line-height: 1.8;
        }
        .markdown-content img {
          max-width: 100%;
          height: auto;
          margin: 2rem auto;
          display: block;
          border-radius: 4px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .markdown-content ul, .markdown-content ol {
          margin-bottom: 1.5rem;
          padding-left: 2rem;
        }
        .markdown-content li {
          margin-bottom: 0.5rem;
        }
        .markdown-content blockquote {
          border-left: 4px solid #3b82f6;
          padding-left: 1rem;
          margin-left: 0;
          margin-right: 0;
          font-style: italic;
          color: #4b5563;
          margin-bottom: 1.5rem;
        }
        .markdown-content pre {
          background-color: #f3f4f6;
          padding: 1rem;
          border-radius: 4px;
          overflow-x: auto;
          margin-bottom: 1.5rem;
        }
        .markdown-content code {
          background-color: #f3f4f6;
          padding: 0.2rem 0.4rem;
          border-radius: 3px;
          font-size: 0.9rem;
        }
        .markdown-content hr {
          border: 0;
          height: 1px;
          background-color: #e5e7eb;
          margin: 2rem 0;
        }
        .markdown-content table {
          width: 100%;
          border-collapse: collapse;
          margin: 2rem 0;
          font-size: 0.9rem;
        }
        .markdown-content th {
          background-color: #f9fafb;
          padding: 0.75rem 1rem;
          text-align: left;
          font-weight: 600;
          border: 1px solid #e5e7eb;
        }
        .markdown-content td {
          padding: 0.75rem 1rem;
          border: 1px solid #e5e7eb;
        }
        .markdown-content tr:nth-child(even) {
          background-color: #f9fafb;
        }
      </style>
    `;
    
    // 处理图片链接，确保正确加载
    let styledResult = result.replace(
      /<img src="\.\/images\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
    ).replace(
      /<img src="images\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
    ).replace(
      /<img src="\.\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/$1"'
    ).replace(
      /<img src="\.\.\/images\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/images/$1"'
    ).replace(
      /<img src="\.\.\/(.*?)"/g,
      '<img src="https://blog-manage.chinaelectron.com/api/$1"'
    );
    
    // 包装内容到带样式的div中
    styledResult = `${globalStyles}<div class="markdown-content">${styledResult}</div>`;
    
    return styledResult;
  } catch (error) {
    console.error(`Error fetching markdown content:`, error);
    return null;
  }
}

// 生成页面元数据
export async function generateMetadata(
  props: { params: Promise<{ id: string; articleId: string }> }
): Promise<Metadata> {
  // 解析异步 params
  const params = await props.params;
  
  // 从URL中提取ID
  const topicId = extractTopicIdFromUrl(params.id);
  const articleId = extractArticleIdFromUrl(params.articleId);
  
  // 获取文章详情
  const article = await getBlogArticle(articleId.toString());
  
  if (!article) {
    return {
      title: 'Article Not Found'
    }
  }
  
  // 获取作者名称
  const authorName = article.author?.name || '';
  
  // 解析标签
  let tags: string[] = [];
  try {
    if (article.tags) {
      tags = article.tags.split(',').map(tag => tag.trim());
    }
  } catch (e) {
    console.error('Error parsing tags:', e);
  }
  
  // 获取关键词
  const keywords = tags.length > 0 
    ? `${tags.join(', ')}, ${article.category || 'industry insights'}, China Electron`
    : `${article.category || 'industry insights'}, electronics industry, China Electron`;
  
  return {
    title: `${article.title} | China Electron Insights`,
    description: article.subtitle || article.description || `Read ${article.title} on China Electron Insights.`,
    keywords: keywords,
    openGraph: {
      title: article.title,
      description: article.subtitle || article.description,
      images: article.cover_image ? [article.cover_image] : [],
      type: 'article',
      authors: authorName ? [authorName] : [],
      publishedTime: article.created_at
    },
    alternates: {
      canonical: getCanonicalUrl(`insights/${params.id}/${params.articleId}`),
    },
  }
}

export default async function ArticleDetailPage(
  props: { params: Promise<{ id: string; articleId: string }> }
) {
  // 解析异步 params
  const params = await props.params;
  
  // 从URL中提取ID
  const topicId = extractTopicIdFromUrl(params.id);
  const articleId = extractArticleIdFromUrl(params.articleId);
  
  console.log('Route params:', params);
  console.log('Extracted topic ID:', topicId);
  console.log('Extracted article ID:', articleId);
  
  // 获取专题详情
  const topic = await getTopicById(topicId.toString());
  console.log('Topic found:', !!topic);
  
  // 如果专题不存在，显示404页面
  if (!topic) {
    console.log('Topic not found, returning 404');
    notFound();
  }
  
  // 获取文章详情
  const article = await getBlogArticle(articleId.toString());
  console.log('Article found:', !!article);
  
  // 如果文章不存在，显示404页面
  if (!article) {
    console.log('Article not found, returning 404');
    notFound();
  }
  
  // 获取文章内容（如果获取失败则使用null）
  let markdownContent = null;
  if (article.content_markdown) {
    markdownContent = await getMarkdownContent(article.content_markdown);
  }
  
  // 获取专题的所有文章（用于目录）
  const allTopicArticles = await getArticlesByTopicId(topicId.toString());

  return (
    <div className="min-h-screen bg-white">
      {/* 文章头部 */}
      <div className="relative h-[300px] overflow-hidden bg-gray-800">
        {/* 背景图像 */}
        <div className="absolute inset-0">
          <Image 
            src={article.cover_image || `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1920' height='600' viewBox='0 0 1920 600'%3E%3Crect width='1920' height='600' fill='%23333333'/%3E%3C/svg%3E`}
            alt={article.title}
            width={1920}
            height={600}
            className="w-full h-full object-cover"
          />
          {/* 渐变叠加 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/30"></div>
        </div>
        
        {/* 文章标题和元数据 */}
        <div className="absolute inset-0 flex flex-col justify-end px-14 pb-12">
          <div className="max-w-5xl">
            {/* 类别 */}
            <div className="inline-block bg-blue-600 text-white px-3 py-1 text-sm font-medium rounded mb-4">
              {topic.title}
            </div>
            
            {/* 标题 */}
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {article.title}
            </h1>
            
            {/* 作者和日期 */}
            {article.author && (
            <div className="flex items-center">
              {/* 作者头像 */}
              <div className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                  {article.author.avatar ? (
                    <Image 
                      src={article.author.avatar}
                      alt={article.author.name}
                      width={40}
                      height={40}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                <div className="w-full h-full flex items-center justify-center text-sm text-gray-500">
                  {article.author.name.charAt(0)}
                    </div>
                  )}
              </div>
              <div className="ml-3">
                <div className="text-white font-medium">{article.author.name}</div>
                  <div className="text-gray-300 text-sm">{new Date(article.created_at).toLocaleDateString()}</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="mx-auto px-14 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 左侧：文章内容 */}
          <div className="lg:w-2/3">
            {/* 文章标签 */}
            {article.tags && (
            <div className="mb-8 flex flex-wrap gap-2">
                {article.tags.split(',').map((tag, index) => (
                <span key={index} className="inline-block bg-gray-100 text-gray-800 px-3 py-1.5 rounded-full text-sm">
                    {tag.trim()}
                </span>
              ))}
            </div>
            )}
            
            {/* 文章内容 */}
            <div className="prose prose-lg max-w-none mb-12">
              {markdownContent ? (
                <div dangerouslySetInnerHTML={{ __html: markdownContent }}></div>
              ) : (
                <div>
                  <p className="text-gray-700 text-lg mb-6">{article.description}</p>
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                    <p className="text-blue-700">This is a preview of the article. The full content is not available at this moment.</p>
                  </div>
                </div>
              )}
            </div>
            
            {/* 作者简介 */}
            {article.author && (
            <div className="border-t border-b border-gray-200 py-6 my-12">
              <div className="flex items-start gap-4">
                {/* 作者头像 */}
                <div className="w-16 h-16 rounded-full bg-gray-300 overflow-hidden flex-shrink-0">
                    {article.author.avatar ? (
                      <Image 
                        src={article.author.avatar}
                        alt={article.author.name}
                        width={64}
                        height={64}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                  <div className="w-full h-full flex items-center justify-center text-lg text-gray-500">
                    {article.author.name.charAt(0)}
                      </div>
                    )}
                </div>
                <div>
                  <div className="text-xl font-bold mb-2">{article.author.name}</div>
                    <p className="text-gray-600">{article.author.bio || article.author.description}</p>
                      </div>
                </div>
              </div>
            )}
          </div>
          
          {/* 右侧：专题文章目录 */}
          <div className="lg:w-1/3">
            <div className="bg-gray-50 rounded-lg border border-gray-200 p-6 sticky top-4">
              <h3 className="text-lg font-bold mb-4 text-gray-900">{topic.title}</h3>
              
              <div className="space-y-3">
                {allTopicArticles.length > 0 ? (
                  allTopicArticles.map((topicArticle, index) => (
                  <div key={topicArticle.id} className="flex items-center">
                    <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${
                        topicArticle.id.toString() === articleId.toString() 
                        ? 'bg-blue-600 text-white' 
                        : 'bg-blue-100 text-blue-600'
                    }`}>
                      {index + 1}
                    </span>
                    <Link 
                      href={`/insights/${params.id}/${slugifyTitle(topicArticle.title)}-${topicArticle.id}`}
                      className={`hover:text-blue-600 transition-colors line-clamp-1 text-sm ${
                          topicArticle.id.toString() === articleId.toString()
                          ? 'text-blue-600 font-medium'
                          : 'text-gray-700'
                      }`}
                    >
                      {topicArticle.title}
                    </Link>
                  </div>
                  ))
                ) : (
                  <div className="text-gray-500 text-sm">暂无相关文章</div>
                )}
              </div>
              
              {/* 专题数据概览 */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <h4 className="text-sm font-semibold mb-3 text-gray-700">About this topic</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-xs text-gray-500">Articles</div>
                    <div className="text-lg font-bold text-gray-900">{allTopicArticles.length}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Updated</div>
                    <div className="text-lg font-bold text-gray-900">{new Date(topic.updated_at).toLocaleDateString()}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 