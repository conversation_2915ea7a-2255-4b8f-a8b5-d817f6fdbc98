'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import ProductTable from './ProductTable'

interface ProductPrice {
  quantity: number
  price: number
}

interface Brand {
  name_cn: string | null
  name_en: string | null
  name_ru: string | null
  logo_url?: string
  website?: string
}

interface Parameter {
  param_name: string
  param_value: string
}

interface Product {
  product_id: string;
  model: string;
  url?: string; // 添加url字段
  brand_id: string;
  brand: Brand;
  price_key: string;
  stock_key: string;
  datasheet_url: string;
  parameters_key: string;
  parameters?: {
    english?: Parameter[];
    chinese?: Parameter[];
    russian?: Parameter[];
  };
  description: string;
  prices: ProductPrice[];
  stock: number;
  image_list?: string[];
  category_path?: string[];
  category_names?: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
  category_id?: string;
  full?: {
    product_id: string;
    model: string;
    url?: string; // 添加url字段到full对象
    brand_id: string;
    brand: Brand;
    price_key: string;
    stock_key: string;
    datasheet_url: string;
    parameters_key: string;
    parameters?: {
      english?: Parameter[];
      chinese?: Parameter[];
      russian?: Parameter[];
    };
    description: string;
    prices: ProductPrice[];
    stock: number;
    image_list?: string[];
    category_path?: string[];
    category_names?: {
      [key: string]: {
        name_cn: string;
        name_en: string;
        name_ru: string;
      }
    };
    category_id?: string;
  };
}

interface SearchResponse {
  total: number
  page: number
  results: Product[]
  keyword?: string;
}

// Helper function to create a URL with query params
function createQueryUrl(baseUrl: string, params: Record<string, string>) {
  const url = new URL(baseUrl, window.location.origin)
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      url.searchParams.append(key, value)
    }
  })
  return url.toString()
}

// Search component
export default function SearchClient() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const query = searchParams.get('q') || ''
  const category = searchParams.get('category') || ''
  const brand = searchParams.get('brand') || ''
  const page = parseInt(searchParams.get('page') || '1')
  
  // Search state
  const [products, setProducts] = useState<Product[]>([])
  const [totalResults, setTotalResults] = useState(0)
  const [currentPage, setCurrentPage] = useState(page)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Effect to perform search when parameters change
  useEffect(() => {
    if (!query && !category && !brand) {
      return
    }
    
    performSearch()
  }, [query, category, brand, page])
  
  // Function to perform search request
  const performSearch = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      let searchUrl = ''
      
      // Determine which search API to use based on parameters
      if (query) {
        searchUrl = createQueryUrl('https://webapi.chinaelectron.com/products', {
          search: query,
          page: page.toString()
        })
      } else if (category) {
        searchUrl = createQueryUrl('https://webapi.chinaelectron.com/products', {
          category: category,
          page: page.toString()
        })
      } else if (brand) {
        searchUrl = createQueryUrl('https://webapi.chinaelectron.com/products', {
          brand: brand,
          page: page.toString()
        })
      }
      
      if (!searchUrl) {
        setProducts([])
        setTotalResults(0)
        setIsLoading(false)
        return
      }
      
      const response = await fetch(searchUrl)
      
      if (!response.ok) {
        throw new Error(`Error fetching data: ${response.statusText}`)
      }
      
      const data = await response.json() as SearchResponse
      
      // 确保每个产品数据包含必要字段，以便正确构建URL
      const processedResults = data.results.map(product => {
        // 处理可能的品牌信息缺失
        if (!product.brand) {
          product.brand = {
            name_cn: null,
            name_en: null,
            name_ru: null
          }
        }
        
        // 确保full字段存在
        if (!product.full) {
          product.full = {
            ...product,
            brand: product.brand,
            parameters: product.parameters
          }
        }
        
        // 如果没有分类路径，添加默认路径
        if (!product.category_path || product.category_path.length === 0) {
          product.category_path = ['component', 'electronic', 'general']
        }
        
        // 确保参数字段存在
        if (!product.parameters) {
          product.parameters = {
            english: [],
            chinese: [],
            russian: []
          }
        }
        
        return product
      })
      
      setProducts(processedResults)
      setTotalResults(data.total)
      setCurrentPage(page)
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message)
      } else {
        setError('An unknown error occurred')
      }
      console.error('Search error:', err)
    } finally {
      setIsLoading(false)
    }
  }
  
  // Function to navigate to search results page
  const navigateToPage = (newPage: number) => {
    const params = new URLSearchParams()
    if (query) params.set('q', query)
    if (category) params.set('category', category)
    if (brand) params.set('brand', brand)
    params.set('page', newPage.toString())
    
    router.push(`/search?${params.toString()}`)
  }
  
  // Function to display appropriate title based on search parameters
  const getSearchTitle = () => {
    if (query) return `Search results for "${query}"`
    if (category) return 'Category results'
    if (brand) return 'Brand results'
    return 'Search results'
  }
  
  // Main render
  return (
    <div className="container-fluid px-2 py-4">
      <h1 className="text-2xl font-bold mb-4">{getSearchTitle()}</h1>
      
      {isLoading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500 mb-4">{error}</p>
          <button 
            onClick={performSearch}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      ) : products.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-gray-500 mb-2">No products found matching your search criteria.</p>
          <p className="text-gray-500">Try a different search term or browse categories.</p>
        </div>
      ) : (
        <ProductTable 
          products={products} 
          totalResults={totalResults} 
          currentPage={currentPage} 
          navigateToPage={navigateToPage} 
        />
      )}
    </div>
  )
} 