#!/usr/bin/env python3
"""
创建合规声明表的脚本
"""

import requests
import json

# 数据库API配置
DB_API_BASE_URL = "https://api.chinaelectron.com"

def create_compliance_table():
    """创建合规声明表"""
    
    # 创建表的SQL
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS compliance_statement (
        compliance_id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        compliance_confirmed BOOLEAN DEFAULT FALSE,
        resell_components BOOLEAN DEFAULT FALSE,
        ultimate_consignee TEXT,
        country TEXT,
        application_type TEXT,
        additional_information TEXT,
        is_default BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
    """
    
    try:
        print("🔧 创建合规声明表...")
        
        response = requests.post(
            f"{DB_API_BASE_URL}/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": create_table_sql,
                "params": []
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 合规声明表创建成功!")
            print(f"📊 响应: {json.dumps(result, indent=2)}")
        else:
            print(f"❌ 创建表失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 创建表时发生错误: {str(e)}")

def test_table_structure():
    """测试表结构"""
    
    try:
        print("\n🔍 检查表结构...")
        
        # 查询表结构
        response = requests.post(
            f"{DB_API_BASE_URL}/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": "PRAGMA table_info(compliance_statement)",
                "params": []
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 表结构查询成功!")
            
            if result.get('data'):
                print("\n📋 表字段信息:")
                for column in result['data']:
                    print(f"  - {column[1]} ({column[2]}) {'NOT NULL' if column[3] else 'NULL'} {'PK' if column[5] else ''}")
            else:
                print("⚠️  表可能不存在或无数据")
                
        else:
            print(f"❌ 查询表结构失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 查询表结构时发生错误: {str(e)}")

def test_insert():
    """测试插入数据"""
    
    try:
        print("\n🧪 测试插入数据...")
        
        test_data = {
            "user_id": "test_user_001",
            "compliance_confirmed": True,
            "resell_components": False,
            "ultimate_consignee": "Test Company Ltd",
            "country": "Germany",
            "application_type": "Industrial",
            "additional_information": "Test compliance statement",
            "is_default": False
        }
        
        insert_sql = """
        INSERT INTO compliance_statement (
            user_id, compliance_confirmed, resell_components, ultimate_consignee,
            country, application_type, additional_information, is_default, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))
        """
        
        response = requests.post(
            f"{DB_API_BASE_URL}/query/",
            headers={"Content-Type": "application/json"},
            json={
                "sql": insert_sql,
                "params": [
                    test_data["user_id"],
                    test_data["compliance_confirmed"],
                    test_data["resell_components"],
                    test_data["ultimate_consignee"],
                    test_data["country"],
                    test_data["application_type"],
                    test_data["additional_information"],
                    test_data["is_default"]
                ]
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 测试数据插入成功!")
            print(f"📊 响应: {json.dumps(result, indent=2)}")
            
            # 获取插入的ID
            if 'data' in result and 'insertId' in result['data']:
                insert_id = result['data']['insertId']
                print(f"🆔 插入的记录ID: {insert_id}")
            elif 'insertId' in result:
                insert_id = result['insertId']
                print(f"🆔 插入的记录ID: {insert_id}")
            else:
                print("⚠️  未找到插入的记录ID")
                
        else:
            print(f"❌ 插入数据失败: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 插入数据时发生错误: {str(e)}")

def main():
    """主函数"""
    print("🌍 合规声明表创建和测试工具")
    print("=" * 40)
    
    # 1. 创建表
    create_compliance_table()
    
    # 2. 检查表结构
    test_table_structure()
    
    # 3. 测试插入
    test_insert()
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    main()
