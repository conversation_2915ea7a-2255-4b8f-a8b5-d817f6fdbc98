import Link from 'next/link';

// Error display component
export default function ErrorDisplay({ error }: { error: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="bg-red-50 text-red-700 p-4 rounded-lg max-w-md">
        <h3 className="font-bold text-lg mb-2">Error loading data</h3>
        <p>{error}</p>
        <Link
          href="/manufacturers"
          className="mt-4 inline-block bg-red-100 hover:bg-red-200 text-red-800 px-4 py-2 rounded transition-colors"
        >
          Retry
        </Link>
      </div>
    </div>
  );
} 