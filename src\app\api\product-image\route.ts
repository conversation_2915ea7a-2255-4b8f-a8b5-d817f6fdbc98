import { NextRequest, NextResponse } from 'next/server';

/**
 * 处理产品图片请求
 * 包装底层API，不暴露底层API地址
 */
export async function GET(request: NextRequest) {
  try {
    // 获取图片名称参数
    const { searchParams } = new URL(request.url);
    const imageName = searchParams.get('name');
    
    if (!imageName) {
      return NextResponse.json(
        { error: 'Image name is required' },
        { status: 400 }
      );
    }
    
    // 构建基本API URL
    const baseUrl = 'https://webapi.chinaelectron.com/product/image/';
    
    // 获取图片
    const imageUrl = `${baseUrl}${encodeURIComponent(imageName)}`;
    
    // 获取token（如果存在）
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    
    // 构建请求头
    const headers: HeadersInit = {
      'Accept': 'image/*',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || request.headers.get('origin') || 'https://example.com'
    };
    
    // 如果有token则添加到请求头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    // 发送请求获取图片
    const response = await fetch(imageUrl, { headers });
    
    // 如果图片不存在，返回默认图片
    if (!response.ok) {
      console.warn(`Image not found: ${imageName}, using default image`);
      const defaultResponse = await fetch(`${baseUrl}default.jpg`, { headers });
      
      // 如果默认图片也不存在，返回错误
      if (!defaultResponse.ok) {
        return NextResponse.json(
          { error: 'Image not found and default image is also unavailable' },
          { status: 404 }
        );
      }
      
      // 返回默认图片
      const defaultImageBuffer = await defaultResponse.arrayBuffer();
      return new NextResponse(defaultImageBuffer, {
        headers: {
          'Content-Type': defaultResponse.headers.get('Content-Type') || 'image/jpeg',
          'Cache-Control': 'public, max-age=86400' // 缓存1天
        }
      });
    }
    
    // 返回请求的图片
    const imageBuffer = await response.arrayBuffer();
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': response.headers.get('Content-Type') || 'image/jpeg',
        'Cache-Control': 'public, max-age=86400' // 缓存1天
      }
    });
  } catch (error) {
    console.error('Error fetching product image:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product image' },
      { status: 500 }
    );
  }
} 