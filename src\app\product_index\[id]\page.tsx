import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Metadata, ResolvingMetadata } from 'next';

// 定义产品索引项的类型
interface ProductIndexItem {
  id: string;
  first_text: string;
  product_id: string;
  model: string;
  url: string;
}

// 定义API响应的类型
interface ApiResponse {
  total: number;
  page: number;
  limit: number;
  first_text: string;
  results: ProductIndexItem[];
}

// 定义首字母API响应类型
interface FirstTextResponse {
  total: number;
  results: { first_text: string }[];
}

// 生成元数据
export async function generateMetadata(
  props: { params: Promise<{ id: string }>, searchParams: Promise<{ page?: string }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  const params = await props.params;
  const searchParams = await props.searchParams;
  
  const firstText = params.id;
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  
  // 构建规范URL - 对于第一页，不包含page参数；对于其他页面，包含page参数
  let canonicalUrl = `https://www.chinaelectron.com/product_index/${firstText}`;
  if (page > 1) {
    canonicalUrl += `?page=${page}`;
  }
  
  return {
    title: `Product Index - ${firstText}${page > 1 ? ` - Page ${page}` : ''} | China Electron`,
    description: `Browse electronic components starting with ${firstText}. Find specifications, datasheets and suppliers.`,
    alternates: {
      canonical: canonicalUrl,
    }
  };
}

// 获取所有首字母列表
async function getAllFirstTexts(): Promise<string[]> {
  try {
    const response = await fetch(
      'https://webapi.chinaelectron.com/product-index/first-text',
      { 
        cache: 'force-cache', // 使用强制缓存，确保服务端和客户端获取相同的数据
        next: { revalidate: 86400 } // 每天重新验证一次
      }
    );
    
    if (!response.ok) {
      throw new Error(`Failed to fetch first texts: ${response.status}`);
    }
    
    const data: FirstTextResponse = await response.json();
    // 确保结果是排序的，这样服务端和客户端渲染的顺序一致
    return data.results.map(item => item.first_text).sort();
  } catch (error) {
    console.error('Error fetching first texts:', error);
    return [];
  }
}

// 获取产品索引数据
async function getProductIndexData(firstText: string, page: number = 1): Promise<ApiResponse> {
  try {
    const response = await fetch(
      `https://webapi.chinaelectron.com/product-index/by-first?first_text=${firstText}&page=${page}`,
      { 
        cache: 'force-cache', // 使用强制缓存，确保服务端和客户端获取相同的数据
        next: { revalidate: 3600 } // 每小时重新验证一次
      }
    );
    
    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching product index data:', error);
    return {
      total: 0,
      page: page,
      limit: 30,
      first_text: firstText,
      results: []
    };
  }
}

// 生成字母数字导航
function FirstTextNavigation({ firstTexts, currentFirstText }: { firstTexts: string[], currentFirstText: string }) {
  // 对firstTexts进行排序，确保服务端和客户端渲染的顺序一致
  const sortedFirstTexts = [...firstTexts].sort();
  
  return (
    <div className="flex flex-wrap gap-2 mb-6 p-4 bg-gray-50 rounded-lg overflow-x-auto">
      {sortedFirstTexts.map(text => (
        <Link 
          key={text}
          href={`/product_index/${encodeURIComponent(text)}`}
          className={`px-3 py-1 rounded-md ${
            currentFirstText === text 
              ? 'bg-blue-600 text-white' 
              : 'bg-white border hover:bg-gray-100'
          }`}
        >
          {text}
        </Link>
      ))}
    </div>
  );
}

// 分页组件
function Pagination({ currentPage, totalPages, firstText }: { currentPage: number, totalPages: number, firstText: string }) {
  const pages = [];
  const maxVisiblePages = 5;
  
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
  
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }
  
  return (
    <div className="flex justify-center my-8">
      <div className="flex gap-2">
        {currentPage > 1 && (
          <Link 
            href={`/product_index/${firstText}?page=${currentPage - 1}`}
            className="px-4 py-2 border rounded-md hover:bg-gray-100"
          >
            Previous
          </Link>
        )}
        
        {pages.map(page => (
          <Link 
            key={page}
            href={`/product_index/${firstText}?page=${page}`}
            className={`px-4 py-2 border rounded-md ${
              currentPage === page 
                ? 'bg-blue-600 text-white' 
                : 'hover:bg-gray-100'
            }`}
          >
            {page}
          </Link>
        ))}
        
        {currentPage < totalPages && (
          <Link 
            href={`/product_index/${firstText}?page=${currentPage + 1}`}
            className="px-4 py-2 border rounded-md hover:bg-gray-100"
          >
            Next
          </Link>
        )}
      </div>
    </div>
  );
}

// 页面组件
export default async function ProductIndexPage(
  props: { 
    params: Promise<{ id: string }>, 
    searchParams: Promise<{ page?: string }> 
  }
) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  
  const firstText = params.id;
  const page = searchParams.page ? parseInt(searchParams.page) : 1;
  
  // 并行获取数据
  const [firstTexts, data] = await Promise.all([
    getAllFirstTexts(),
    getProductIndexData(firstText, page)
  ]);
  
  // 如果没有结果且是第一页，返回404
  if (data.results.length === 0 && page === 1) {
    notFound();
  }
  
  // 计算总页数
  const totalPages = Math.ceil(data.total / data.limit);
  
  // 确保产品列表的稳定排序，使用安全的排序方法
  const stableResults = [...data.results].sort((a, b) => {
    // 确保id存在并且是字符串
    const idA = typeof a.id === 'string' ? a.id : String(a.id || '');
    const idB = typeof b.id === 'string' ? b.id : String(b.id || '');
    return idA.localeCompare(idB);
  });
  
  return (
    <>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-6">
          Product Index with "{firstText}"
        </h1>
        
        {/* 字母数字导航 */}
        <FirstTextNavigation firstTexts={firstTexts} currentFirstText={firstText} />
        
        {/* 产品列表 - 平铺展示 */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-wrap gap-3">
            {stableResults.map((item: any, index: number) => (
              <a 
                key={item.id || index} 
                href={item.url || `#`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block px-4 py-2 bg-gray-50 hover:bg-gray-100 border rounded-md transition-colors duration-150 text-blue-600 hover:text-blue-800"
              >
                {item.model}
              </a>
            ))}
          </div>
          
          {/* 如果没有结果 */}
          {stableResults.length === 0 && (
            <div className="p-8 text-center text-gray-500">
              No products found starting with "{firstText}"
            </div>
          )}
        </div>
        
        {/* 分页 */}
        {totalPages > 1 && (
          <Pagination 
            currentPage={page} 
            totalPages={totalPages} 
            firstText={firstText} 
          />
        )}
        
        {/* 数据摘要 */}
        <div className="mt-6 text-sm text-gray-500">
          Showing {stableResults.length} of {data.total} products starting with "{firstText}"
          {totalPages > 1 && ` - Page ${page} of ${totalPages}`}
        </div>
      </div>
    </>
  );
} 