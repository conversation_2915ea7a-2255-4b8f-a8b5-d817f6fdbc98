'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'

interface AdItem {
  id: string
  title: string
  image_url: string
  link_url: string
  alt_text: string
}

interface AdCarouselProps {
  ads: AdItem[]
  className?: string
  adLocation?: string
}

// 修复AbortSignal.timeout可能在某些环境中不支持的问题
if (typeof AbortSignal !== 'undefined' && !AbortSignal.timeout) {
  AbortSignal.timeout = function timeout(ms) {
    const controller = new AbortController();
    setTimeout(() => controller.abort(new DOMException('TimeoutError', 'TimeoutError')), ms);
    return controller.signal;
  };
}

export default function AdCarousel({ ads = [], className = '', adLocation = '' }: AdCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPaused, setIsPaused] = useState(false)

  // 自动轮播
  useEffect(() => {
    if (ads.length <= 1) return
    if (isPaused) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [ads.length, isPaused])

  // 如果没有广告数据，显示默认占位符
  if (ads.length === 0) {
    return (
      <div className={`relative bg-gradient-to-br from-orange-500 to-orange-600 rounded-md overflow-hidden shadow-sm ${className}`} data-location={adLocation}>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-white text-6xl font-semibold">
            Advertisement Space
          </div>
        </div>
        <div className="aspect-[16/9] w-full"></div>
      </div>
    )
  }

  return (
    <div
      className={`group relative rounded-md overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 ${className}`}
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
      data-location={adLocation}
    >
      {/* 轮播图容器 */}
      <div className="relative aspect-[16/7.7] w-full overflow-hidden bg-gradient-to-br from-orange-400 to-orange-600">
        {ads.map((ad, index) => (
          <Link
            key={ad.id}
            href={ad.link_url || '#'}
            className={`absolute inset-0 transition-all duration-500 ease-out ${
              index === currentIndex ? 'opacity-100 z-10 scale-100' : 'opacity-0 z-0 scale-100'
            }`}
          >
            {ad.image_url ? (
              <>
                <Image
                  src={ad.image_url}
                  alt={ad.alt_text || ad.title}
                  fill
                  priority={index === 0}
                  className="object-cover group-hover:scale-102 transition-transform duration-300"
                />
                {/* 悬停遮罩 */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent
                              opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </>
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-white text-4xl md:text-6xl font-semibold mb-2">
                    Ad {index + 1}
                  </div>
                  <div className="text-white/90 text-lg">
                    Advertisement Space
                  </div>
                </div>
              </div>
            )}
          </Link>
        ))}
      </div>

      {/* 现代化指示器 */}
      {ads.length > 1 && (
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-20">
          {ads.map((_, index) => (
            <button
              key={index}
              className={`h-2 rounded-full transition-all duration-500 ease-out ${
                index === currentIndex
                  ? 'bg-white w-8 shadow-lg'
                  : 'bg-white/50 w-2 hover:bg-white/70'
              }`}
              onClick={() => setCurrentIndex(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* 现代化导航按钮 */}
      {ads.length > 1 && (
        <>
          <button
            className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12
                     bg-white/20 backdrop-blur-sm hover:bg-white/30
                     text-white rounded-lg flex items-center justify-center z-20
                     transition-all duration-300"
            onClick={() => setCurrentIndex((prevIndex) => (prevIndex - 1 + ads.length) % ads.length)}
            aria-label="Previous slide"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <button
            className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12
                     bg-white/20 backdrop-blur-sm hover:bg-white/30
                     text-white rounded-lg flex items-center justify-center z-20
                     transition-all duration-300"
            onClick={() => setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length)}
            aria-label="Next slide"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}
    </div>
  )
} 