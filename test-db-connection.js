// 测试数据库连接和基本操作
const DB_API_BASE_URL = 'https://api.chinaelectron.com/api';

async function testDatabaseConnection() {
  try {
    console.log('Testing database connection...');
    
    // 1. 测试数据库信息
    const infoResponse = await fetch(`${DB_API_BASE_URL}/info`);
    if (!infoResponse.ok) {
      throw new Error(`Info request failed: ${infoResponse.status}`);
    }
    const infoResult = await infoResponse.json();
    console.log('Database info:', infoResult);
    
    // 2. 测试查询 orders 表结构
    const ordersResponse = await fetch(`${DB_API_BASE_URL}/table/orders?limit=1`);
    if (!ordersResponse.ok) {
      throw new Error(`Orders query failed: ${ordersResponse.status}`);
    }
    const ordersResult = await ordersResponse.json();
    console.log('Orders table test:', ordersResult);
    
    // 3. 测试创建一个简单的合规声明
    const testCompliance = {
      user_id: 'test_user_001',
      compliance_confirmed: 1,
      resell_components: 0,
      ultimate_consignee: 'Test Company',
      country: 'China',
      application_type: 'Industrial',
      additional_information: 'Test compliance statement',
      is_default: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    
    const complianceResponse = await fetch(`${DB_API_BASE_URL}/query/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql: `INSERT INTO compliance_statement (
          user_id, compliance_confirmed, resell_components, ultimate_consignee,
          country, application_type, additional_information, is_default, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        params: [
          testCompliance.user_id,
          testCompliance.compliance_confirmed,
          testCompliance.resell_components,
          testCompliance.ultimate_consignee,
          testCompliance.country,
          testCompliance.application_type,
          testCompliance.additional_information,
          testCompliance.is_default,
          testCompliance.created_at,
          testCompliance.updated_at
        ]
      })
    });
    
    if (!complianceResponse.ok) {
      const errorText = await complianceResponse.text();
      console.error('Compliance creation failed:', errorText);
      throw new Error(`Compliance creation failed: ${complianceResponse.status} - ${errorText}`);
    }
    
    const complianceResult = await complianceResponse.json();
    console.log('Compliance creation result:', complianceResult);
    
    console.log('All tests passed!');
    
  } catch (error) {
    console.error('Database test failed:', error);
  }
}

// 运行测试
testDatabaseConnection();
