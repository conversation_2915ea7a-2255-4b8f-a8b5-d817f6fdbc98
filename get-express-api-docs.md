# LCSC Express Shipping API Documentation

## 概述
这个API用于获取LCSC（立创商城）的国际快递运费报价，支持多种快递公司如DHL、FedEx、UPS等。

## 基本信息
- **API URL**: `https://get-express.chinaelectron.com/api/express`
- **请求方法**: `POST`
- **Content-Type**: `application/json`
- **响应格式**: `JSON`

## 请求参数

### 必需参数
| 参数名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `countryCode` | string | 目的地国家代码（ISO 3166-1 alpha-2） | "DE", "US", "GB" |
| `weight` | number | 包裹重量（单位：克） | 102 |
| `orderAmt` | number | 订单金额（美元） | 256.69 |

### 可选参数
| 参数名 | 类型 | 默认值 | 说明 | 示例 |
|--------|------|--------|------|------|
| `province` | string | "" | 省份/州名称 | "Bayern", "California" |
| `city` | string | "" | 城市名称 | "Munich", "Los Angeles" |
| `postCode` | string | "" | 邮政编码 | "80331", "90210" |
| `currencyCode` | string | "USD" | 货币代码 | "USD", "EUR", "GBP" |
| `orderItemTotal` | number | 1 | 订单商品总数量 | 2 |
| `vat` | boolean | false | 是否包含增值税 | true, false |
| `isFrom` | boolean | true | 是否来源标识 | true, false |
| `isHasBuzzer` | boolean | false | 是否包含蜂鸣器类产品 | true, false |
| `isIncludeSpecialCategory` | boolean | false | 是否包含特殊类别产品 | true, false |

## 请求示例

### cURL 示例
```bash
curl --location 'https://get-express.chinaelectron.com/api/express' \
--header 'Content-Type: application/json' \
--data '{
    "countryCode": "DE",
    "province": "Bayern",
    "city": "Munich",
    "postCode": "80331",
    "currencyCode": "USD",
    "weight": 102,
    "orderAmt": 256.69,
    "orderItemTotal": 2,
    "vat": false,
    "isFrom": true,
    "isHasBuzzer": false,
    "isIncludeSpecialCategory": false
}'
```

### JavaScript 示例
```javascript
const response = await fetch('https://get-express.chinaelectron.com/api/express', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        countryCode: "DE",
        province: "Bayern",
        city: "Munich",
        postCode: "80331",
        currencyCode: "USD",
        weight: 102,
        orderAmt: 256.69,
        orderItemTotal: 2,
        vat: false
    })
});

const data = await response.json();
console.log(data);
```

## 响应格式

### 成功响应结构
```json
{
    "success": boolean,
    "data": {
        "code": number,
        "msg": string | null,
        "result": {
            "expressShoppingMap": {
                "sf": array,
                "lc": array,
                "customer": array
            }
        }
    },
    "requestData": object,
    "timestamp": string
}
```

### 响应字段说明

#### 顶层字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `success` | boolean | API调用是否成功 |
| `data` | object | LCSC API返回的原始数据 |
| `requestData` | object | 发送给LCSC API的请求参数 |
| `timestamp` | string | 响应时间戳（ISO 8601格式） |

#### data 字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `code` | number | LCSC API响应状态码（200表示成功） |
| `msg` | string/null | LCSC API响应消息 |
| `result` | object | 快递选项结果 |

#### expressShoppingMap 字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `sf` | array | 顺丰快递选项（通常为空） |
| `lc` | array | LCSC官方快递选项（付费） |
| `customer` | array | 客户自付快递选项（客户承担运费） |

#### 快递选项对象字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `boxWeight` | number/null | 包装重量 | null |
| `costOrigin` | number | 运费成本（美元） | 30.16 |
| `days` | string | 预计送达时间 | "3-7" |
| `expressType` | string | 快递类型 | "courier" |
| `forwarderType` | string | 承运商类型 | "DHL", "FEDEX", "UPS" |
| `expressNameEn` | string | 快递服务英文名称 | "DHL Express" |
| `expressNameCn` | string | 快递服务中文名称 | "DHL 官方" |
| `remoteFee` | number | 偏远地区附加费 | 0 |
| `expressCode` | string | 快递服务代码 | "DHL", "FexEx_IP" |
| `shipmentType` | string | 运费承担方式 | "lc", "customer" |
| `remoteMsg` | string/null | 偏远地区消息 | null |
| `tips` | string/null | 提示信息 | null |
| `grossWeight` | number | 总重量（克） | 358 |
| `taxName` | string/null | 税费名称 | null |
| `currencyCode` | string | 货币代码 | "USD" |

### shipmentType 说明
- `lc`: LCSC承担运费，客户支付给LCSC
- `customer`: 客户自付运费，使用客户自己的快递账户
- `sf`: 顺丰快递选项

### 常见快递服务代码
| 代码 | 服务名称 | 承运商 | 时效 |
|------|----------|--------|------|
| `DHL` | DHL Express | DHL | 3-7天 |
| `FexEx_IP` | FedEx International Priority | FedEx | 3-7天 |
| `FedEx_IE` | FedEx International Economy | FedEx | 4-8天 |
| `UPS-HK` | UPS | UPS | 3-7天 |

## 完整响应示例

### 成功响应
```json
{
    "success": true,
    "data": {
        "code": 200,
        "msg": null,
        "result": {
            "expressShoppingMap": {
                "sf": [],
                "lc": [
                    {
                        "boxWeight": null,
                        "costOrigin": 30.16,
                        "days": "3-7",
                        "expressType": "courier",
                        "forwarderType": "DHL",
                        "expressNameEn": "DHL Express",
                        "uuid": null,
                        "expressNameCn": "DHL 官方",
                        "remoteFee": 0,
                        "expressCode": "DHL",
                        "shipmentType": "lc",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    },
                    {
                        "boxWeight": null,
                        "costOrigin": 23.57,
                        "days": "3-7",
                        "expressType": "courier",
                        "forwarderType": "FEDEX",
                        "expressNameEn": "FedEx International Priority",
                        "uuid": null,
                        "expressNameCn": "FexEx 官方联邦IP",
                        "remoteFee": 0,
                        "expressCode": "FexEx_IP",
                        "shipmentType": "lc",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    },
                    {
                        "boxWeight": null,
                        "costOrigin": 29.19,
                        "days": "3-7",
                        "expressType": "courier",
                        "forwarderType": "UPS",
                        "expressNameEn": "UPS",
                        "uuid": null,
                        "expressNameCn": "UPS-HK",
                        "remoteFee": 0,
                        "expressCode": "UPS-HK",
                        "shipmentType": "lc",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    },
                    {
                        "boxWeight": null,
                        "costOrigin": 23.07,
                        "days": "4-8",
                        "expressType": "courier",
                        "forwarderType": "FEDEX",
                        "expressNameEn": "FedEx International Economy",
                        "uuid": null,
                        "expressNameCn": "FedEx 官方联邦IE",
                        "remoteFee": 0,
                        "expressCode": "FedEx_IE",
                        "shipmentType": "lc",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    }
                ],
                "customer": [
                    {
                        "boxWeight": null,
                        "costOrigin": 0,
                        "days": "3-7",
                        "expressType": "courier",
                        "forwarderType": "DHL",
                        "expressNameEn": "DHL Express - Bill Account",
                        "uuid": null,
                        "expressNameCn": "DHL 官方- Bill Account",
                        "remoteFee": 0,
                        "expressCode": "DHL",
                        "shipmentType": "customer",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    },
                    {
                        "boxWeight": null,
                        "costOrigin": 0,
                        "days": "3-7",
                        "expressType": "courier",
                        "forwarderType": "FEDEX",
                        "expressNameEn": "FedEx International Priority - Bill Account",
                        "uuid": null,
                        "expressNameCn": "FexEx 官方联邦IP- Bill Account",
                        "remoteFee": 0,
                        "expressCode": "FexEx_IP",
                        "shipmentType": "customer",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    },
                    {
                        "boxWeight": null,
                        "costOrigin": 0,
                        "days": "3-7",
                        "expressType": "courier",
                        "forwarderType": "UPS",
                        "expressNameEn": "UPS - Bill Account",
                        "uuid": null,
                        "expressNameCn": "UPS-HK- Bill Account",
                        "remoteFee": 0,
                        "expressCode": "UPS-HK",
                        "shipmentType": "customer",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    },
                    {
                        "boxWeight": null,
                        "costOrigin": 0,
                        "days": "4-8",
                        "expressType": "courier",
                        "forwarderType": "FEDEX",
                        "expressNameEn": "FedEx International Economy - Bill Account",
                        "uuid": null,
                        "expressNameCn": "FedEx 官方联邦IE- Bill Account",
                        "remoteFee": 0,
                        "expressCode": "FedEx_IE",
                        "shipmentType": "customer",
                        "remoteMsg": null,
                        "tips": null,
                        "grossWeight": 358,
                        "taxName": null,
                        "currencyCode": "USD"
                    }
                ]
            }
        }
    },
    "requestData": {
        "countryCode": "DE",
        "province": "Bayern",
        "city": "asdw",
        "postCode": "asd",
        "currencyCode": "USD",
        "weight": 102,
        "orderAmt": 256.69,
        "orderItemTotal": 2,
        "vat": false,
        "isFrom": true,
        "isHasBuzzer": false,
        "isIncludeSpecialCategory": false
    },
    "timestamp": "2025-06-28T08:40:09.235Z"
}
```

### 错误响应示例

#### 缺少必需参数
```json
{
    "success": false,
    "error": "Missing required fields",
    "missingFields": ["countryCode", "weight"],
    "message": "Please provide: countryCode, weight, orderAmt",
    "timestamp": "2025-06-28T08:40:09.235Z"
}
```

#### 服务器错误
```json
{
    "success": false,
    "error": "Internal server error",
    "message": "LCSC API responded with status: 500",
    "timestamp": "2025-06-28T08:40:09.235Z"
}
```

## 使用建议

### 1. 重量计算
- API接受的重量单位是**克（g）**
- 建议在前端进行单位转换：
  ```javascript
  const weightInGrams = weightInKg * 1000;
  ```

### 2. 价格显示
- `costOrigin` 字段包含运费价格（美元）
- `shipmentType` 为 "customer" 时，`costOrigin` 为 0（客户自付）
- 建议根据 `shipmentType` 显示不同的价格信息

### 3. 服务选择
- `lc` 数组：LCSC承担运费的选项，客户需要支付运费
- `customer` 数组：客户使用自己快递账户的选项，运费为0
- 根据业务需求选择合适的服务类型

### 4. 错误处理
- 始终检查 `success` 字段
- 处理网络错误和API错误
- 为用户提供友好的错误提示

### 5. 缓存策略
- 相同参数的查询结果可以短时间缓存（建议5-10分钟）
- 运费价格可能实时变动，不建议长时间缓存

## 常见问题

### Q: 为什么有些快递选项的 costOrigin 是 0？
A: 当 `shipmentType` 为 "customer" 时，表示客户使用自己的快递账户，LCSC不收取运费。

### Q: grossWeight 和 weight 参数有什么区别？
A: `weight` 是请求参数中的商品重量，`grossWeight` 是响应中的总重量（包含包装重量）。

### Q: 如何选择最优的快递服务？
A: 可以根据 `costOrigin`（价格）、`days`（时效）和 `forwarderType`（承运商）综合考虑。

### Q: API 有调用频率限制吗？
A: 建议控制调用频率，避免过于频繁的请求。可以实现防抖或缓存机制。
