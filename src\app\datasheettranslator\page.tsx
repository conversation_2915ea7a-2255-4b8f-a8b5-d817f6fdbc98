'use client'

import { Suspense, useEffect, useState } from 'react'
import { useSearchParams, useRouter, usePathname } from 'next/navigation'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import ServerBreadcrumbWrapper from '@/components/Breadcrumb/ServerBreadcrumbWrapper'

function DatasheetTranslatorContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()
  const [pdfUrl, setPdfUrl] = useState<string>('')
  const [productId, setProductId] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    // 检查是否是新的SEO友好URL格式
    if (pathname.startsWith('/datasheettranslator/') && pathname !== '/datasheettranslator') {
      // 从路径中提取文件名部分
      const pathWithoutBase = pathname.replace('/datasheettranslator/', '')

      // 从文件名中提取产品ID (CMA开头的部分)
      const productIdMatch = pathWithoutBase.match(/(CMA\d+)/);

      if (productIdMatch) {
        const extractedProductId = productIdMatch[1]
        const constructedPdfUrl = `https://download-datasheet.chinaelectron.com/?product_id=${extractedProductId}`

        setPdfUrl(constructedPdfUrl)
        setProductId(extractedProductId)
        setLoading(false)
        return
      } else {
        setError('Invalid URL format - Product ID not found')
        setLoading(false)
        return
      }
    }

    // 原有的查询参数逻辑（向后兼容）
    const urlParam = searchParams.get('pdf')
    const idParam = searchParams.get('productId')

    if (!urlParam) {
      setError('PDF URL parameter is required')
      setLoading(false)
      return
    }

    try {
      // 验证URL格式
      new URL(urlParam)
      setPdfUrl(urlParam)
      setProductId(idParam || '')
      setLoading(false)
    } catch {
      setError('Invalid PDF URL provided')
      setLoading(false)
    }
  }, [searchParams, pathname])

  const handleGoBack = () => {
    if (productId) {
      router.push(`/product/${productId}`)
    } else {
      router.back()
    }
  }

  if (loading) {
    return (
      <div className="h-screen flex flex-col bg-gray-50">
        <div className="flex-shrink-0 flex items-center justify-between bg-white border-b border-gray-200">
          <div className="flex-1">
            <ServerBreadcrumbWrapper />
          </div>
          <div className="px-4 py-3">
            <button
              onClick={handleGoBack}
              className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Product
            </button>
          </div>
        </div>
        <div className="flex-1 flex justify-center items-center">
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-screen flex flex-col bg-gray-50">
        <div className="flex-shrink-0 flex items-center justify-between bg-white border-b border-gray-200">
          <div className="flex-1">
            <ServerBreadcrumbWrapper />
          </div>
          <div className="px-4 py-3">
            <button
              onClick={handleGoBack}
              className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Product
            </button>
          </div>
        </div>
        <div className="flex-1 flex justify-center items-center">
          <div className="bg-white rounded-lg shadow-md p-8 text-center max-w-md">
            <div className="text-red-500 mb-4">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Error</h2>
              <p className="text-gray-600">{error}</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const translatorUrl = `https://pdf-translator.chinaelectron.com/?pdf=${encodeURIComponent(pdfUrl)}`

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* 面包屑导航区域 */}
      <div className="flex-shrink-0 flex items-center justify-between bg-white border-b border-gray-200">
        <div className="flex-1">
          <ServerBreadcrumbWrapper />
        </div>
        <div className="px-4 py-3">
          <button
            onClick={handleGoBack}
            className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors text-sm"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Product
          </button>
        </div>
      </div>

      {/* 全屏 iframe - 占据剩余空间 */}
      <div className="flex-1 w-full">
        <iframe
          src={translatorUrl}
          className="w-full h-full border-0"
          title="Datasheet Translator"
          allow="fullscreen"
          loading="lazy"
        />
      </div>
    </div>
  )
}

export default function DatasheetTranslatorPage() {
  return (
    <Suspense fallback={
      <div className="h-screen flex flex-col bg-gray-50">
        <div className="flex-shrink-0 bg-white border-b border-gray-200">
          <ServerBreadcrumbWrapper />
        </div>
        <div className="flex-1 flex justify-center items-center">
          <LoadingSpinner />
        </div>
      </div>
    }>
      <DatasheetTranslatorContent />
    </Suspense>
  )
}
