'use client'

import { useState } from 'react'
import { ComponentDetail, FullData } from '@/types/component'

interface AIAssistantClientProps {
  component: ComponentDetail
  parsedData: FullData
}

export default function AIAssistantClient({ component, parsedData }: AIAssistantClientProps) {
  const [message, setMessage] = useState('')

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 
                    hover:border-[#DE2910]/20 hover:shadow-lg hover:shadow-[#DE2910]/10 
                    transition-all duration-300 sticky top-24">
      <div className="p-6 border-b border-gray-100">
        <h2 className="text-xl font-semibold flex items-center gap-2 text-gray-900">
          <svg className="w-6 h-6 text-[#DE2910]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                  d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          AI Assistant
        </h2>
      </div>
      
      <div className="p-6">
        <div className="h-[400px] overflow-y-auto mb-6 space-y-4">
          <div className="flex items-start gap-3">
            <div className="w-10 h-10 rounded-xl bg-[#DE2910]/5 flex items-center justify-center flex-shrink-0">
              <svg className="w-6 h-6 text-[#DE2910]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <div className="bg-gray-50 rounded-xl p-4 max-w-[85%]">
              <p className="text-gray-700">
                I can help you understand the specifications and features of {component.metadata.name}. 
                What would you like to know?
              </p>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-100 pt-6">
          <div className="flex gap-3">
            <input 
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="flex-1 px-4 py-3 rounded-xl
                       bg-gray-50 
                       text-gray-900 placeholder-gray-400
                       border border-gray-200 focus:border-[#DE2910]/50
                       transition-all duration-200
                       focus:outline-none"
              placeholder="Ask about this component..."
            />
            <button className="px-5 py-3 rounded-xl
                           bg-gradient-to-r from-[#DE2910] to-[#FF6B45]
                           text-white font-medium
                           hover:from-[#FF3B20] hover:to-[#FF8055]
                           transition-all duration-300
                           shadow-lg hover:shadow-[#DE2910]/25
                           flex items-center gap-2">
              <span>Send</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  )
} 