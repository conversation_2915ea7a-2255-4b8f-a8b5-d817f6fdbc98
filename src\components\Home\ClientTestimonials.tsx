'use client'

import Image from 'next/image'

interface Testimonial {
  id: string
  name: string
  position: string
  company: string
  avatar: string
  content: string
  rating: number
}

interface ClientTestimonialsProps {
  testimonials?: Testimonial[]
}

// 客户评价数据
const defaultTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    position: 'Procurement Manager',
    company: 'TechFlow Industries',
    avatar: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=80&h=80&fit=crop&crop=face&auto=format&q=80',
    content: 'China Electron has been our go-to supplier for over 2 years. Their extensive inventory of authentic components and competitive pricing have helped us reduce procurement costs by 25%. The quality is consistently excellent and delivery is always on time.',
    rating: 5
  },
  {
    id: '2',
    name: '<PERSON>',
    position: 'Engineering Director',
    company: 'Innovation Electronics',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=80&h=80&fit=crop&crop=face&auto=format&q=80',
    content: 'What sets China Electron apart is their technical expertise. Their engineers provide excellent support during component selection and their detailed datasheets save us hours of research. The customer service team is responsive and knowledgeable.',
    rating: 5
  },
  {
    id: '3',
    name: '<PERSON> <PERSON>',
    position: 'Supply Chain Director',
    company: 'Global Manufacturing Corp',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=80&h=80&fit=crop&crop=face&auto=format&q=80',
    content: 'Reliability is crucial in our industry, and China Electron delivers every time. Their quality assurance process ensures we receive only genuine components. The online platform is user-friendly and makes bulk ordering seamless.',
    rating: 5
  }
]

export default function ClientTestimonials({ testimonials = defaultTestimonials }: ClientTestimonialsProps) {

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <svg
        key={index}
        className={`w-4 h-4 ${index < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ))
  }

  return (
    <section className="py-12 bg-gray-50">
      <div className="mx-auto px-14 relative">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900">What Our Clients Say</h2>
          <div className="w-16 h-1 bg-red-500 mt-2"></div>
        </div>

        {/* Testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial.id}
              className="bg-white rounded-md p-6 shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100"
            >
              {/* User info */}
              <div className="flex items-center mb-4">
                <div className="relative w-12 h-12 rounded-md overflow-hidden bg-gray-100 flex-shrink-0">
                  <Image
                    src={testimonial.avatar}
                    alt={testimonial.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <div className="ml-3 flex-1">
                  <h4 className="font-semibold text-gray-900 text-sm">{testimonial.name}</h4>
                  <p className="text-gray-500 text-xs">{testimonial.position}</p>
                  <p className="text-gray-400 text-xs">{testimonial.company}</p>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center mb-3">
                {renderStars(testimonial.rating)}
              </div>

              {/* Content */}
              <p className="text-gray-600 text-sm leading-relaxed line-clamp-4">
                {testimonial.content}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
