import { NextRequest, NextResponse } from 'next/server'

const DB_API_BASE_URL = 'https://api.chinaelectron.com/api'

// 测试数据库连接
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    console.log('Testing database connection...')
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

    const response = await fetch(`${DB_API_BASE_URL}/table/orders?limit=1`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    const endTime = Date.now()
    const duration = endTime - startTime

    if (!response.ok) {
      const errorText = await response.text()
      return NextResponse.json({
        success: false,
        error: `HTTP ${response.status}: ${errorText}`,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString()
      }, { status: response.status })
    }

    const result = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'Database connection successful',
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      sample_data: result.data ? result.data.slice(0, 1) : null,
      total_records: result.data ? result.data.length : 0
    })

  } catch (error) {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.error('Database connection test failed:', error)
    
    let errorMessage = 'Unknown error'
    let errorType = 'unknown'
    
    if (error instanceof Error) {
      errorMessage = error.message
      if (error.name === 'AbortError') {
        errorType = 'timeout'
        errorMessage = 'Connection timed out after 10 seconds'
      } else if (error.message.includes('fetch failed')) {
        errorType = 'network'
        errorMessage = 'Network connection failed'
      }
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      error_type: errorType,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}

// 测试订单状态更新
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const body = await request.json()
    const { order_id = 'TEST_ORDER_001' } = body
    
    console.log(`Testing order status update for ${order_id}...`)
    
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

    const response = await fetch(`${DB_API_BASE_URL}/table/orders?where_order_id=${order_id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        order_status: 'TEST_STATUS',
        payment_method: 'TEST_PAYMENT',
        updated_at: new Date().toISOString()
      }),
      signal: controller.signal
    })

    clearTimeout(timeoutId)
    const endTime = Date.now()
    const duration = endTime - startTime

    if (!response.ok) {
      const errorText = await response.text()
      return NextResponse.json({
        success: false,
        error: `HTTP ${response.status}: ${errorText}`,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        order_id
      }, { status: response.status })
    }

    const result = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'Order status update test successful',
      duration: `${duration}ms`,
      timestamp: new Date().toISOString(),
      order_id,
      result
    })

  } catch (error) {
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.error('Order status update test failed:', error)
    
    let errorMessage = 'Unknown error'
    let errorType = 'unknown'
    
    if (error instanceof Error) {
      errorMessage = error.message
      if (error.name === 'AbortError') {
        errorType = 'timeout'
        errorMessage = 'Update operation timed out after 15 seconds'
      } else if (error.message.includes('fetch failed')) {
        errorType = 'network'
        errorMessage = 'Network connection failed during update'
      }
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      error_type: errorType,
      duration: `${duration}ms`,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
