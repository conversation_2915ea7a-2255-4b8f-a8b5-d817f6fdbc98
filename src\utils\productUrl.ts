/**
 * 产品URL工具函数
 * 用于生成统一的产品详情页链接
 */

// 基本产品数据接口
interface BaseProductData {
  product_id: string;
  model: string;
  url?: string; // 添加url字段，用于直接获取产品详情页URL
  brand_name_en?: string;
  brand_en?: string;
  brand_cn?: string;
  brand?: {
    name_en?: string;
    name_cn?: string;
    name_ru?: string;
  };
  category_path?: string[];
  category_names?: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
  category_id?: string;
  full?: {
    brand?: {
      name_en?: string;
      name_cn?: string;
      name_ru?: string;
    };
    category_path?: string[];
    category_names?: {
      [key: string]: {
        name_cn: string;
        name_en: string;
        name_ru: string;
      }
    };
    category_id?: string;
  };
  [key: string]: any;
}

// 处理品牌名称，替换特殊字符为横杠
export function formatBrandName(name: string): string {
  if (!name) return 'unknown';
  return name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-');
}

// 处理分类名称，替换特殊字符为横杠，空格为下划线
export function formatCategoryName(name: string): string {
  if (!name) return 'unknown';
  // 先将空格替换为下划线
  const nameWithUnderscores = name.toLowerCase().replace(/\s+/g, '_');
  // 再将其他特殊字符替换为横杠
  return nameWithUnderscores.replace(/[^a-zA-Z0-9_\u4e00-\u9fa5]/g, '-');
}

// 处理产品型号，替换特殊字符为横杠
export function formatModelName(model: string): string {
  if (!model) return 'unknown';
  return model.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '-');
}

/**
 * 构建产品URL
 * 格式: /product/{一级分类}-{二级分类}-{三级分类}-{品牌名称（特殊符号用横杠替代）}-{model}-{产品id}
 * 
 * @param item 产品数据对象
 * @returns 格式化的产品URL
 */
export function buildProductUrl(item: BaseProductData): string {
  // 初始化默认分类名称
  let level1 = 'component';
  let level2 = 'electronic';
  let level3 = 'general';
  
  // 如果有full字段且包含分类信息，使用实际分类
  if (item.full && item.full.category_path && item.full.category_names) {
    if (item.full.category_path.length >= 1) {
      level1 = item.full.category_names[item.full.category_path[0]]?.name_en || level1;
    }
    if (item.full.category_path.length >= 2) {
      level2 = item.full.category_names[item.full.category_path[1]]?.name_en || level2;
    }
    if (item.full.category_path.length >= 3) {
      level3 = item.full.category_names[item.full.category_path[2]]?.name_en || level3;
    }
  } else if (item.category_path && item.category_names) {
    // 使用根级对象上的分类信息
    if (item.category_path.length >= 1) {
      level1 = item.category_names[item.category_path[0]]?.name_en || level1;
    }
    if (item.category_path.length >= 2) {
      level2 = item.category_names[item.category_path[1]]?.name_en || level2;
    }
    if (item.category_path.length >= 3) {
      level3 = item.category_names[item.category_path[2]]?.name_en || level3;
    }
  }
  
  // 获取品牌名称(优先级顺序)
  const brandName = 
    (item.full?.brand?.name_en) || 
    (item.brand?.name_en) || 
    (item.brand_name_en) || 
    (item.brand_en) || 
    (item.brand_cn) || 
    'unknown';
  
  // 格式化品牌名称（替换特殊字符）
  const formattedBrand = formatBrandName(brandName);
  
  // 格式化各级分类名称（替换空格为下划线，转为小写，并替换特殊字符为横杠）
  const formattedLevel1 = formatCategoryName(level1);
  const formattedLevel2 = formatCategoryName(level2);
  const formattedLevel3 = formatCategoryName(level3);
  
  // 格式化产品型号（替换特殊字符为横杠）
  const formattedModel = formatModelName(item.model);
  
  // 构建链接（始终使用新格式）
  return `/product/${formattedLevel1}-${formattedLevel2}-${formattedLevel3}-${formattedBrand}-${formattedModel}-${item.product_id}`;
}

/**
 * 构建数据表翻译器URL
 * 格式: /datasheettranslator/{一级分类}_{二级分类}_{三级分类}_{品牌名称}-{model}_{产品id}
 *
 * @param item 产品数据对象
 * @returns 格式化的数据表翻译器URL
 */
export function buildDatasheetTranslatorUrl(item: BaseProductData): string {
  // 初始化默认分类名称
  let level1 = 'passive_components';
  let level2 = 'capacitors';
  let level3 = 'Multilayer-Ceramic-Capacitor-MLCC';

  // 从category_path提取分类信息
  if (item.category_path && item.category_path.length >= 3) {
    const categoryNames = item.category_names;
    if (categoryNames) {
      // 获取各级分类的英文名称
      const level1Id = item.category_path[0];
      const level2Id = item.category_path[1];
      const level3Id = item.category_path[2];

      if (categoryNames[level1Id]?.name_en) {
        level1 = categoryNames[level1Id].name_en;
      }
      if (categoryNames[level2Id]?.name_en) {
        level2 = categoryNames[level2Id].name_en;
      }
      if (categoryNames[level3Id]?.name_en) {
        level3 = categoryNames[level3Id].name_en;
      }
    }
  }

  // 获取品牌名称
  let brandName = 'Unknown-Brand';
  if (item.brand?.name_en) {
    brandName = item.brand.name_en;
  } else if (item.brand_name_en) {
    brandName = item.brand_name_en;
  } else if (item.brand_en) {
    brandName = item.brand_en;
  } else if (item.brand_cn) {
    brandName = item.brand_cn;
  }

  // 格式化品牌名称（替换特殊字符）
  const formattedBrand = formatBrandName(brandName);

  // 格式化各级分类名称（替换空格为下划线，转为小写，并替换特殊字符为横杠）
  const formattedLevel1 = formatCategoryName(level1);
  const formattedLevel2 = formatCategoryName(level2);
  const formattedLevel3 = formatCategoryName(level3);

  // 格式化产品型号（替换特殊字符为横杠）
  const formattedModel = formatModelName(item.model);

  // 构建链接
  return `/datasheettranslator/${formattedLevel1}_${formattedLevel2}_${formattedLevel3}_${formattedBrand}-${formattedModel}_${item.product_id}`;
}