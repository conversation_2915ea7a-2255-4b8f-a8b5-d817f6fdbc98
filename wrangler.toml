name = "d1-database-api"
main = "db-script.js"
compatibility_date = "2024-06-27"
compatibility_flags = ["nodejs_compat"]

# D1 数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "404f09be-cb7e-46ed-8c6a-37960f31147e"

# 环境变量（可选）
[vars]
API_VERSION = "1.0.0"
MAX_QUERY_LIMIT = "1000"

# 开发环境配置
[env.dev]
[[env.dev.d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "404f09be-cb7e-46ed-8c6a-37960f31147e"

# 生产环境配置
[env.production]
[[env.production.d1_databases]]
binding = "DB"
database_name = "little-field-db"
database_id = "404f09be-cb7e-46ed-8c6a-37960f31147e"
