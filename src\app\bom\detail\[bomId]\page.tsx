'use client'

import { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { getBOMDetail } from '@/services/bomApi'
import { addBatchToCart, getProductDetail, CartItem } from '@/services/api'
import { BOMDetailResponse, BOMItem, MatchStatus, StockStatus } from '@/types/bom'
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  Package,
  DollarSign,
  TrendingUp,
  ExternalLink,
  Download,
  ArrowLeft,
  Eye
} from 'lucide-react'

interface BOMDetailPageProps {
  params: Promise<{
    bomId: string
  }>
}

export default function BOMDetailPage({ params }: BOMDetailPageProps) {
  const router = useRouter()
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const resolvedParams = use(params)
  const [bomData, setBomData] = useState<BOMDetailResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [addingToCart, setAddingToCart] = useState(false)
  const [cartMessage, setCartMessage] = useState<{ type: 'success' | 'error' | null; text: string }>({
    type: null,
    text: ''
  })

  const bomId = resolvedParams.bomId

  useEffect(() => {
    // 等待认证状态加载完成
    if (authLoading) return

    if (!isAuthenticated) {
      router.push('/login')
      return
    }

    loadBOMDetail()
  }, [isAuthenticated, authLoading, bomId])

  // 自动清除消息
  useEffect(() => {
    if (cartMessage.text) {
      const timer = setTimeout(() => {
        setCartMessage({ type: null, text: '' })
      }, 5000) // 5秒后自动清除消息

      return () => clearTimeout(timer)
    }
  }, [cartMessage.text])

  const loadBOMDetail = async () => {
    try {
      setLoading(true)
      setError('')
      
      const response = await getBOMDetail(bomId)
      setBomData(response)
    } catch (err) {
      console.error('Load BOM detail error:', err)
      setError(err instanceof Error ? err.message : 'Failed to load BOM details')
    } finally {
      setLoading(false)
    }
  }

  const getMatchStatusBadge = (status: number) => {
    switch (status) {
      case MatchStatus.EXACT_MATCH:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Exact Match
          </span>
        )
      case MatchStatus.PARTIAL_MATCH:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Partial Match
          </span>
        )
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="h-3 w-3 mr-1" />
            No Match
          </span>
        )
    }
  }

  const getStockStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase()
    if (statusLower.includes('in stock')) {
      return <span className="text-green-600 font-medium">In Stock</span>
    } else if (statusLower.includes('out of stock')) {
      return <span className="text-red-600 font-medium">Out of Stock</span>
    } else if (statusLower.includes('backordered')) {
      return <span className="text-yellow-600 font-medium">Backordered</span>
    } else {
      return <span className="text-gray-600 font-medium">{status}</span>
    }
  }

  const handleSelectItem = (itemId: string) => {
    const newSelected = new Set(selectedItems)
    if (newSelected.has(itemId)) {
      newSelected.delete(itemId)
    } else {
      newSelected.add(itemId)
    }
    setSelectedItems(newSelected)
  }

  const handleSelectAll = () => {
    if (selectedItems.size === bomData?.bom_items.length) {
      setSelectedItems(new Set())
    } else {
      setSelectedItems(new Set(bomData?.bom_items.map(item => item.bom_items_id) || []))
    }
  }

  const handleViewProduct = (productCode: string) => {
    window.open(`/product/${productCode}`, '_blank')
  }

  const handleAddToCart = async () => {
    if (!userId || !bomData || selectedItems.size === 0) {
      setCartMessage({ type: 'error', text: 'Please select items to add to cart' })
      return
    }

    setAddingToCart(true)
    setCartMessage({ type: null, text: '' })

    try {
      // 获取选中的BOM项目
      const selectedBomItems = bomData.bom_items.filter(item =>
        selectedItems.has(item.bom_items_id)
      )

      // 为每个选中的项目获取产品详情并构建购物车项目
      const cartItems: Omit<CartItem, 'cart_id' | 'added_at' | 'updated_at'>[] = []

      for (const bomItem of selectedBomItems) {
        try {
          // 获取产品详情
          const productDetail = await getProductDetail(bomItem.product_code)

          // 构建购物车项目
          const cartItem: Omit<CartItem, 'cart_id' | 'added_at' | 'updated_at'> = {
            user_id: userId,
            product_id: bomItem.product_code,
            model_number: productDetail?.model || bomItem.product_code,
            brand: productDetail?.brand?.name_en || productDetail?.brand?.name_cn || 'Unknown',
            description: productDetail?.description || bomItem.customer_part_number,
            unit_price: bomItem.unit_price,
            stock_quantity: parseInt(bomItem.stock_availability.replace(/[^\d]/g, '')) || 0,
            cart_quantity: Math.round(bomItem.order_qty * bomData.bom_main.quantity_multiplier)
          }

          cartItems.push(cartItem)
        } catch (error) {
          console.error(`Failed to get product detail for ${bomItem.product_code}:`, error)
          // 如果获取产品详情失败，使用BOM项目的基本信息
          const cartItem: Omit<CartItem, 'cart_id' | 'added_at' | 'updated_at'> = {
            user_id: userId,
            product_id: bomItem.product_code,
            model_number: bomItem.product_code,
            brand: 'Unknown',
            description: bomItem.customer_part_number,
            unit_price: bomItem.unit_price,
            stock_quantity: parseInt(bomItem.stock_availability.replace(/[^\d]/g, '')) || 0,
            cart_quantity: Math.round(bomItem.order_qty * bomData.bom_main.quantity_multiplier)
          }

          cartItems.push(cartItem)
        }
      }

      // 批量添加到购物车
      const result = await addBatchToCart(cartItems)

      if (result.success) {
        setCartMessage({
          type: 'success',
          text: `Successfully added ${cartItems.length} items to cart!`
        })
        // 清空选择
        setSelectedItems(new Set())
      } else {
        setCartMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      console.error('Error adding items to cart:', error)
      setCartMessage({ type: 'error', text: 'Failed to add items to cart. Please try again.' })
    } finally {
      setAddingToCart(false)
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{authLoading ? 'Checking authentication...' : 'Loading BOM details...'}</p>
        </div>
      </div>
    )
  }

  if (error || !bomData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error || 'BOM not found'}</p>
          <button
            onClick={() => router.push('/bom')}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Back to BOM List
          </button>
        </div>
      </div>
    )
  }

  const { bom_main, bom_items } = bomData

  return (
    <>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w mx-auto px-14">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={() => router.push('/bom')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{bom_main.bom_name}</h1>
              <p className="text-gray-600">BOM ID: {bom_main.bom_id} | Quantity Multiplier: {bom_main.quantity_multiplier}</p>
            </div>
          </div>
          <div className="flex space-x-3">
            <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 flex items-center">
              <Download className="h-4 w-4 mr-2" />
              Export
            </button>
            <button
              onClick={handleAddToCart}
              disabled={selectedItems.size === 0 || addingToCart || !isAuthenticated}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
            >
              {addingToCart ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Adding...
                </>
              ) : (
                <>
                  <Package className="h-4 w-4 mr-2" />
                  Add to Cart ({selectedItems.size})
                </>
              )}
            </button>
          </div>
        </div>

        {/* 消息显示 */}
        {cartMessage.text && (
          <div className={`mb-6 p-4 rounded-lg ${
            cartMessage.type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'
          }`}>
            <div className="flex items-center">
              {cartMessage.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 mr-2" />
              )}
              {cartMessage.text}
            </div>
          </div>
        )}

        {/* 统计信息卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Items</p>
                <p className="text-2xl font-bold text-gray-900">{bom_main.item_count}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">Matched</p>
                <p className="text-2xl font-bold text-gray-900">{bom_main.success_count}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Price</p>
                <p className="text-2xl font-bold text-gray-900">${bom_main.est_total_price.toFixed(2)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <p className="text-sm font-medium text-gray-600">Price per Set</p>
                <p className="text-2xl font-bold text-gray-900">${bom_main.each_price.toFixed(2)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* BOM项目表格 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-medium text-gray-900">BOM Items</h2>
              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedItems.size === bom_items.length && bom_items.length > 0}
                    onChange={handleSelectAll}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-600">Select All</span>
                </label>
                <span className="text-sm text-gray-500">
                  {selectedItems.size} of {bom_items.length} selected
                </span>
              </div>
            </div>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Select
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer Part #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    CE Part #
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Match Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Qty
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Stock
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Unit Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ext. Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {bom_items.map((item, index) => (
                  <tr key={`bom-item-${item.bom_items_id || index}`} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedItems.has(item.bom_items_id)}
                        onChange={() => handleSelectItem(item.bom_items_id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.customer_part_number}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800">
                      <button
                        onClick={() => handleViewProduct(item.product_code)}
                        className="flex items-center"
                      >
                        {item.product_code}
                        <ExternalLink className="h-3 w-3 ml-1" />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getMatchStatusBadge(item.match_status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {Math.round(item.order_qty * bom_main.quantity_multiplier)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {getStockStatusBadge(item.stock_availability)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${item.unit_price.toFixed(4)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      ${item.ext_price.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <button
                        onClick={() => handleViewProduct(item.product_code)}
                        className="text-blue-600 hover:text-blue-800 mr-3"
                        title="View Product"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 总计信息 */}
        <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <p className="text-sm text-gray-600">Exact Matches</p>
              <p className="text-2xl font-bold text-green-600">{bom_main.exact_match_count}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Partial Matches</p>
              <p className="text-2xl font-bold text-yellow-600">{bom_main.partial_match_count}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">No Matches</p>
              <p className="text-2xl font-bold text-red-600">{bom_main.no_match_count}</p>
            </div>
          </div>
        </div>
      </div>
      </div>
    </>
  )
}
