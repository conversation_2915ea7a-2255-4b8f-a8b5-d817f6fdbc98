import { NextRequest, NextResponse } from 'next/server'

/**
 * API Route for fetching hot items
 * This internal API route handles the external API call to avoid CORS issues
 * and provides a consistent interface for both server and client-side requests
 */
export async function GET(request: NextRequest) {
  try {
    // Prepare request headers for external API
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'User-Agent': 'ChinaElectron-API/1.0',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://chinaelectron.com'
    }

    // 添加超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 8000) // 8秒超时

    // Fetch data from external API
    const response = await fetch('https://get-hot.chinaelectron.com/latest?type=hot', {
      method: 'GET',
      headers,
      signal: controller.signal,
      cache: 'no-store' // Don't cache to get fresh data
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      console.error(`External API error: ${response.status} ${response.statusText}`)
      return NextResponse.json(
        { 
          success: false, 
          error: `External API responded with status: ${response.status}`,
          data: []
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    
    // Validate response format
    if (!data || typeof data.success !== 'boolean') {
      console.error('Invalid response format from external API')
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid response format from external API',
          data: []
        },
        { status: 502 }
      )
    }

    // Return the data as-is, maintaining the original structure
    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=300', // Cache for 5 minutes
      }
    })

  } catch (error: any) {
    console.error('Error in hot-items API route:', error)
    
    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        error: error.message || 'Internal server error',
        data: []
      },
      { status: 500 }
    )
  }
}
