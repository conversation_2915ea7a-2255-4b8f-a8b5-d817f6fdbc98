'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  addComplianceStatement,
  updateComplianceStatement,
  getUserComplianceStatements,
  ComplianceStatement
} from '@/services/api'

// 使用内联SVG图标
const FileTextIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 2v6h6M16 13H8M16 17H8M10 9H8" />
  </svg>
)

const CheckIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
  </svg>
)

const InfoIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="none" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
)

export default function CompliancePage() {
  const { userId, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()

  const [formData, setFormData] = useState({
    hasUnderstood: true,
    willResell: 'yes',
    ultimateConsignee: '',
    country: 'Germany',
    application: 'Industrial',
    additionalInfo: '',
    setAsDefault: false
  })

  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | null; text: string }>({
    type: null,
    text: ''
  })

  const countries = [
    'Germany', 'United States', 'United Kingdom', 'France', 'Italy', 'Spain', 
    'Canada', 'Australia', 'Japan', 'South Korea', 'Singapore', 'Netherlands',
    'Belgium', 'Switzerland', 'Austria', 'Sweden', 'Norway', 'Denmark'
  ]

  const applications = [
    'Industrial', 'Consumer Electronics', 'Automotive', 'Medical', 
    'Telecommunications', 'Aerospace', 'Military/Defense', 'Research & Development'
  ]

  // 检查认证状态
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [authLoading, isAuthenticated, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!userId) {
      setMessage({ type: 'error', text: 'User not authenticated' })
      return
    }

    if (!formData.hasUnderstood) {
      setMessage({ type: 'error', text: 'You must confirm that you understand the compliance statement' })
      return
    }

    if (!formData.ultimateConsignee.trim()) {
      setMessage({ type: 'error', text: 'Ultimate Consignee is required' })
      return
    }

    try {
      setLoading(true)
      setMessage({ type: null, text: '' })

      const complianceData = {
        user_id: userId,
        compliance_confirmed: formData.hasUnderstood,
        resell_components: formData.willResell === 'yes',
        ultimate_consignee: formData.ultimateConsignee,
        country: formData.country,
        application_type: formData.application,
        additional_information: formData.additionalInfo || undefined,
        is_default: formData.setAsDefault
      }

      const result = await addComplianceStatement(complianceData)

      if (result.success) {
        setMessage({ type: 'success', text: 'Compliance statement saved successfully' })
        // 重置表单
        setFormData({
          hasUnderstood: true,
          willResell: 'yes',
          ultimateConsignee: '',
          country: 'Germany',
          application: 'Industrial',
          additionalInfo: '',
          setAsDefault: false
        })
      } else {
        setMessage({ type: 'error', text: result.message })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save compliance statement' })
    } finally {
      setLoading(false)
    }
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-[#DE2910] border-t-transparent rounded-full animate-spin"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="flex items-center gap-3 mb-8">
          <FileTextIcon className="w-8 h-8 text-[#DE2910]" />
          <h1 className="text-3xl font-bold text-gray-900">Compliance Statement</h1>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
          {/* 消息显示 */}
          {message.text && (
            <div className={`p-4 rounded-lg mb-6 ${
              message.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
            }`}>
              {message.text}
            </div>
          )}

          {/* 确认理解部分 */}
          <div className="mb-8">
            <label className="flex items-start gap-3 cursor-pointer">
              <div className="relative mt-1">
                <input
                  type="checkbox"
                  checked={formData.hasUnderstood}
                  onChange={(e) => setFormData(prev => ({ ...prev, hasUnderstood: e.target.checked }))}
                  className="sr-only"
                />
                <div className={`w-5 h-5 border-2 rounded flex items-center justify-center transition-colors ${
                  formData.hasUnderstood 
                    ? 'bg-[#DE2910] border-[#DE2910] text-white' 
                    : 'border-gray-300 bg-white'
                }`}>
                  {formData.hasUnderstood && <CheckIcon className="w-3 h-3" />}
                </div>
              </div>
              <div className="flex-1">
                <span className="text-gray-900">
                  I have understood and confirmed that I comply with the contents of the{' '}
                  <Link href="/compliance/customer-statement" target="_blank" className="text-[#DE2910] hover:text-[#DE2910]/80 underline">
                    "customer compliance statement"
                  </Link>
                  . Always keep the above selection and do not ask again.
                </span>
              </div>
            </label>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 转售问题 */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Will components purchased from China Electron be resold by you in their purchased form?
              </h3>
              <div className="space-y-3">
                <label className="flex items-center gap-3 cursor-pointer">
                  <div className="relative">
                    <input
                      type="radio"
                      name="willResell"
                      value="yes"
                      checked={formData.willResell === 'yes'}
                      onChange={(e) => setFormData(prev => ({ ...prev, willResell: e.target.value }))}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded-full flex items-center justify-center transition-colors ${
                      formData.willResell === 'yes'
                        ? 'border-[#DE2910]'
                        : 'border-gray-300'
                    }`}>
                      {formData.willResell === 'yes' && (
                        <div className="w-2.5 h-2.5 bg-[#DE2910] rounded-full"></div>
                      )}
                    </div>
                  </div>
                  <span className="text-gray-900">Yes</span>
                </label>
                <label className="flex items-center gap-3 cursor-pointer">
                  <div className="relative">
                    <input
                      type="radio"
                      name="willResell"
                      value="no"
                      checked={formData.willResell === 'no'}
                      onChange={(e) => setFormData(prev => ({ ...prev, willResell: e.target.value }))}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 border-2 rounded-full flex items-center justify-center transition-colors ${
                      formData.willResell === 'no'
                        ? 'border-[#DE2910]'
                        : 'border-gray-300'
                    }`}>
                      {formData.willResell === 'no' && (
                        <div className="w-2.5 h-2.5 bg-[#DE2910] rounded-full"></div>
                      )}
                    </div>
                  </div>
                  <span className="text-gray-900">No</span>
                </label>
              </div>
            </div>

            {/* 详细信息说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 text-sm">
                Please enter the Ultimate Consignee, the country where these products will be consumed or used in a 
                manufacturing process, the application that best describes how these products will be used, and finally any 
                additional information related to the application or Ultimate Consignee.
              </p>
            </div>

            {/* Ultimate Consignee */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                <span className="text-red-500">*</span>
                Ultimate Consignee
                <InfoIcon className="text-gray-400" />
              </label>
              <input
                type="text"
                value={formData.ultimateConsignee}
                onChange={(e) => setFormData(prev => ({ ...prev, ultimateConsignee: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                placeholder="Enter ultimate consignee"
                required
              />
            </div>

            {/* Country */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                <span className="text-red-500">*</span>
                Country
              </label>
              <select
                value={formData.country}
                onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                required
              >
                {countries.map((country) => (
                  <option key={country} value={country}>
                    {country}
                  </option>
                ))}
              </select>
            </div>

            {/* Application */}
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-gray-700 mb-2">
                <span className="text-red-500">*</span>
                What application best describe how the products will be used?
              </label>
              <select
                value={formData.application}
                onChange={(e) => setFormData(prev => ({ ...prev, application: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                required
              >
                {applications.map((app) => (
                  <option key={app} value={app}>
                    {app}
                  </option>
                ))}
              </select>
            </div>

            {/* Additional Information */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Please provide any additional information related to the application. (optional)
              </label>
              <textarea
                value={formData.additionalInfo}
                onChange={(e) => setFormData(prev => ({ ...prev, additionalInfo: e.target.value }))}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#DE2910]/50"
                placeholder="Enter additional information..."
              />
            </div>

            {/* Set as Default */}
            <div>
              <label className="flex items-center gap-3 cursor-pointer">
                <div className="relative">
                  <input
                    type="checkbox"
                    checked={formData.setAsDefault}
                    onChange={(e) => setFormData(prev => ({ ...prev, setAsDefault: e.target.checked }))}
                    className="sr-only"
                  />
                  <div className={`w-5 h-5 border-2 rounded flex items-center justify-center transition-colors ${
                    formData.setAsDefault 
                      ? 'bg-[#DE2910] border-[#DE2910] text-white' 
                      : 'border-gray-300 bg-white'
                  }`}>
                    {formData.setAsDefault && <CheckIcon className="w-3 h-3" />}
                  </div>
                </div>
                <span className="text-gray-900">Set As Default</span>
              </label>
            </div>

            {/* 提交按钮 */}
            <div className="flex justify-end gap-4 pt-6 border-t border-gray-200">
              <Link
                href="/checkout"
                className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={!formData.hasUnderstood || !formData.ultimateConsignee || loading}
                className="px-6 py-2 bg-[#DE2910] text-white rounded-lg hover:bg-[#DE2910]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Saving...' : 'Save Compliance Statement'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
