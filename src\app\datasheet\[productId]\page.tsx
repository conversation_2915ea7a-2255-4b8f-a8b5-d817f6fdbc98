import { Suspense } from 'react'
import { notFound } from 'next/navigation'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import SimplePDFViewer from '@/components/SimplePDFViewer'
import Link from 'next/link'

// 从新URL格式中提取产品ID
function extractProductIdFromUrl(urlPath: string): string {
  // console.log('Extracting product ID from URL path:', urlPath);

  // 针对CMA格式产品ID的精确匹配
  const cmaMatch = urlPath.match(/CMA\d{9,12}/);
  if (cmaMatch) {
    const productId = cmaMatch[0];
    // console.log('Direct CMA match found:', productId);
    return productId;
  }

  // 对于包含括号和特殊字符的复杂URL，先进行URL解码
  const decodedPath = decodeURIComponent(urlPath);
  // 尝试从解码后的URL中找CMA格式
  const decodedCmaMatch = decodedPath.match(/CMA\d{9,12}/);
  if (decodedCmaMatch) {
    const productId = decodedCmaMatch[0];
    // console.log('Found CMA product ID after URL decoding:', productId);
    return productId;
  }

  // 检查URL是否以CMA结尾
  if (/CMA\d+$/.test(urlPath)) {
    const parts = urlPath.split('-');
    const lastPart = parts[parts.length - 1];
    // console.log('Found CMA ID at end of URL:', lastPart);
    return lastPart;
  }

  // 分割路径并检查每个部分
  try {
    // 对于包含横杠的URL，分割并检查每个部分
    const parts = urlPath.split('-');
    // console.log('URL parts:', parts);

    // 从尾部开始查找CMA格式的部分
    for (let i = parts.length - 1; i >= 0; i--) {
      const part = parts[i];
      if (part && part.includes('CMA')) {
        const cmaMatch = part.match(/CMA\d+/);
        if (cmaMatch) {
          // console.log(`Found CMA ID in part ${i}:`, cmaMatch[0]);
          return cmaMatch[0];
        }
      }
    }

    // 检查最后一部分是否为产品ID格式
    if (parts.length > 0) {
      const lastPart = parts[parts.length - 1];
      // console.log('Last part of URL:', lastPart);
      if (/^CMA\d+$/.test(lastPart) || /^\d+$/.test(lastPart)) {
        // console.log('Last part is a valid product ID:', lastPart);
        return lastPart;
      }
    }

    // 如果我们有足够的部分，假设最后一个是产品ID（新URL格式）
    if (parts.length >= 6) {
      const productId = parts[parts.length - 1];
      // console.log('Using last part as product ID based on URL format:', productId);
      return productId;
    }
  } catch (error) {
    console.error('Error parsing URL parts:', error);
  }

  // 如果所有方法都失败，返回原始路径
  return urlPath;
}

interface ProductDetail {
  product_id: string
  model: string
  brand: {
    id: string
    name_en: string
    name_cn: string
    name_ru: string
    logo_url: string
    website: string
  }
  datasheet_url: string
  description: string
}

// 获取datasheet详情页URL的函数
async function getDatasheetPageUrl(productId: string): Promise<string | null> {
  try {
    const response = await fetch(
      `https://webapi.chinaelectron.com/datasheet/product?product_id=${encodeURIComponent(productId)}`,
      {
        headers: {
          'Accept': 'application/json',
        },
        next: { revalidate: 3600 }
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch datasheet URL: ${response.status}`);
    }

    const data = await response.json();

    if (data.exists && data.datasheet_url) {
      return data.datasheet_url;
    }

    return null;
  } catch (err) {
    console.error('Error fetching datasheet URL:', err);
    return null;
  }
}

// 获取产品数据的函数，在服务端执行
async function getProductDetail(productId: string) {
  try {
    const headers: HeadersInit = {
      'Accept': 'application/json',
      'Origin': process.env.NEXT_PUBLIC_SITE_URL || 'https://example.com'
    }

    const response = await fetch(
      `https://webapi.chinaelectron.com/products/${encodeURIComponent(productId)}`,
      {
        headers,
        next: { revalidate: 3600 }
      }
    )

    if (!response.ok) {
      if (response.status === 404) {
        return null
      }
      throw new Error(`Failed to fetch product details: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()

    if (!data || !data.product_id) {
      return null
    }

    return data as ProductDetail
  } catch (err) {
    console.error('Fetch error:', err)
    throw new Error('Failed to fetch product details')
  }
}

// 为元数据生成函数，用于SEO
export async function generateMetadata(props: { params: Promise<{ productId: string }> }) {
  const params = await props.params
  const rawProductId = params.productId
  const productId = extractProductIdFromUrl(rawProductId)
  const productDetail = await getProductDetail(productId)

  if (!productDetail) {
    return {
      title: 'Datasheet Not Found',
      description: 'The requested product datasheet could not be found'
    }
  }

  const brandName = productDetail.brand?.name_en || 'Unknown Brand'

  // 通过新API获取datasheet详情页URL作为canonical URL
  const datasheetPageUrl = await getDatasheetPageUrl(productId)

  return {
    title: `${productDetail.model} Datasheet | ${brandName} | China Electron`,
    description: `Free download the datasheet pdf for ${productDetail.model} by ${brandName}. Technical specifications`,
    keywords: `${productDetail.model} datasheet, ${brandName}, ${productDetail.model} specifications, ${productDetail.model} pinout, technical documentation`,
    alternates: {
      canonical: datasheetPageUrl || `https://www.chinaelectron.com/datasheet/${productId}`,
    },
  }
}

// PDF查看器客户端组件
function DatasheetViewer({ productDetail }: { productDetail: ProductDetail }) {
  if (!productDetail.datasheet_url) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-screen-2xl mx-auto px-6 py-8">
          <div className="bg-white rounded-lg shadow-md p-8 text-center">
            <div className="text-gray-500 mb-4">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">No Datasheet Available</h2>
              <p className="text-gray-600">The datasheet for this product is not currently available.</p>
            </div>
            <Link 
              href={`/product/${productDetail.product_id}`}
              className="inline-flex items-center gap-2 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Product
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-screen-2xl mx-auto px-6 py-4">
        {/* 页面头部 - 压缩高度 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-4 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-bold text-gray-900 mb-1">
                {productDetail.model} Datasheet
              </h1>
              <p className="text-gray-600 text-sm">
                {productDetail.brand?.name_en} • {productDetail.description}
              </p>
            </div>
            <Link
              href={`/product/${productDetail.product_id}`}
              className="inline-flex items-center gap-2 bg-gray-100 text-gray-700 px-3 py-2 rounded hover:bg-gray-200 transition-colors text-sm"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              Back to Product
            </Link>
          </div>
        </div>

        {/* PDF查看器 - 增加高度 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden" style={{ height: 'calc(100vh - 180px)' }}>
          <Suspense fallback={
            <div className="flex justify-center items-center h-96">
              <LoadingSpinner />
            </div>
          }>
            <SimplePDFViewer
              url={productDetail.datasheet_url}
              productName={productDetail.model}
              productId={productDetail.product_id}
              className="h-full"
            />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

// 主页面组件 - 服务端渲染
export default async function DatasheetPage(props: { params: Promise<{ productId: string }> }) {
  const params = await props.params
  const rawProductId = params.productId
  const productId = extractProductIdFromUrl(rawProductId)
  const productDetail = await getProductDetail(productId)
  
  // 如果产品不存在，返回404
  if (!productDetail) {
    notFound()
  }

  return <DatasheetViewer productDetail={productDetail} />
}
