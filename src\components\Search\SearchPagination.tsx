'use client'

import { useRouter } from 'next/navigation'
import ProductTable from './ProductTable'

interface ProductPrice {
  quantity: number
  price: number
}

interface Brand {
  name_cn: string | null
  name_en: string | null
  name_ru: string | null
  logo_url?: string
  website?: string
}

interface Parameter {
  param_name: string
  param_value: string
}

interface Product {
  product_id: string;
  model: string;
  brand_id: string;
  brand: Brand;
  price_key: string;
  stock_key: string;
  datasheet_url: string;
  parameters_key: string;
  parameters?: {
    english?: Parameter[];
    chinese?: Parameter[];
    russian?: Parameter[];
  };
  description: string;
  prices: ProductPrice[];
  stock: number;
  image_list?: string[];
  category_path?: string[];
  category_names?: {
    [key: string]: {
      name_cn: string;
      name_en: string;
      name_ru: string;
    }
  };
  category_id?: string;
}

interface SearchPaginationProps {
  products: Product[];
  totalResults: number;
  currentPage: number;
  query: string;
  category: string;
  brand: string;
}

export function SearchPagination({ products, totalResults, currentPage, query, category, brand }: SearchPaginationProps) {
  const router = useRouter();

  // 处理页面导航
  const navigateToPage = (newPage: number) => {
    const params = new URLSearchParams();
    if (query) params.set('q', query);
    if (category) params.set('category', category);
    if (brand) params.set('brand', brand);
    params.set('page', newPage.toString());
    
    router.push(`/search?${params.toString()}`);
  };

  return (
    <ProductTable 
      products={products} 
      totalResults={totalResults} 
      currentPage={currentPage}
      navigateToPage={navigateToPage}
    />
  );
} 