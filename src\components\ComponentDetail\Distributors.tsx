'use client'

import Image from 'next/image'
import Link from 'next/link'
import { ComponentDetail, FullData } from '@/types/component'

interface DistributorsProps {
  component: ComponentDetail
  parsedData: FullData
}

// 定义分销商接口
interface Distributor {
  distributor_id: string;
  name_cn: string;
  name_en: string;
  name_ru: string;
  logo: string;
  address_cn: string;
  address_en: string;
  address_ru: string;
}

// 处理分销商图片URL
function getDistributorImageUrl(logo: string): string {
  if (!logo) {
    return 'https://placehold.co/300x150/e0e0e0/cccccc?text=No+Logo';
  }
  
  // 如果已经是完整URL则直接返回
  if (logo.startsWith('http')) {
    return logo;
  }
  
  // 否则，拼接基础URL
  return `https://webapi.chinaelectron.com/distributors/image/${encodeURIComponent(logo)}`;
}

export default function Distributors({ component, parsedData }: DistributorsProps) {
  // 从parsedData中获取分销商数据
  const distributors = (parsedData as any).distributors || [];
  
  if (!distributors.length) {
    return null;
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Distributors</h2>
      
      <div className="space-y-3 max-h-[400px] overflow-y-auto pr-2">
        {distributors.map((distributor: Distributor) => (
          <Link
            key={distributor.distributor_id}
            href={`/distributors/${distributor.distributor_id}`}
            className="flex items-center p-3 bg-gray-50 rounded-lg border border-gray-100 hover:border-[#DE2910]/20 hover:shadow-sm transition-all w-full"
          >
            <div className="h-8 w-8 relative mr-3 flex-shrink-0">
              <Image
                src={getDistributorImageUrl(distributor.logo)}
                alt={distributor.name_en || distributor.name_cn}
                fill
                className="object-contain"
                sizes="32px"
              />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {distributor.name_en || distributor.name_cn}
              </p>
            </div>
            <div className="flex items-center ml-2">
              <span className="px-2.5 py-0.5 text-xs bg-blue-50 text-blue-600 rounded-full">
                RFQ
              </span>
            </div>
          </Link>
        ))}
      </div>
    </div>
  )
} 