// 分销商列表组件 

interface DistributorListProps {
  productId: string
}

export default function DistributorList({ productId }: DistributorListProps) {
  // 临时模拟数据
  const distributors = [
    {
      id: 1,
      name: "Distributor A",
      country: "USA xxxx", // 地区信息
      stock: 1000,
      price: "$10.99",
      dc: "A123", // 批次信息
      package: "Standard", // 封装信息
      note: "Original", // 说明信息
    },
    {
      id: 2,
      name: "Distributor B",
      country: "China xxxx",
      stock: 500,
      price: "$9.99",
      dc: "B456",
      package: "Standard",
      note: "Original",
    }
  ]

  return (
    <div className="px-6 pb-6">
      {/* 表格标题 */}
     
      <div className="overflow-x-auto rounded-xl border border-gray-100">
        <table className="w-full text-sm">
          <thead>
            <tr className="bg-gray-50 border-b border-gray-100">
              <th className="text-left py-3 px-4 font-medium text-gray-500">Distributor</th>
              <th className="text-left py-3 px-4 font-medium text-gray-500">Country</th>
              <th className="text-left py-3 px-4 font-medium text-gray-500">Stock</th>
              <th className="text-left py-3 px-4 font-medium text-gray-500">Price</th>
              <th className="text-left py-3 px-4 font-medium text-gray-500">D/C</th>
              <th className="text-left py-3 px-4 font-medium text-gray-500">Package</th>
              <th className="text-left py-3 px-4 font-medium text-gray-500">Note</th>
              <th className="text-right py-3 px-4 font-medium text-gray-500">
                <div className="flex justify-end gap-3">
                  <span>Contact</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {distributors.map(dist => (
              <tr key={dist.id} className="hover:bg-gray-50/50 transition-colors">
                <td className="py-3 px-4 font-medium text-gray-900">{dist.name}</td>
                <td className="py-3 px-4 text-gray-600">{dist.country}</td>
                <td className="py-3 px-4 text-gray-600">{dist.stock}</td>
                <td className="py-3 px-4 font-medium text-[#DE2910]">{dist.price}</td>
                <td className="py-3 px-4 text-gray-600">{dist.dc}</td>
                <td className="py-3 px-4 text-gray-600">{dist.package}</td>
                <td className="py-3 px-4 text-gray-600">{dist.note}</td>
                <td className="py-3 px-4">
                  <div className="flex justify-end gap-2">
                    <button className="p-1.5 hover:bg-[#DE2910]/5 rounded-lg transition-colors group" 
                            title="Send Email">
                      <svg className="w-5 h-5 text-gray-400 group-hover:text-[#DE2910] transition-colors" 
                           fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </button>
                    <button className="p-1.5 hover:bg-[#DE2910]/5 rounded-lg transition-colors group" 
                            title="WhatsApp Contact">
                      <svg className="w-5 h-5 text-gray-400 group-hover:text-[#DE2910] transition-colors" 
                           fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    </button>
                    <button className="p-1.5 hover:bg-[#DE2910]/5 rounded-lg transition-colors group" 
                            title="More Links">
                      <svg className="w-5 h-5 text-gray-400 group-hover:text-[#DE2910] transition-colors" 
                           fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
} 