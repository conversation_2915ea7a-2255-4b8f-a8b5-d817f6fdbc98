import Link from 'next/link'

export default function PopularParts() {
  const popularParts = [
    'PESD0603-240', 'ERJ2RKF10R0X', 'ERJ2RKF1001X', 'ERJ2GE0R00X', 'GRM188R6YA106MA73D',
    'ERJ3EKF1002V', 'LIS2DW12TR', 'DMP3098L-7', '0467.250NRHF', 'CUS10S30,H3F',
    'ERJ3GEY0R00V', 'ERJ3EKF1001V', 'LIS2DH12TR', 'BLM21PG221SN1D', 'B560C-13-F',
    'B340A-13-F', '1N4148W-7-F', '1N5819HW-7-F', 'GRM155R71H104KE14D', 'LM317MDT-TR',
    'ULN2003D1013TR', '1N4148WS-7-F', 'AP2112K-3.3TRG1', 'MRA4007T3G', 'CL21B105KBFNNNE'
  ]

  return (
    <section className="py-8 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <h3 className="text-base font-medium text-gray-900 mb-4">Popular Searches:</h3>
        <div className="flex flex-wrap gap-2">
          {popularParts.map((part, index) => (
            <Link
              key={index}
              href={`/part/${encodeURIComponent(part)}`}
              className="inline-block text-sm text-gray-600 hover:text-[var(--primary-500)] font-mono 
                       transition-colors duration-200"
            >
              {part}
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
} 