'use client'
import { useState, useEffect } from 'react'
import Image from 'next/image'

export default function Carousel() {
  const [currentSlide, setCurrentSlide] = useState(0)
  
  const slides = [
    { 
      id: 1, 
      image: 'https://dummyimage.com/400x300/f0f0f0/333333.png&text=Smartphone',
      title: 'Hot Products Today',
      subtitle: 'Smartphone 6S 32GB LTE',
      price: '$1,100.00',
      originalPrice: '$1,250.00',
      category: 'Smart Phones & Tablets'
    },
    { 
      id: 2, 
      image: 'https://dummyimage.com/400x300/f0f0f0/333333.png&text=Ultrabook',
      title: 'Hot Products Today',
      subtitle: 'Ultrabook UX305CA-FC050T',
      price: '$1,200.00',
      originalPrice: '$1,350.00',
      category: 'Smart Phones & Tablets'
    },
    { 
      id: 3, 
      image: 'https://dummyimage.com/400x300/f0f0f0/333333.png&text=Headphones',
      title: 'Hot Products Today',
      subtitle: 'Wireless Noise Cancelling Headphones',
      price: '$299.00',
      originalPrice: '$349.00',
      category: 'Audio & Accessories'
    }
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="relative bg-white border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex items-center mb-4">
          <div className="text-amber-500 mr-2">🔥</div>
          <h2 className="text-xl font-semibold text-gray-800">Hot Products Today</h2>
        </div>
        
        <div className="relative h-[300px] bg-white rounded-lg shadow-sm border border-gray-100 p-4 overflow-hidden">
          <div className="flex h-full">
            {slides.map((slide, index) => (
              <div
                key={slide.id}
                className={`absolute w-full h-full transition-all duration-700 ease-in-out
                          ${index === currentSlide ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full'}`}
              >
                <div className="grid grid-cols-1 md:grid-cols-2 h-full">
                  <div className="flex flex-col justify-center p-4">
                    <div className="text-sm text-gray-500 mb-1">{slide.category}</div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{slide.subtitle}</h3>
                    <div className="flex items-baseline mb-4">
                      <span className="text-red-500 font-bold text-xl">{slide.price}</span>
                      <span className="text-gray-400 line-through text-sm ml-2">{slide.originalPrice}</span>
                    </div>
                    <div className="flex space-x-2">
                      <button className="p-2 bg-gray-100 rounded-full hover:bg-gray-200 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 10.5V6a3.75 3.75 0 1 0-7.5 0v4.5m11.356-1.993 1.263 12c.07.665-.45 1.243-1.119 1.243H4.25a1.125 1.125 0 0 1-1.12-1.243l1.264-12A1.125 1.125 0 0 1 5.513 7.5h12.974c.576 0 1.059.435 1.119 1.007Z" />
                        </svg>
                      </button>
                      <button className="p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div className="flex items-center justify-center relative h-full">
                    <Image
                      src={slide.image}
                      alt={slide.subtitle}
                      width={200}
                      height={200}
                      className="object-contain"
                      priority={index === 0}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="flex justify-center mt-4 gap-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`h-2 rounded-full transition-all duration-300 
                        ${index === currentSlide 
                          ? 'w-6 bg-blue-500' 
                          : 'w-2 bg-gray-300 hover:bg-gray-400'}`}
            />
          ))}
        </div>
      </div>
    </div>
  )
} 