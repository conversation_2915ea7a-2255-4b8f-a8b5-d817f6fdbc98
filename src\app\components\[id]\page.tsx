'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import LoadingSpinner from '@/components/common/LoadingSpinner'
import AIAssistant from '@/components/ComponentDetail/AIAssistant'
import ComponentOverview from '@/components/ComponentDetail/ComponentOverview'
import Specifications from '@/components/ComponentDetail/Specifications'
import PriceAndStock from '@/components/ComponentDetail/PriceAndStock'
import DocumentsAndFiles from '@/components/ComponentDetail/DocumentsAndFiles'
import { ComponentDetail, FullData, APIResponse } from '@/types/component'
import ProductJsonLd from '@/components/SEO/ProductJsonLd'

interface ProductParameter {
  param_name: string
  param_value: string
}

interface ProductDetail {
  product_id: string
  model: string
  url?: string // 添加url字段
  brand: {
    id: string
    name_en: string
    name_cn: string
    name_ru: string
    logo_url: string
    website: string
  }
  prices: {
    quantity: number
    price: number
  }[]
  stock: number
  parameters: {
    en: ProductParameter[]
    cn: ProductParameter[]
    ru: ProductParameter[]
  }
  category_path: string[]
  category_id: string
  category_names: {
    [key: string]: {
      name_cn: string
      name_en: string
      name_ru: string
    }
  }
  datasheet_url: string
  image_list: string[]
  description: string
  rohs?: string | null
}

export default function ComponentDetailPage() {
  const [productDetail, setProductDetail] = useState<ProductDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  useEffect(() => {
    const fetchProductDetail = async () => {
      setLoading(true)
      setError('')
      
      try {
        const token = localStorage.getItem('token')
        if (!token) {
          router.push('/login')
          return
        }

        // console.log('Fetching product details for:', productId)

        const response = await fetch(
          `https://webapi.chinaelectron.com/products/${encodeURIComponent(productId)}`,
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Accept': 'application/json',
              'Origin': window.location.origin
            }
          }
        )

        if (!response.ok) {
          console.error('Response not ok:', response.status, response.statusText)
          if (response.status === 401) {
            localStorage.removeItem('token')
            router.push('/login')
            return
          }
          throw new Error(`Failed to fetch product details: ${response.status} ${response.statusText}`)
        }

        const data = await response.json()
        // console.log('Product detail data:', data)
        
        if (!data || !data.product_id) {
          throw new Error('Invalid product data received')
        }
        
        setProductDetail(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Error loading product details')
        console.error('Fetch error:', err)
      } finally {
        setLoading(false)
      }
    }

    if (productId) {
      fetchProductDetail()
    }
  }, [productId, router])

  // 转换产品数据为组件需要的数据格式
  const convertToComponentFormat = (): { component: ComponentDetail, parsedData: FullData } | null => {
    if (!productDetail) return null;
    
    // 创建ComponentDetail对象
    const component: ComponentDetail = {
      id: productDetail.product_id,
      score: 1, // 默认值
      metadata: {
        id: productDetail.product_id,
        model: productDetail.model,
        name: productDetail.model,
        desc: productDetail.description,
        manufacture: productDetail.brand.name_en,
        img: productDetail.image_list?.[0] || '',
        datasheet: productDetail.datasheet_url || '',
        cad: '',
        full_data: '' // 将在下面填充
      },
      values: []
    } as ComponentDetail;
    
    // 创建FullData对象并适配FullData接口的形式
    const parsedData = {
      name: productDetail.model,
      desc: productDetail.description,
      model: productDetail.model,
      location: '', // 默认值
      manufacture: productDetail.brand.name_en,
      parameter: {
        languages: {
          english: {
            name: productDetail.model,
            desc: productDetail.description,
            parameters: {
              main_parameters: Object.fromEntries(
                (productDetail.parameters?.en || []).map(p => [p.param_name, p.param_value])
              ),
              secondary_parameters: {},
              common_parameters: {}
            },
            features: [],
            applications: []
          },
          chinese: {},
          russian: {}
        }
      },
      img: productDetail.image_list || [],
      datasheet: productDetail.datasheet_url ? [productDetail.datasheet_url] : [],
      cad: [],
      update_time: new Date().toISOString()
    } as FullData;
    
    // 将parsedData转为字符串并保存到component.metadata.full_data
    component.metadata.full_data = JSON.stringify(parsedData);
    
    return { component, parsedData };
  };

  const componentData = convertToComponentFormat();

  // 构建产品的完整 URL - 优先使用url字段，回退到手动构建
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://www.chinaelectron.com';
  const productUrl = productDetail?.url
    ? (productDetail.url.startsWith('http') ? productDetail.url : `${baseUrl}${productDetail.url}`)
    : `${baseUrl}/components/${productId}`;

  return (
    <div className="min-h-screen relative">
      {/* 添加 JSON-LD 结构化数据 */}
      {productDetail && (
        <ProductJsonLd
          productDetail={productDetail}
          productUrl={productUrl}
          componentId={productId}
        />
      )}

      {/* 背景装饰 */}
      <div className="fixed inset-0 bg-gradient-to-b from-gray-50 to-gray-100 z-[-2]" />
      <div className="fixed inset-0 z-[-1]">
        <div className="absolute inset-0"
             style={{
               backgroundImage: `radial-gradient(circle at center, #64748B 2px, transparent 0.7px)`,
               backgroundSize: '24px 24px',
               backgroundPosition: '0 0',
               opacity: 0.12
             }}
        />
        <div className="absolute inset-0 bg-gradient-to-b from-[#DE2910]/3 via-transparent to-transparent" />
      </div>

      <div className="relative pt-20">
        <div className="max-w-screen-2xl mx-auto px-6 py-8">
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <LoadingSpinner size="lg" />
            </div>
          ) : error ? (
            <div className="text-center p-8 rounded-2xl bg-red-50 border border-red-100">
              <svg className="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-red-600 font-medium">{error}</p>
            </div>
          ) : componentData ? (
            <div className="grid grid-cols-[400px,1fr] gap-8">
              <div>
                <AIAssistant component={componentData.component} parsedData={componentData.parsedData} />
              </div>

              <div className="space-y-6">
                <ComponentOverview component={componentData.component} parsedData={componentData.parsedData} />
                <Specifications component={componentData.component} parsedData={componentData.parsedData} />
                <PriceAndStock component={componentData.component} parsedData={componentData.parsedData} />
                <DocumentsAndFiles component={componentData.component} parsedData={componentData.parsedData} />
              </div>
            </div>
          ) : (
            <div className="text-center p-8 rounded-2xl bg-gray-50 border border-gray-100">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} 
                      d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-600 font-medium">Product not found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 