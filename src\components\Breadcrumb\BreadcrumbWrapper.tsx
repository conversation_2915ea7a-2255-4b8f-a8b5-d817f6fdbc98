'use client';

import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import Breadcrumb from './Breadcrumb'

export default function BreadcrumbWrapper() {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);
  
  // 在客户端渲染后标记组件为已挂载
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // 服务器端渲染时不显示任何内容，避免水合错误
  if (!mounted) {
    return null;
  }
  
  // 客户端渲染完成后，根据路径决定是否显示面包屑
  // 不在首页显示面包屑
  if (pathname === '/') {
    return null;
  }

  return (
    <div className="w-full">
      <div className="max-w-[1900px] mx-auto">
        <Breadcrumb pathname={pathname} />
      </div>
    </div>
  );
} 