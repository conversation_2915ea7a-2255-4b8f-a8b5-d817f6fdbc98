import Link from 'next/link';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  baseUrl: string;
  searchParam?: string;
}

export default function Pagination({ currentPage, totalPages, baseUrl, searchParam }: PaginationProps) {
  // Generate the array of page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5; // Maximum number of visible page numbers
    
    // Calculate which page numbers to show
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    // Adjust to ensure we show the correct number of pages
    if (endPage - startPage + 1 < maxVisiblePages && startPage > 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    // Generate the array of page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };
  
  // Generate URL with search parameters
  const getPageUrl = (page: number) => {
    const url = new URL(baseUrl, 'http://placeholder');
    url.searchParams.set('page', page.toString());
    if (searchParam) {
      url.searchParams.set('search', searchParam);
    }
    return `${url.pathname}${url.search}`;
  };
  
  const pageNumbers = getPageNumbers();
  
  return (
    <div className="flex justify-center items-center space-x-1">
      {/* Previous page button */}
      {currentPage > 1 && (
        <Link
          href={getPageUrl(currentPage - 1)}
          className="px-3 py-1 rounded border border-gray-300 text-gray-700 hover:bg-gray-100"
        >
          Previous
        </Link>
      )}
      
      {/* First page and ellipsis */}
      {pageNumbers[0] > 1 && (
        <>
          <Link
            href={getPageUrl(1)}
            className="px-3 py-1 rounded border border-gray-300 text-gray-700 hover:bg-gray-100"
          >
            1
          </Link>
          {pageNumbers[0] > 2 && (
            <span className="px-3 py-1 text-gray-500">...</span>
          )}
        </>
      )}
      
      {/* Page numbers */}
      {pageNumbers.map(page => (
        <Link
          key={page}
          href={getPageUrl(page)}
          className={`px-3 py-1 rounded border ${
            page === currentPage 
              ? 'bg-[#DE2910] text-white border-[#DE2910]' 
              : 'border-gray-300 text-gray-700 hover:bg-gray-100'
          }`}
        >
          {page}
        </Link>
      ))}
      
      {/* Ellipsis and last page */}
      {pageNumbers[pageNumbers.length - 1] < totalPages && (
        <>
          {pageNumbers[pageNumbers.length - 1] < totalPages - 1 && (
            <span className="px-3 py-1 text-gray-500">...</span>
          )}
          <Link
            href={getPageUrl(totalPages)}
            className="px-3 py-1 rounded border border-gray-300 text-gray-700 hover:bg-gray-100"
          >
            {totalPages}
          </Link>
        </>
      )}
      
      {/* Next page button */}
      {currentPage < totalPages && (
        <Link
          href={getPageUrl(currentPage + 1)}
          className="px-3 py-1 rounded border border-gray-300 text-gray-700 hover:bg-gray-100"
        >
          Next
        </Link>
      )}
    </div>
  );
} 